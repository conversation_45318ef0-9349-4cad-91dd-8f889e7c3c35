import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';

/// خدمة التحليلات
/// 
/// تجمع وتحلل بيانات استخدام التطبيق
/// تساعد في فهم سلوك المستخدمين وتحسين التطبيق
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  /// قائمة الأحداث المسجلة
  final List<Map<String, dynamic>> _events = [];
  
  /// معرف الجلسة الحالية
  late String _sessionId;
  
  /// وقت بداية الجلسة
  late DateTime _sessionStartTime;
  
  /// إحصائيات الاستخدام
  final Map<String, dynamic> _usageStats = {};

  /// تهيئة خدمة التحليلات
  void initialize() {
    _sessionId = _generateSessionId();
    _sessionStartTime = DateTime.now();
    
    _recordEvent('app_start', {
      'timestamp': _sessionStartTime.toIso8601String(),
      'platform': defaultTargetPlatform.name,
    });
    
    if (kDebugMode) {
      print('📊 تم تهيئة خدمة التحليلات - جلسة: $_sessionId');
    }
  }

  /// توليد معرف جلسة فريد
  String _generateSessionId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(9999);
    return 'session_${timestamp}_$randomNum';
  }

  /// تسجيل حدث
  void recordEvent(String eventName, [Map<String, dynamic>? parameters]) {
    _recordEvent(eventName, parameters ?? {});
  }

  /// تسجيل حدث داخلي
  void _recordEvent(String eventName, Map<String, dynamic> parameters) {
    final event = {
      'eventName': eventName,
      'parameters': parameters,
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': _sessionId,
    };
    
    _events.add(event);
    _updateUsageStats(eventName, parameters);
    
    // الاحتفاظ بآخر 1000 حدث فقط
    if (_events.length > 1000) {
      _events.removeAt(0);
    }
    
    if (kDebugMode) {
      print('📝 حدث مسجل: $eventName - ${jsonEncode(parameters)}');
    }
  }

  /// تحديث إحصائيات الاستخدام
  void _updateUsageStats(String eventName, Map<String, dynamic> parameters) {
    // عدد الأحداث
    _usageStats['totalEvents'] = (_usageStats['totalEvents'] ?? 0) + 1;
    
    // عدد الأحداث حسب النوع
    final eventCounts = _usageStats['eventCounts'] as Map<String, int>? ?? <String, int>{};
    eventCounts[eventName] = (eventCounts[eventName] ?? 0) + 1;
    _usageStats['eventCounts'] = eventCounts;
    
    // آخر نشاط
    _usageStats['lastActivity'] = DateTime.now().toIso8601String();
  }

  /// تسجيل زيارة صفحة
  void recordPageView(String pageName, [Map<String, dynamic>? parameters]) {
    recordEvent('page_view', {
      'page_name': pageName,
      ...?parameters,
    });
  }

  /// تسجيل نقرة على زر
  void recordButtonClick(String buttonName, [Map<String, dynamic>? parameters]) {
    recordEvent('button_click', {
      'button_name': buttonName,
      ...?parameters,
    });
  }

  /// تسجيل بحث
  void recordSearch(String query, [Map<String, dynamic>? parameters]) {
    recordEvent('search', {
      'query': query,
      'query_length': query.length,
      ...?parameters,
    });
  }

  /// تسجيل مشاركة محتوى
  void recordShare(String contentType, String contentId, [Map<String, dynamic>? parameters]) {
    recordEvent('share', {
      'content_type': contentType,
      'content_id': contentId,
      ...?parameters,
    });
  }

  /// تسجيل خطأ
  void recordError(String errorType, String errorMessage, [Map<String, dynamic>? parameters]) {
    recordEvent('error', {
      'error_type': errorType,
      'error_message': errorMessage,
      ...?parameters,
    });
  }

  /// تسجيل وقت قضاه المستخدم في صفحة
  void recordTimeSpent(String pageName, Duration duration) {
    recordEvent('time_spent', {
      'page_name': pageName,
      'duration_seconds': duration.inSeconds,
      'duration_minutes': duration.inMinutes,
    });
  }

  /// تسجيل تفاعل مع المحتوى
  void recordContentInteraction(String interactionType, String contentId, [Map<String, dynamic>? parameters]) {
    recordEvent('content_interaction', {
      'interaction_type': interactionType,
      'content_id': contentId,
      ...?parameters,
    });
  }

  /// تسجيل استخدام ميزة
  void recordFeatureUsage(String featureName, [Map<String, dynamic>? parameters]) {
    recordEvent('feature_usage', {
      'feature_name': featureName,
      ...?parameters,
    });
  }

  /// الحصول على إحصائيات الجلسة
  Map<String, dynamic> getSessionStats() {
    final sessionDuration = DateTime.now().difference(_sessionStartTime);
    
    return {
      'sessionId': _sessionId,
      'startTime': _sessionStartTime.toIso8601String(),
      'duration': {
        'seconds': sessionDuration.inSeconds,
        'minutes': sessionDuration.inMinutes,
        'hours': sessionDuration.inHours,
      },
      'eventsCount': _events.length,
      'usageStats': Map.from(_usageStats),
    };
  }

  /// الحصول على أكثر الصفحات زيارة
  List<Map<String, dynamic>> getMostVisitedPages() {
    final pageViews = <String, int>{};
    
    for (final event in _events) {
      if (event['eventName'] == 'page_view') {
        final pageName = event['parameters']['page_name'] as String?;
        if (pageName != null) {
          pageViews[pageName] = (pageViews[pageName] ?? 0) + 1;
        }
      }
    }
    
    final sortedPages = pageViews.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedPages.map((entry) => {
      'pageName': entry.key,
      'visits': entry.value,
    }).toList();
  }

  /// الحصول على أكثر الميزات استخداماً
  List<Map<String, dynamic>> getMostUsedFeatures() {
    final featureUsage = <String, int>{};
    
    for (final event in _events) {
      if (event['eventName'] == 'feature_usage') {
        final featureName = event['parameters']['feature_name'] as String?;
        if (featureName != null) {
          featureUsage[featureName] = (featureUsage[featureName] ?? 0) + 1;
        }
      }
    }
    
    final sortedFeatures = featureUsage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedFeatures.map((entry) => {
      'featureName': entry.key,
      'usageCount': entry.value,
    }).toList();
  }

  /// الحصول على إحصائيات البحث
  Map<String, dynamic> getSearchStats() {
    final searches = _events.where((e) => e['eventName'] == 'search').toList();
    final queries = <String>[];
    int totalSearches = searches.length;
    double averageQueryLength = 0;
    
    for (final search in searches) {
      final query = search['parameters']['query'] as String?;
      if (query != null) {
        queries.add(query);
      }
    }
    
    if (queries.isNotEmpty) {
      averageQueryLength = queries.map((q) => q.length).reduce((a, b) => a + b) / queries.length;
    }
    
    return {
      'totalSearches': totalSearches,
      'uniqueQueries': queries.toSet().length,
      'averageQueryLength': averageQueryLength.round(),
      'topQueries': _getTopQueries(queries),
    };
  }

  /// الحصول على أكثر الاستعلامات بحثاً
  List<Map<String, dynamic>> _getTopQueries(List<String> queries) {
    final queryCounts = <String, int>{};
    
    for (final query in queries) {
      queryCounts[query] = (queryCounts[query] ?? 0) + 1;
    }
    
    final sortedQueries = queryCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedQueries.take(10).map((entry) => {
      'query': entry.key,
      'count': entry.value,
    }).toList();
  }

  /// الحصول على إحصائيات الأخطاء
  Map<String, dynamic> getErrorStats() {
    final errors = _events.where((e) => e['eventName'] == 'error').toList();
    final errorTypes = <String, int>{};
    
    for (final error in errors) {
      final errorType = error['parameters']['error_type'] as String?;
      if (errorType != null) {
        errorTypes[errorType] = (errorTypes[errorType] ?? 0) + 1;
      }
    }
    
    return {
      'totalErrors': errors.length,
      'errorTypes': errorTypes,
      'errorRate': errors.length / _events.length,
    };
  }

  /// توليد تقرير تحليلي شامل
  Map<String, dynamic> generateAnalyticsReport() {
    return {
      'session': getSessionStats(),
      'mostVisitedPages': getMostVisitedPages(),
      'mostUsedFeatures': getMostUsedFeatures(),
      'searchStats': getSearchStats(),
      'errorStats': getErrorStats(),
      'recommendations': _generateRecommendations(),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// توليد توصيات بناءً على البيانات
  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    final errorStats = getErrorStats();
    final searchStats = getSearchStats();
    
    // توصيات بناءً على الأخطاء
    if ((errorStats['totalErrors'] as int) > 10) {
      recommendations.add('معدل الأخطاء مرتفع - راجع سجلات الأخطاء وحسن التطبيق');
    }
    
    // توصيات بناءً على البحث
    if ((searchStats['totalSearches'] as int) > 50) {
      recommendations.add('المستخدمون يبحثون كثيراً - حسن نتائج البحث');
    }
    
    // توصيات بناءً على الاستخدام
    final mostUsedFeatures = getMostUsedFeatures();
    if (mostUsedFeatures.isNotEmpty) {
      final topFeature = mostUsedFeatures.first['featureName'];
      recommendations.add('الميزة الأكثر استخداماً: $topFeature - ركز على تحسينها');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('البيانات تبدو جيدة - استمر في المراقبة');
    }
    
    return recommendations;
  }

  /// تصدير البيانات
  String exportData() {
    final data = {
      'session': getSessionStats(),
      'events': _events,
      'analytics': generateAnalyticsReport(),
    };
    
    return jsonEncode(data);
  }

  /// مسح البيانات
  void clearData() {
    _events.clear();
    _usageStats.clear();
    
    if (kDebugMode) {
      print('🗑️ تم مسح بيانات التحليلات');
    }
  }

  /// إنهاء الجلسة
  void endSession() {
    final sessionDuration = DateTime.now().difference(_sessionStartTime);
    
    recordEvent('app_end', {
      'session_duration_seconds': sessionDuration.inSeconds,
      'session_duration_minutes': sessionDuration.inMinutes,
      'total_events': _events.length,
    });
    
    if (kDebugMode) {
      print('🏁 انتهت الجلسة: $_sessionId - المدة: ${sessionDuration.inMinutes} دقيقة');
    }
  }
}
