// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI',
    appId: '1:23427667665:web:6b87ae5dec2b15449f57f0',
    messagingSenderId: '23427667665',
    projectId: 'myagriculture-dc833',
    authDomain: 'myagriculture-dc833.firebaseapp.com',
    storageBucket: 'myagriculture-dc833.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI',
    appId: '1:23427667665:android:6b87ae5dec2b15449f57f0',
    messagingSenderId: '23427667665',
    projectId: 'myagriculture-dc833',
    storageBucket: 'myagriculture-dc833.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI',
    appId: '1:23427667665:ios:6b87ae5dec2b15449f57f0',
    messagingSenderId: '23427667665',
    projectId: 'myagriculture-dc833',
    storageBucket: 'myagriculture-dc833.firebasestorage.app',
    iosBundleId: 'com.example.agriculture',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI',
    appId: '1:23427667665:macos:6b87ae5dec2b15449f57f0',
    messagingSenderId: '23427667665',
    projectId: 'myagriculture-dc833',
    storageBucket: 'myagriculture-dc833.firebasestorage.app',
    iosBundleId: 'com.example.agriculture',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI',
    appId: '1:23427667665:windows:6b87ae5dec2b15449f57f0',
    messagingSenderId: '23427667665',
    projectId: 'myagriculture-dc833',
    storageBucket: 'myagriculture-dc833.firebasestorage.app',
  );
}
