import 'package:equatable/equatable.dart';

/// كيان المعلم الزراعي في طبقة المنطق التجاري
class LandmarkEntity extends Equatable {
  /// اسم المعلم الزراعي
  final String name;
  
  /// تفاصيل المعلم
  final String details;
  
  /// العمليات المرتبطة بالمعلم
  final String operations;
  
  /// رابط صورة المعلم (اختياري)
  final String? imageUrl;
  
  /// تاريخ البداية بالتقويم الميلادي
  final DateTime gregorianStartDate;
  
  /// تاريخ النهاية بالتقويم الميلادي
  final DateTime gregorianEndDate;
  
  /// تاريخ البداية بالتقويم الكريستي
  final DateTime crestStartDate;
  
  /// تاريخ النهاية بالتقويم الكريستي
  final DateTime crestEndDate;
  
  /// تاريخ البداية بالتقويم الحميري
  final DateTime himyarStartDate;
  
  /// تاريخ النهاية بالتقويم الحميري
  final DateTime himyarEndDate;

  /// إنشاء كيان معلم زراعي جديد
  const LandmarkEntity({
    required this.name,
    required this.details,
    required this.operations,
    this.imageUrl,
    required this.gregorianStartDate,
    required this.gregorianEndDate,
    required this.crestStartDate,
    required this.crestEndDate,
    required this.himyarStartDate,
    required this.himyarEndDate,
  });

  @override
  List<Object?> get props => [
        name,
        details,
        operations,
        imageUrl,
        gregorianStartDate,
        gregorianEndDate,
        crestStartDate,
        crestEndDate,
        himyarStartDate,
        himyarEndDate,
      ];
}
