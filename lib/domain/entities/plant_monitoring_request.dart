import 'package:equatable/equatable.dart';

/// كيان طلب مراقبة النبات
/// 
/// يمثل طلب مراقبة النبات في طبقة الـ Domain
/// يحتوي على جميع البيانات المطلوبة لطلب مراقبة النبات
class PlantMonitoringRequest extends Equatable {
  
  /// معرف الطلب الفريد
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// اسم المزارع
  final String farmerName;
  
  /// رقم هاتف المزارع
  final String phone;
  
  /// موقع المزرعة
  final String farmLocation;
  
  /// مساحة المزرعة بالمتر المربع
  final double farmSize;
  
  /// نوع المحصول
  final String cropType;
  
  /// نوع المراقبة المطلوبة
  final String monitoringType;
  
  /// مستوى الأولوية
  final String urgency;
  
  /// وصف المشكلة أو الهدف من المراقبة
  final String? problemDescription;
  
  /// ملاحظات إضافية
  final String? notes;
  
  /// روابط صور النبات
  final List<String> plantImageUrls;
  
  /// عدد الصور المرفقة
  final int imageCount;
  
  /// التكلفة المقدرة
  final double estimatedCost;
  
  /// الموقع الجغرافي للمزرعة
  final GpsLocation? gpsLocation;
  
  /// التاريخ المجدول للزيارة
  final DateTime? scheduledDate;
  
  /// الوقت المجدول للزيارة
  final String? scheduledTime;
  
  /// حالة الطلب
  final String status;
  
  /// معرف المهندس المعين
  final String? engineerId;
  
  /// اسم المهندس المعين
  final String? engineerName;
  
  /// حالة تحليل الذكاء الاصطناعي
  final String aiAnalysisStatus;
  
  /// نتيجة تحليل الذكاء الاصطناعي
  final Map<String, dynamic>? aiAnalysisResult;
  
  /// أولوية المهمة
  final String priority;
  
  /// المدة المقدرة للزيارة بالدقائق
  final int estimatedDuration;
  
  /// تاريخ إنشاء الطلب
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// معلومات إضافية
  final Map<String, dynamic>? metadata;

  /// منشئ كيان طلب مراقبة النبات
  const PlantMonitoringRequest({
    required this.id,
    required this.userId,
    required this.farmerName,
    required this.phone,
    required this.farmLocation,
    required this.farmSize,
    required this.cropType,
    required this.monitoringType,
    required this.urgency,
    this.problemDescription,
    this.notes,
    required this.plantImageUrls,
    required this.imageCount,
    required this.estimatedCost,
    this.gpsLocation,
    this.scheduledDate,
    this.scheduledTime,
    required this.status,
    this.engineerId,
    this.engineerName,
    required this.aiAnalysisStatus,
    this.aiAnalysisResult,
    required this.priority,
    required this.estimatedDuration,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة جديدة مع تحديث بعض الحقول
  PlantMonitoringRequest copyWith({
    String? id,
    String? userId,
    String? farmerName,
    String? phone,
    String? farmLocation,
    double? farmSize,
    String? cropType,
    String? monitoringType,
    String? urgency,
    String? problemDescription,
    String? notes,
    List<String>? plantImageUrls,
    int? imageCount,
    double? estimatedCost,
    GpsLocation? gpsLocation,
    DateTime? scheduledDate,
    String? scheduledTime,
    String? status,
    String? engineerId,
    String? engineerName,
    String? aiAnalysisStatus,
    Map<String, dynamic>? aiAnalysisResult,
    String? priority,
    int? estimatedDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PlantMonitoringRequest(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      farmerName: farmerName ?? this.farmerName,
      phone: phone ?? this.phone,
      farmLocation: farmLocation ?? this.farmLocation,
      farmSize: farmSize ?? this.farmSize,
      cropType: cropType ?? this.cropType,
      monitoringType: monitoringType ?? this.monitoringType,
      urgency: urgency ?? this.urgency,
      problemDescription: problemDescription ?? this.problemDescription,
      notes: notes ?? this.notes,
      plantImageUrls: plantImageUrls ?? this.plantImageUrls,
      imageCount: imageCount ?? this.imageCount,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      gpsLocation: gpsLocation ?? this.gpsLocation,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      status: status ?? this.status,
      engineerId: engineerId ?? this.engineerId,
      engineerName: engineerName ?? this.engineerName,
      aiAnalysisStatus: aiAnalysisStatus ?? this.aiAnalysisStatus,
      aiAnalysisResult: aiAnalysisResult ?? this.aiAnalysisResult,
      priority: priority ?? this.priority,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'farmerName': farmerName,
      'phone': phone,
      'farmLocation': farmLocation,
      'farmSize': farmSize,
      'cropType': cropType,
      'monitoringType': monitoringType,
      'urgency': urgency,
      'problemDescription': problemDescription,
      'notes': notes,
      'plantImageUrls': plantImageUrls,
      'imageCount': imageCount,
      'estimatedCost': estimatedCost,
      'gpsLocation': gpsLocation?.toMap(),
      'scheduledDate': scheduledDate?.toIso8601String(),
      'scheduledTime': scheduledTime,
      'status': status,
      'engineerId': engineerId,
      'engineerName': engineerName,
      'aiAnalysisStatus': aiAnalysisStatus,
      'aiAnalysisResult': aiAnalysisResult,
      'priority': priority,
      'estimatedDuration': estimatedDuration,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// إنشاء كائن من Map
  factory PlantMonitoringRequest.fromMap(Map<String, dynamic> map) {
    return PlantMonitoringRequest(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      farmerName: map['farmerName'] ?? '',
      phone: map['phone'] ?? '',
      farmLocation: map['farmLocation'] ?? '',
      farmSize: (map['farmSize'] ?? 0.0).toDouble(),
      cropType: map['cropType'] ?? '',
      monitoringType: map['monitoringType'] ?? '',
      urgency: map['urgency'] ?? '',
      problemDescription: map['problemDescription'],
      notes: map['notes'],
      plantImageUrls: List<String>.from(map['plantImageUrls'] ?? []),
      imageCount: map['imageCount'] ?? 0,
      estimatedCost: (map['estimatedCost'] ?? 0.0).toDouble(),
      gpsLocation: map['gpsLocation'] != null 
          ? GpsLocation.fromMap(map['gpsLocation']) 
          : null,
      scheduledDate: map['scheduledDate'] != null 
          ? DateTime.parse(map['scheduledDate']) 
          : null,
      scheduledTime: map['scheduledTime'],
      status: map['status'] ?? 'pending',
      engineerId: map['engineerId'],
      engineerName: map['engineerName'],
      aiAnalysisStatus: map['aiAnalysisStatus'] ?? 'pending',
      aiAnalysisResult: map['aiAnalysisResult'],
      priority: map['priority'] ?? 'normal',
      estimatedDuration: map['estimatedDuration'] ?? 120,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      metadata: map['metadata'],
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    farmerName,
    phone,
    farmLocation,
    farmSize,
    cropType,
    monitoringType,
    urgency,
    problemDescription,
    notes,
    plantImageUrls,
    imageCount,
    estimatedCost,
    gpsLocation,
    scheduledDate,
    scheduledTime,
    status,
    engineerId,
    engineerName,
    aiAnalysisStatus,
    aiAnalysisResult,
    priority,
    estimatedDuration,
    createdAt,
    updatedAt,
    metadata,
  ];

  @override
  String toString() {
    return 'PlantMonitoringRequest(id: $id, farmerName: $farmerName, cropType: $cropType, status: $status)';
  }
}

/// كيان الموقع الجغرافي
class GpsLocation extends Equatable {
  /// خط العرض
  final double latitude;
  
  /// خط الطول
  final double longitude;
  
  /// دقة التحديد بالمتر
  final double accuracy;
  
  /// وقت تحديد الموقع
  final DateTime? timestamp;

  /// منشئ كيان الموقع الجغرافي
  const GpsLocation({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    this.timestamp,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp?.toIso8601String(),
    };
  }

  /// إنشاء كائن من Map
  factory GpsLocation.fromMap(Map<String, dynamic> map) {
    return GpsLocation(
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      accuracy: (map['accuracy'] ?? 0.0).toDouble(),
      timestamp: map['timestamp'] != null 
          ? DateTime.parse(map['timestamp']) 
          : null,
    );
  }

  @override
  List<Object?> get props => [latitude, longitude, accuracy, timestamp];

  @override
  String toString() {
    return 'GpsLocation(lat: $latitude, lng: $longitude, accuracy: $accuracy)';
  }
}
