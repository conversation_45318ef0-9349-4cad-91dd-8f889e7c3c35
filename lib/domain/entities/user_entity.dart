import 'package:equatable/equatable.dart';

/// كيان المستخدم الأساسي في طبقة المنطق التجاري
class UserEntity extends Equatable {
  final String id;
  final String name;
  final String email;
  final String image;
  final String phone;
  final bool isVerified;
  final String address;
  final String? bio;
  final String createdAt;
  final String lastUpdated;
  final String theme;
  final String language;
  final String? specialty; // تخصص المستخدم (مرشد زراعي أو مزارع)

  const UserEntity({
    required this.id,
    required this.name,
    required this.email,
    required this.image,
    required this.phone,
    required this.isVerified,
    required this.address,
    required this.createdAt,
    required this.lastUpdated,
    required this.theme,
    required this.bio,
    required this.language,
    this.specialty, // جعل الحقل اختياريًا
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        image,
        phone,
        isVerified,
        address,
        createdAt,
        lastUpdated,
        theme,
        bio,
        language,
        specialty,
      ];
}
