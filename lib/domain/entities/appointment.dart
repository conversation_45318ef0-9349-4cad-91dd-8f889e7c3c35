import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// حالات الموعد
enum AppointmentStatus {
  /// في الانتظار
  pending('pending', 'في الانتظار'),
  
  /// مؤكد
  confirmed('confirmed', 'مؤكد'),
  
  /// قيد التنفيذ
  inProgress('in_progress', 'قيد التنفيذ'),
  
  /// مكتمل
  completed('completed', 'مكتمل'),
  
  /// ملغي
  cancelled('cancelled', 'ملغي'),
  
  /// لم يحضر
  noShow('no_show', 'لم يحضر');

  const AppointmentStatus(this.value, this.displayName);
  
  /// القيمة المخزنة
  final String value;
  
  /// الاسم المعروض
  final String displayName;

  /// تحويل من نص إلى enum
  static AppointmentStatus fromString(String value) {
    return AppointmentStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => AppointmentStatus.pending,
    );
  }
}

/// أنواع الاستشارة
enum ConsultationType {
  /// استشارة عامة
  general('general', 'استشارة عامة'),
  
  /// مشاكل الآفات
  pestControl('pest_control', 'مكافحة الآفات'),
  
  /// أمراض النبات
  plantDiseases('plant_diseases', 'أمراض النبات'),
  
  /// التسميد
  fertilization('fertilization', 'التسميد'),
  
  /// الري
  irrigation('irrigation', 'الري'),
  
  /// زراعة المحاصيل
  cropPlanting('crop_planting', 'زراعة المحاصيل'),
  
  /// الحصاد
  harvesting('harvesting', 'الحصاد'),
  
  /// تحليل التربة
  soilAnalysis('soil_analysis', 'تحليل التربة');

  const ConsultationType(this.value, this.displayName);
  
  /// القيمة المخزنة
  final String value;
  
  /// الاسم المعروض
  final String displayName;

  /// تحويل من نص إلى enum
  static ConsultationType fromString(String value) {
    return ConsultationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ConsultationType.general,
    );
  }
}

/// نموذج الموعد المحسن
/// 
/// وفق المعيار #2: Clean Architecture - Domain Entity
/// وفق المعيار #12: تعليقات عربية شاملة
class Appointment extends Equatable {
  /// معرف الموعد
  final String id;
  
  /// معرف المزارع
  final String farmerId;
  
  /// اسم المزارع
  final String farmerName;
  
  /// رقم هاتف المزارع
  final String? farmerPhone;
  
  /// معرف المرشد
  final String advisorId;
  
  /// اسم المرشد
  final String? advisorName;
  
  /// تاريخ الموعد
  final DateTime appointmentDate;
  
  /// وقت الموعد
  final TimeOfDay appointmentTime;
  
  /// نوع الاستشارة
  final ConsultationType consultationType;
  
  /// وصف المشكلة
  final String problemDescription;
  
  /// حالة الموعد
  final AppointmentStatus status;
  
  /// ملاحظات المرشد
  final String? advisorNotes;
  
  /// ملاحظات المزارع
  final String? farmerNotes;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime updatedAt;
  
  /// تاريخ التأكيد
  final DateTime? confirmedAt;
  
  /// تاريخ الإلغاء
  final DateTime? cancelledAt;
  
  /// سبب الإلغاء
  final String? cancellationReason;
  
  /// من قام بالإلغاء
  final String? cancelledBy;
  
  /// تاريخ إعادة الجدولة
  final DateTime? rescheduledAt;
  
  /// سبب إعادة الجدولة
  final String? rescheduleReason;
  
  /// تاريخ الاكتمال
  final DateTime? completedAt;
  
  /// تقييم المزارع (1-5)
  final int? farmerRating;
  
  /// تعليق المزارع
  final String? farmerFeedback;
  
  /// تقييم المرشد (1-5)
  final int? advisorRating;
  
  /// رابط الاجتماع الافتراضي (إذا كان عن بُعد)
  final String? meetingLink;
  
  /// هل الموعد عن بُعد
  final bool isVirtual;
  
  /// الموقع (إذا كان حضوري)
  final String? location;
  
  /// المدة المتوقعة بالدقائق
  final int durationMinutes;
  
  /// التكلفة
  final double? cost;
  
  /// العملة
  final String currency;

  /// منشئ نموذج الموعد
  /// 
  /// المعلمات:
  /// - [id]: معرف الموعد
  /// - [farmerId]: معرف المزارع
  /// - [farmerName]: اسم المزارع
  /// - [advisorId]: معرف المرشد
  /// - [appointmentDate]: تاريخ الموعد
  /// - [appointmentTime]: وقت الموعد
  /// - [consultationType]: نوع الاستشارة
  /// - [problemDescription]: وصف المشكلة
  /// - [status]: حالة الموعد (افتراضي: pending)
  /// - [createdAt]: تاريخ الإنشاء
  /// - [updatedAt]: تاريخ التحديث
  /// - [isVirtual]: هل الموعد عن بُعد (افتراضي: false)
  /// - [durationMinutes]: المدة بالدقائق (افتراضي: 30)
  /// - [currency]: العملة (افتراضي: SAR)
  const Appointment({
    required this.id,
    required this.farmerId,
    required this.farmerName,
    required this.advisorId,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.consultationType,
    required this.problemDescription,
    required this.createdAt,
    required this.updatedAt,
    this.farmerPhone,
    this.advisorName,
    this.status = AppointmentStatus.pending,
    this.advisorNotes,
    this.farmerNotes,
    this.confirmedAt,
    this.cancelledAt,
    this.cancellationReason,
    this.cancelledBy,
    this.rescheduledAt,
    this.rescheduleReason,
    this.completedAt,
    this.farmerRating,
    this.farmerFeedback,
    this.advisorRating,
    this.meetingLink,
    this.isVirtual = false,
    this.location,
    this.durationMinutes = 30,
    this.cost,
    this.currency = 'SAR',
  });

  /// إنشاء نسخة محدثة من الموعد
  /// 
  /// المعلمات:
  /// - جميع الحقول اختيارية للتحديث
  /// 
  /// الإرجاع: [Appointment] نسخة محدثة
  Appointment copyWith({
    String? id,
    String? farmerId,
    String? farmerName,
    String? farmerPhone,
    String? advisorId,
    String? advisorName,
    DateTime? appointmentDate,
    TimeOfDay? appointmentTime,
    ConsultationType? consultationType,
    String? problemDescription,
    AppointmentStatus? status,
    String? advisorNotes,
    String? farmerNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? confirmedAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    String? cancelledBy,
    DateTime? rescheduledAt,
    String? rescheduleReason,
    DateTime? completedAt,
    int? farmerRating,
    String? farmerFeedback,
    int? advisorRating,
    String? meetingLink,
    bool? isVirtual,
    String? location,
    int? durationMinutes,
    double? cost,
    String? currency,
  }) {
    return Appointment(
      id: id ?? this.id,
      farmerId: farmerId ?? this.farmerId,
      farmerName: farmerName ?? this.farmerName,
      farmerPhone: farmerPhone ?? this.farmerPhone,
      advisorId: advisorId ?? this.advisorId,
      advisorName: advisorName ?? this.advisorName,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      consultationType: consultationType ?? this.consultationType,
      problemDescription: problemDescription ?? this.problemDescription,
      status: status ?? this.status,
      advisorNotes: advisorNotes ?? this.advisorNotes,
      farmerNotes: farmerNotes ?? this.farmerNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledBy: cancelledBy ?? this.cancelledBy,
      rescheduledAt: rescheduledAt ?? this.rescheduledAt,
      rescheduleReason: rescheduleReason ?? this.rescheduleReason,
      completedAt: completedAt ?? this.completedAt,
      farmerRating: farmerRating ?? this.farmerRating,
      farmerFeedback: farmerFeedback ?? this.farmerFeedback,
      advisorRating: advisorRating ?? this.advisorRating,
      meetingLink: meetingLink ?? this.meetingLink,
      isVirtual: isVirtual ?? this.isVirtual,
      location: location ?? this.location,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      cost: cost ?? this.cost,
      currency: currency ?? this.currency,
    );
  }

  /// تحويل إلى Map
  /// 
  /// الإرجاع: [Map<String, dynamic>] بيانات الموعد
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farmerId': farmerId,
      'farmerName': farmerName,
      'farmerPhone': farmerPhone,
      'advisorId': advisorId,
      'advisorName': advisorName,
      'appointmentDate': appointmentDate.toIso8601String(),
      'appointmentTime': '${appointmentTime.hour}:${appointmentTime.minute}',
      'consultationType': consultationType.value,
      'problemDescription': problemDescription,
      'status': status.value,
      'advisorNotes': advisorNotes,
      'farmerNotes': farmerNotes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'confirmedAt': confirmedAt?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'cancellationReason': cancellationReason,
      'cancelledBy': cancelledBy,
      'rescheduledAt': rescheduledAt?.toIso8601String(),
      'rescheduleReason': rescheduleReason,
      'completedAt': completedAt?.toIso8601String(),
      'farmerRating': farmerRating,
      'farmerFeedback': farmerFeedback,
      'advisorRating': advisorRating,
      'meetingLink': meetingLink,
      'isVirtual': isVirtual,
      'location': location,
      'durationMinutes': durationMinutes,
      'cost': cost,
      'currency': currency,
    };
  }

  /// إنشاء من Map
  /// 
  /// المعلمات:
  /// - [map]: البيانات في شكل Map
  /// 
  /// الإرجاع: [Appointment] نموذج الموعد
  factory Appointment.fromMap(Map<String, dynamic> map) {
    final timeString = map['appointmentTime'] as String;
    final timeParts = timeString.split(':');
    final appointmentTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    return Appointment(
      id: map['id'] ?? '',
      farmerId: map['farmerId'] ?? '',
      farmerName: map['farmerName'] ?? '',
      farmerPhone: map['farmerPhone'],
      advisorId: map['advisorId'] ?? '',
      advisorName: map['advisorName'],
      appointmentDate: DateTime.parse(map['appointmentDate']),
      appointmentTime: appointmentTime,
      consultationType: ConsultationType.fromString(map['consultationType'] ?? 'general'),
      problemDescription: map['problemDescription'] ?? '',
      status: AppointmentStatus.fromString(map['status'] ?? 'pending'),
      advisorNotes: map['advisorNotes'],
      farmerNotes: map['farmerNotes'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      confirmedAt: map['confirmedAt'] != null ? DateTime.parse(map['confirmedAt']) : null,
      cancelledAt: map['cancelledAt'] != null ? DateTime.parse(map['cancelledAt']) : null,
      cancellationReason: map['cancellationReason'],
      cancelledBy: map['cancelledBy'],
      rescheduledAt: map['rescheduledAt'] != null ? DateTime.parse(map['rescheduledAt']) : null,
      rescheduleReason: map['rescheduleReason'],
      completedAt: map['completedAt'] != null ? DateTime.parse(map['completedAt']) : null,
      farmerRating: map['farmerRating'],
      farmerFeedback: map['farmerFeedback'],
      advisorRating: map['advisorRating'],
      meetingLink: map['meetingLink'],
      isVirtual: map['isVirtual'] ?? false,
      location: map['location'],
      durationMinutes: map['durationMinutes'] ?? 30,
      cost: map['cost']?.toDouble(),
      currency: map['currency'] ?? 'SAR',
    );
  }

  /// التحقق من إمكانية الإلغاء
  /// 
  /// الإرجاع: true إذا كان يمكن إلغاء الموعد
  bool get canBeCancelled {
    return status == AppointmentStatus.pending || 
           status == AppointmentStatus.confirmed;
  }

  /// التحقق من إمكانية إعادة الجدولة
  /// 
  /// الإرجاع: true إذا كان يمكن إعادة جدولة الموعد
  bool get canBeRescheduled {
    return status == AppointmentStatus.pending || 
           status == AppointmentStatus.confirmed;
  }

  /// التحقق من إمكانية التأكيد
  /// 
  /// الإرجاع: true إذا كان يمكن تأكيد الموعد
  bool get canBeConfirmed {
    return status == AppointmentStatus.pending;
  }

  /// الحصول على وقت الموعد كنص
  /// 
  /// الإرجاع: وقت الموعد منسق
  String get formattedTime {
    final hour = appointmentTime.hour.toString().padLeft(2, '0');
    final minute = appointmentTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// الحصول على تاريخ الموعد كنص
  /// 
  /// الإرجاع: تاريخ الموعد منسق
  String get formattedDate {
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }

  /// الحصول على تاريخ ووقت الموعد مجتمعين
  /// 
  /// الإرجاع: DateTime كامل للموعد
  DateTime get fullDateTime {
    return DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
      appointmentTime.hour,
      appointmentTime.minute,
    );
  }

  @override
  List<Object?> get props => [
        id,
        farmerId,
        farmerName,
        farmerPhone,
        advisorId,
        advisorName,
        appointmentDate,
        appointmentTime,
        consultationType,
        problemDescription,
        status,
        advisorNotes,
        farmerNotes,
        createdAt,
        updatedAt,
        confirmedAt,
        cancelledAt,
        cancellationReason,
        cancelledBy,
        rescheduledAt,
        rescheduleReason,
        completedAt,
        farmerRating,
        farmerFeedback,
        advisorRating,
        meetingLink,
        isVirtual,
        location,
        durationMinutes,
        cost,
        currency,
      ];

  @override
  String toString() {
    return 'Appointment(id: $id, farmerName: $farmerName, advisorId: $advisorId, '
        'date: $formattedDate, time: $formattedTime, status: ${status.displayName})';
  }
}
