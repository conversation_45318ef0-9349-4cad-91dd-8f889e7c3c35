import 'package:equatable/equatable.dart';

/// كيان التنبؤ الأساسي في طبقة المنطق التجاري
class PredictionEntity extends Equatable {
  final String id;
  final String image;
  final String leaf;
  final String accuracy;
  final String disease;
  final String info;
  final String cure;
  final String products;
  final String cause;

  const PredictionEntity({
    required this.id,
    required this.image,
    required this.leaf,
    required this.accuracy,
    required this.disease,
    required this.info,
    required this.cure,
    required this.products,
    required this.cause,
  });

  @override
  List<Object> get props => [
        id,
        image,
        leaf,
        accuracy,
        disease,
        info,
        cure,
        products,
        cause,
      ];
}
