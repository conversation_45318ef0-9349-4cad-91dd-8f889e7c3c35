import '../entities/user_entity.dart';

/// واجهة مستودع المصادقة
abstract class AuthRepositoryInterface {
  /// تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserEntity?> signInWithEmailAndPassword(String email, String password);

  /// إنشاء حساب باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserEntity?> createUserWithEmailAndPassword(
      String name, String email, String password, String phone);

  /// تسجيل الدخول باستخدام Google
  Future<UserEntity?> signInWithGoogle();

  /// تسجيل الدخول برقم الهاتف - إرسال رمز التحقق
  Future<void> signInWithPhone(String phoneNumber);

  /// التحقق من رمز OTP
  Future<UserEntity?> verifyOTP(String verificationId, String otp);

  /// التحقق مما إذا كان المستخدم قد أكمل ملفه الشخصي
  Future<bool> hasUserCompletedProfile(String userId);

  /// تسجيل الخروج
  Future<void> signOut();
}
