import 'dart:io';
import '../../../core/utils/media/video_quality.dart';

/// واجهة مستودع التخزين
///
/// تحدد هذه الواجهة العقد بين طبقة المنطق التجاري وطبقة البيانات لعمليات التخزين.
/// يمكن تنفيذ هذه الواجهة باستخدام خدمات تخزين مختلفة مثل Firebase Storage أو Google Drive.
abstract class StorageRepositoryInterface {
  /// رفع صورة مضغوطة إلى التخزين
  ///
  /// المعلمات:
  /// - [file]: ملف الصورة المراد رفعه
  /// - [path]: المسار الذي سيتم تخزين الملف فيه
  /// - [quality]: جودة الصورة بعد الضغط (0-100)
  /// - [maxWidth]: العرض الأقصى للصورة بعد الضغط
  /// - [maxHeight]: الارتفاع الأقصى للصورة بعد الضغط
  ///
  /// يعيد رابط الصورة المرفوعة أو null في حالة الفشل
  Future<String?> uploadCompressedImage(File file, String path, {
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  });

  /// رفع فيديو مضغوط إلى التخزين
  ///
  /// المعلمات:
  /// - [file]: ملف الفيديو المراد رفعه
  /// - [path]: المسار الذي سيتم تخزين الملف فيه
  /// - [quality]: جودة الفيديو بعد الضغط
  ///
  /// يعيد رابط الفيديو المرفوع أو null في حالة الفشل
  Future<String?> uploadCompressedVideo(File file, String path, {
    VideoQuality quality = VideoQuality.MediumQuality,
  });
  /// رفع ملف إلى التخزين
  ///
  /// المعلمات:
  /// - [file]: الملف المراد رفعه
  /// - [path]: المسار الذي سيتم تخزين الملف فيه
  /// - [compress]: ما إذا كان يجب ضغط الملف قبل رفعه
  ///
  /// يعيد رابط الملف المرفوع أو null في حالة الفشل
  Future<String?> uploadFile(File file, String path, {bool compress = true});

  /// حذف ملف من التخزين
  ///
  /// المعلمات:
  /// - [path]: مسار الملف المراد حذفه
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> deleteFile(String path);

  /// تحديث صورة البروفايل
  ///
  /// يقوم برفع الصورة الجديدة وحذف الصورة القديمة إذا كانت موجودة
  ///
  /// المعلمات:
  /// - [file]: ملف الصورة الجديدة
  /// - [path]: مسار الصورة الجديدة
  /// - [oldImageUrl]: رابط الصورة القديمة (اختياري)
  /// - [compress]: ما إذا كان يجب ضغط الصورة قبل رفعها
  ///
  /// يعيد رابط الصورة الجديدة أو null في حالة الفشل
  Future<String?> updateProfileImage(File file, String path,
      {String? oldImageUrl, bool compress = true});
}
