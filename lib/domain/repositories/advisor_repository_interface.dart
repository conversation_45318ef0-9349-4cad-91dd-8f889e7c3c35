import 'dart:io';

import '../../data/models/agricultural_advisor/advisor_model.dart';
import '../../data/models/agricultural_advisor/consultation_model.dart';
import '../../data/models/agricultural_advisor/appointment_model.dart';

/// واجهة مستودع المرشدين الزراعيين
///
/// تحدد هذه الواجهة العقد بين طبقة المنطق التجاري وطبقة البيانات لعمليات المرشدين الزراعيين.
abstract class AdvisorRepositoryInterface {
  /// الحصول على قائمة المرشدين الزراعيين
  ///
  /// يعيد قائمة من المرشدين مرتبة حسب التقييم (الأعلى أولاً)
  Future<List<AdvisorModel>> getAdvisors({int limit = 10, String? lastAdvisorId});

  /// الحصول على مرشد محدد
  ///
  /// المعلمات:
  /// - [advisorId]: معرف المرشد المطلوب
  ///
  /// يعيد المرشد المطلوب أو null إذا لم يتم العثور عليه
  Future<AdvisorModel?> getAdvisor(String advisorId);

  /// البحث في المرشدين
  ///
  /// المعلمات:
  /// - [query]: مصطلح البحث
  /// - [specialty]: التخصص المطلوب (اختياري)
  /// - [limit]: عدد المرشدين المراد جلبهم
  ///
  /// يعيد قائمة المرشدين التي تطابق مصطلح البحث
  Future<List<AdvisorModel>> searchAdvisors({
    required String query,
    String? specialty,
    int limit = 20,
  });

  /// إنشاء استشارة جديدة
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [userName]: اسم المستخدم
  /// - [userImage]: صورة المستخدم
  /// - [advisorId]: معرف المرشد
  /// - [cropType]: نوع المحصول
  /// - [problemDescription]: وصف المشكلة
  /// - [area]: المساحة المزروعة
  /// - [imageFiles]: صور المشكلة (اختياري)
  ///
  /// يعيد الاستشارة التي تم إنشاؤها أو null في حالة الفشل
  Future<ConsultationModel?> createConsultation({
    required String userId,
    required String userName,
    String? userImage,
    required String advisorId,
    required String cropType,
    required String problemDescription,
    required String area,
    List<File>? imageFiles,
  });

  /// الحصول على استشارة محددة
  ///
  /// المعلمات:
  /// - [consultationId]: معرف الاستشارة المطلوبة
  ///
  /// يعيد الاستشارة المطلوبة أو null إذا لم يتم العثور عليها
  Future<ConsultationModel?> getConsultation(String consultationId);

  /// الحصول على جميع الاستشارات
  ///
  /// المعلمات:
  /// - [limit]: عدد الاستشارات المطلوبة
  ///
  /// يعيد قائمة من جميع الاستشارات مرتبة حسب تاريخ الإنشاء
  Future<List<ConsultationModel>> getAllConsultations({
    int limit = 50,
  });

  /// الحصول على استشارات المستخدم
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [limit]: عدد الاستشارات المطلوبة
  ///
  /// يعيد قائمة من الاستشارات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<ConsultationModel>> getUserConsultations({
    required String userId,
    int limit = 20,
  });

  /// الحصول على استشارات المرشد
  ///
  /// المعلمات:
  /// - [advisorId]: معرف المرشد
  /// - [limit]: عدد الاستشارات المطلوبة
  ///
  /// يعيد قائمة من الاستشارات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<ConsultationModel>> getAdvisorConsultations({
    required String advisorId,
    int limit = 20,
  });

  /// تحديث حالة الاستشارة
  ///
  /// المعلمات:
  /// - [consultationId]: معرف الاستشارة
  /// - [status]: الحالة الجديدة
  /// - [advisorResponse]: رد المرشد (اختياري)
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> updateConsultationStatus({
    required String consultationId,
    required ConsultationStatus status,
    String? advisorResponse,
  });

  /// تقييم الاستشارة
  ///
  /// المعلمات:
  /// - [consultationId]: معرف الاستشارة
  /// - [rating]: التقييم (من 1 إلى 5)
  /// - [comment]: تعليق التقييم (اختياري)
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> rateConsultation({
    required String consultationId,
    required double rating,
    String? comment,
  });

  /// حجز موعد مع المرشد
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [userName]: اسم المستخدم
  /// - [userPhone]: رقم هاتف المستخدم
  /// - [advisorId]: معرف المرشد
  /// - [consultationType]: نوع الاستشارة
  /// - [appointmentDate]: تاريخ الموعد
  /// - [appointmentTime]: وقت الموعد
  /// - [problemDescription]: وصف المشكلة
  ///
  /// يعيد الموعد الذي تم إنشاؤه أو null في حالة الفشل
  Future<AppointmentModel?> bookAppointment({
    required String userId,
    required String userName,
    required String userPhone,
    required String advisorId,
    required String consultationType,
    required String appointmentDate,
    required String appointmentTime,
    required String problemDescription,
  });

  /// الحصول على مواعيد المستخدم
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [limit]: عدد المواعيد المطلوبة
  ///
  /// يعيد قائمة من المواعيد مرتبة حسب تاريخ الموعد
  Future<List<AppointmentModel>> getUserAppointments({
    required String userId,
    int limit = 20,
  });

  /// الحصول على مواعيد المرشد
  ///
  /// المعلمات:
  /// - [advisorId]: معرف المرشد
  /// - [limit]: عدد المواعيد المطلوبة
  ///
  /// يعيد قائمة من المواعيد مرتبة حسب تاريخ الموعد
  Future<List<AppointmentModel>> getAdvisorAppointments({
    required String advisorId,
    int limit = 20,
  });

  /// تحديث حالة الموعد
  ///
  /// المعلمات:
  /// - [appointmentId]: معرف الموعد
  /// - [status]: الحالة الجديدة
  /// - [advisorNotes]: ملاحظات المرشد (اختياري)
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> updateAppointmentStatus({
    required String appointmentId,
    required AppointmentStatus status,
    String? advisorNotes,
  });

  /// إلغاء الموعد
  ///
  /// المعلمات:
  /// - [appointmentId]: معرف الموعد
  /// - [userId]: معرف المستخدم (للتحقق من الصلاحية)
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> cancelAppointment({
    required String appointmentId,
    required String userId,
  });

  /// الحصول على إحصائيات المرشد
  ///
  /// المعلمات:
  /// - [advisorId]: معرف المرشد
  ///
  /// يعيد خريطة تحتوي على الإحصائيات
  Future<Map<String, dynamic>> getAdvisorStats(String advisorId);

  /// الرد على استشارة
  ///
  /// المعلمات:
  /// - [consultationId]: معرف الاستشارة
  /// - [advisorId]: معرف المرشد
  /// - [response]: نص الرد
  /// - [recommendations]: التوصيات (اختياري)
  /// - [followUpRequired]: هل يحتاج متابعة (اختياري)
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> respondToConsultation({
    required String consultationId,
    required String advisorId,
    required String response,
    String? recommendations,
    bool followUpRequired = false,
  });
}
