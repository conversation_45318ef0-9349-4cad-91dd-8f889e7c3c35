import 'dart:io';

import '../../data/models/community_forum/comment_model.dart';
import '../../data/models/community_forum/post_model.dart';

/// واجهة مستودع المنشورات
///
/// تحدد هذه الواجهة العقد بين طبقة المنطق التجاري وطبقة البيانات لعمليات المنشورات.
abstract class PostRepositoryInterface {
  /// الحصول على قائمة المنشورات
  ///
  /// يعيد قائمة من المنشورات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<PostModel>> getPosts({int limit = 10, String? lastPostId});

  /// الحصول على منشور محدد
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المطلوب
  ///
  /// يعيد المنشور المطلوب أو null إذا لم يتم العثور عليه
  Future<PostModel?> getPost(String postId);

  /// إنشاء منشور جديد
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم الذي ينشئ المنشور
  /// - [userName]: اسم المستخدم الذي ينشئ المنشور
  /// - [userImage]: صورة المستخدم الذي ينشئ المنشور
  /// - [text]: نص المنشور (اختياري)
  /// - [imageFiles]: ملفات الصور المرفقة بالمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو المرفق بالمنشور (اختياري)
  /// - [hashtags]: قائمة الوسوم (هاشتاج) المرفقة بالمنشور (اختياري)
  ///
  /// يعيد المنشور الذي تم إنشاؤه أو null في حالة الفشل
  Future<PostModel?> createPost({
    required String userId,
    required String userName,
    required String userImage,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? hashtags,
  });

  /// تحديث منشور موجود
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد تحديثه
  /// - [text]: النص الجديد للمنشور (اختياري)
  /// - [imageFiles]: ملفات الصور الجديدة للمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو الجديد للمنشور (اختياري)
  /// - [deleteImages]: قائمة روابط الصور المراد حذفها (اختياري)
  /// - [deleteVideo]: ما إذا كان يجب حذف الفيديو الحالي (اختياري)
  ///
  /// يعيد المنشور بعد التحديث أو null في حالة الفشل
  Future<PostModel?> updatePost({
    required String postId,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? deleteImages,
    bool deleteVideo = false,
  });

  // تم نقل دالة deletePost إلى نهاية الواجهة

  /// إضافة إعجاب لمنشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يبدي الإعجاب
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> likePost(String postId, String userId);

  /// إزالة إعجاب من منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يزيل الإعجاب
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> unlikePost(String postId, String userId);

  /// إضافة تعليق على منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يضيف التعليق
  /// - [userName]: اسم المستخدم الذي يضيف التعليق
  /// - [userImage]: صورة المستخدم الذي يضيف التعليق
  /// - [text]: نص التعليق
  ///
  /// يعيد التعليق الذي تم إنشاؤه أو null في حالة الفشل
  Future<CommentModel?> addComment({
    required String postId,
    required String userId,
    required String userName,
    required String userImage,
    required String text,
  });

  /// الحصول على تعليقات منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [limit]: عدد التعليقات المطلوبة (افتراضيًا 20)
  /// - [lastCommentId]: معرف آخر تعليق تم تحميله (للتحميل التدريجي)
  ///
  /// يعيد قائمة من التعليقات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<CommentModel>> getComments({
    required String postId,
    int limit = 20,
    String? lastCommentId,
  });

  /// حذف تعليق
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [commentId]: معرف التعليق المراد حذفه
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> deleteComment(String postId, String commentId);

  /// زيادة عدد مشاهدات المنشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> incrementPostViews(String postId);

  /// البحث في المنشورات
  ///
  /// المعلمات:
  /// - [query]: مصطلح البحث
  /// - [limit]: عدد المنشورات المراد جلبها
  ///
  /// يعيد قائمة المنشورات التي تطابق مصطلح البحث
  Future<List<PostModel>> searchPosts({
    required String query,
    int limit = 20,
  });

  /// حذف منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف المنشور
  ///
  /// يعيد true في حالة نجاح الحذف و false في حالة الفشل
  Future<bool> deletePost(String postId, String userId);
}
