import 'dart:io';

import '../../../data/models/community_forum/post_model.dart';
import '../../repositories/post_repository_interface.dart';

/// حالة استخدام تحديث منشور
///
/// تستخدم هذه الحالة لتحديث منشور موجود في المنتدى المجتمعي.
class UpdatePost {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام تحديث منشور
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  UpdatePost(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد تحديثه
  /// - [text]: النص الجديد للمنشور (اختياري)
  /// - [imageFiles]: ملفات الصور الجديدة للمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو الجديد للمنشور (اختياري)
  /// - [deleteImages]: قائمة روابط الصور المراد حذفها (اختياري)
  /// - [deleteVideo]: ما إذا كان يجب حذف الفيديو الحالي (اختياري)
  ///
  /// يعيد المنشور بعد التحديث أو null في حالة الفشل
  Future<PostModel?> call({
    required String postId,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? deleteImages,
    bool deleteVideo = false,
  }) {
    return repository.updatePost(
      postId: postId,
      text: text,
      imageFiles: imageFiles,
      videoFile: videoFile,
      deleteImages: deleteImages,
      deleteVideo: deleteVideo,
    );
  }
}
