import '../../../data/models/community_forum/post_model.dart';
import '../../repositories/post_repository_interface.dart';

/// حالة استخدام الحصول على المنشورات
///
/// تستخدم هذه الحالة للحصول على قائمة المنشورات من المستودع.
class GetPosts {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام الحصول على المنشورات
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  GetPosts(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [limit]: عدد المنشورات المطلوبة (افتراضيًا 10)
  /// - [lastPostId]: معرف آخر منشور تم تحميله (للتحميل التدريجي)
  ///
  /// يعيد قائمة من المنشورات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<PostModel>> call({int limit = 10, String? lastPostId}) {
    return repository.getPosts(limit: limit, lastPostId: lastPostId);
  }
}
