import '../../../data/models/community_forum/comment_model.dart';
import '../../repositories/post_repository_interface.dart';

/// حالة استخدام إضافة تعليق على منشور
///
/// تستخدم هذه الحالة لإضافة تعليق على منشور في المنتدى المجتمعي.
class AddComment {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام إضافة تعليق
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  AddComment(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يضيف التعليق
  /// - [userName]: اسم المستخدم الذي يضيف التعليق
  /// - [userImage]: صورة المستخدم الذي يضيف التعليق
  /// - [text]: نص التعليق
  ///
  /// يعيد التعليق الذي تم إنشاؤه أو null في حالة الفشل
  Future<CommentModel?> call({
    required String postId,
    required String userId,
    required String userName,
    required String userImage,
    required String text,
  }) {
    return repository.addComment(
      postId: postId,
      userId: userId,
      userName: userName,
      userImage: userImage,
      text: text,
    );
  }
}

/// حالة استخدام الحصول على تعليقات منشور
///
/// تستخدم هذه الحالة للحصول على قائمة التعليقات على منشور في المنتدى المجتمعي.
class GetComments {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام الحصول على تعليقات
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  GetComments(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [limit]: عدد التعليقات المطلوبة (افتراضيًا 20)
  /// - [lastCommentId]: معرف آخر تعليق تم تحميله (للتحميل التدريجي)
  ///
  /// يعيد قائمة من التعليقات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
  Future<List<CommentModel>> call({
    required String postId,
    int limit = 20,
    String? lastCommentId,
  }) {
    return repository.getComments(
      postId: postId,
      limit: limit,
      lastCommentId: lastCommentId,
    );
  }
}

/// حالة استخدام حذف تعليق
///
/// تستخدم هذه الحالة لحذف تعليق من منشور في المنتدى المجتمعي.
class DeleteComment {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام حذف تعليق
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  DeleteComment(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [commentId]: معرف التعليق المراد حذفه
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> call(String postId, String commentId) {
    return repository.deleteComment(postId, commentId);
  }
}
