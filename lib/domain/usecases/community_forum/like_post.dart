import '../../repositories/post_repository_interface.dart';

/// حالة استخدام الإعجاب بمنشور
///
/// تستخدم هذه الحالة لإضافة إعجاب لمنشور في المنتدى المجتمعي.
class LikePost {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام الإعجاب بمنشور
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  LikePost(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يبدي الإعجاب
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> call(String postId, String userId) {
    return repository.likePost(postId, userId);
  }
}

/// حالة استخدام إلغاء الإعجاب بمنشور
///
/// تستخدم هذه الحالة لإزالة إعجاب من منشور في المنتدى المجتمعي.
class UnlikePost {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام إلغاء الإعجاب بمنشور
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  UnlikePost(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يزيل الإعجاب
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> call(String postId, String userId) {
    return repository.unlikePost(postId, userId);
  }
}
