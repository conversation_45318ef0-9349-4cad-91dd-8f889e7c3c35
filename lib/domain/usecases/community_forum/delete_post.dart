import '../../repositories/post_repository_interface.dart';

/// حالة استخدام حذف منشور
///
/// تستخدم هذه الحالة لحذف منشور من المنتدى المجتمعي.
class DeletePost {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام حذف منشور
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  DeletePost(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف المنشور
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> call(String postId, String userId) {
    return repository.deletePost(postId, userId);
  }
}
