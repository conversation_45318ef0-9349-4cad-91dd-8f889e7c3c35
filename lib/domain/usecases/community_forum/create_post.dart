import 'dart:io';

import '../../../data/models/community_forum/post_model.dart';
import '../../repositories/post_repository_interface.dart';

/// حالة استخدام إنشاء منشور جديد
///
/// تستخدم هذه الحالة لإنشاء منشور جديد في المنتدى المجتمعي.
class CreatePost {
  final PostRepositoryInterface repository;

  /// منشئ حالة استخدام إنشاء منشور جديد
  ///
  /// المعلمات:
  /// - [repository]: مستودع المنشورات
  CreatePost(this.repository);

  /// تنفيذ حالة الاستخدام
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم الذي ينشئ المنشور
  /// - [userName]: اسم المستخدم الذي ينشئ المنشور
  /// - [userImage]: صورة المستخدم الذي ينشئ المنشور
  /// - [text]: نص المنشور (اختياري)
  /// - [imageFiles]: ملفات الصور المرفقة بالمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو المرفق بالمنشور (اختياري)
  ///
  /// يعيد المنشور الذي تم إنشاؤه أو null في حالة الفشل
  Future<PostModel?> call({
    required String userId,
    required String userName,
    required String userImage,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
  }) {
    return repository.createPost(
      userId: userId,
      userName: userName,
      userImage: userImage,
      text: text,
      imageFiles: imageFiles,
      videoFile: videoFile,
    );
  }
}
