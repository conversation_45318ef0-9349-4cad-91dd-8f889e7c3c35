import '../../entities/user_entity.dart';
import '../../repositories/auth_repository_interface.dart';

/// حالة استخدام تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
class SignInWithEmailPassword {
  final AuthRepositoryInterface repository;

  SignInWithEmailPassword(this.repository);

  Future<UserEntity?> call(String email, String password) {
    return repository.signInWithEmailAndPassword(email, password);
  }
}
