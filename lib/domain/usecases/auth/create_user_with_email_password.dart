import '../../entities/user_entity.dart';
import '../../repositories/auth_repository_interface.dart';

/// حالة استخدام إنشاء حساب باستخدام البريد الإلكتروني وكلمة المرور
class CreateUserWithEmailPassword {
  final AuthRepositoryInterface repository;

  CreateUserWithEmailPassword(this.repository);

  Future<UserEntity?> call(String name, String email, String password, String phone) {
    return repository.createUserWithEmailAndPassword(name, email, password, phone);
  }
}
