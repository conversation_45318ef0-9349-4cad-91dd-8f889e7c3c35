import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../data/repositories/auth_repository.dart';
import '../data/repositories/post_repository.dart';

import '../data/repositories/storage_repository.dart';
import '../core/di/advisor_injection.dart';
import '../imports.dart';
// تم حذف الاستيرادات المكررة - موجودة في imports.dart
// تم تعطيل صفحات الاختبار للعمل بالصفحات الحقيقية

class AppRouter {
  static List<RouteModel> routes() => [
        RouteModel(
          name: RouteConstants.onBoarding,
          view: OnBoardingPage(),
        ),
        RouteModel(
          name: RouteConstants.login,
          view: LoginPage(),
        ),
        RouteModel(
          name: RouteConstants.register,
          view: RegisterPage(),
        ),
        RouteModel(
          name: RouteConstants.phoneInput,
          view: PhoneInputPage(),
        ),
        RouteModel(
          name: RouteConstants.registerProfile,
          view: RegisterProfilePage(),
        ),
        RouteModel(
          name: RouteConstants.homeScreen,
          view: AppBottomNavigationBar(),
        ),
        RouteModel(
          name: RouteConstants.profile,
          view: Profile(),
        ),
        //updateProfile
        RouteModel(
          name: RouteConstants.updateProfile,
          view: UpdateProfile(),
        ),
        // comunityForum
        RouteModel(
          name: RouteConstants.communityForum,
          view: ComunityForum(),
        ),
        // تم تعطيل صفحات الاختبار للعمل بالصفحات الحقيقية
        // RouteModel(
        //   name: RouteConstants.mediaTest,
        //   view: const MediaTestPage(),
        // ),
        // RouteModel(
        //   name: RouteConstants.mediaOptimizationTest,
        //   view: const MediaOptimizationTestPage(),
        // ),
        //education
        RouteModel(
          name: RouteConstants.education,
          view: Education(),
        ),
        // واجهة الاستشاري الزراعي الافتراضية
        RouteModel(
          name: RouteConstants.advisorVirtualInterface,
          view: const AdvisorVirtualInterface(),
        ),
        // الدليل الزراعي
        RouteModel(
          name: RouteConstants.agriculturalGuide,
          view: const AgriculturalGuideScreen(),
        ),
        //enviroment
        RouteModel(
          name: RouteConstants.agriculturalCrops,
          view: AgriculturalCrops(),
        ),
        //government
        RouteModel(
          name: RouteConstants.government,
          view: Government(),
        ),
        //information
        RouteModel(
          name: RouteConstants.landmarks,
          view: Information(),
        ),
        //marketingProducts
        RouteModel(
          name: RouteConstants.marketingProducts,
          view: MarketingProducts(),
        ),
        //pestsAndDiseases
        RouteModel(
          name: RouteConstants.pestsAndDiseases,
          view: PestsAndDiseases(),
        ),
        //ReachEngineer
        RouteModel(
          name: RouteConstants.reachEngineer,
          view: ReachEngineer(),
        ),
        //search
        RouteModel(
          name: RouteConstants.search,
          view: Search(),
        ),
        //settings
        RouteModel(
          name: RouteConstants.settings,
          view: Settinges(),
        ),
        //weather
        RouteModel(
          name: RouteConstants.weather,
          view: WeatherView(),
        ),
        //ShowData
        RouteModel(
          name: RouteConstants.dayWeather,
          view: SevenDayWeatherView(),
        ),
        //camera
        RouteModel(
          name: RouteConstants.camera,
          view: CameraPage(),
        ),
        //myRequests - تم تعطيله مؤقتاً
        // RouteModel(
        //   name: RouteConstants.myRequests,
        //   view: MyRequestsScreen(),
        // ),
        //adminDashboard
        RouteModel(
          name: RouteConstants.adminDashboard,
          view: AdminDashboardScreen(),
        ),
      ];

  /// توليد المسار المناسب بناءً على حالة المستخدم
  ///
  /// يقوم بتوليد المسار المناسب بناءً على حالة المستخدم (مسجل الدخول أم لا)
  /// وحالة صفحة الترحيب (رآها المستخدم من قبل أم لا).
  static Route? onGenerateRoute(RouteSettings settings) {
    // لا يمكن استخدام async/await في onGenerateRoute
    // لذلك سنستخدم طريقة أخرى للتحقق من اكتمال بيانات البروفايل
    if (settings.name != null) {
      String nextRoute = settings.name!;

      try {
        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'توليد مسار: المسار المطلوب = $nextRoute, uid = "$uid", isOnBoarding = $isOnBoarding',
          tag: 'AppRouter.onGenerateRoute',
        );

        // التحقق من حالة المصادقة
        final bool isAuthenticated = uid.isNotEmpty;

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'حالة المصادقة: ${isAuthenticated ? "مسجل الدخول" : "غير مسجل الدخول"}',
          tag: 'AppRouter.onGenerateRoute',
        );

        // قائمة المسارات المسموح بها للمستخدم غير المسجل
        final List<String> unauthenticatedRoutes = [
          RouteConstants.login,
          RouteConstants.onBoarding,
          RouteConstants.register,
          RouteConstants.phoneInput,
          RouteConstants.otpVerification,
          RouteConstants.registerProfile,
        ];

        // إذا كان المستخدم مسجل الدخول
        if (isAuthenticated) {
          // التحقق من اكتمال بيانات البروفايل باستخدام SharedPrefs
          final hasCompletedProfile =
              SharedPrefs.getBool('hasCompletedProfile') ?? false;

          LoggerService.debug(
            'التحقق من اكتمال بيانات البروفايل: $hasCompletedProfile',
            tag: 'AppRouter.onGenerateRoute',
          );

          // إذا لم يكمل المستخدم بيانات البروفايل وكان المسار المطلوب ليس صفحة إكمال البروفايل
          if (!hasCompletedProfile &&
              nextRoute != RouteConstants.registerProfile) {
            LoggerService.debug(
              'المستخدم لم يكمل بيانات البروفايل، توجيهه إلى صفحة إكمال البروفايل',
              tag: 'AppRouter.onGenerateRoute',
            );

            nextRoute = RouteConstants.registerProfile;
          }
          // إذا كان المستخدم قد أكمل بيانات البروفايل وكان المسار المطلوب هو أحد مسارات المصادقة (باستثناء صفحة إكمال البروفايل)
          else if (hasCompletedProfile &&
              unauthenticatedRoutes.contains(nextRoute) &&
              nextRoute != RouteConstants.registerProfile) {
            LoggerService.debug(
              'المستخدم مسجل الدخول وأكمل بيانات البروفايل ويحاول الوصول إلى مسار مصادقة ($nextRoute)، توجيهه إلى الصفحة الرئيسية',
              tag: 'AppRouter.onGenerateRoute',
            );

            nextRoute = RouteConstants.homeScreen;
          }
        }
        // إذا كان المستخدم غير مسجل الدخول
        else {
          // إذا كان المسار المطلوب ليس أحد مسارات المصادقة
          if (!unauthenticatedRoutes.contains(nextRoute)) {
            LoggerService.debug(
              'المستخدم غير مسجل الدخول ويحاول الوصول إلى مسار محمي ($nextRoute)',
              tag: 'AppRouter.onGenerateRoute',
            );

            // إذا كان المستخدم قد رأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول
            if (isOnBoarding) {
              LoggerService.debug(
                'المستخدم رأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول',
                tag: 'AppRouter.onGenerateRoute',
              );

              nextRoute = RouteConstants.login;
            }
            // إذا كان المستخدم لم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب
            else {
              LoggerService.debug(
                'المستخدم لم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب',
                tag: 'AppRouter.onGenerateRoute',
              );

              nextRoute = RouteConstants.onBoarding;
            }
          }
        }

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'المسار النهائي: $nextRoute',
          tag: 'AppRouter.onGenerateRoute',
        );
      } catch (e) {
        LoggerService.error(
          'خطأ في توليد المسار: $e',
          error: e,
          tag: 'AppRouter.onGenerateRoute',
        );
      }

      Iterable<RouteModel> result =
          routes().where((element) => element.name == nextRoute);
      if (result.isNotEmpty) {
        return MaterialPageRoute(
          builder: (context) => result.first.view,
        );
      }
    }

    return null;
  }

  /// قائمة مزودي BLoC/Cubit
  ///
  /// تقوم هذه الدالة بإنشاء قائمة بجميع مزودي BLoC/Cubit المستخدمة في التطبيق.
  /// يتم تنظيم المزودين حسب الميزات لتسهيل الصيانة.
  static List<BlocProvider> allBlocProviders() {
    return [
      // مزودي المصادقة
      _authProviders(),

      // مزودي المعالم الزراعية
      _landmarksProviders(),

      // مزودي المحاصيل الزراعية
      _cropsProviders(),

      // مزودي الطقس
      _weatherProviders(),

      // مزودي المنتدى المجتمعي
      _communityForumProviders(),

      // مزودي المرشد الزراعي
      _advisorProviders(),
    ].expand((providers) => providers).toList();
  }

  /// مزودي المصادقة
  static List<BlocProvider> _authProviders() {
    // إنشاء مستودع المصادقة مرة واحدة
    final authRepository = AuthRepository();

    // إنشاء AuthCubit مرة واحدة
    final authCubit = AuthCubit(authRepository);

    return [
      // مزود AuthCubit الرئيسي
      BlocProvider<AuthCubit>(
        create: (context) => authCubit,
      ),

      // مزود LoginCubit
      BlocProvider<LoginCubit>(
        create: (context) => LoginCubit(authRepository, authCubit),
      ),

      // مزود RegisterCubit
      BlocProvider<RegisterCubit>(
        create: (context) => RegisterCubit(authRepository, authCubit),
      ),

      // مزود PhoneAuthCubit
      BlocProvider<PhoneAuthCubit>(
        create: (context) => PhoneAuthCubit(),
      ),

      // مزود RegisterProfileCubit
      BlocProvider<RegisterProfileCubit>(
        create: (context) => RegisterProfileCubit(authCubit),
      ),
    ];
  }

  /// مزودي المعالم الزراعية
  static List<BlocProvider> _landmarksProviders() {
    return [
      BlocProvider<InformationCubit>(
        create: (context) {
          // إنشاء كيوبت المعلومات
          final cubit = InformationCubit();

          // تحميل البيانات الأساسية فقط (المعالم الزراعية)
          cubit.loadInitialData();

          return cubit;
        },
      ),
    ];
  }

  /// مزودي المحاصيل الزراعية
  static List<BlocProvider> _cropsProviders() {
    return [
      BlocProvider<CropsCubit>(
        // إنشاء CropsCubit بدون تحميل البيانات مباشرة
        // سيتم تحميل البيانات عند الحاجة إليها في صفحة المحاصيل الزراعية
        create: (context) => CropsCubit(),
      ),
    ];
  }

  /// مزودي الطقس
  static List<BlocProvider> _weatherProviders() {
    return [
      BlocProvider<WeatherCubit>(
        create: (context) {
          final dio = Dio(BaseOptions(
            baseUrl: 'https://api.openweathermap.org/data/2.5/',
            connectTimeout: const Duration(seconds: 10),
            receiveTimeout: const Duration(seconds: 10),
          ));
          final weatherService = WeatherService(dio: dio);
          return WeatherCubit(weatherService: weatherService);
        },
      ),
    ];
  }

  /// مزودي المنتدى المجتمعي
  static List<BlocProvider> _communityForumProviders() {
    return [
      BlocProvider<PostsCubit>(
        create: (context) {
          final storageRepository = StorageRepositoryFactory.create();
          return PostsCubit(
            postRepository: PostRepositoryFactory.create(storageRepository),
            storageRepository: storageRepository,
          );
        },
      ),
    ];
  }

  /// تهيئة الموجه
  ///
  /// يقوم بتهيئة الموجه والتحقق من حالة تسجيل الدخول.
  /// ملاحظة: هذه الدالة لا تقوم بتصحيح التناقضات بين Firebase والتخزين المحلي،
  /// حيث يتم ذلك في `_synchronizeAuthenticationState` في `AppInitializationService`.
  static Future<void> init() async {
    try {
      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء تهيئة AppRouter. uid = "$uid", isOnBoarding = $isOnBoarding',
        tag: 'AppRouter.init',
      );

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'اكتملت تهيئة AppRouter. uid = "$uid", isOnBoarding = $isOnBoarding',
        tag: 'AppRouter.init',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في تهيئة AppRouter: $e',
        error: e,
        tag: 'AppRouter.init',
      );
    }
  }

  /// التنقل إلى مسار
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  /// - [replace]: ما إذا كان يجب استبدال المسار الحالي (اختياري)
  static void navigateTo(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool replace = false,
  }) {
    if (replace) {
      Navigator.pushReplacementNamed(
        context,
        routeName,
        arguments: arguments,
      );
    } else {
      Navigator.pushNamed(
        context,
        routeName,
        arguments: arguments,
      );
    }
  }

  /// التنقل إلى المسار الرئيسي
  ///
  /// يقوم بتوجيه المستخدم إلى الصفحة الرئيسية وإزالة جميع المسارات السابقة.
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  static void navigateToHome(BuildContext context) {
    try {
      LoggerService.debug(
        'بدء التنقل إلى الصفحة الرئيسية. uid = "$uid"',
        tag: 'AppRouter.navigateToHome',
      );

      // التحقق من أن uid غير فارغ
      if (uid.isEmpty) {
        LoggerService.warning(
          'محاولة التنقل إلى الصفحة الرئيسية ولكن uid فارغ. توجيه المستخدم إلى صفحة تسجيل الدخول بدلاً من ذلك.',
          tag: 'AppRouter.navigateToHome',
        );

        // توجيه المستخدم إلى صفحة تسجيل الدخول بدلاً من الصفحة الرئيسية
        Navigator.pushNamedAndRemoveUntil(
          context,
          RouteConstants.login,
          (route) => false,
        );

        return;
      }

      // توجيه المستخدم إلى الصفحة الرئيسية
      Navigator.pushNamedAndRemoveUntil(
        context,
        RouteConstants.homeScreen,
        (route) => false,
      );

      LoggerService.debug(
        'تم التنقل إلى الصفحة الرئيسية بنجاح',
        tag: 'AppRouter.navigateToHome',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في التنقل إلى الصفحة الرئيسية: $e',
        error: e,
        tag: 'AppRouter.navigateToHome',
      );

      // محاولة التنقل إلى الصفحة الرئيسية بطريقة أخرى
      try {
        Navigator.pushReplacementNamed(context, RouteConstants.homeScreen);
      } catch (e2) {
        LoggerService.error(
          'فشل في التنقل إلى الصفحة الرئيسية بالطريقة البديلة: $e2',
          error: e2,
          tag: 'AppRouter.navigateToHome',
        );
      }
    }
  }

  /// التنقل إلى مسار وإزالة جميع المسارات السابقة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  static void navigateAndRemoveUntil(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// الرجوع إلى المسار السابق
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [result]: نتيجة الرجوع (اختياري)
  static void goBack(BuildContext context, {dynamic result}) {
    // التحقق مما إذا كان يمكن الرجوع
    if (Navigator.canPop(context)) {
      // التحقق من المسار الحالي
      final currentRoute = ModalRoute.of(context)?.settings.name;

      // تسجيل المسار الحالي للتصحيح
      LoggerService.debug(
        'محاولة الرجوع من المسار: $currentRoute',
        tag: 'AppRouter',
      );

      // إذا كان المستخدم في صفحة تسجيل الدخول وقد رأى صفحة الترحيب من قبل
      if (currentRoute == RouteConstants.login && isOnBoarding) {
        // منع الرجوع إلى صفحة الترحيب
        LoggerService.debug(
          'منع الرجوع من صفحة تسجيل الدخول إلى صفحة الترحيب',
          tag: 'AppRouter',
        );

        // استخدام WillPopScope في الصفحة نفسها لمعالجة الرجوع
        // لا نحتاج لعمل أي شيء هنا لأن الصفحة ستتعامل مع الرجوع
      } else {
        // الرجوع بشكل طبيعي
        Navigator.pop(context, result);
      }
    } else {
      // لا يمكن الرجوع، عرض مربع حوار للخروج من التطبيق
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
          contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          title: Text(
            'الخروج من التطبيق',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          content: Text(
            'هل تريد الخروج من التطبيق؟',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontSize: 16,
              color: Colors.black54,
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                backgroundColor: Colors.white,
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontFamily: AssetsFonts.messiri,
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // إغلاق مربع الحوار
                Navigator.pop(context);

                // إغلاق التطبيق
                SystemNavigator.pop();
              },
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: AppColors.primary,
              ),
              child: Text(
                'خروج',
                style: TextStyle(
                  fontFamily: AssetsFonts.messiri,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  /// مزودي المرشد الزراعي
  static List<BlocProvider> _advisorProviders() {
    return [
      BlocProvider<AdvisorCubit>(
        create: (context) => AdvisorCubit(
          advisorRepository: AdvisorInjection.createAdvisorRepository(),
        ),
      ),
    ];
  }
}
