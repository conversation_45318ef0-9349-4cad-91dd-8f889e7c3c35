/// ثوابت المسارات في التطبيق
///
/// يحتوي هذا الكلاس على جميع مسارات التطبيق المستخدمة للتنقل بين الصفحات.
class RouteConstants {
  // منع إنشاء نسخة من الكلاس
  RouteConstants._();

  /// صفحة الترحيب
  static const String onBoarding = '/onBoarding';

  /// صفحات المصادقة
  static const String login = '/login';
  static const String register = '/register';
  static const String phoneInput = '/phoneInput';
  static const String otpVerification = '/otpVerification';
  static const String forgotPassword = '/forgotPassword';
  static const String resetPassword = '/resetPassword';
  static const String registerProfile = '/registerProfile';

  /// الصفحات الرئيسية
  static const String homeScreen = '/home';
  static const String profile = '/profile';
  static const String updateProfile = '/updateProfile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String search = '/search';

  /// صفحات المنتدى المجتمعي
  static const String communityForum = '/communityForum';
  static const String createPost = '/createPost';
  static const String editPost = '/editPost';
  static const String postDetails = '/postDetails';
  static const String userPosts = '/userPosts';

  /// صفحات الطقس
  static const String weather = '/weather';
  static const String weatherDetails = '/weatherDetails';
  static const String weatherForecast = '/weatherForecast';
  static const String dayWeather = '/dayWeather';

  /// صفحة الكاميرا
  static const String camera = '/camera';

  /**
   * صفحات المحاصيل الزراعية
   */
  static const String agriculturalCrops = '/agriculturalCrops';
  static const String cropDetails = '/cropDetails';
  static const String cropCalendar = '/cropCalendar';
  static const String cropDiseases = '/cropDiseases';

  /// صفحات المعالم الزراعية
  static const String landmarks = '/landmarks';
  static const String landmarkDetails = '/landmarkDetails';
  static const String landmarkMap = '/landmarkMap';
  static const String showDataLandmark = '/showDataLandmark';
  static const String showData = '/showData';

  /// صفحات التعليم
  static const String education = '/education';
  static const String educationDetails = '/educationDetails';
  static const String educationCategories = '/educationCategories';
  static const String advisorVirtualInterface = '/advisorVirtualInterface';
  static const String agriculturalGuide = '/agriculturalGuide';

  /// صفحات الحكومة
  static const String government = '/government';
  static const String governmentDetails = '/governmentDetails';
  static const String governmentServices = '/governmentServices';

  /// صفحات تسويق المنتجات
  static const String marketingProducts = '/marketingProducts';
  static const String productDetails = '/productDetails';
  static const String addProduct = '/addProduct';
  static const String myProducts = '/myProducts';

  /// صفحات الآفات والأمراض
  static const String pestsAndDiseases = '/pestsAndDiseases';
  static const String pestDetails = '/pestDetails';
  static const String diseaseDetails = '/diseaseDetails';
  static const String treatmentDetails = '/treatmentDetails';

  /// صفحات الوصول للمهندس
  static const String reachEngineer = '/reachEngineer';
  static const String engineerDetails = '/engineerDetails';
  static const String engineerChat = '/engineerChat';

  /// صفحات الطلبات والاستشارات
  static const String myRequests = '/myRequests';
  static const String adminDashboard = '/adminDashboard';

  /// صفحات أخرى
  static const String about = '/about';
  static const String contactUs = '/contactUs';
  static const String privacyPolicy = '/privacyPolicy';
  static const String termsOfService = '/termsOfService';
  static const String error = '/error';
  static const String cloudinaryTest = '/cloudinaryTest';
  static const String mediaTest = '/mediaTest';
  static const String mediaOptimizationTest = '/mediaOptimizationTest';

  /// مجموعات المسارات
  static const List<String> authRoutes = [
    login,
    register,
    phoneInput,
    otpVerification,
    forgotPassword,
    resetPassword,
    registerProfile,
  ];

  static const List<String> mainRoutes = [
    homeScreen,
    profile,
    updateProfile,
    settings,
    notifications,
    search,
  ];

  static const List<String> communityForumRoutes = [
    communityForum,
    createPost,
    editPost,
    postDetails,
    userPosts,
  ];

  static const List<String> weatherRoutes = [
    weather,
    weatherDetails,
    weatherForecast,
    dayWeather,
  ];

  static const List<String> cameraRoutes = [
    camera,
  ];

  static const List<String> agriculturalCropsRoutes = [
    agriculturalCrops,
    cropDetails,
    cropCalendar,
    cropDiseases,
  ];

  static const List<String> landmarksRoutes = [
    landmarks,
    landmarkDetails,
    landmarkMap,
    showDataLandmark,
    showData,
  ];

  static const List<String> educationRoutes = [
    education,
    educationDetails,
    educationCategories,
    advisorVirtualInterface,
  ];

  static const List<String> governmentRoutes = [
    government,
    governmentDetails,
    governmentServices,
  ];

  static const List<String> marketingProductsRoutes = [
    marketingProducts,
    productDetails,
    addProduct,
    myProducts,
  ];

  static const List<String> pestsAndDiseasesRoutes = [
    pestsAndDiseases,
    pestDetails,
    diseaseDetails,
    treatmentDetails,
  ];

  static const List<String> reachEngineerRoutes = [
    reachEngineer,
    engineerDetails,
    engineerChat,
  ];

  static const List<String> otherRoutes = [
    about,
    contactUs,
    privacyPolicy,
    termsOfService,
    error,
    cloudinaryTest,
  ];

  /// مسارات تتطلب المصادقة
  static const List<String> authenticatedRoutes = [
    profile,
    updateProfile,
    createPost,
    editPost,
    addProduct,
    myProducts,
    engineerChat,
  ];

  /// مسارات لا تتطلب المصادقة
  static const List<String> unauthenticatedRoutes = [
    onBoarding,
    login,
    register,
    phoneInput,
    otpVerification,
    forgotPassword,
    resetPassword,
  ];
}
