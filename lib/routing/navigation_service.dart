import 'package:flutter/material.dart';

/// خدمة التنقل
///
/// توفر هذه الخدمة وظائف للتنقل بين صفحات التطبيق.
/// تستخدم نمط Singleton لضمان وجود نسخة واحدة فقط من الخدمة في التطبيق.
class NavigationService {
  // نمط Singleton
  static final NavigationService _instance = NavigationService._internal();
  
  // مفتاح التنقل العام
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  // المنشئ الخاص
  NavigationService._internal();
  
  // المصنع
  factory NavigationService() {
    return _instance;
  }
  
  /// الحصول على سياق التنقل الحالي
  BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// الحصول على حالة التنقل الحالية
  NavigatorState? get currentState => navigatorKey.currentState;
  
  /// التنقل إلى مسار
  ///
  /// المعلمات:
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  ///
  /// القيمة المرجعة:
  /// نتيجة التنقل
  Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamed(
      routeName,
      arguments: arguments,
    );
  }
  
  /// التنقل إلى مسار واستبدال المسار الحالي
  ///
  /// المعلمات:
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  ///
  /// القيمة المرجعة:
  /// نتيجة التنقل
  Future<dynamic> navigateToReplacement(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }
  
  /// التنقل إلى مسار وإزالة جميع المسارات السابقة
  ///
  /// المعلمات:
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  ///
  /// القيمة المرجعة:
  /// نتيجة التنقل
  Future<dynamic> navigateToAndRemoveUntil(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
  
  /// الرجوع إلى المسار السابق
  ///
  /// المعلمات:
  /// - [result]: نتيجة الرجوع (اختياري)
  ///
  /// القيمة المرجعة:
  /// true إذا تم الرجوع بنجاح، false إذا لم يكن هناك مسار سابق
  bool goBack({dynamic result}) {
    if (navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop(result);
      return true;
    }
    return false;
  }
  
  /// الرجوع إلى المسار الرئيسي
  ///
  /// المعلمات:
  /// - [routeName]: اسم المسار الرئيسي
  ///
  /// القيمة المرجعة:
  /// نتيجة التنقل
  Future<dynamic> goBackToRoot(String routeName) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
    );
  }
}
