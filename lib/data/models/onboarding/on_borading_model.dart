import 'package:agriculture/core/constants/assets_boarding.dart';

class OnBoardingModel {
  final String title;
  final String bodyTitle;
  final String imageUrl;

  const OnBoardingModel({
    required this.title,
    required this.bodyTitle,
    required this.imageUrl,
  });
}

const listOnboarding = [
  OnBoardingModel(
    title: 'مرحباً بك في تطبيق البلدة الطيبة',
    bodyTitle:
        'دليلك الشامل للزراعة الذكية والمستدامة، نجمع بين الخبرة التقليدية والتكنولوجيا الحديثة',
    imageUrl: AssetsBoarding.image1,
  ),
  OnBoardingModel(
    title: 'استكشف عالم الزراعة المتطورة',
    bodyTitle:
        'تعرف على أحدث التقنيات الزراعية وأفضل الممارسات لزيادة إنتاجية محاصيلك وتحسين جودتها',
    imageUrl: AssetsBoarding.image2,
  ),
  OnBoardingModel(
    title: 'انضم إلى مجتمع المزارعين',
    bodyTitle:
        'تواصل مع مزارعين آخرين، شارك خبراتك واستفد من تجارب الآخرين في منتدانا المجتمعي',
    imageUrl: AssetsBoarding.image4,
  ),
];
