import 'package:flutter/material.dart';

/// نموذج بيانات إجراء الزر العائم
/// وفق المعيار #2: Clean Architecture - طبقة البيانات
class FabActionModel {
  /// معرف الإجراء
  final String id;
  
  /// عنوان الإجراء
  final String title;
  
  /// وصف الإجراء
  final String description;
  
  /// أيقونة الإجراء
  final IconData icon;
  
  /// لون الإجراء
  final Color color;
  
  /// أولوية الإجراء (للترتيب)
  final int priority;
  
  /// هل الإجراء مفعل
  final bool isEnabled;
  
  /// هل الإجراء مرئي
  final bool isVisible;
  
  /// دالة تنفيذ الإجراء
  final VoidCallback? onTap;
  
  /// معرف الإشعار المرتبط
  final String? notificationChannelId;
  
  /// صوت الإشعار
  final String? notificationSound;
  
  /// بيانات إضافية للإجراء
  final Map<String, dynamic>? metadata;

  const FabActionModel({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    this.priority = 0,
    this.isEnabled = true,
    this.isVisible = true,
    this.onTap,
    this.notificationChannelId,
    this.notificationSound,
    this.metadata,
  });

  /// إنشاء نسخة من الإجراء مع تعديل بعض الخصائص
  FabActionModel copyWith({
    String? id,
    String? title,
    String? description,
    IconData? icon,
    Color? color,
    int? priority,
    bool? isEnabled,
    bool? isVisible,
    VoidCallback? onTap,
    String? notificationChannelId,
    String? notificationSound,
    Map<String, dynamic>? metadata,
  }) {
    return FabActionModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      priority: priority ?? this.priority,
      isEnabled: isEnabled ?? this.isEnabled,
      isVisible: isVisible ?? this.isVisible,
      onTap: onTap ?? this.onTap,
      notificationChannelId: notificationChannelId ?? this.notificationChannelId,
      notificationSound: notificationSound ?? this.notificationSound,
      metadata: metadata ?? this.metadata,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon.codePoint,
      'color': color.value,
      'priority': priority,
      'isEnabled': isEnabled,
      'isVisible': isVisible,
      'notificationChannelId': notificationChannelId,
      'notificationSound': notificationSound,
      'metadata': metadata,
    };
  }

  /// إنشاء من JSON
  factory FabActionModel.fromJson(Map<String, dynamic> json) {
    return FabActionModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      icon: IconData(json['icon'] ?? Icons.help.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(json['color'] ?? Colors.grey.value),
      priority: json['priority'] ?? 0,
      isEnabled: json['isEnabled'] ?? true,
      isVisible: json['isVisible'] ?? true,
      notificationChannelId: json['notificationChannelId'],
      notificationSound: json['notificationSound'],
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata']) 
          : null,
    );
  }

  /// مقارنة الإجراءات
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FabActionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// تمثيل نصي للإجراء
  @override
  String toString() {
    return 'FabActionModel(id: $id, title: $title, priority: $priority, isEnabled: $isEnabled)';
  }
}

/// مجموعة إجراءات الزر العائم
class FabActionGroup {
  /// معرف المجموعة
  final String id;
  
  /// عنوان المجموعة
  final String title;
  
  /// قائمة الإجراءات
  final List<FabActionModel> actions;
  
  /// أيقونة المجموعة
  final IconData icon;
  
  /// لون المجموعة
  final Color color;

  const FabActionGroup({
    required this.id,
    required this.title,
    required this.actions,
    required this.icon,
    required this.color,
  });

  /// الحصول على الإجراءات المرئية والمفعلة
  List<FabActionModel> get visibleActions {
    return actions
        .where((action) => action.isVisible && action.isEnabled)
        .toList()
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  /// الحصول على عدد الإجراءات المفعلة
  int get enabledActionsCount {
    return actions.where((action) => action.isEnabled).length;
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'actions': actions.map((action) => action.toJson()).toList(),
      'icon': icon.codePoint,
      'color': color.value,
    };
  }

  /// إنشاء من JSON
  factory FabActionGroup.fromJson(Map<String, dynamic> json) {
    return FabActionGroup(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      actions: (json['actions'] as List<dynamic>?)
              ?.map((actionJson) => FabActionModel.fromJson(actionJson))
              .toList() ??
          [],
      icon: IconData(json['icon'] ?? Icons.apps.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(json['color'] ?? Colors.blue.value),
    );
  }
}
