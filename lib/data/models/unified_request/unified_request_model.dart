import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج الطلب الموحد
/// 
/// يدعم جميع أنواع الطلبات: الاستشارات، المواعيد، مراقبة النبات
class UnifiedRequestModel extends Equatable {
  /// معرف الطلب
  final String id;
  
  /// نوع الطلب
  final RequestType type;
  
  /// معرف المستخدم
  final String userId;
  
  /// اسم المستخدم
  final String userName;
  
  /// صورة المستخدم
  final String? userImage;
  
  /// رقم هاتف المستخدم
  final String? userPhone;
  
  /// معرف المرشد/المهندس
  final String? assignedToId;
  
  /// اسم المرشد/المهندس
  final String? assignedToName;
  
  /// العنوان الرئيسي للطلب
  final String title;
  
  /// الوصف التفصيلي
  final String description;
  
  /// حالة الطلب
  final RequestStatus status;
  
  /// الأولوية
  final RequestPriority priority;
  
  /// البيانات الخاصة بكل نوع طلب
  final Map<String, dynamic> specificData;
  
  /// الصور المرفقة
  final List<String>? images;
  
  /// الموقع الجغرافي
  final GeoLocation? location;
  
  /// التاريخ المجدول (للمواعيد)
  final DateTime? scheduledDate;
  
  /// الوقت المجدول (للمواعيد)
  final String? scheduledTime;
  
  /// التكلفة المقدرة
  final double? estimatedCost;
  
  /// ملاحظات إضافية
  final String? notes;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime lastUpdated;
  
  /// تاريخ الاكتمال
  final DateTime? completedAt;

  const UnifiedRequestModel({
    required this.id,
    required this.type,
    required this.userId,
    required this.userName,
    this.userImage,
    this.userPhone,
    this.assignedToId,
    this.assignedToName,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    required this.specificData,
    this.images,
    this.location,
    this.scheduledDate,
    this.scheduledTime,
    this.estimatedCost,
    this.notes,
    required this.createdAt,
    required this.lastUpdated,
    this.completedAt,
  });

  /// إنشاء نموذج من مستند Firestore
  factory UnifiedRequestModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UnifiedRequestModel(
      id: doc.id,
      type: RequestType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => RequestType.consultation,
      ),
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userImage: data['userImage'],
      userPhone: data['userPhone'],
      assignedToId: data['assignedToId'],
      assignedToName: data['assignedToName'],
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      status: RequestStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => RequestStatus.pending,
      ),
      priority: RequestPriority.values.firstWhere(
        (e) => e.name == data['priority'],
        orElse: () => RequestPriority.normal,
      ),
      specificData: Map<String, dynamic>.from(data['specificData'] ?? {}),
      images: data['images'] != null ? List<String>.from(data['images']) : null,
      location: data['location'] != null ? GeoLocation.fromMap(data['location']) : null,
      scheduledDate: data['scheduledDate'] != null 
          ? (data['scheduledDate'] as Timestamp).toDate() 
          : null,
      scheduledTime: data['scheduledTime'],
      estimatedCost: data['estimatedCost']?.toDouble(),
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      completedAt: data['completedAt'] != null 
          ? (data['completedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  /// تحويل النموذج إلى Map للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'userPhone': userPhone,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'title': title,
      'description': description,
      'status': status.name,
      'priority': priority.name,
      'specificData': specificData,
      'images': images,
      'location': location?.toMap(),
      'scheduledDate': scheduledDate != null ? Timestamp.fromDate(scheduledDate!) : null,
      'scheduledTime': scheduledTime,
      'estimatedCost': estimatedCost,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  UnifiedRequestModel copyWith({
    String? id,
    RequestType? type,
    String? userId,
    String? userName,
    String? userImage,
    String? userPhone,
    String? assignedToId,
    String? assignedToName,
    String? title,
    String? description,
    RequestStatus? status,
    RequestPriority? priority,
    Map<String, dynamic>? specificData,
    List<String>? images,
    GeoLocation? location,
    DateTime? scheduledDate,
    String? scheduledTime,
    double? estimatedCost,
    String? notes,
    DateTime? createdAt,
    DateTime? lastUpdated,
    DateTime? completedAt,
  }) {
    return UnifiedRequestModel(
      id: id ?? this.id,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      userPhone: userPhone ?? this.userPhone,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      specificData: specificData ?? this.specificData,
      images: images ?? this.images,
      location: location ?? this.location,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        userId,
        userName,
        userImage,
        userPhone,
        assignedToId,
        assignedToName,
        title,
        description,
        status,
        priority,
        specificData,
        images,
        location,
        scheduledDate,
        scheduledTime,
        estimatedCost,
        notes,
        createdAt,
        lastUpdated,
        completedAt,
      ];
}

/// أنواع الطلبات
enum RequestType {
  consultation('استشارة'),
  appointment('موعد'),
  plantMonitoring('مراقبة نبات');

  const RequestType(this.displayName);
  final String displayName;
}

/// حالات الطلب
enum RequestStatus {
  pending('في الانتظار'),
  assigned('تم التعيين'),
  inProgress('قيد التنفيذ'),
  completed('مكتمل'),
  cancelled('ملغي');

  const RequestStatus(this.displayName);
  final String displayName;
}

/// أولويات الطلب
enum RequestPriority {
  low('منخفضة'),
  normal('عادية'),
  high('عالية'),
  urgent('عاجلة');

  const RequestPriority(this.displayName);
  final String displayName;
}

/// الموقع الجغرافي
class GeoLocation extends Equatable {
  final double latitude;
  final double longitude;
  final String? address;

  const GeoLocation({
    required this.latitude,
    required this.longitude,
    this.address,
  });

  factory GeoLocation.fromMap(Map<String, dynamic> map) {
    return GeoLocation(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      address: map['address'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
    };
  }

  @override
  List<Object?> get props => [latitude, longitude, address];
}
