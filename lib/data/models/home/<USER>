import 'package:flutter/material.dart';

import '../../../imports.dart';

class CardHomeModel {
  final String titel;
  final IconData icon;
  final String router;

  const CardHomeModel({
    required this.titel,
    required this.icon,
    required this.router,
  });
}

const cardHome = [
  CardHomeModel(
    titel: 'الطقس',
    icon: Icons.cloud,
    router: RouteConstants.weather,
  ),
  CardHomeModel(
    titel: 'المحاصيل الزراعية',
    icon: Icons.agriculture,
    router: RouteConstants.agriculturalCrops,
  ),
  CardHomeModel(
    titel: 'المعالم الزراعية',
    icon: Icons.landscape,
    router: RouteConstants.landmarks,
  ),
  CardHomeModel(
    titel: 'المنتدى المجتمعي',
    icon: Icons.forum,
    router: RouteConstants.communityForum,
  ),
  CardHomeModel(
    titel: 'التعليم والتدريب',
    icon: Icons.school,
    router: RouteConstants.education,
  ),
  CardHomeModel(
    titel: 'الدعم الحكومي',
    icon: Icons.support_agent,
    router: RouteConstants.government,
  ),
  CardHomeModel(
    titel: 'سوق محصولك',
    icon: Icons.shopping_cart,
    router: RouteConstants.marketingProducts,
  ),
  CardHomeModel(
    titel: 'الآفات والأمراض',
    icon: Icons.bug_report,
    router: RouteConstants.pestsAndDiseases,
  ),
  CardHomeModel(
    titel: 'التواصل مع المهندس',
    icon: Icons.engineering,
    router: RouteConstants.reachEngineer,
  ),
  CardHomeModel(
    titel: 'طلباتي',
    icon: Icons.assignment,
    router: RouteConstants.myRequests,
  ),
];
