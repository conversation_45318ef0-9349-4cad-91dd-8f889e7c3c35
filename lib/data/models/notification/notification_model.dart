import 'package:hive/hive.dart';

part 'notification_model.g.dart';

/// نموذج الإشعار للتخزين المحلي
@HiveType(typeId: 0)
class NotificationModel extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String title;
  
  @HiveField(2)
  String body;
  
  @HiveField(3)
  String type; // 'consultation_reply', 'new_consultation', 'weather_alert', etc.
  
  @HiveField(4)
  bool isRead;
  
  @HiveField(5)
  DateTime timestamp;
  
  @HiveField(6)
  String? imageUrl;
  
  @HiveField(7)
  Map<String, dynamic>? data;
  
  @HiveField(8)
  String? userId; // معرف المستخدم المستهدف
  
  @HiveField(9)
  String? consultationId; // معرف الاستشارة إذا كان الإشعار متعلق بها
  
  @HiveField(10)
  String? advisorId; // معرف المرشد إذا كان الإشعار من مرشد
  
  @HiveField(11)
  NotificationPriority priority; // أولوية الإشعار
  
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.isRead = false,
    required this.timestamp,
    this.imageUrl,
    this.data,
    this.userId,
    this.consultationId,
    this.advisorId,
    this.priority = NotificationPriority.normal,
  });
  
  /// تحويل من JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      type: json['type'] ?? '',
      isRead: json['isRead'] ?? false,
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      imageUrl: json['imageUrl'],
      data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
      userId: json['userId'],
      consultationId: json['consultationId'],
      advisorId: json['advisorId'],
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == 'NotificationPriority.${json['priority']}',
        orElse: () => NotificationPriority.normal,
      ),
    );
  }
  
  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'isRead': isRead,
      'timestamp': timestamp.toIso8601String(),
      'imageUrl': imageUrl,
      'data': data,
      'userId': userId,
      'consultationId': consultationId,
      'advisorId': advisorId,
      'priority': priority.toString().split('.').last,
    };
  }
  
  /// نسخ مع تعديل
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? type,
    bool? isRead,
    DateTime? timestamp,
    String? imageUrl,
    Map<String, dynamic>? data,
    String? userId,
    String? consultationId,
    String? advisorId,
    NotificationPriority? priority,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      timestamp: timestamp ?? this.timestamp,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      userId: userId ?? this.userId,
      consultationId: consultationId ?? this.consultationId,
      advisorId: advisorId ?? this.advisorId,
      priority: priority ?? this.priority,
    );
  }
}

/// أولوية الإشعار
@HiveType(typeId: 1)
enum NotificationPriority {
  @HiveField(0)
  low,
  
  @HiveField(1)
  normal,
  
  @HiveField(2)
  high,
  
  @HiveField(3)
  urgent,
}

/// أنواع الإشعارات
class NotificationTypes {
  static const String consultationReply = 'consultation_reply';
  static const String newConsultation = 'new_consultation';
  static const String weatherAlert = 'weather_alert';
  static const String pestAlert = 'pest_alert';
  static const String irrigationReminder = 'irrigation_reminder';
  static const String harvestTime = 'harvest_time';
  static const String generalNews = 'general_news';
  static const String systemUpdate = 'system_update';
}
