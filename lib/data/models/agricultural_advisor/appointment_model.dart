import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الموعد مع المرشد الزراعي
class AppointmentModel {
  /// معرف الموعد
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// اسم المستخدم
  final String userName;
  
  /// رقم هاتف المستخدم
  final String userPhone;
  
  /// معرف المرشد
  final String advisorId;
  
  /// اسم المرشد
  final String advisorName;
  
  /// نوع الاستشارة
  final String consultationType;
  
  /// تاريخ الموعد
  final String appointmentDate;
  
  /// وقت الموعد
  final String appointmentTime;
  
  /// وصف المشكلة
  final String problemDescription;
  
  /// حالة الموعد
  final AppointmentStatus status;
  
  /// ملاحظات المرشد
  final String? advisorNotes;
  
  /// تاريخ الإنشاء
  final String createdAt;
  
  /// تاريخ آخر تحديث
  final String lastUpdated;
  
  /// تاريخ التأكيد
  final String? confirmedAt;

  const AppointmentModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userPhone,
    required this.advisorId,
    required this.advisorName,
    required this.consultationType,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.problemDescription,
    required this.status,
    this.advisorNotes,
    required this.createdAt,
    required this.lastUpdated,
    this.confirmedAt,
  });

  /// إنشاء نموذج من مستند Firestore
  factory AppointmentModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AppointmentModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userPhone: data['userPhone'] ?? '',
      advisorId: data['advisorId'] ?? '',
      advisorName: data['advisorName'] ?? '',
      consultationType: data['consultationType'] ?? '',
      appointmentDate: data['appointmentDate'] ?? '',
      appointmentTime: data['appointmentTime'] ?? '',
      problemDescription: data['problemDescription'] ?? '',
      status: AppointmentStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => AppointmentStatus.pending,
      ),
      advisorNotes: data['advisorNotes'],
      createdAt: data['createdAt'] ?? DateTime.now().toIso8601String(),
      lastUpdated: data['lastUpdated'] ?? DateTime.now().toIso8601String(),
      confirmedAt: data['confirmedAt'],
    );
  }

  /// تحويل النموذج إلى Map للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userPhone': userPhone,
      'advisorId': advisorId,
      'advisorName': advisorName,
      'consultationType': consultationType,
      'appointmentDate': appointmentDate,
      'appointmentTime': appointmentTime,
      'problemDescription': problemDescription,
      'status': status.name,
      'advisorNotes': advisorNotes,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
      'confirmedAt': confirmedAt,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  AppointmentModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userPhone,
    String? advisorId,
    String? advisorName,
    String? consultationType,
    String? appointmentDate,
    String? appointmentTime,
    String? problemDescription,
    AppointmentStatus? status,
    String? advisorNotes,
    String? createdAt,
    String? lastUpdated,
    String? confirmedAt,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userPhone: userPhone ?? this.userPhone,
      advisorId: advisorId ?? this.advisorId,
      advisorName: advisorName ?? this.advisorName,
      consultationType: consultationType ?? this.consultationType,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      problemDescription: problemDescription ?? this.problemDescription,
      status: status ?? this.status,
      advisorNotes: advisorNotes ?? this.advisorNotes,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      confirmedAt: confirmedAt ?? this.confirmedAt,
    );
  }

  @override
  String toString() {
    return 'AppointmentModel(id: $id, date: $appointmentDate, time: $appointmentTime, status: $status)';
  }
}

/// حالات الموعد
enum AppointmentStatus {
  /// بانتظار التأكيد
  pending,
  
  /// مؤكد
  confirmed,
  
  /// مكتمل
  completed,
  
  /// ملغى
  cancelled,
  
  /// لم يحضر
  noShow,
}
