import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الاستشارة الزراعية
class ConsultationModel {
  /// معرف الاستشارة
  final String id;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// صورة المستخدم
  final String? userImage;

  /// معرف المرشد
  final String advisorId;

  /// اسم المرشد
  final String advisorName;

  /// نوع المحصول
  final String cropType;

  /// وصف المشكلة
  final String problemDescription;

  /// المساحة المزروعة
  final String area;

  /// صور المشكلة
  final List<String>? images;

  /// حالة الاستشارة
  final ConsultationStatus status;

  /// التقييم (من 1 إلى 5)
  final double? rating;

  /// تعليق التقييم
  final String? ratingComment;

  /// رد المرشد
  final String? advisorResponse;

  /// تاريخ الإنشاء
  final String createdAt;

  /// تاريخ آخر تحديث
  final String lastUpdated;

  /// تاريخ الرد
  final String? respondedAt;

  const ConsultationModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.userImage,
    required this.advisorId,
    required this.advisorName,
    required this.cropType,
    required this.problemDescription,
    required this.area,
    this.images,
    required this.status,
    this.rating,
    this.ratingComment,
    this.advisorResponse,
    required this.createdAt,
    required this.lastUpdated,
    this.respondedAt,
  });

  /// إنشاء نموذج من مستند Firestore
  factory ConsultationModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ConsultationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userImage: data['userImage'],
      advisorId: data['advisorId'] ?? '',
      advisorName: data['advisorName'] ?? '',
      cropType: data['cropType'] ?? '',
      problemDescription: data['problemDescription'] ?? '',
      area: data['area'] ?? '',
      images: data['images'] != null ? List<String>.from(data['images']) : null,
      status: ConsultationStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => ConsultationStatus.pending,
      ),
      rating: data['rating']?.toDouble(),
      ratingComment: data['ratingComment'],
      advisorResponse: data['advisorResponse'],
      createdAt: data['createdAt'] ?? DateTime.now().toIso8601String(),
      lastUpdated: data['lastUpdated'] ?? DateTime.now().toIso8601String(),
      respondedAt: data['respondedAt'],
    );
  }

  /// تحويل النموذج إلى Map للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'advisorId': advisorId,
      'advisorName': advisorName,
      'cropType': cropType,
      'problemDescription': problemDescription,
      'area': area,
      'images': images,
      'status': status.name,
      'rating': rating,
      'ratingComment': ratingComment,
      'advisorResponse': advisorResponse,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
      'respondedAt': respondedAt,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  ConsultationModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userImage,
    String? advisorId,
    String? advisorName,
    String? cropType,
    String? problemDescription,
    String? area,
    List<String>? images,
    ConsultationStatus? status,
    double? rating,
    String? ratingComment,
    String? advisorResponse,
    String? createdAt,
    String? lastUpdated,
    String? respondedAt,
  }) {
    return ConsultationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      advisorId: advisorId ?? this.advisorId,
      advisorName: advisorName ?? this.advisorName,
      cropType: cropType ?? this.cropType,
      problemDescription: problemDescription ?? this.problemDescription,
      area: area ?? this.area,
      images: images ?? this.images,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      ratingComment: ratingComment ?? this.ratingComment,
      advisorResponse: advisorResponse ?? this.advisorResponse,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      respondedAt: respondedAt ?? this.respondedAt,
    );
  }

  @override
  String toString() {
    return 'ConsultationModel(id: $id, cropType: $cropType, status: $status)';
  }
}

/// حالات الاستشارة
enum ConsultationStatus {
  /// بانتظار الرد
  pending,

  /// جاري المعالجة
  inProgress,

  /// تمت الإجابة
  answered,

  /// مغلقة
  closed,

  /// ملغاة
  cancelled,
}
