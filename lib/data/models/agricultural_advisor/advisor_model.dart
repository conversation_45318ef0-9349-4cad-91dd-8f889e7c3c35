import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المرشد الزراعي
class AdvisorModel {
  /// معرف المرشد
  final String id;
  
  /// اسم المرشد
  final String name;
  
  /// التخصص الرئيسي
  final String specialty;
  
  /// صورة المرشد
  final String? image;
  
  /// رقم الهاتف
  final String phone;
  
  /// البريد الإلكتروني
  final String email;
  
  /// الموقع/المدينة
  final String location;
  
  /// التقييم (من 1 إلى 5)
  final double rating;
  
  /// عدد المراجعات
  final int reviewsCount;
  
  /// عدد العملاء
  final int clientsCount;
  
  /// سنوات الخبرة
  final int experienceYears;
  
  /// نسبة النجاح
  final double successRate;
  
  /// قائمة التخصصات
  final List<String> specialties;
  
  /// أوقات العمل
  final Map<String, String> workingHours;
  
  /// حالة المرشد (متاح/غير متاح)
  final bool isAvailable;
  
  /// تاريخ الإنشاء
  final String createdAt;
  
  /// تاريخ آخر تحديث
  final String lastUpdated;

  const AdvisorModel({
    required this.id,
    required this.name,
    required this.specialty,
    this.image,
    required this.phone,
    required this.email,
    required this.location,
    required this.rating,
    required this.reviewsCount,
    required this.clientsCount,
    required this.experienceYears,
    required this.successRate,
    required this.specialties,
    required this.workingHours,
    this.isAvailable = true,
    required this.createdAt,
    required this.lastUpdated,
  });

  /// إنشاء نموذج من مستند Firestore
  factory AdvisorModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AdvisorModel(
      id: doc.id,
      name: data['name'] ?? '',
      specialty: data['specialty'] ?? '',
      image: data['image'],
      phone: data['phone'] ?? '',
      email: data['email'] ?? '',
      location: data['location'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      reviewsCount: data['reviewsCount'] ?? 0,
      clientsCount: data['clientsCount'] ?? 0,
      experienceYears: data['experienceYears'] ?? 0,
      successRate: (data['successRate'] ?? 0.0).toDouble(),
      specialties: List<String>.from(data['specialties'] ?? []),
      workingHours: Map<String, String>.from(data['workingHours'] ?? {}),
      isAvailable: data['isAvailable'] ?? true,
      createdAt: data['createdAt'] ?? DateTime.now().toIso8601String(),
      lastUpdated: data['lastUpdated'] ?? DateTime.now().toIso8601String(),
    );
  }

  /// تحويل النموذج إلى Map للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'specialty': specialty,
      'image': image,
      'phone': phone,
      'email': email,
      'location': location,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'clientsCount': clientsCount,
      'experienceYears': experienceYears,
      'successRate': successRate,
      'specialties': specialties,
      'workingHours': workingHours,
      'isAvailable': isAvailable,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  AdvisorModel copyWith({
    String? id,
    String? name,
    String? specialty,
    String? image,
    String? phone,
    String? email,
    String? location,
    double? rating,
    int? reviewsCount,
    int? clientsCount,
    int? experienceYears,
    double? successRate,
    List<String>? specialties,
    Map<String, String>? workingHours,
    bool? isAvailable,
    String? createdAt,
    String? lastUpdated,
  }) {
    return AdvisorModel(
      id: id ?? this.id,
      name: name ?? this.name,
      specialty: specialty ?? this.specialty,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      location: location ?? this.location,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      clientsCount: clientsCount ?? this.clientsCount,
      experienceYears: experienceYears ?? this.experienceYears,
      successRate: successRate ?? this.successRate,
      specialties: specialties ?? this.specialties,
      workingHours: workingHours ?? this.workingHours,
      isAvailable: isAvailable ?? this.isAvailable,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'AdvisorModel(id: $id, name: $name, specialty: $specialty, rating: $rating)';
  }
}
