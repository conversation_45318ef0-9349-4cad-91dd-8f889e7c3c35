import 'package:equatable/equatable.dart';

/// نموذج سجل المحصول للزراعة الذكية
/// يحتوي على جميع البيانات المتعلقة بمحصول معين
/// مع دعم التحويل من وإلى JSON للتخزين والاسترجاع
class CropRecordModel extends Equatable {
  /// معرف فريد للمحصول
  final String id;
  
  /// اسم المحصول
  final String name;
  
  /// نوع المحصول (قمح، أرز، خضروات، إلخ)
  final String type;
  
  /// المساحة المزروعة بالهكتار
  final double area;
  
  /// تاريخ الزراعة
  final String plantingDate;
  
  /// تاريخ الحصاد المتوقع
  final String? expectedHarvestDate;
  
  /// حالة المحصول الحالية
  final CropStatus status;
  
  /// مرحلة النمو الحالية
  final GrowthStage growthStage;
  
  /// نسبة التقدم في النمو (0-100)
  final double growthProgress;
  
  /// الموقع الجغرافي للمحصول
  final String location;
  
  /// المحافظة اليمنية
  final String province;
  
  /// ملاحظات إضافية
  final String? notes;
  
  /// تاريخ آخر تحديث
  final String lastUpdated;
  
  /// معرف المزارع المالك
  final String farmerId;

  const CropRecordModel({
    required this.id,
    required this.name,
    required this.type,
    required this.area,
    required this.plantingDate,
    this.expectedHarvestDate,
    required this.status,
    required this.growthStage,
    required this.growthProgress,
    required this.location,
    required this.province,
    this.notes,
    required this.lastUpdated,
    required this.farmerId,
  });

  /// إنشاء نسخة جديدة مع تعديل بعض القيم
  CropRecordModel copyWith({
    String? id,
    String? name,
    String? type,
    double? area,
    String? plantingDate,
    String? expectedHarvestDate,
    CropStatus? status,
    GrowthStage? growthStage,
    double? growthProgress,
    String? location,
    String? province,
    String? notes,
    String? lastUpdated,
    String? farmerId,
  }) {
    return CropRecordModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      area: area ?? this.area,
      plantingDate: plantingDate ?? this.plantingDate,
      expectedHarvestDate: expectedHarvestDate ?? this.expectedHarvestDate,
      status: status ?? this.status,
      growthStage: growthStage ?? this.growthStage,
      growthProgress: growthProgress ?? this.growthProgress,
      location: location ?? this.location,
      province: province ?? this.province,
      notes: notes ?? this.notes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      farmerId: farmerId ?? this.farmerId,
    );
  }

  /// تحويل من JSON
  factory CropRecordModel.fromJson(Map<String, dynamic> json) {
    return CropRecordModel(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      area: (json['area'] as num).toDouble(),
      plantingDate: json['plantingDate'] as String,
      expectedHarvestDate: json['expectedHarvestDate'] as String?,
      status: CropStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => CropStatus.growing,
      ),
      growthStage: GrowthStage.values.firstWhere(
        (e) => e.name == json['growthStage'],
        orElse: () => GrowthStage.seedling,
      ),
      growthProgress: (json['growthProgress'] as num).toDouble(),
      location: json['location'] as String,
      province: json['province'] as String,
      notes: json['notes'] as String?,
      lastUpdated: json['lastUpdated'] as String,
      farmerId: json['farmerId'] as String,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'area': area,
      'plantingDate': plantingDate,
      'expectedHarvestDate': expectedHarvestDate,
      'status': status.name,
      'growthStage': growthStage.name,
      'growthProgress': growthProgress,
      'location': location,
      'province': province,
      'notes': notes,
      'lastUpdated': lastUpdated,
      'farmerId': farmerId,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        area,
        plantingDate,
        expectedHarvestDate,
        status,
        growthStage,
        growthProgress,
        location,
        province,
        notes,
        lastUpdated,
        farmerId,
      ];
}

/// حالات المحصول المختلفة
enum CropStatus {
  /// قيد الزراعة
  planting('قيد الزراعة'),
  
  /// قيد النمو
  growing('قيد النمو'),

  /// مرحلة الإزهار
  flowering('مرحلة الإزهار'),

  /// جاهز للحصاد
  readyToHarvest('جاهز للحصاد'),
  
  /// تم الحصاد
  harvested('تم الحصاد'),
  
  /// متضرر
  damaged('متضرر'),
  
  /// فاشل
  failed('فاشل');

  const CropStatus(this.displayName);
  
  /// الاسم المعروض باللغة العربية
  final String displayName;
}

/// مراحل النمو المختلفة
enum GrowthStage {
  /// البذرة
  seed('البذرة'),
  
  /// الشتلة
  seedling('الشتلة'),
  
  /// النمو الخضري
  vegetative('النمو الخضري'),
  
  /// الإزهار
  flowering('الإزهار'),
  
  /// تكوين الثمار
  fruiting('تكوين الثمار'),
  
  /// النضج
  maturity('النضج');

  const GrowthStage(this.displayName);
  
  /// الاسم المعروض باللغة العربية
  final String displayName;
}

/// أنواع المحاصيل المدعومة في اليمن
class YemeniCropTypes {
  static const List<String> cereals = [
    'قمح',
    'شعير',
    'ذرة رفيعة',
    'دخن',
  ];

  static const List<String> vegetables = [
    'طماطم',
    'خيار',
    'فلفل',
    'باذنجان',
    'كوسا',
    'بامية',
    'فاصولياء',
    'بازلاء',
  ];

  static const List<String> fruits = [
    'عنب',
    'رمان',
    'تين',
    'موز',
    'مانجو',
    'جوافة',
  ];

  static const List<String> legumes = [
    'عدس',
    'حمص',
    'فول',
    'لوبيا',
  ];

  static const List<String> herbs = [
    'حلبة',
    'كمون',
    'كزبرة',
    'شمر',
  ];

  /// جميع أنواع المحاصيل
  static List<String> get allTypes => [
        ...cereals,
        ...vegetables,
        ...fruits,
        ...legumes,
        ...herbs,
      ];
}

/// المحافظات اليمنية المدعومة
class YemeniProvinces {
  static const List<String> provinces = [
    'صنعاء',
    'عدن',
    'تعز',
    'الحديدة',
    'إب',
    'ذمار',
    'حضرموت',
    'لحج',
    'أبين',
    'شبوة',
    'المهرة',
    'حجة',
    'الجوف',
    'مأرب',
    'البيضاء',
    'الضالع',
    'ريمة',
    'عمران',
    'صعدة',
    'المحويت',
    'سقطرى',
  ];
}
