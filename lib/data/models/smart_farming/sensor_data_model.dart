import 'package:equatable/equatable.dart';

/// نموذج بيانات المستشعرات للزراعة الذكية
/// يحتوي على قراءات المستشعرات المختلفة للمراقبة الذكية
class SensorDataModel extends Equatable {
  /// معرف فريد للقراءة
  final String id;
  
  /// معرف المحصول المرتبط
  final String cropId;
  
  /// رطوبة التربة (نسبة مئوية)
  final double soilMoisture;
  
  /// درجة الحرارة (مئوية)
  final double temperature;
  
  /// الرطوبة الجوية (نسبة مئوية)
  final double humidity;
  
  /// شدة الإضاءة (لوكس)
  final double lightIntensity;
  
  /// مستوى الحموضة في التربة (pH)
  final double soilPH;
  
  /// مستوى النيتروجين في التربة
  final NutrientLevel nitrogenLevel;
  
  /// مستوى الفوسفور في التربة
  final NutrientLevel phosphorusLevel;
  
  /// مستوى البوتاسيوم في التربة
  final NutrientLevel potassiumLevel;
  
  /// سرعة الرياح (كم/ساعة)
  final double windSpeed;
  
  /// كمية الأمطار (مم)
  final double rainfall;
  
  /// تاريخ ووقت القراءة
  final String timestamp;
  
  /// حالة المستشعرات
  final SensorStatus status;

  const SensorDataModel({
    required this.id,
    required this.cropId,
    required this.soilMoisture,
    required this.temperature,
    required this.humidity,
    required this.lightIntensity,
    required this.soilPH,
    required this.nitrogenLevel,
    required this.phosphorusLevel,
    required this.potassiumLevel,
    required this.windSpeed,
    required this.rainfall,
    required this.timestamp,
    required this.status,
  });

  /// إنشاء نسخة جديدة مع تعديل بعض القيم
  SensorDataModel copyWith({
    String? id,
    String? cropId,
    double? soilMoisture,
    double? temperature,
    double? humidity,
    double? lightIntensity,
    double? soilPH,
    NutrientLevel? nitrogenLevel,
    NutrientLevel? phosphorusLevel,
    NutrientLevel? potassiumLevel,
    double? windSpeed,
    double? rainfall,
    String? timestamp,
    SensorStatus? status,
  }) {
    return SensorDataModel(
      id: id ?? this.id,
      cropId: cropId ?? this.cropId,
      soilMoisture: soilMoisture ?? this.soilMoisture,
      temperature: temperature ?? this.temperature,
      humidity: humidity ?? this.humidity,
      lightIntensity: lightIntensity ?? this.lightIntensity,
      soilPH: soilPH ?? this.soilPH,
      nitrogenLevel: nitrogenLevel ?? this.nitrogenLevel,
      phosphorusLevel: phosphorusLevel ?? this.phosphorusLevel,
      potassiumLevel: potassiumLevel ?? this.potassiumLevel,
      windSpeed: windSpeed ?? this.windSpeed,
      rainfall: rainfall ?? this.rainfall,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
    );
  }

  /// تحويل من JSON
  factory SensorDataModel.fromJson(Map<String, dynamic> json) {
    return SensorDataModel(
      id: json['id'] as String,
      cropId: json['cropId'] as String,
      soilMoisture: (json['soilMoisture'] as num).toDouble(),
      temperature: (json['temperature'] as num).toDouble(),
      humidity: (json['humidity'] as num).toDouble(),
      lightIntensity: (json['lightIntensity'] as num).toDouble(),
      soilPH: (json['soilPH'] as num).toDouble(),
      nitrogenLevel: NutrientLevel.values.firstWhere(
        (e) => e.name == json['nitrogenLevel'],
        orElse: () => NutrientLevel.medium,
      ),
      phosphorusLevel: NutrientLevel.values.firstWhere(
        (e) => e.name == json['phosphorusLevel'],
        orElse: () => NutrientLevel.medium,
      ),
      potassiumLevel: NutrientLevel.values.firstWhere(
        (e) => e.name == json['potassiumLevel'],
        orElse: () => NutrientLevel.medium,
      ),
      windSpeed: (json['windSpeed'] as num).toDouble(),
      rainfall: (json['rainfall'] as num).toDouble(),
      timestamp: json['timestamp'] as String,
      status: SensorStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SensorStatus.active,
      ),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cropId': cropId,
      'soilMoisture': soilMoisture,
      'temperature': temperature,
      'humidity': humidity,
      'lightIntensity': lightIntensity,
      'soilPH': soilPH,
      'nitrogenLevel': nitrogenLevel.name,
      'phosphorusLevel': phosphorusLevel.name,
      'potassiumLevel': potassiumLevel.name,
      'windSpeed': windSpeed,
      'rainfall': rainfall,
      'timestamp': timestamp,
      'status': status.name,
    };
  }

  /// تقييم الحالة العامة للمحصول بناءً على قراءات المستشعرات
  CropHealthStatus get overallHealth {
    int healthScore = 0;
    int totalChecks = 0;

    // فحص رطوبة التربة (مثالي: 40-70%)
    totalChecks++;
    if (soilMoisture >= 40 && soilMoisture <= 70) {
      healthScore++;
    }

    // فحص درجة الحرارة (مثالي: 20-30°C للمحاصيل اليمنية)
    totalChecks++;
    if (temperature >= 20 && temperature <= 30) {
      healthScore++;
    }

    // فحص الحموضة (مثالي: 6.0-7.5)
    totalChecks++;
    if (soilPH >= 6.0 && soilPH <= 7.5) {
      healthScore++;
    }

    // فحص مستويات المغذيات
    totalChecks += 3;
    if (nitrogenLevel == NutrientLevel.medium || nitrogenLevel == NutrientLevel.high) {
      healthScore++;
    }
    if (phosphorusLevel == NutrientLevel.medium || phosphorusLevel == NutrientLevel.high) {
      healthScore++;
    }
    if (potassiumLevel == NutrientLevel.medium || potassiumLevel == NutrientLevel.high) {
      healthScore++;
    }

    final healthPercentage = (healthScore / totalChecks) * 100;

    if (healthPercentage >= 80) {
      return CropHealthStatus.excellent;
    } else if (healthPercentage >= 60) {
      return CropHealthStatus.good;
    } else if (healthPercentage >= 40) {
      return CropHealthStatus.fair;
    } else {
      return CropHealthStatus.poor;
    }
  }

  @override
  List<Object?> get props => [
        id,
        cropId,
        soilMoisture,
        temperature,
        humidity,
        lightIntensity,
        soilPH,
        nitrogenLevel,
        phosphorusLevel,
        potassiumLevel,
        windSpeed,
        rainfall,
        timestamp,
        status,
      ];
}

/// مستويات المغذيات في التربة
enum NutrientLevel {
  /// منخفض
  low('منخفض'),
  
  /// متوسط
  medium('متوسط'),
  
  /// عالي
  high('عالي'),
  
  /// مفرط
  excessive('مفرط');

  const NutrientLevel(this.displayName);
  
  /// الاسم المعروض باللغة العربية
  final String displayName;
}

/// حالة المستشعرات
enum SensorStatus {
  /// نشط
  active('نشط'),
  
  /// غير نشط
  inactive('غير نشط'),
  
  /// خطأ
  error('خطأ'),
  
  /// صيانة
  maintenance('صيانة');

  const SensorStatus(this.displayName);
  
  /// الاسم المعروض باللغة العربية
  final String displayName;
}

/// حالة صحة المحصول
enum CropHealthStatus {
  /// ممتاز
  excellent('ممتاز'),
  
  /// جيد
  good('جيد'),
  
  /// مقبول
  fair('مقبول'),
  
  /// ضعيف
  poor('ضعيف');

  const CropHealthStatus(this.displayName);
  
  /// الاسم المعروض باللغة العربية
  final String displayName;
}
