import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج المنشور في المنتدى المجتمعي
///
/// يستخدم هذا النموذج لتمثيل المنشورات في المنتدى المجتمعي.
class PostModel extends Equatable {
  /// معرف المنشور
  final String id;

  /// معرف المستخدم الذي أنشأ المنشور
  final String userId;

  /// اسم المستخدم الذي أنشأ المنشور
  final String userName;

  /// صورة المستخدم الذي أنشأ المنشور
  final String userImage;

  /// نص المنشور
  final String? text;

  /// روابط الصور في المنشور
  final List<String>? images;

  /// رابط الفيديو في المنشور
  final String? video;

  /// تاريخ إنشاء المنشور
  final String createdAt;

  /// تاريخ آخر تحديث للمنشور
  final String lastUpdated;

  /// عدد الإعجابات على المنشور
  final int likesCount;

  /// عدد التعليقات على المنشور
  final int commentsCount;

  /// قائمة معرفات المستخدمين الذين أعجبوا بالمنشور
  final List<String> likedBy;

  /// قائمة الوسوم (هاشتاج) في المنشور
  final List<String> hashtags;

  /// عدد مشاهدات المنشور
  final int viewsCount;

  /// منشئ نموذج المنشور
  const PostModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userImage,
    this.text,
    this.images,
    this.video,
    required this.createdAt,
    required this.lastUpdated,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.likedBy = const [],
    this.hashtags = const [],
    this.viewsCount = 0,
  });

  /// إنشاء نسخة جديدة من النموذج مع تحديث بعض الحقول
  PostModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userImage,
    String? text,
    List<String>? images,
    String? video,
    String? createdAt,
    String? lastUpdated,
    int? likesCount,
    int? commentsCount,
    List<String>? likedBy,
    List<String>? hashtags,
    int? viewsCount,
  }) {
    return PostModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      text: text ?? this.text,
      images: images ?? this.images,
      video: video ?? this.video,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      likedBy: likedBy ?? this.likedBy,
      hashtags: hashtags ?? this.hashtags,
      viewsCount: viewsCount ?? this.viewsCount,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'text': text,
      'images': images,
      'video': video,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'likedBy': likedBy,
      'hashtags': hashtags,
      'viewsCount': viewsCount,
    };
  }

  /// إنشاء نموذج من Map
  factory PostModel.fromMap(Map<String, dynamic> map) {
    return PostModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'] ?? '',
      text: map['text'],
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      video: map['video'],
      createdAt: map['createdAt'] ?? '',
      lastUpdated: map['lastUpdated'] ?? '',
      likesCount: map['likesCount'] ?? 0,
      commentsCount: map['commentsCount'] ?? 0,
      likedBy: map['likedBy'] != null ? List<String>.from(map['likedBy']) : [],
      hashtags: map['hashtags'] != null ? List<String>.from(map['hashtags']) : [],
      viewsCount: map['viewsCount'] ?? 0,
    );
  }

  /// إنشاء نموذج من DocumentSnapshot
  factory PostModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PostModel.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        userName,
        userImage,
        text,
        images,
        video,
        createdAt,
        lastUpdated,
        likesCount,
        commentsCount,
        likedBy,
        hashtags,
        viewsCount,
      ];
}
