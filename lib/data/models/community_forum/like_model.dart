import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج الإعجاب في المنتدى المجتمعي
///
/// يستخدم هذا النموذج لتمثيل الإعجابات على المنشورات في المنتدى المجتمعي.
class LikeModel extends Equatable {
  /// معرف الإعجاب
  final String id;

  /// معرف المنشور الذي ينتمي إليه الإعجاب
  final String postId;

  /// معرف المستخدم الذي أبدى الإعجاب
  final String userId;

  /// تاريخ إنشاء الإعجاب
  final String createdAt;

  /// منشئ نموذج الإعجاب
  const LikeModel({
    required this.id,
    required this.postId,
    required this.userId,
    required this.createdAt,
  });

  /// إنشاء نسخة جديدة من النموذج مع تحديث بعض الحقول
  LikeModel copyWith({
    String? id,
    String? postId,
    String? userId,
    String? createdAt,
  }) {
    return LikeModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'createdAt': createdAt,
    };
  }

  /// إنشاء نموذج من Map
  factory LikeModel.fromMap(Map<String, dynamic> map) {
    return LikeModel(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      userId: map['userId'] ?? '',
      createdAt: map['createdAt'] ?? '',
    );
  }

  /// إنشاء نموذج من DocumentSnapshot
  factory LikeModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return LikeModel.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  @override
  List<Object> get props => [
        id,
        postId,
        userId,
        createdAt,
      ];
}
