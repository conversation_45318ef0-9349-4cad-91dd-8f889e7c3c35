import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج التعليق في المنتدى المجتمعي
///
/// يستخدم هذا النموذج لتمثيل التعليقات على المنشورات في المنتدى المجتمعي.
class CommentModel extends Equatable {
  /// معرف التعليق
  final String id;

  /// معرف المنشور الذي ينتمي إليه التعليق
  final String postId;

  /// معرف المستخدم الذي أنشأ التعليق
  final String userId;

  /// اسم المستخدم الذي أنشأ التعليق
  final String userName;

  /// صورة المستخدم الذي أنشأ التعليق
  final String userImage;

  /// نص التعليق
  final String text;

  /// تاريخ إنشاء التعليق
  final String createdAt;

  /// منشئ نموذج التعليق
  const CommentModel({
    required this.id,
    required this.postId,
    required this.userId,
    required this.userName,
    required this.userImage,
    required this.text,
    required this.createdAt,
  });

  /// إنشاء نسخة جديدة من النموذج مع تحديث بعض الحقول
  CommentModel copyWith({
    String? id,
    String? postId,
    String? userId,
    String? userName,
    String? userImage,
    String? text,
    String? createdAt,
  }) {
    return CommentModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      text: text ?? this.text,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'text': text,
      'createdAt': createdAt,
    };
  }

  /// إنشاء نموذج من Map
  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userImage: map['userImage'] ?? '',
      text: map['text'] ?? '',
      createdAt: map['createdAt'] ?? '',
    );
  }

  /// إنشاء نموذج من DocumentSnapshot
  factory CommentModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CommentModel.fromMap({
      'id': doc.id,
      ...data,
    });
  }

  @override
  List<Object> get props => [
        id,
        postId,
        userId,
        userName,
        userImage,
        text,
        createdAt,
      ];
}
