class PlantCategory {
  final int id;
  final String name;
  final String? image;

  PlantCategory({
    required this.id,
    required this.name,
    this.image,
  });

  factory PlantCategory.fromJson(Map<String, dynamic> json) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = json['id'] ?? 0;

    return PlantCategory(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      name: json['name'] ?? '',
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
    };
  }

  @override
  String toString() {
    return 'PlantCategory{id: $id, name: $name, image: $image}';
  }
}
