class PlantOperationCategory {
  final int id;
  final String name;

  PlantOperationCategory({
    required this.id,
    required this.name,
  });

  factory PlantOperationCategory.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;

    return PlantOperationCategory(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      name: map['name'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }
}
