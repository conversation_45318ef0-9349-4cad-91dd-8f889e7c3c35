class Disease {
  final int id;
  final String name;
  final int plantId;
  final int diseaseTypeId;
  final String mainPhoto;
  final String details;

  Disease({
    required this.id,
    required this.name,
    required this.plantId,
    required this.diseaseTypeId,
    required this.mainPhoto,
    required this.details,
  });

  factory Disease.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;
    final plantId = map['plant_id'] ?? 0;
    final diseaseTypeId = map['disease_type_id'] ?? 0;

    return Disease(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      name: map['name'] ?? '',
      plantId: plantId is int ? plantId : int.tryParse(plantId.toString()) ?? 0,
      diseaseTypeId: diseaseTypeId is int
          ? diseaseTypeId
          : int.tryParse(diseaseTypeId.toString()) ?? 0,
      mainPhoto: map['main_photo'] ?? '',
      details: map['details'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'plant_id': plantId,
      'disease_type_id': diseaseTypeId,
      'main_photo': mainPhoto,
      'details': details,
    };
  }
}
