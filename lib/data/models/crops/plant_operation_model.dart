class PlantOperation {
  final int id;
  final int plantId;
  final int operationCategoryId;
  final String description;
  final String name;
  final String details;

  PlantOperation({
    required this.id,
    required this.plantId,
    required this.operationCategoryId,
    required this.description,
    required this.name,
    required this.details,
  });

  factory PlantOperation.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;
    final plantId = map['plant_id'] ?? 0;

    // التعامل مع كلا الحقلين operation_category_id و category_id
    // بعض السجلات تستخدم category_id بدلاً من operation_category_id
    var operationCategoryId = map['operation_category_id'];
    operationCategoryId ??= map['category_id'] ?? 0;

    return PlantOperation(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      plantId: plantId is int ? plantId : int.tryParse(plantId.toString()) ?? 0,
      operationCategoryId: operationCategoryId is int
          ? operationCategoryId
          : int.tryParse(operationCategoryId.toString()) ?? 0,
      description: map['description'] ?? '',
      name: map['name'] ?? '',
      details: map['details'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'plant_id': plantId,
      'operation_category_id': operationCategoryId,
      'description': description,
      'name': name,
      'details': details,
    };
  }
}
