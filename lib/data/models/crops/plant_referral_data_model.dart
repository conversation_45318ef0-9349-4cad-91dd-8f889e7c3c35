class PlantReferralData {
  final int id;
  final int plantId;
  final String description;
  final String soilType;
  final String irrigationMethod;
  final String fertilizationMethod;
  final String harvestTime;
  final String name;
  final String details;

  PlantReferralData({
    required this.id,
    required this.plantId,
    required this.description,
    required this.soilType,
    required this.irrigationMethod,
    required this.fertilizationMethod,
    required this.harvestTime,
    required this.name,
    required this.details,
  });

  factory PlantReferralData.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;
    final plantId = map['plant_id'] ?? 0;

    return PlantReferralData(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      plantId: plantId is int ? plantId : int.tryParse(plantId.toString()) ?? 0,
      description: map['description'] ?? '',
      soilType: map['soil_type'] ?? '',
      irrigationMethod: map['irrigation_method'] ?? '',
      fertilizationMethod: map['fertilization_method'] ?? '',
      harvestTime: map['harvest_time'] ?? '',
      name: map['name'] ?? '',
      details: map['details'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'plant_id': plantId,
      'description': description,
      'soil_type': soilType,
      'irrigation_method': irrigationMethod,
      'fertilization_method': fertilizationMethod,
      'harvest_time': harvestTime,
      'name': name,
      'details': details,
    };
  }
}
