class Plant {
  final int id;
  final String name;
  final int categoryId;
  final String? image;
  final String mainPhoto;
  final String details;

  Plant({
    required this.id,
    required this.name,
    required this.categoryId,
    this.image,
    required this.mainPhoto,
    required this.details,
  });

  factory Plant.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;
    final categoryId = map['category_id'] ?? 0;

    return Plant(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      name: map['name'] ?? '',
      categoryId: categoryId is int
          ? categoryId
          : int.tryParse(categoryId.toString()) ?? 0,
      image: map['image'],
      mainPhoto: map['main_photo'] ?? '',
      details: map['details'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category_id': categoryId,
      'image': image,
      'main_photo': mainPhoto,
      'details': details,
    };
  }
}
