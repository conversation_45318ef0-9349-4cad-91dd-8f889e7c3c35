class DiseaseType {
  final int id;
  final String name;

  DiseaseType({
    required this.id,
    required this.name,
  });

  factory DiseaseType.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;

    return DiseaseType(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      name: map['name'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }
}
