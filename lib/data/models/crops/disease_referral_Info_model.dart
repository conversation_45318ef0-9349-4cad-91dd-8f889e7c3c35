class DiseaseReferralInfo {
  final int id;
  final int diseaseId;
  final String description;
  final String symptoms;
  final String causes;
  final String treatment;
  final String prevention;
  final String title;
  final String details;

  DiseaseReferralInfo({
    required this.id,
    required this.diseaseId,
    required this.description,
    required this.symptoms,
    required this.causes,
    required this.treatment,
    required this.prevention,
    required this.title,
    required this.details,
  });

  factory DiseaseReferralInfo.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود القيم المطلوبة وتحويلها إلى int إذا كانت null
    final id = map['id'] ?? 0;
    final diseaseId = map['disease_id'] ?? 0;

    return DiseaseReferralInfo(
      id: id is int ? id : int.tryParse(id.toString()) ?? 0,
      diseaseId: diseaseId is int
          ? diseaseId
          : int.tryParse(diseaseId.toString()) ?? 0,
      description: map['description'] ?? '',
      symptoms: map['symptoms'] ?? '',
      causes: map['causes'] ?? '',
      treatment: map['treatment'] ?? '',
      prevention: map['prevention'] ?? '',
      title: map['title'] ?? '',
      details: map['details'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'disease_id': diseaseId,
      'description': description,
      'symptoms': symptoms,
      'causes': causes,
      'treatment': treatment,
      'prevention': prevention,
      'title': title,
      'details': details,
    };
  }
}
