import '../../domain/entities/landmark_entity.dart';

/// نموذج المعلم الزراعي في طبقة البيانات
class LandmarkModel extends LandmarkEntity {
  /// إنشاء نموذج معلم زراعي جديد
  const LandmarkModel({
    required super.name,
    required super.details,
    required super.operations,
    super.imageUrl,
    required super.gregorianStartDate,
    required super.gregorianEndDate,
    required super.crestStartDate,
    required super.crestEndDate,
    required super.himyarStartDate,
    required super.himyarEndDate,
  });

  /// إنشاء نموذج معلم زراعي من خريطة بيانات
  factory LandmarkModel.fromMap(Map<String, dynamic> map) {
    return LandmarkModel(
      name: map['name'] ?? '',
      details: map['details'] ?? '',
      operations: map['operations'] ?? '',
      imageUrl: map['imageUrl'],
      gregorianStartDate: DateTime.parse(map['gregorianStartDate']),
      gregorianEndDate: DateTime.parse(map['gregorianEndDate']),
      crestStartDate: DateTime.parse(map['crestStartDate']),
      crestEndDate: DateTime.parse(map['crestEndDate']),
      himyarStartDate: DateTime.parse(map['himyarStartDate']),
      himyarEndDate: DateTime.parse(map['himyarEndDate']),
    );
  }

  /// تحويل نموذج المعلم الزراعي إلى خريطة بيانات
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'details': details,
      'operations': operations,
      'imageUrl': imageUrl,
      'gregorianStartDate': gregorianStartDate.toIso8601String(),
      'gregorianEndDate': gregorianEndDate.toIso8601String(),
      'crestStartDate': crestStartDate.toIso8601String(),
      'crestEndDate': crestEndDate.toIso8601String(),
      'himyarStartDate': himyarStartDate.toIso8601String(),
      'himyarEndDate': himyarEndDate.toIso8601String(),
    };
  }

  /// إنشاء نسخة جديدة من نموذج المعلم الزراعي مع تحديث بعض الحقول
  LandmarkModel copyWith({
    String? name,
    String? details,
    String? operations,
    String? imageUrl,
    DateTime? gregorianStartDate,
    DateTime? gregorianEndDate,
    DateTime? crestStartDate,
    DateTime? crestEndDate,
    DateTime? himyarStartDate,
    DateTime? himyarEndDate,
  }) {
    return LandmarkModel(
      name: name ?? this.name,
      details: details ?? this.details,
      operations: operations ?? this.operations,
      imageUrl: imageUrl ?? this.imageUrl,
      gregorianStartDate: gregorianStartDate ?? this.gregorianStartDate,
      gregorianEndDate: gregorianEndDate ?? this.gregorianEndDate,
      crestStartDate: crestStartDate ?? this.crestStartDate,
      crestEndDate: crestEndDate ?? this.crestEndDate,
      himyarStartDate: himyarStartDate ?? this.himyarStartDate,
      himyarEndDate: himyarEndDate ?? this.himyarEndDate,
    );
  }
}
