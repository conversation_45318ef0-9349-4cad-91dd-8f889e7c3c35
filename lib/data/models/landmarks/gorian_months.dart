class GorianMonths {
  final int id;
  final String name;
  final int days;

  GorianMonths({
    required this.id,
    required this.name,
    required this.days,
  });

  // Factory constructor to create AgriculturalLandmark from Map (JSON)
  factory GorianMonths.fromJson(Map<String, dynamic> map) {
    return GorianMonths(
      id: map['id'] as int,
      name: map['name'] as String,
      days: map['days'],
    );
  }

  // Convert AgriculturalLandmark to Map (JSON)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'days': days,
    };
  }

  // Create a copy of AgriculturalLandmark with optional new values
  GorianMonths copyWith({
    int? id,
    String? name,
    int? days,
  }) {
    return GorianMonths(
      id: id ?? this.id,
      name: name ?? this.name,
      days: days ?? this.days,
    );
  }

  @override
  String toString() {
    return 'CrestMonths(id: $id, name: $name)';
  }
}
