class HemiariMonths {
  final int id;
  final String name;
  final int days;

  HemiariMonths({
    required this.id,
    required this.name,
    required this.days,
  });

  // Factory constructor to create AgriculturalLandmark from Map (JSON)
  factory HemiariMonths.fromJson(Map<String, dynamic> map) {
    return HemiariMonths(
      id: map['id'] as int,
      name: map['name'] as String,
      days: map['days'],
    );
  }

  // Convert AgriculturalLandmark to Map (JSON)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'days': days,
    };
  }

  // Create a copy of AgriculturalLandmark with optional new values
  HemiariMonths copyWith({
    int? id,
    String? name,
    int? days,
  }) {
    return HemiariMonths(
      id: id ?? this.id,
      name: name ?? this.name,
      days: days ?? this.days,
    );
  }

  @override
  String toString() {
    return 'HemiariMonths(id: $id, name: $name)';
  }
}
