import '../../../domain/entities/landmark_entity.dart';
import '../../models/landmark_model.dart';
import 'agricultural_landmark_model.dart';

/// محول بين نماذج المعالم الزراعية
///
/// يستخدم لتحويل النماذج القديمة إلى النماذج الجديدة والعكس
class LandmarkMapper {
  /// تحويل نموذج المعلم الزراعي القديم إلى النموذج الجديد
  static LandmarkModel fromAgriculturalLandmark(AgriculturalLandmark landmark) {
    return LandmarkModel(
      name: landmark.name,
      details: landmark.details,
      operations: landmark.operations ?? '',
      imageUrl: landmark.image,
      gregorianStartDate: landmark.gregorianStartDate,
      gregorianEndDate: landmark.gregorianEndDate,
      crestStartDate: landmark.crestStartDate,
      crestEndDate: landmark.crestEndDate,
      himyarStartDate: landmark.himyarStartDate,
      himyarEndDate: landmark.himyarEndDate,
    );
  }

  /// تحويل قائمة من نماذج المعالم الزراعية القديمة إلى قائمة من النماذج الجديدة
  static List<LandmarkEntity> fromAgriculturalLandmarkList(
      List<AgriculturalLandmark> landmarks) {
    return landmarks
        .map((landmark) => fromAgriculturalLandmark(landmark))
        .toList();
  }
}
