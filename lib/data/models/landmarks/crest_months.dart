class CrestMonths {
  final int id;
  final String name;
  final int days;

  CrestMonths({
    required this.id,
    required this.name,
    required this.days,
  });

  // Factory constructor to create AgriculturalLandmark from Map (JSON)
  factory CrestMonths.fromJson(Map<String, dynamic> map) {
    return CrestMonths(
      id: map['id'] as int,
      name: map['name'] as String,
      days: map['days'],
    );
  }

  // Convert AgriculturalLandmark to Map (JSON)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'days': days,
    };
  }

  // Create a copy of AgriculturalLandmark with optional new values
  CrestMonths copyWith({
    int? id,
    String? name,
    int? days,
  }) {
    return CrestMonths(
      id: id ?? this.id,
      name: name ?? this.name,
      days: days ?? this.days,
    );
  }

  @override
  String toString() {
    return 'CrestMonths(id: $id, name: $name)';
  }
}
