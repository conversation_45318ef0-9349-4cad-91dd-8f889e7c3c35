class AgriculturalLandmark {
  final int id;
  final String name;
  final String image;
  final String details;
  final String? operations;
  final DateTime gregorianStartDate;
  final DateTime himyarStartDate;
  final DateTime crestStartDate;
  final DateTime gregorianEndDate;
  final DateTime himyarEndDate;
  final DateTime crestEndDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AgriculturalLandmark({
    required this.id,
    required this.name,
    required this.image,
    required this.details,
    this.operations,
    required this.gregorianStartDate,
    required this.himyarStartDate,
    required this.crestStartDate,
    required this.gregorianEndDate,
    required this.himyarEndDate,
    required this.crestEndDate,
    this.createdAt,
    this.updatedAt,
  });

  // Factory constructor to create AgriculturalLandmark from Map (JSON)
  factory AgriculturalLandmark.fromJson(Map<String, dynamic> map) {
    return AgriculturalLandmark(
      id: map['id'] as int,
      name: map['name'] as String,
      image: map['image'] as String,
      details: map['details'] as String,
      operations: map['operations'] as String?,
      gregorianStartDate: DateTime.parse(map['gregorian_start_date']),
      himyarStartDate: DateTime.parse(map['himyar_start_date']),
      crestStartDate: DateTime.parse(map['crest_start_date']),
      gregorianEndDate: DateTime.parse(map['gregorian_end_date']),
      himyarEndDate: DateTime.parse(map['himyar_end_date']),
      crestEndDate: DateTime.parse(map['crest_end_date']),
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // Convert AgriculturalLandmark to Map (JSON)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'details': details,
      'operations': operations,
      'gregorian_start_date': gregorianStartDate.toIso8601String(),
      'himyar_start_date': himyarStartDate.toIso8601String(),
      'crest_start_date': crestStartDate.toIso8601String(),
      'gregorian_end_date': gregorianEndDate.toIso8601String(),
      'himyar_end_date': himyarEndDate.toIso8601String(),
      'crest_end_date': crestEndDate.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a copy of AgriculturalLandmark with optional new values
  AgriculturalLandmark copyWith({
    int? id,
    String? name,
    String? image,
    String? details,
    String? operations,
    DateTime? gregorianStartDate,
    DateTime? himyarStartDate,
    DateTime? crestStartDate,
    DateTime? gregorianEndDate,
    DateTime? himyarEndDate,
    DateTime? crestEndDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AgriculturalLandmark(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      details: details ?? this.details,
      operations: operations ?? this.operations,
      gregorianStartDate: gregorianStartDate ?? this.gregorianStartDate,
      himyarStartDate: himyarStartDate ?? this.himyarStartDate,
      crestStartDate: crestStartDate ?? this.crestStartDate,
      gregorianEndDate: gregorianEndDate ?? this.gregorianEndDate,
      himyarEndDate: himyarEndDate ?? this.himyarEndDate,
      crestEndDate: crestEndDate ?? this.crestEndDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AgriculturalLandmark(id: $id, name: $name)';
  }
}
