import 'package:agriculture/core/utils/type_conversion_helper.dart';

class HourlyModel {
  String? cod;
  dynamic message;
  dynamic cnt;
  List<Listweather>? listweather;
  Cityy? cityy;

  HourlyModel({this.cod, this.message, this.cnt, this.listweather, this.cityy});

  HourlyModel.fromJson(Map<String, dynamic> json) {
    cod = json['cod'];
    message = json['message'];
    cnt = json['cnt'];
    if (json['list'] != null) {
      listweather = <Listweather>[];
      json['list'].forEach((v) {
        listweather!.add(Listweather.fromJson(v));
      });
    }
    cityy = json['city'] != null ? Cityy.fromJson(json['city']) : null;
  }
}

class Listweather {
  dynamic dt;
  Main? main;
  List<Weathr>? weathr;
  Clouds? clouds;
  Wind? wind;
  dynamic visibility;
  dynamic pop;
  Sys? sys;
  String? dtTxt;

  Listweather(
      {this.dt,
      this.main,
      this.weathr,
      this.clouds,
      this.wind,
      this.visibility,
      this.pop,
      this.sys,
      this.dtTxt});

  Listweather.fromJson(Map<String, dynamic> json) {
    dt = json['dt'];
    main = json['main'] != null ? Main.fromJson(json['main']) : null;
    if (json['weather'] != null) {
      weathr = <Weathr>[];
      json['weather'].forEach((v) {
        weathr!.add(Weathr.fromJson(v));
      });
    }
    clouds =
        json['clouds'] != null ? Clouds.fromJson(json['clouds']) : null;
    wind = json['wind'] != null ? Wind.fromJson(json['wind']) : null;
    visibility = json['visibility'];
    // استخراج احتمالية هطول الأمطار من الاستجابة
    pop = TypeConversionHelper.toDouble(json['pop']) ?? 0.0;
    sys = json['sys'] != null ? Sys.fromJson(json['sys']) : null;
    dtTxt = json['dt_txt'];
  }
}

class Main {
  dynamic temp;
  dynamic feelsLike;
  dynamic tempMin;
  dynamic tempMax;
  dynamic pressure;
  dynamic seaLevel;
  dynamic grndLevel;
  dynamic humidity;
  dynamic tempKf;

  Main(
      {this.temp,
      this.feelsLike,
      this.tempMin,
      this.tempMax,
      this.pressure,
      this.seaLevel,
      this.grndLevel,
      this.humidity,
      this.tempKf});

  Main.fromJson(Map<String, dynamic> json) {
    temp = json['temp'];
    feelsLike = json['feels_like'];
    tempMin = json['temp_min'];
    tempMax = json['temp_max'];
    pressure = json['pressure'];
    seaLevel = json['sea_level'];
    grndLevel = json['grnd_level'];
    humidity = json['humidity'];
    tempKf = json['temp_kf'];
  }
}

class Weathr {
  dynamic id;
  String? main;
  String? description;
  String? icon;

  Weathr({this.id, this.main, this.description, this.icon});

  Weathr.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    main = json['main'];
    description = json['description'];
    icon = json['icon'];
  }
}

class Clouds {
  dynamic all;

  Clouds({this.all});

  Clouds.fromJson(Map<String, dynamic> json) {
    all = json['all'];
  }
}

class Wind {
  dynamic speed;
  dynamic deg;
  dynamic gust;

  Wind({this.speed, this.deg, this.gust});

  Wind.fromJson(Map<String, dynamic> json) {
    speed = TypeConversionHelper.toDouble(json['speed']);
    deg = TypeConversionHelper.toInt(json['deg']);
    gust = TypeConversionHelper.toDouble(json['gust']);
  }
}

class Sys {
  String? pod;

  Sys({this.pod});

  Sys.fromJson(Map<String, dynamic> json) {
    pod = json['pod'];
  }
}

class Cityy {
  dynamic id;
  String? name;
  Coord? coord;
  String? country;
  dynamic population;
  dynamic timezone;
  dynamic sunrise;
  dynamic sunset;

  Cityy(
      {this.id,
      this.name,
      this.coord,
      this.country,
      this.population,
      this.timezone,
      this.sunrise,
      this.sunset});

  Cityy.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    coord = json['coord'] != null ? Coord.fromJson(json['coord']) : null;
    country = json['country'];
    population = json['population'];
    timezone = json['timezone'];
    sunrise = json['sunrise'];
    sunset = json['sunset'];
  }
}

class Coord {
  dynamic lat;
  dynamic lon;

  Coord({this.lat, this.lon});

  Coord.fromJson(Map<String, dynamic> json) {
    lat = json['lat'];
    lon = json['lon'];
  }
}
