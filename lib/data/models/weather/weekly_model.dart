class WeeklyModel {
  dynamic latitude;
  dynamic longitude;
  dynamic generationtimeMs;
  dynamic utcOffsetSeconds;
  String? timezone;
  String? timezoneAbbreviation;
  dynamic elevation;
  CurrentUnits? currentUnits;
  Current? current;
  DailyUnits? dailyUnits;
  Daily? daily;

  WeeklyModel(
      {this.latitude,
      this.longitude,
      this.generationtimeMs,
      this.utcOffsetSeconds,
      this.timezone,
      this.timezoneAbbreviation,
      this.elevation,
      this.currentUnits,
      this.current,
      this.dailyUnits,
      this.daily});

  WeeklyModel.fromJson(Map<String, dynamic> json) {
    latitude = json['latitude'];
    longitude = json['longitude'];
    generationtimeMs = json['generationtime_ms'];
    utcOffsetSeconds = json['utc_offset_seconds'];
    timezone = json['timezone'];
    timezoneAbbreviation = json['timezone_abbreviation'];
    elevation = json['elevation'];
    currentUnits = json['current_units'] != null
        ? CurrentUnits.fromJson(json['current_units'])
        : null;
    current =
        json['current'] != null ? Current.fromJson(json['current']) : null;
    dailyUnits = json['daily_units'] != null
        ? DailyUnits.fromJson(json['daily_units'])
        : null;
    daily = json['daily'] != null ? Daily.fromJson(json['daily']) : null;
  }
}

class CurrentUnits {
  String? time;
  String? interval;

  CurrentUnits({this.time, this.interval});

  CurrentUnits.fromJson(Map<String, dynamic> json) {
    time = json['time'];
    interval = json['interval'];
  }
}

class Current {
  String? time;
  dynamic interval;

  Current({this.time, this.interval});

  Current.fromJson(Map<String, dynamic> json) {
    time = json['time'];
    interval = json['interval'];
  }
}

class DailyUnits {
  String? time;
  String? weatherCode;
  String? temperature2mMax;
  String? temperature2mMin;

  DailyUnits(
      {this.time,
      this.weatherCode,
      this.temperature2mMax,
      this.temperature2mMin});

  DailyUnits.fromJson(Map<String, dynamic> json) {
    time = json['time'];
    weatherCode = json['weather_code'];
    temperature2mMax = json['temperature_2m_max'];
    temperature2mMin = json['temperature_2m_min'];
  }
}

class Daily {
  List<String>? time;
  List<int>? weatherCode;
  List<double>? temperature2mMax;
  List<double>? temperature2mMin;

  Daily(
      {this.time,
      this.weatherCode,
      this.temperature2mMax,
      this.temperature2mMin});

  Daily.fromJson(Map<String, dynamic> json) {
    time = json['time'].cast<String>();
    weatherCode = json['weather_code'].cast<int>();
    temperature2mMax = json['temperature_2m_max'].cast<double>();
    temperature2mMin = json['temperature_2m_min'].cast<double>();
  }
}
