import 'package:agriculture/core/utils/type_conversion_helper.dart';

class WeatherModel {
  Coord? coord;
  List<Weather>? weather;
  String? base;
  Main? main;
  dynamic visibility;
  Wind? wind;
  Clouds? clouds;
  dynamic dt;
  Sys? sys;
  dynamic timezone;
  dynamic id;
  String? name;
  dynamic cod;

  /// الحصول على الوصف الرئيسي للطقس من رمز الطقس
  static String _getWeatherMainFromCode(dynamic code) {
    if (code == null) return 'صافي';

    final weatherCode = code is int ? code : int.tryParse(code.toString()) ?? 0;

    if (weatherCode < 300) return 'عاصف';
    if (weatherCode < 500) return 'رذاذ';
    if (weatherCode < 600) return 'مطر';
    if (weatherCode < 700) return 'ثلج';
    if (weatherCode < 800) return 'ضباب';
    if (weatherCode == 800) return 'صافي';
    if (weatherCode < 900) return 'غائم';

    return 'صافي';
  }

  /// الحصول على وصف الطقس من رمز الطقس
  static String _getWeatherDescriptionFromCode(dynamic code) {
    if (code == null) return 'سماء صافية';

    final weatherCode = code is int ? code : int.tryParse(code.toString()) ?? 0;

    if (weatherCode < 300) return 'عاصفة رعدية';
    if (weatherCode < 500) return 'رذاذ مطر';
    if (weatherCode < 600) return 'مطر';
    if (weatherCode < 700) return 'ثلج';
    if (weatherCode < 800) return 'ضباب';
    if (weatherCode == 800) return 'سماء صافية';
    if (weatherCode < 900) return 'غائم جزئياً';

    return 'سماء صافية';
  }

  /// الحصول على رمز الطقس من رمز الطقس
  static String _getWeatherIconFromCode(dynamic code, bool isDay) {
    if (code == null) return isDay ? '01d' : '01n';

    final weatherCode = code is int ? code : int.tryParse(code.toString()) ?? 0;
    final daySuffix = isDay ? 'd' : 'n';

    if (weatherCode < 300) return '11$daySuffix'; // عاصفة رعدية
    if (weatherCode < 500) return '09$daySuffix'; // رذاذ
    if (weatherCode < 600) return '10$daySuffix'; // مطر
    if (weatherCode < 700) return '13$daySuffix'; // ثلج
    if (weatherCode < 800) return '50$daySuffix'; // ضباب
    if (weatherCode == 800) return '01$daySuffix'; // صافي
    if (weatherCode == 801) return '02$daySuffix'; // غائم جزئياً
    if (weatherCode == 802) return '03$daySuffix'; // غائم متفرق
    if (weatherCode > 802) return '04$daySuffix'; // غائم

    return isDay ? '01d' : '01n';
  }

  WeatherModel(
      {this.coord,
      this.weather,
      this.base,
      this.main,
      this.visibility,
      this.wind,
      this.clouds,
      this.dt,
      this.sys,
      this.timezone,
      this.id,
      this.name,
      this.cod});

  WeatherModel.fromJson(Map<String, dynamic> json) {
    // التعامل مع بيانات OpenWeatherMap
    if (json.containsKey('coord')) {
      coord = json['coord'] != null ? Coord.fromJson(json['coord']) : null;
    }

    // التعامل مع بيانات الطقس
    if (json['weather'] != null) {
      weather = <Weather>[];
      json['weather'].forEach((v) {
        weather!.add(Weather.fromJson(v));
      });
    } else if (json['current'] != null && json['current']['weather_code'] != null) {
      // إذا كانت البيانات من Open-Meteo
      weather = <Weather>[];
      weather!.add(Weather(
        id: json['current']['weather_code'],
        main: _getWeatherMainFromCode(json['current']['weather_code']),
        description: _getWeatherDescriptionFromCode(json['current']['weather_code']),
        icon: _getWeatherIconFromCode(json['current']['weather_code'], json['current']['is_day'] == 1),
      ));
    }

    base = json['base'];

    // التعامل مع بيانات الطقس الرئيسية
    if (json['main'] != null) {
      main = Main.fromJson(json['main']);
    } else if (json['current'] != null) {
      // إذا كانت البيانات من Open-Meteo
      main = Main(
        temp: json['current']['temperature_2m'],
        feelsLike: json['current']['apparent_temperature'],
        humidity: json['current']['relative_humidity_2m'],
        pressure: json['current']['pressure_msl'],
      );
    }

    visibility = json['visibility'];

    // التعامل مع بيانات الرياح
    if (json['wind'] != null) {
      wind = Wind.fromJson(json['wind']);
    } else if (json['current'] != null) {
      // إذا كانت البيانات من Open-Meteo
      wind = Wind(
        speed: json['current']['wind_speed_10m'],
        deg: json['current']['wind_direction_10m'],
        gust: json['current']['wind_gusts_10m'],
      );
    }

    clouds = json['clouds'] != null ? Clouds.fromJson(json['clouds']) :
             (json['current'] != null && json['current']['cloud_cover'] != null ?
              Clouds(all: json['current']['cloud_cover']) : null);

    dt = json['dt'];

    // التعامل مع بيانات النظام
    sys = json['sys'] != null ? Sys.fromJson(json['sys']) : null;

    timezone = json['timezone'];
    id = json['id'];
    name = json['name'] ?? 'موقعك الحالي'; // إذا لم يكن هناك اسم للموقع
    cod = json['cod'];
  }
}

class Coord {
  dynamic lon;
  dynamic lat;

  Coord({this.lon, this.lat});

  Coord.fromJson(Map<String, dynamic> json) {
    lon = json['lon'];
    lat = json['lat'];
  }
}

class Weather {
  dynamic id;
  String? main;
  String? description;
  String? icon;

  Weather({this.id, this.main, this.description, this.icon});

  Weather.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    main = json['main'];
    description = json['description'];
    icon = json['icon'];
  }
}

class Main {
  dynamic temp;
  dynamic feelsLike;
  dynamic tempMin;
  dynamic tempMax;
  dynamic pressure;
  dynamic humidity;
  dynamic seaLevel;
  dynamic grndLevel;

  Main(
      {this.temp,
      this.feelsLike,
      this.tempMin,
      this.tempMax,
      this.pressure,
      this.humidity,
      this.seaLevel,
      this.grndLevel});

  Main.fromJson(Map<String, dynamic> json) {
    temp = TypeConversionHelper.toDouble(json['temp']);
    feelsLike = TypeConversionHelper.toDouble(json['feels_like']);
    tempMin = TypeConversionHelper.toDouble(json['temp_min']);
    tempMax = TypeConversionHelper.toDouble(json['temp_max']);
    pressure = TypeConversionHelper.toDouble(json['pressure']);
    humidity = TypeConversionHelper.toInt(json['humidity']);
    seaLevel = TypeConversionHelper.toDouble(json['sea_level']);
    grndLevel = TypeConversionHelper.toDouble(json['grnd_level']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['temp'] = temp;
    data['feels_like'] = feelsLike;
    data['temp_min'] = tempMin;
    data['temp_max'] = tempMax;
    data['pressure'] = pressure;
    data['humidity'] = humidity;
    data['sea_level'] = seaLevel;
    data['grnd_level'] = grndLevel;
    return data;
  }
}

class Wind {
  dynamic speed;
  dynamic deg;
  dynamic gust;

  Wind({this.speed, this.deg, this.gust});

  Wind.fromJson(Map<String, dynamic> json) {
    speed = TypeConversionHelper.toDouble(json['speed']);
    deg = TypeConversionHelper.toInt(json['deg']);
    gust = TypeConversionHelper.toDouble(json['gust']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['speed'] = speed;
    data['deg'] = deg;
    data['gust'] = gust;
    return data;
  }
}

class Clouds {
  dynamic all;

  Clouds({this.all});

  Clouds.fromJson(Map<String, dynamic> json) {
    all = json['all'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['all'] = all;
    return data;
  }
}

class Sys {
  String? country;
  dynamic sunrise;
  dynamic sunset;

  Sys({this.country, this.sunrise, this.sunset});

  Sys.fromJson(Map<String, dynamic> json) {
    country = json['country'];
    sunrise = TypeConversionHelper.toInt(json['sunrise']);
    sunset = TypeConversionHelper.toInt(json['sunset']);

    // التأكد من أن بيانات الشروق والغروب موجودة وصالحة
    if (sunrise == null && json['sys'] != null && json['sys']['sunrise'] != null) {
      sunrise = TypeConversionHelper.toInt(json['sys']['sunrise']);
    }

    if (sunset == null && json['sys'] != null && json['sys']['sunset'] != null) {
      sunset = TypeConversionHelper.toInt(json['sys']['sunset']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['country'] = country;
    data['sunrise'] = sunrise;
    data['sunset'] = sunset;
    return data;
  }
}
