import 'dart:io';
import 'package:agriculture/core/utils/logging/logging.dart';
import 'package:firebase_storage/firebase_storage.dart';

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // رفع صورة إلى Firebase Storage
  Future<String?> uploadImage(File imageFile, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask.whenComplete(() => null);
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      printError('Error uploading image: $e');
      return null;
    }
  }

  // حذف صورة من Firebase Storage
  Future<bool> deleteImage(String path) async {
    try {
      await _storage.ref().child(path).delete();
      return true;
    } catch (e) {
      printError('Error deleting image: $e');
      return false;
    }
  }
}

// مصنع خدمة التخزين
class StorageServiceFactory {
  static StorageService create() {
    return StorageService();
  }
}
