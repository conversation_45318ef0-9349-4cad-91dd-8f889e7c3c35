import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:path/path.dart' as path;

import '../../../../core/utils/logging/logger_service.dart';

/// خدمة Google Drive
///
/// توفر هذه الخدمة وظائف للتفاعل مع Google Drive API لرفع وحذف الملفات.
class GoogleDriveService {
  /// معرف مجلد الصور
  String? _imagesFolderId;

  /// معرف مجلد الفيديوهات
  String? _videosFolderId;

  /// عميل Google Drive API
  drive.DriveApi? _driveApi;

  /// مسار ملف اعتماد حساب الخدمة
  static const String _serviceAccountPath =
      'assets/credentials/service_account.json';

  /// نطاقات الوصول المطلوبة
  static const List<String> _scopes = [drive.DriveApi.driveFileScope];

  /// إنشاء عميل Google Drive API
  Future<drive.DriveApi> _getDriveApi() async {
    if (_driveApi != null) {
      return _driveApi!;
    }

    try {
      // قراءة ملف اعتماد حساب الخدمة
      final serviceAccountJson =
          await rootBundle.loadString(_serviceAccountPath);
      final serviceAccountCredentials = ServiceAccountCredentials.fromJson(
        json.decode(serviceAccountJson),
      );

      // إنشاء عميل HTTP مصادق
      final client = await clientViaServiceAccount(
        serviceAccountCredentials,
        _scopes,
      );

      // إنشاء عميل Google Drive API
      _driveApi = drive.DriveApi(client);

      LoggerService.info('تم إنشاء عميل Google Drive API بنجاح',
          tag: 'GoogleDriveService');

      return _driveApi!;
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء عميل Google Drive API',
        error: e,
        tag: 'GoogleDriveService',
      );
      rethrow;
    }
  }

  /// الحصول على معرف مجلد الصور
  ///
  /// يبحث عن مجلد "image" وينشئه إذا لم يكن موجودًا
  Future<String> _getImagesFolderId() async {
    if (_imagesFolderId != null) {
      return _imagesFolderId!;
    }

    try {
      final driveApi = await _getDriveApi();

      // البحث عن مجلد "image"
      final fileList = await driveApi.files.list(
        q: "name='image' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        $fields: 'files(id, name)',
      );

      // إذا وجد المجلد، استخدم معرفه
      if (fileList.files != null && fileList.files!.isNotEmpty) {
        _imagesFolderId = fileList.files!.first.id;
        LoggerService.info(
          'تم العثور على مجلد الصور: $_imagesFolderId',
          tag: 'GoogleDriveService',
        );
      } else {
        // إنشاء مجلد جديد
        final folder = drive.File()
          ..name = 'image'
          ..mimeType = 'application/vnd.google-apps.folder';

        final createdFolder = await driveApi.files.create(folder);
        _imagesFolderId = createdFolder.id;

        // تعيين إذن الوصول العام للقراءة
        await _setPublicPermission(_imagesFolderId!);

        LoggerService.info(
          'تم إنشاء مجلد الصور: $_imagesFolderId',
          tag: 'GoogleDriveService',
        );
      }

      return _imagesFolderId!;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على معرف مجلد الصور',
        error: e,
        tag: 'GoogleDriveService',
      );
      rethrow;
    }
  }

  /// الحصول على معرف مجلد الفيديوهات
  ///
  /// يبحث عن مجلد "video" وينشئه إذا لم يكن موجودًا
  Future<String> _getVideosFolderId() async {
    if (_videosFolderId != null) {
      return _videosFolderId!;
    }

    try {
      final driveApi = await _getDriveApi();

      // البحث عن مجلد "video"
      final fileList = await driveApi.files.list(
        q: "name='video' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        $fields: 'files(id, name)',
      );

      // إذا وجد المجلد، استخدم معرفه
      if (fileList.files != null && fileList.files!.isNotEmpty) {
        _videosFolderId = fileList.files!.first.id;
        LoggerService.info(
          'تم العثور على مجلد الفيديوهات: $_videosFolderId',
          tag: 'GoogleDriveService',
        );
      } else {
        // إنشاء مجلد جديد
        final folder = drive.File()
          ..name = 'video'
          ..mimeType = 'application/vnd.google-apps.folder';

        final createdFolder = await driveApi.files.create(folder);
        _videosFolderId = createdFolder.id;

        // تعيين إذن الوصول العام للقراءة
        await _setPublicPermission(_videosFolderId!);

        LoggerService.info(
          'تم إنشاء مجلد الفيديوهات: $_videosFolderId',
          tag: 'GoogleDriveService',
        );
      }

      return _videosFolderId!;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على معرف مجلد الفيديوهات',
        error: e,
        tag: 'GoogleDriveService',
      );
      rethrow;
    }
  }

  /// تعيين إذن الوصول العام للقراءة
  ///
  /// المعلمات:
  /// - [fileId]: معرف الملف أو المجلد
  Future<void> _setPublicPermission(String fileId) async {
    try {
      final driveApi = await _getDriveApi();

      // إنشاء إذن وصول عام للقراءة
      final permission = drive.Permission()
        ..type = 'anyone'
        ..role = 'reader';

      await driveApi.permissions.create(permission, fileId);

      LoggerService.info(
        'تم تعيين إذن الوصول العام للقراءة للملف: $fileId',
        tag: 'GoogleDriveService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تعيين إذن الوصول العام للقراءة',
        error: e,
        tag: 'GoogleDriveService',
      );
    }
  }

  /// رفع ملف إلى Google Drive
  ///
  /// المعلمات:
  /// - [file]: الملف المراد رفعه
  /// - [path]: المسار الذي سيتم تخزين الملف فيه (يستخدم فقط لاستخراج اسم الملف)
  ///
  /// يعيد رابط الملف المرفوع أو null في حالة الفشل
  Future<String?> uploadFile(File file, String filePath) async {
    try {
      // التحقق من وجود الملف
      if (!await file.exists()) {
        LoggerService.error(
          'الملف غير موجود: ${file.path}',
          tag: 'GoogleDriveService',
        );
        return null;
      }

      // التحقق من حجم الملف (الحد الأقصى 10 ميجابايت)
      final fileSize = await file.length();
      final maxSize = 10 * 1024 * 1024; // 10 ميجابايت

      if (fileSize > maxSize) {
        LoggerService.error(
          'حجم الملف كبير جدًا: ${fileSize ~/ 1024} كيلوبايت (الحد الأقصى: ${maxSize ~/ 1024} كيلوبايت)',
          tag: 'GoogleDriveService',
        );
        return null;
      }

      // الحصول على عميل Google Drive API
      final driveApi = await _getDriveApi();

      // استخراج اسم الملف من المسار وإضافة طابع زمني لتجنب تكرار الأسماء
      final fileNameBase = path.basename(filePath);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName =
          '${path.basenameWithoutExtension(fileNameBase)}_$timestamp${path.extension(fileNameBase)}';

      // تحديد نوع الملف ومجلد التخزين المناسب
      String mimeType;
      String folderId;
      final extension = path.extension(fileName).toLowerCase();

      // تحديد نوع الملف ومجلد التخزين المناسب
      if (extension == '.mp4' || extension == '.mov' || extension == '.avi' ||
          extension == '.wmv' || extension == '.flv' || extension == '.webm') {
        // ملف فيديو
        mimeType = 'video/${extension.substring(1)}';
        folderId = await _getVideosFolderId();
        LoggerService.info(
          'تم تحديد الملف كفيديو، سيتم تخزينه في مجلد الفيديوهات',
          tag: 'GoogleDriveService',
        );
      } else {
        // ملف صورة أو ملف آخر (نعتبره صورة افتراضيًا)
        switch (extension) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          default:
            mimeType = 'image/jpeg'; // افتراضي للصور غير المعروفة
        }
        folderId = await _getImagesFolderId();
        LoggerService.info(
          'تم تحديد الملف كصورة، سيتم تخزينه في مجلد الصور',
          tag: 'GoogleDriveService',
        );
      }

      LoggerService.info(
        'بدء رفع الملف: $fileName (${fileSize ~/ 1024} كيلوبايت)',
        tag: 'GoogleDriveService',
      );

      // إنشاء ملف في Google Drive
      final driveFile = drive.File()
        ..name = fileName
        ..parents = [folderId]
        ..mimeType = mimeType;

      // قراءة محتوى الملف
      final fileContent = await file.readAsBytes();

      // رفع الملف
      final uploadedFile = await driveApi.files.create(
        driveFile,
        uploadMedia: drive.Media(
          Stream.value(fileContent),
          fileContent.length,
          contentType: mimeType,
        ),
      );

      // التحقق من نجاح الرفع
      if (uploadedFile.id == null) {
        LoggerService.error(
          'فشل رفع الملف: لم يتم الحصول على معرف الملف',
          tag: 'GoogleDriveService',
        );
        return null;
      }

      // تعيين إذن الوصول العام للقراءة
      await _setPublicPermission(uploadedFile.id!);

      // إنشاء رابط مباشر للملف
      final fileId = uploadedFile.id!;
      final directLink = 'https://drive.google.com/uc?export=view&id=$fileId';

      LoggerService.info(
        'تم رفع الملف بنجاح: $fileName، الرابط: $directLink',
        tag: 'GoogleDriveService',
      );

      return directLink;
    } catch (e) {
      LoggerService.error(
        'خطأ في رفع الملف',
        error: e,
        tag: 'GoogleDriveService',
      );
      return null;
    }
  }

  /// حذف ملف من Google Drive
  ///
  /// المعلمات:
  /// - [fileUrl]: رابط الملف المراد حذفه
  ///
  /// يعيد true في حالة النجاح و false في حالة الفشل
  Future<bool> deleteFile(String fileUrl) async {
    try {
      // استخراج معرف الملف من الرابط
      final uri = Uri.parse(fileUrl);
      final fileId = uri.queryParameters['id'];

      if (fileId == null) {
        LoggerService.error(
          'لم يتم العثور على معرف الملف في الرابط: $fileUrl',
          tag: 'GoogleDriveService',
        );
        return false;
      }

      final driveApi = await _getDriveApi();

      // حذف الملف
      await driveApi.files.delete(fileId);

      LoggerService.info(
        'تم حذف الملف بنجاح: $fileId',
        tag: 'GoogleDriveService',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف الملف',
        error: e,
        tag: 'GoogleDriveService',
      );
      return false;
    }
  }
}

/// مصنع خدمة Google Drive
///
/// يوفر هذا المصنع طريقة لإنشاء نسخة من خدمة Google Drive.
class GoogleDriveServiceFactory {
  /// إنشاء نسخة من خدمة Google Drive
  static GoogleDriveService create() {
    return GoogleDriveService();
  }
}
