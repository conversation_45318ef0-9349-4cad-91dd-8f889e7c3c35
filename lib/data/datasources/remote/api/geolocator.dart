import 'package:agriculture/core/errors/location_exception.dart';
import 'package:agriculture/core/utils/logging/logging.dart';
import 'package:geolocator/geolocator.dart';

Future<Position> getLocation() async {
  try {
    LocationPermission permission;

    permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw LocationException('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw LocationException(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    return await Geolocator.getCurrentPosition();
  } catch (e) {
    printError('Error getting location: $e');
    rethrow;
  }
}
