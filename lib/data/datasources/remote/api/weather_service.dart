import 'dart:io';

import 'package:agriculture/core/constants/api_constants.dart';
import 'package:agriculture/core/errors/api_exception.dart';
import 'package:agriculture/core/errors/location_exception.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/core/utils/services/cache_service.dart';
import 'package:agriculture/data/models/weather/hourly_weather_model.dart';
import 'package:agriculture/data/models/weather/weather_model.dart';
import 'package:agriculture/data/models/weather/weekly_model.dart';
import 'package:dio/dio.dart';

import 'geolocator.dart';

/// خدمة الطقس
///
/// توفر هذه الخدمة وظائف للحصول على بيانات الطقس من مصادر مختلفة
/// وتخزينها مؤقتًا لتحسين الأداء وتقليل استهلاك البيانات.
class WeatherService {
  final Dio _dio;

  /// إنشاء خدمة الطقس
  ///
  /// المعلمات:
  /// - [dio]: عميل Dio للاتصال بواجهات برمجة التطبيقات
  WeatherService({required Dio dio}) : _dio = dio;

  /// التحقق من وجود اتصال بالإنترنت
  ///
  /// يقوم بالتحقق من وجود اتصال بالإنترنت عن طريق محاولة الاتصال بخادم DNS
  Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } catch (e) {
      LoggerService.error(
        'خطأ في التحقق من الاتصال بالإنترنت',
        error: e,
        tag: 'WeatherService',
      );
      return false;
    }
  }

  /// تحديث بيانات الطقس في الخلفية
  ///
  /// يقوم بتحديث بيانات الطقس في الخلفية دون انتظار الاستجابة
  /// المعلمات:
  /// - [url]: عنوان URL للطلب
  /// - [cacheKey]: مفتاح التخزين المؤقت
  Future<void> _updateDataInBackground(String url, String cacheKey) async {
    try {
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        await CacheService.set<Map<String, dynamic>>(
          cacheKey,
          response.data as Map<String, dynamic>,
          persistToDisk: true,
        );

        LoggerService.debug(
          'تم تحديث البيانات في الخلفية: $cacheKey',
          tag: 'WeatherService',
        );
      }
    } catch (e) {
      // تجاهل الأخطاء في التحديث في الخلفية
      LoggerService.debug(
        'فشل تحديث البيانات في الخلفية: $cacheKey',
        tag: 'WeatherService',
      );
    }
  }

  /// الحصول على بيانات الطقس الحالي
  ///
  /// يقوم بجلب بيانات الطقس الحالي للموقع الحالي
  /// ويستخدم التخزين المؤقت إذا كانت البيانات متوفرة وصالحة
  Future<WeatherModel> getCurrentWeather() async {
    try {
      // الحصول على الموقع الحالي
      final position = await getLocation();
      double lat = position.latitude;
      double lon = position.longitude;

      // مفتاح التخزين المؤقت
      final cacheKey = ApiConstants.currentWeatherCacheKey(lat, lon);

      // عنوان URL للطلب
      final url = ApiConstants.weatherUrl(lat, lon);

      // التحقق من وجود بيانات مخزنة مؤقتًا
      final cachedData = CacheService.get<Map<String, dynamic>>(cacheKey);
      WeatherModel? cachedWeather;

      // محاولة استخدام البيانات المخزنة مؤقتًا
      if (cachedData != null) {
        try {
          cachedWeather = WeatherModel.fromJson(cachedData);
          LoggerService.debug(
            'تم تحويل بيانات الطقس المخزنة مؤقتًا بنجاح',
            tag: 'WeatherService',
          );
        } catch (e) {
          // إذا فشل التحويل، نحذف البيانات المخزنة مؤقتًا
          LoggerService.warning(
            'فشل تحويل بيانات الطقس الحالي المخزنة مؤقتًا: ${e.toString()}',
            tag: 'WeatherService',
          );
          await CacheService.remove(cacheKey);
          cachedWeather = null;
        }
      }

      // التحقق من وجود اتصال بالإنترنت
      final hasConnection = await hasInternetConnection();

      // إذا كان لدينا بيانات مخزنة مؤقتًا
      if (cachedWeather != null) {
        if (!hasConnection) {
          // إذا لم يكن هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا
          LoggerService.info(
            'استخدام بيانات الطقس المخزنة مؤقتًا (لا يوجد اتصال بالإنترنت)',
            tag: 'WeatherService',
          );
        } else {
          // إذا كان هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا وحدثها في الخلفية
          LoggerService.debug(
            'استخدام بيانات الطقس المخزنة مؤقتًا مع تحديثها في الخلفية',
            tag: 'WeatherService',
          );
          // تحديث البيانات في الخلفية
          _updateDataInBackground(url, cacheKey);
        }
        // إرجاع البيانات المخزنة مؤقتًا في جميع الحالات
        return cachedWeather;
      }

      // إذا لم يكن لدينا بيانات مخزنة مؤقتًا ولا يوجد اتصال بالإنترنت
      if (!hasConnection) {
        LoggerService.warning(
          'لا يوجد اتصال بالإنترنت ولا توجد بيانات مخزنة مؤقتًا',
          tag: 'WeatherService',
        );
        throw ApiException(
          message: 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى',
          isConnectionError: true,
        );
      }

      // جلب بيانات جديدة من الخادم
      LoggerService.debug(
        'جلب بيانات الطقس الحالي من الخادم',
        tag: 'WeatherService',
      );
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        // تخزين البيانات مؤقتًا
        await CacheService.set<Map<String, dynamic>>(
          cacheKey,
          response.data as Map<String, dynamic>,
          persistToDisk: true,
        );

        LoggerService.debug(
          'تم جلب بيانات الطقس الحالي من الخادم وتخزينها مؤقتًا',
          tag: 'WeatherService',
        );

        return WeatherModel.fromJson(response.data);
      } else {
        throw ApiException(
          message: 'فشل جلب بيانات الطقس',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is LocationException) {
        LoggerService.error(
          'خطأ في الحصول على الموقع',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is ApiException) {
        LoggerService.error(
          'خطأ في API الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is DioException) {
        // معالجة أخطاء Dio بشكل محدد
        String errorMessage = 'خطأ في الاتصال بخدمة الطقس';
        if (e.response?.statusCode != null) {
          errorMessage += ' (رمز الحالة: ${e.response?.statusCode})';
        }
        LoggerService.error(errorMessage, error: e, tag: 'WeatherService');
        throw ApiException(message: errorMessage);
      } else {
        LoggerService.error(
          'خطأ غير متوقع في خدمة الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw ApiException(message: e.toString());
      }
    }
  }

  /// الحصول على توقعات الطقس بالساعة
  ///
  /// يقوم بجلب توقعات الطقس بالساعة للموقع الحالي
  /// ويستخدم التخزين المؤقت إذا كانت البيانات متوفرة وصالحة
  Future<HourlyModel> getHourlyForecast() async {
    try {
      // الحصول على الموقع الحالي
      final position = await getLocation();
      double lat = position.latitude;
      double lon = position.longitude;

      // مفتاح التخزين المؤقت
      final cacheKey = ApiConstants.hourlyForecastCacheKey(lat, lon);

      // عنوان URL للطلب
      final url = ApiConstants.forecastHourlyUrl(lat, lon);

      // التحقق من وجود بيانات مخزنة مؤقتًا
      final cachedData = CacheService.get<Map<String, dynamic>>(cacheKey);
      HourlyModel? cachedHourly;

      // محاولة استخدام البيانات المخزنة مؤقتًا
      if (cachedData != null) {
        try {
          cachedHourly = HourlyModel.fromJson(cachedData);
          LoggerService.debug(
            'تم تحويل توقعات الطقس بالساعة المخزنة مؤقتًا بنجاح',
            tag: 'WeatherService',
          );
        } catch (e) {
          // إذا فشل التحويل، نحذف البيانات المخزنة مؤقتًا
          LoggerService.warning(
            'فشل تحويل توقعات الطقس بالساعة المخزنة مؤقتًا: ${e.toString()}',
            tag: 'WeatherService',
          );
          await CacheService.remove(cacheKey);
          cachedHourly = null;
        }
      }

      // التحقق من وجود اتصال بالإنترنت
      final hasConnection = await hasInternetConnection();

      // إذا كان لدينا بيانات مخزنة مؤقتًا
      if (cachedHourly != null) {
        if (!hasConnection) {
          // إذا لم يكن هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا
          LoggerService.info(
            'استخدام توقعات الطقس بالساعة المخزنة مؤقتًا (لا يوجد اتصال بالإنترنت)',
            tag: 'WeatherService',
          );
        } else {
          // إذا كان هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا وحدثها في الخلفية
          LoggerService.debug(
            'استخدام توقعات الطقس بالساعة المخزنة مؤقتًا مع تحديثها في الخلفية',
            tag: 'WeatherService',
          );
          // تحديث البيانات في الخلفية
          _updateDataInBackground(url, cacheKey);
        }
        // إرجاع البيانات المخزنة مؤقتًا في جميع الحالات
        return cachedHourly;
      }

      // إذا لم يكن لدينا بيانات مخزنة مؤقتًا ولا يوجد اتصال بالإنترنت
      if (!hasConnection) {
        LoggerService.warning(
          'لا يوجد اتصال بالإنترنت ولا توجد بيانات مخزنة مؤقتًا لتوقعات الطقس بالساعة',
          tag: 'WeatherService',
        );
        throw ApiException(
          message: 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى',
          isConnectionError: true,
        );
      }

      // جلب بيانات جديدة من الخادم
      LoggerService.debug(
        'جلب توقعات الطقس بالساعة من الخادم',
        tag: 'WeatherService',
      );
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        // تخزين البيانات مؤقتًا
        await CacheService.set<Map<String, dynamic>>(
          cacheKey,
          response.data as Map<String, dynamic>,
          persistToDisk: true,
        );

        LoggerService.debug(
          'تم جلب توقعات الطقس بالساعة من الخادم وتخزينها مؤقتًا',
          tag: 'WeatherService',
        );

        return HourlyModel.fromJson(response.data);
      } else {
        throw ApiException(
          message: 'فشل جلب توقعات الطقس بالساعة',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is LocationException) {
        LoggerService.error(
          'خطأ في الحصول على الموقع',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is ApiException) {
        LoggerService.error(
          'خطأ في API الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is DioException) {
        // معالجة أخطاء Dio بشكل محدد
        String errorMessage = 'خطأ في الاتصال بخدمة الطقس';
        if (e.response?.statusCode != null) {
          errorMessage += ' (رمز الحالة: ${e.response?.statusCode})';
        }
        LoggerService.error(errorMessage, error: e, tag: 'WeatherService');
        throw ApiException(message: errorMessage);
      } else {
        LoggerService.error(
          'خطأ غير متوقع في خدمة الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw ApiException(message: e.toString());
      }
    }
  }

  /// الحصول على توقعات الطقس الأسبوعية
  ///
  /// يقوم بجلب توقعات الطقس الأسبوعية للموقع الحالي
  /// ويستخدم التخزين المؤقت إذا كانت البيانات متوفرة وصالحة
  Future<WeeklyModel> getWeeklyForecast() async {
    try {
      // الحصول على الموقع الحالي
      final position = await getLocation();
      double lat = position.latitude;
      double lon = position.longitude;

      // مفتاح التخزين المؤقت
      final cacheKey = ApiConstants.weeklyForecastCacheKey(lat, lon);

      // عنوان URL للطلب
      final url = ApiConstants.weeklyForecastUrl(lat, lon);

      // التحقق من وجود بيانات مخزنة مؤقتًا
      final cachedData = CacheService.get<Map<String, dynamic>>(cacheKey);
      WeeklyModel? cachedWeekly;

      // محاولة استخدام البيانات المخزنة مؤقتًا
      if (cachedData != null) {
        try {
          cachedWeekly = WeeklyModel.fromJson(cachedData);
          LoggerService.debug(
            'تم تحويل توقعات الطقس الأسبوعية المخزنة مؤقتًا بنجاح',
            tag: 'WeatherService',
          );
        } catch (e) {
          // إذا فشل التحويل، نحذف البيانات المخزنة مؤقتًا
          LoggerService.warning(
            'فشل تحويل توقعات الطقس الأسبوعية المخزنة مؤقتًا: ${e.toString()}',
            tag: 'WeatherService',
          );
          await CacheService.remove(cacheKey);
          cachedWeekly = null;
        }
      }

      // التحقق من وجود اتصال بالإنترنت
      final hasConnection = await hasInternetConnection();

      // إذا كان لدينا بيانات مخزنة مؤقتًا
      if (cachedWeekly != null) {
        if (!hasConnection) {
          // إذا لم يكن هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا
          LoggerService.info(
            'استخدام توقعات الطقس الأسبوعية المخزنة مؤقتًا (لا يوجد اتصال بالإنترنت)',
            tag: 'WeatherService',
          );
        } else {
          // إذا كان هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا وحدثها في الخلفية
          LoggerService.debug(
            'استخدام توقعات الطقس الأسبوعية المخزنة مؤقتًا مع تحديثها في الخلفية',
            tag: 'WeatherService',
          );
          // تحديث البيانات في الخلفية
          _updateDataInBackground(url, cacheKey);
        }
        // إرجاع البيانات المخزنة مؤقتًا في جميع الحالات
        return cachedWeekly;
      }

      // إذا لم يكن لدينا بيانات مخزنة مؤقتًا ولا يوجد اتصال بالإنترنت
      if (!hasConnection) {
        LoggerService.warning(
          'لا يوجد اتصال بالإنترنت ولا توجد بيانات مخزنة مؤقتًا لتوقعات الطقس الأسبوعية',
          tag: 'WeatherService',
        );
        throw ApiException(
          message: 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى',
          isConnectionError: true,
        );
      }

      // جلب بيانات جديدة من الخادم
      LoggerService.debug(
        'جلب توقعات الطقس الأسبوعية من الخادم',
        tag: 'WeatherService',
      );
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        // تخزين البيانات مؤقتًا
        await CacheService.set<Map<String, dynamic>>(
          cacheKey,
          response.data as Map<String, dynamic>,
          persistToDisk: true,
        );

        LoggerService.debug(
          'تم جلب توقعات الطقس الأسبوعية من الخادم وتخزينها مؤقتًا',
          tag: 'WeatherService',
        );

        return WeeklyModel.fromJson(response.data);
      } else {
        throw ApiException(
          message: 'فشل جلب توقعات الطقس الأسبوعية',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is LocationException) {
        LoggerService.error(
          'خطأ في الحصول على الموقع',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is ApiException) {
        LoggerService.error(
          'خطأ في API الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is DioException) {
        // معالجة أخطاء Dio بشكل محدد
        String errorMessage = 'خطأ في الاتصال بخدمة الطقس';
        if (e.response?.statusCode != null) {
          errorMessage += ' (رمز الحالة: ${e.response?.statusCode})';
        }
        LoggerService.error(errorMessage, error: e, tag: 'WeatherService');
        throw ApiException(message: errorMessage);
      } else {
        LoggerService.error(
          'خطأ غير متوقع في خدمة الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw ApiException(message: e.toString());
      }
    }
  }

  /// الحصول على بيانات الطقس حسب المدينة
  ///
  /// المعلمات:
  /// - [city]: اسم المدينة
  ///
  /// يقوم بجلب بيانات الطقس الحالي للمدينة المحددة
  /// ويستخدم التخزين المؤقت إذا كانت البيانات متوفرة وصالحة
  Future<WeatherModel> getWeatherByCity(String city) async {
    try {
      // مفتاح التخزين المؤقت
      final cacheKey = 'weather_city_${city.toLowerCase()}';

      // عنوان URL للطلب
      final url = ApiConstants.weatherByCityUrl(city);

      // التحقق من وجود بيانات مخزنة مؤقتًا
      final cachedData = CacheService.get<Map<String, dynamic>>(cacheKey);
      WeatherModel? cachedWeather;

      // محاولة استخدام البيانات المخزنة مؤقتًا
      if (cachedData != null) {
        try {
          cachedWeather = WeatherModel.fromJson(cachedData);
          LoggerService.debug(
            'تم تحويل بيانات الطقس للمدينة المخزنة مؤقتًا بنجاح: $city',
            tag: 'WeatherService',
          );
        } catch (e) {
          // إذا فشل التحويل، نحذف البيانات المخزنة مؤقتًا
          LoggerService.warning(
            'فشل تحويل بيانات الطقس للمدينة المخزنة مؤقتًا: ${e.toString()}',
            tag: 'WeatherService',
          );
          await CacheService.remove(cacheKey);
          cachedWeather = null;
        }
      }

      // التحقق من وجود اتصال بالإنترنت
      final hasConnection = await hasInternetConnection();

      // إذا كان لدينا بيانات مخزنة مؤقتًا
      if (cachedWeather != null) {
        if (!hasConnection) {
          // إذا لم يكن هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا
          LoggerService.info(
            'استخدام بيانات الطقس للمدينة المخزنة مؤقتًا (لا يوجد اتصال بالإنترنت): $city',
            tag: 'WeatherService',
          );
        } else {
          // إذا كان هناك اتصال بالإنترنت، استخدم البيانات المخزنة مؤقتًا وحدثها في الخلفية
          LoggerService.debug(
            'استخدام بيانات الطقس للمدينة المخزنة مؤقتًا مع تحديثها في الخلفية: $city',
            tag: 'WeatherService',
          );
          // تحديث البيانات في الخلفية
          _updateDataInBackground(url, cacheKey);
        }
        // إرجاع البيانات المخزنة مؤقتًا في جميع الحالات
        return cachedWeather;
      }

      // إذا لم يكن لدينا بيانات مخزنة مؤقتًا ولا يوجد اتصال بالإنترنت
      if (!hasConnection) {
        LoggerService.warning(
          'لا يوجد اتصال بالإنترنت ولا توجد بيانات مخزنة مؤقتًا لمدينة: $city',
          tag: 'WeatherService',
        );
        throw ApiException(
          message: 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى',
          isConnectionError: true,
        );
      }

      // جلب بيانات جديدة من الخادم
      LoggerService.debug(
        'جلب بيانات الطقس للمدينة من الخادم: $city',
        tag: 'WeatherService',
      );
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        // تخزين البيانات مؤقتًا
        await CacheService.set<Map<String, dynamic>>(
          cacheKey,
          response.data as Map<String, dynamic>,
          persistToDisk: true,
        );

        LoggerService.debug(
          'تم جلب بيانات الطقس للمدينة من الخادم وتخزينها مؤقتًا: $city',
          tag: 'WeatherService',
        );

        return WeatherModel.fromJson(response.data);
      } else {
        throw ApiException(
          message: 'فشل جلب بيانات الطقس للمدينة: $city',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        LoggerService.error(
          'خطأ في API الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw e;
      } else if (e is DioException) {
        // معالجة أخطاء Dio بشكل محدد
        String errorMessage = 'خطأ في الاتصال بخدمة الطقس';
        if (e.response?.statusCode != null) {
          errorMessage += ' (رمز الحالة: ${e.response?.statusCode})';
        }
        LoggerService.error(errorMessage, error: e, tag: 'WeatherService');
        throw ApiException(message: errorMessage);
      } else {
        LoggerService.error(
          'خطأ غير متوقع في خدمة الطقس',
          error: e,
          tag: 'WeatherService',
        );
        throw ApiException(message: e.toString());
      }
    }
  }

  /// تنظيف التخزين المؤقت للطقس
  ///
  /// يقوم بحذف جميع بيانات الطقس المخزنة مؤقتًا
  Future<void> clearWeatherCache() async {
    try {
      // حذف جميع مفاتيح التخزين المؤقت المتعلقة بالطقس
      // استخدام clear لحذف جميع البيانات المخزنة مؤقتًا لأن deleteByPattern غير متوفرة
      await CacheService.clear();

      LoggerService.debug(
        'تم تنظيف التخزين المؤقت للطقس',
        tag: 'WeatherService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تنظيف التخزين المؤقت للطقس',
        error: e,
        tag: 'WeatherService',
      );
      rethrow;
    }
  }
}
