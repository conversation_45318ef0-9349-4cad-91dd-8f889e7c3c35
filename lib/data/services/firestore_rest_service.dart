
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// خدمة Firestore REST API
/// 
/// تستخدم HTTP requests مباشرة بدلاً من Firebase SDK
/// لا تحتاج Google Play Services
class FirestoreRestService {
  static const String _projectId = 'myagriculture-dc833';
  static const String _apiKey = 'AIzaSyALqO5wBcYzIYtafwlBzcr_9EYmgBZfEuI';
  static const String _baseUrl = 'https://firestore.googleapis.com/v1/projects/$_projectId/databases/(default)/documents';
  
  final Dio _dio = Dio();

  /// حفظ مستند في Firestore باستخدام REST API
  Future<bool> setDocument({
    required String collection,
    required String documentId,
    required Map<String, dynamic> data,
  }) async {
    try {
      debugPrint('🔥 بدء حفظ المستند في Firestore REST API');
      debugPrint('Collection: $collection');
      debugPrint('Document ID: $documentId');
      
      // تحويل البيانات إلى تنسيق Firestore
      final firestoreData = _convertToFirestoreFormat(data);
      
      // URL للطلب
      final url = '$_baseUrl/$collection/$documentId?key=$_apiKey';
      
      // إرسال الطلب
      final response = await _dio.patch(
        url,
        data: {
          'fields': firestoreData,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      
      if (response.statusCode == 200) {
        debugPrint('✅ تم حفظ المستند بنجاح في Firestore REST API');
        return true;
      } else {
        debugPrint('❌ فشل حفظ المستند: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ المستند: $e');
      return false;
    }
  }

  /// جلب مستند من Firestore باستخدام REST API
  Future<Map<String, dynamic>?> getDocument({
    required String collection,
    required String documentId,
  }) async {
    try {
      debugPrint('🔍 بدء جلب المستند من Firestore REST API');
      
      final url = '$_baseUrl/$collection/$documentId?key=$_apiKey';
      
      final response = await _dio.get(url);
      
      if (response.statusCode == 200) {
        debugPrint('✅ تم جلب المستند بنجاح');
        final fields = response.data['fields'] as Map<String, dynamic>?;
        if (fields != null) {
          return _convertFromFirestoreFormat(fields);
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المستند: $e');
      return null;
    }
  }

  /// جلب مجموعة من المستندات
  Future<List<Map<String, dynamic>>> getCollection({
    required String collection,
    int? limit,
    String? orderBy,
    bool descending = false,
  }) async {
    try {
      debugPrint('🔍 بدء جلب المجموعة من Firestore REST API');
      
      String url = '$_baseUrl/$collection?key=$_apiKey';
      
      // إضافة معاملات الاستعلام
      if (limit != null) {
        url += '&pageSize=$limit';
      }
      
      final response = await _dio.get(url);
      
      if (response.statusCode == 200) {
        debugPrint('✅ تم جلب المجموعة بنجاح');
        final documents = response.data['documents'] as List<dynamic>? ?? [];
        
        return documents.map((doc) {
          final fields = doc['fields'] as Map<String, dynamic>? ?? {};
          final data = _convertFromFirestoreFormat(fields);
          
          // إضافة معرف المستند
          final name = doc['name'] as String? ?? '';
          final id = name.split('/').last;
          data['id'] = id;
          
          return data;
        }).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في جلب المجموعة: $e');
      return [];
    }
  }

  /// تحويل البيانات إلى تنسيق Firestore
  Map<String, dynamic> _convertToFirestoreFormat(Map<String, dynamic> data) {
    final result = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        result[key] = {'stringValue': value};
      } else if (value is int) {
        result[key] = {'integerValue': value.toString()};
      } else if (value is double) {
        result[key] = {'doubleValue': value};
      } else if (value is bool) {
        result[key] = {'booleanValue': value};
      } else if (value is List) {
        result[key] = {
          'arrayValue': {
            'values': value.map((item) => {
              if (item is String) 'stringValue': item
              else if (item is int) 'integerValue': item.toString()
              else if (item is double) 'doubleValue': item
              else if (item is bool) 'booleanValue': item
              else 'stringValue': item.toString()
            }).toList()
          }
        };
      } else if (value == null) {
        result[key] = {'nullValue': null};
      } else {
        result[key] = {'stringValue': value.toString()};
      }
    }
    
    return result;
  }

  /// تحويل البيانات من تنسيق Firestore
  Map<String, dynamic> _convertFromFirestoreFormat(Map<String, dynamic> fields) {
    final result = <String, dynamic>{};
    
    for (final entry in fields.entries) {
      final key = entry.key;
      final value = entry.value as Map<String, dynamic>;
      
      if (value.containsKey('stringValue')) {
        result[key] = value['stringValue'];
      } else if (value.containsKey('integerValue')) {
        result[key] = int.tryParse(value['integerValue']) ?? 0;
      } else if (value.containsKey('doubleValue')) {
        result[key] = value['doubleValue'];
      } else if (value.containsKey('booleanValue')) {
        result[key] = value['booleanValue'];
      } else if (value.containsKey('arrayValue')) {
        final arrayValue = value['arrayValue'] as Map<String, dynamic>?;
        final values = arrayValue?['values'] as List<dynamic>? ?? [];
        result[key] = values.map((item) {
          final itemMap = item as Map<String, dynamic>;
          if (itemMap.containsKey('stringValue')) {
            return itemMap['stringValue'];
          } else if (itemMap.containsKey('integerValue')) {
            return int.tryParse(itemMap['integerValue']) ?? 0;
          } else if (itemMap.containsKey('doubleValue')) {
            return itemMap['doubleValue'];
          } else if (itemMap.containsKey('booleanValue')) {
            return itemMap['booleanValue'];
          }
          return null;
        }).where((item) => item != null).toList();
      } else if (value.containsKey('nullValue')) {
        result[key] = null;
      }
    }
    
    return result;
  }
}

/// مصنع خدمة Firestore REST
class FirestoreRestServiceFactory {
  static FirestoreRestService create() {
    return FirestoreRestService();
  }
}
