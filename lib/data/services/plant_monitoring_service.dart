import 'dart:io';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:http/http.dart' as http;

import 'package:agriculture/domain/entities/plant_monitoring_request.dart';
import 'package:agriculture/core/constants/api_endpoints.dart';
import 'package:agriculture/core/constants/plant_monitoring_constants.dart';

/// خدمة مراقبة النبات الحقيقية
/// 
/// تتعامل مع جميع العمليات المتعلقة بمراقبة النبات
/// - رفع الصور إلى Firebase Storage
/// - حفظ الطلبات في Firestore
/// - إرسال الطلبات إلى API خارجي
/// - إرسال الإشعارات
class PlantMonitoringService {
  
  /// مثيل Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// مثيل Firebase Auth
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  /// مثيل Firebase Storage
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// رفع الصور إلى Firebase Storage
  /// 
  /// [images] قائمة الصور المراد رفعها
  /// [folder] مجلد التخزين
  /// 
  /// يرجع قائمة بروابط الصور المرفوعة
  Future<List<String>> uploadImages(List<File> images, String folder) async {
    final List<String> imageUrls = [];
    
    try {
      for (int i = 0; i < images.length; i++) {
        final String fileName = '${folder}_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
        final Reference ref = _storage.ref().child('$folder/$fileName');
        
        // إعداد metadata للصورة
        final SettableMetadata metadata = SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'uploaded_by': _auth.currentUser?.uid ?? 'anonymous',
            'upload_time': DateTime.now().toIso8601String(),
            'compressed': 'true',
            'folder': folder,
          },
        );
        
        // رفع الصورة
        final UploadTask uploadTask = ref.putFile(images[i], metadata);
        final TaskSnapshot snapshot = await uploadTask;
        final String downloadUrl = await snapshot.ref.getDownloadURL();
        
        imageUrls.add(downloadUrl);
        
        print('✅ تم رفع الصورة ${i + 1}/${images.length}: $downloadUrl');
      }
      
      print('✅ تم رفع جميع الصور بنجاح: ${imageUrls.length} صورة');
      return imageUrls;
      
    } catch (e) {
      print('❌ خطأ في رفع الصور: $e');
      throw Exception('فشل في رفع الصور: $e');
    }
  }

  /// إرسال طلب مراقبة النبات
  /// 
  /// [request] طلب مراقبة النبات
  /// 
  /// يرجع طلب المراقبة مع المعرف المحدث
  Future<PlantMonitoringRequest> submitRequest(PlantMonitoringRequest request) async {
    try {
      // إنشاء معرف فريد للطلب
      final String requestId = _firestore.collection(PlantMonitoringConstants.firestoreCollection).doc().id;
      final User? currentUser = _auth.currentUser;
      
      // تحديث الطلب بالمعرف ومعرف المستخدم
      final PlantMonitoringRequest updatedRequest = request.copyWith(
        id: requestId,
        userId: currentUser?.uid ?? 'anonymous',
        updatedAt: DateTime.now(),
      );
      
      // حفظ في Firestore
      await _saveToFirestore(updatedRequest);
      
      // إرسال إلى API خارجي
      await _sendToExternalAPI(updatedRequest);
      
      // إرسال الإشعارات
      await _sendNotifications(updatedRequest);
      
      // جدولة مهمة للمهندس
      await _scheduleEngineerTask(updatedRequest);
      
      // إرسال SMS تأكيد
      await _sendSMSConfirmation(updatedRequest);
      
      print('✅ تم إرسال طلب مراقبة النبات بنجاح: $requestId');
      return updatedRequest;
      
    } catch (e) {
      print('❌ خطأ في إرسال طلب مراقبة النبات: $e');
      throw Exception('فشل في إرسال الطلب: $e');
    }
  }

  /// حفظ الطلب في Firestore
  Future<void> _saveToFirestore(PlantMonitoringRequest request) async {
    try {
      await _firestore
          .collection(PlantMonitoringConstants.firestoreCollection)
          .doc(request.id)
          .set(request.toMap());
      
      print('✅ تم حفظ الطلب في Firestore: ${request.id}');
      
    } catch (e) {
      print('❌ خطأ في حفظ الطلب في Firestore: $e');
      throw Exception('فشل في حفظ الطلب: $e');
    }
  }

  /// إرسال الطلب إلى API خارجي
  Future<void> _sendToExternalAPI(PlantMonitoringRequest request) async {
    try {
      final Map<String, dynamic> requestData = {
        'request_id': request.id,
        'farmer_name': request.farmerName,
        'phone': request.phone,
        'farm_location': request.farmLocation,
        'farm_size': request.farmSize,
        'crop_type': request.cropType,
        'monitoring_type': request.monitoringType,
        'urgency': request.urgency,
        'problem_description': request.problemDescription,
        'estimated_cost': request.estimatedCost,
        'image_urls': request.plantImageUrls,
        'gps_location': request.gpsLocation?.toMap(),
        'scheduled_date': request.scheduledDate?.toIso8601String(),
        'scheduled_time': request.scheduledTime,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      final response = await http.post(
        Uri.parse(ApiEndpoints.createMonitoringRequest),
        headers: ApiEndpoints.authHeaders('YOUR_API_TOKEN'),
        body: jsonEncode(requestData),
      );
      
      if (response.statusCode == ApiEndpoints.createdCode) {
        print('✅ تم إرسال الطلب إلى API الخارجي بنجاح');
      } else {
        print('⚠️ فشل في إرسال الطلب إلى API الخارجي: ${response.statusCode}');
      }
      
    } catch (e) {
      print('❌ خطأ في إرسال الطلب إلى API الخارجي: $e');
      // لا نرمي استثناء هنا لأن الطلب تم حفظه في Firestore
    }
  }

  /// إرسال الإشعارات
  Future<void> _sendNotifications(PlantMonitoringRequest request) async {
    try {
      // إشعار للمهندسين
      await _firestore.collection(PlantMonitoringConstants.notificationsCollection).add({
        'type': 'new_plant_monitoring_request',
        'requestId': request.id,
        'title': '🌱 طلب مراقبة نبات جديد',
        'body': 'طلب مراقبة ${request.cropType} في ${request.farmLocation}',
        'farmerName': request.farmerName,
        'farmerPhone': request.phone,
        'cropType': request.cropType,
        'monitoringType': request.monitoringType,
        'urgency': request.urgency,
        'estimatedCost': request.estimatedCost,
        'imageCount': request.imageCount,
        'priority': request.priority,
        'targetRole': 'engineer',
        'isRead': false,
        'isRealNotification': true,
        'createdAt': FieldValue.serverTimestamp(),
      });
      
      // إرسال Push Notification
      await _sendPushNotification(request);
      
      print('✅ تم إرسال الإشعارات بنجاح');
      
    } catch (e) {
      print('❌ خطأ في إرسال الإشعارات: $e');
    }
  }

  /// إرسال Push Notification
  Future<void> _sendPushNotification(PlantMonitoringRequest request) async {
    try {
      final Map<String, dynamic> notificationData = {
        'to': '/topics/${PlantMonitoringConstants.notificationTopic}',
        'notification': {
          'title': '🌱 طلب مراقبة نبات جديد',
          'body': 'طلب مراقبة ${request.cropType} من ${request.farmerName}',
          'sound': PlantMonitoringConstants.notificationSound,
          'badge': 1,
        },
        'data': {
          'click_action': 'FLUTTER_NOTIFICATION_CLICK',
          'type': 'plant_monitoring_request',
          'requestId': request.id,
          'urgency': request.urgency,
          'cropType': request.cropType,
          'estimatedCost': request.estimatedCost.toString(),
        },
      };
      
      final response = await http.post(
        Uri.parse(ApiEndpoints.fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=${ApiEndpoints.fcmServerKey}',
        },
        body: jsonEncode(notificationData),
      );
      
      if (response.statusCode == ApiEndpoints.successCode) {
        print('✅ تم إرسال Push Notification بنجاح');
      } else {
        print('⚠️ فشل في إرسال Push Notification: ${response.statusCode}');
      }
      
    } catch (e) {
      print('❌ خطأ في إرسال Push Notification: $e');
    }
  }

  /// جدولة مهمة للمهندس
  Future<void> _scheduleEngineerTask(PlantMonitoringRequest request) async {
    try {
      await _firestore.collection(PlantMonitoringConstants.tasksCollection).add({
        'requestId': request.id,
        'requestType': 'plant_monitoring',
        'engineerId': 'auto_assign', // سيتم تعيين مهندس تلقائياً
        'status': 'assigned',
        'priority': request.priority,
        'farmLocation': request.farmLocation,
        'cropType': request.cropType,
        'monitoringType': request.monitoringType,
        'scheduledDate': request.scheduledDate?.toIso8601String(),
        'scheduledTime': request.scheduledTime,
        'estimatedDuration': request.estimatedDuration,
        'estimatedCost': request.estimatedCost,
        'gpsLocation': request.gpsLocation?.toMap(),
        'farmerName': request.farmerName,
        'farmerPhone': request.phone,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      print('✅ تم جدولة مهمة للمهندس بنجاح');
      
    } catch (e) {
      print('❌ خطأ في جدولة مهمة للمهندس: $e');
    }
  }

  /// إرسال SMS تأكيد
  Future<void> _sendSMSConfirmation(PlantMonitoringRequest request) async {
    try {
      final String message = '''
تم استلام طلب مراقبة النبات رقم: ${request.id}
نوع المحصول: ${request.cropType}
نوع المراقبة: ${request.monitoringType}
التكلفة المقدرة: ${request.estimatedCost.toStringAsFixed(0)} ريال
سيتم التواصل معك لجدولة الزيارة قريباً.
      ''';
      
      await _firestore.collection('sms_queue').add({
        'phone': request.phone,
        'message': message,
        'status': 'pending',
        'type': 'plant_monitoring_confirmation',
        'requestId': request.id,
        'cost': request.estimatedCost,
        'createdAt': FieldValue.serverTimestamp(),
      });
      
      // إرسال SMS عبر API خارجي
      await _sendSMSViaAPI(request.phone, message);
      
      print('✅ تم إرسال SMS التأكيد بنجاح');
      
    } catch (e) {
      print('❌ خطأ في إرسال SMS التأكيد: $e');
    }
  }

  /// إرسال SMS عبر API خارجي
  Future<void> _sendSMSViaAPI(String phone, String message) async {
    try {
      final response = await http.post(
        Uri.parse(ApiEndpoints.sendSMS),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${ApiEndpoints.smsApiKey}',
        },
        body: jsonEncode({
          'phone': phone,
          'message': message,
          'sender': 'Agriculture-Yemen',
        }),
      );
      
      if (response.statusCode == ApiEndpoints.successCode) {
        print('✅ تم إرسال SMS عبر API الخارجي بنجاح');
      } else {
        print('⚠️ فشل في إرسال SMS عبر API الخارجي: ${response.statusCode}');
      }
      
    } catch (e) {
      print('❌ خطأ في إرسال SMS عبر API الخارجي: $e');
    }
  }

  /// الحصول على طلبات المراقبة للمستخدم الحالي
  Future<List<PlantMonitoringRequest>> getUserRequests() async {
    try {
      final User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }
      
      final QuerySnapshot snapshot = await _firestore
          .collection(PlantMonitoringConstants.firestoreCollection)
          .where('userId', isEqualTo: currentUser.uid)
          .orderBy('createdAt', descending: true)
          .get();
      
      final List<PlantMonitoringRequest> requests = snapshot.docs
          .map((doc) => PlantMonitoringRequest.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
      
      print('✅ تم جلب ${requests.length} طلب مراقبة للمستخدم');
      return requests;
      
    } catch (e) {
      print('❌ خطأ في جلب طلبات المراقبة: $e');
      throw Exception('فشل في جلب الطلبات: $e');
    }
  }

  /// تحديث حالة طلب المراقبة
  Future<void> updateRequestStatus(String requestId, String status) async {
    try {
      await _firestore
          .collection(PlantMonitoringConstants.firestoreCollection)
          .doc(requestId)
          .update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      print('✅ تم تحديث حالة الطلب: $requestId إلى $status');
      
    } catch (e) {
      print('❌ خطأ في تحديث حالة الطلب: $e');
      throw Exception('فشل في تحديث حالة الطلب: $e');
    }
  }
}
