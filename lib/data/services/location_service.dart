import 'package:geolocator/geolocator.dart';
import 'package:agriculture/core/constants/agricultural_services_constants.dart';
import 'package:agriculture/domain/entities/plant_monitoring_request.dart';
import 'dart:async';

/// استثناءات مخصصة لخدمة الموقع (المعيار #2 - Clean Architecture)

/// استثناء عدم تفعيل خدمة الموقع
class LocationServiceDisabledException implements Exception {
  final String message;
  const LocationServiceDisabledException([
    this.message = 'خدمة الموقع غير مفعلة',
  ]);

  @override
  String toString() => 'LocationServiceDisabledException: $message';
}

/// استثناء رفض إذن الموقع
class LocationPermissionDeniedException implements Exception {
  final String message;
  const LocationPermissionDeniedException([this.message = 'تم رفض إذن الموقع']);

  @override
  String toString() => 'LocationPermissionDeniedException: $message';
}

/// استثناء رفض إذن الموقع نهائياً
class LocationPermissionDeniedForeverException implements Exception {
  final String message;
  const LocationPermissionDeniedForeverException([
    this.message = 'إذن الموقع مرفوض نهائياً',
  ]);

  @override
  String toString() => 'LocationPermissionDeniedForeverException: $message';
}

/// خدمة الموقع الجغرافي الحقيقية - محدثة
///
/// تتعامل مع جميع العمليات المتعلقة بتحديد الموقع الجغرافي
///

///
/// الوظائف الرئيسية:
/// - تحديد الموقع الحالي مع إعادة المحاولة
/// - التحقق من الأذونات وحالة الخدمة
/// - حساب المسافات والتكاليف
/// - التحقق من دقة الموقع
/// - تحويل البيانات لطبقة Domain
///
/// مثال الاستخدام:
/// ```dart
/// final locationService = LocationService();
/// final position = await locationService.getCurrentLocation();
/// if (position != null) {
///   final cost = locationService.calculateTransportCost(
///     position.latitude,
///     position.longitude
///   );
/// }
/// ```
class LocationService {
  /// ثوابت الخدمة (المعيار #4)
  static const double _defaultAccuracyThreshold = 100.0; // 100 متر
  static const int _defaultTimeoutSeconds = 30;
  static const int _defaultMaxRetries = 3;
  static const int _defaultRetryDelaySeconds = 2;
  static const double _costPerKilometer = 50.0; // 50 ريال لكل كيلومتر

  /// إحداثيات مركز صنعاء (افتراضي للمهندسين)
  static const double _sanaaLatitude = 15.3694;
  static const double _sanaaLongitude = 44.1910;

  /// كاش للمواقع المحفوظة (تحسين الأداء)
  static Position? _cachedPosition;
  static DateTime? _cacheTimestamp;
  static const int _cacheValidityMinutes = 5; // صالح لمدة 5 دقائق

  /// تحديد الموقع الجغرافي الحالي (المعيار #5 - تجزئة الوظائف)
  ///
  /// يرجع الموقع الحالي أو null في حالة الفشل
  /// يتبع المعيار #12 - تعليقات عربية شاملة
  Future<Position?> getCurrentLocation() async {
    try {
      // التحقق من المتطلبات الأساسية (المعيار #5)
      final bool isReady = await _checkLocationRequirements();
      if (!isReady) {
        return null;
      }

      // تحديد الموقع الحالي مع الإعدادات المحسنة
      final Position position = await _getPositionWithTimeout();

      // التحقق من دقة الموقع وتسجيل النتائج
      _logLocationResult(position);

      return position;
    } on LocationServiceDisabledException {
      _logError('خدمة الموقع غير مفعلة');
      return null;
    } on LocationPermissionDeniedException {
      _logError('تم رفض إذن الموقع');
      return null;
    } on LocationPermissionDeniedForeverException {
      _logError('إذن الموقع مرفوض نهائياً');
      return null;
    } on TimeoutException {
      _logError('انتهت مهلة تحديد الموقع');
      return null;
    } catch (e) {
      _logError('خطأ في تحديد الموقع: $e');
      return null;
    }
  }

  /// التحقق من متطلبات الموقع ( - وظيفة صغيرة مستقلة)
  Future<bool> _checkLocationRequirements() async {
    // التحقق من تفعيل خدمة الموقع
    final bool serviceEnabled = await isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw const LocationServiceDisabledException();
    }

    // التحقق من الأذونات
    final LocationPermission permission = await _ensureLocationPermission();
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      throw const LocationPermissionDeniedException();
    }

    return true;
  }

  /// ضمان الحصول على إذن الموقع
  Future<LocationPermission> _ensureLocationPermission() async {
    LocationPermission permission = await checkLocationPermission();

    if (permission == LocationPermission.denied) {
      permission = await requestLocationPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      throw const LocationPermissionDeniedForeverException();
    }

    return permission;
  }

  /// الحصول على الموقع مع timeout
  Future<Position> _getPositionWithTimeout() async {
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
      timeLimit: Duration(seconds: _defaultTimeoutSeconds),
    );
  }

  /// تسجيل نتيجة تحديد الموقع
  void _logLocationResult(Position position) {
    debugPrint(
      '✅ تم تحديد الموقع بنجاح: ${position.latitude}, ${position.longitude}',
    );
    debugPrint('📍 دقة التحديد: ${position.accuracy} متر');

    if (position.accuracy > _defaultAccuracyThreshold) {
      debugPrint('⚠️ دقة الموقع منخفضة: ${position.accuracy} متر');
    }
  }

  /// تسجيل الأخطاء (المعيار #5)
  void _logError(String message) {
    debugPrint('❌ $message');
  }

  /// الحصول على آخر موقع معروف (المعيار #5 - تجزئة الوظائف)
  ///
  /// يرجع آخر موقع تم تحديده أو null إذا لم يكن متاحاً
  /// يتضمن التحقق من عمر الموقع وصحته
  Future<Position?> getLastKnownLocation() async {
    try {
      final Position? lastPosition = await Geolocator.getLastKnownPosition();

      if (lastPosition == null) {
        _logError('لا يوجد موقع معروف سابقاً');
        return null;
      }

      // التحقق من صحة الموقع وعمره
      final bool isValid = _validateLastKnownPosition(lastPosition);
      if (!isValid) {
        return null;
      }

      _logLastKnownLocationResult(lastPosition);
      return lastPosition;
    } catch (e) {
      _logError('خطأ في جلب آخر موقع معروف: $e');
      return null;
    }
  }

  /// التحقق من صحة آخر موقع معروف (المعيار #5)
  bool _validateLastKnownPosition(Position position) {
    // التحقق من وجود timestamp
    if (position.timestamp == null) {
      _logError('آخر موقع معروف لا يحتوي على timestamp');
      return false;
    }

    // التحقق من عمر الموقع
    final Duration age = DateTime.now().difference(position.timestamp!);
    const int maxAgeMinutes = 30;

    if (age.inMinutes > maxAgeMinutes) {
      print('⚠️ آخر موقع معروف قديم: ${age.inMinutes} دقيقة');
      return false;
    }

    return true;
  }

  /// تسجيل نتيجة آخر موقع معروف (المعيار #5)
  void _logLastKnownLocationResult(Position position) {
    print(
      '✅ تم جلب آخر موقع معروف: ${position.latitude}, ${position.longitude}',
    );

    if (position.timestamp != null) {
      final Duration age = DateTime.now().difference(position.timestamp!);
      print('⏰ عمر الموقع: ${age.inMinutes} دقيقة');
    }
  }

  /// حساب المسافة بين نقطتين (المعيار #5 - وظيفة محددة)
  ///
  /// [startLatitude] خط عرض النقطة الأولى
  /// [startLongitude] خط طول النقطة الأولى
  /// [endLatitude] خط عرض النقطة الثانية
  /// [endLongitude] خط طول النقطة الثانية
  ///
  /// يرجع المسافة بالمتر
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    try {
      // التحقق من صحة الإحداثيات
      if (!_areCoordinatesValid(startLatitude, startLongitude) ||
          !_areCoordinatesValid(endLatitude, endLongitude)) {
        _logError('إحداثيات غير صحيحة');
        return 0.0;
      }

      final double distance = Geolocator.distanceBetween(
        startLatitude,
        startLongitude,
        endLatitude,
        endLongitude,
      );

      print('📏 المسافة المحسوبة: ${distance.toStringAsFixed(2)} متر');
      return distance;
    } catch (e) {
      _logError('خطأ في حساب المسافة: $e');
      return 0.0;
    }
  }

  /// حساب المسافة بالكيلومتر (المعيار #5)
  ///
  /// [startLatitude] خط عرض النقطة الأولى
  /// [startLongitude] خط طول النقطة الأولى
  /// [endLatitude] خط عرض النقطة الثانية
  /// [endLongitude] خط طول النقطة الثانية
  ///
  /// يرجع المسافة بالكيلومتر
  double calculateDistanceInKm(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    final double distanceInMeters = calculateDistance(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );

    final double distanceInKm = distanceInMeters / 1000;
    print('📏 المسافة بالكيلومتر: ${distanceInKm.toStringAsFixed(2)} كم');

    return distanceInKm;
  }

  /// التحقق من صحة الإحداثيات (المعيار #5)
  bool _areCoordinatesValid(double latitude, double longitude) {
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  /// التحقق من حالة خدمة الموقع (المعيار #5)
  ///
  /// يرجع true إذا كانت خدمة الموقع مفعلة
  Future<bool> isLocationServiceEnabled() async {
    try {
      final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      _logServiceStatus(serviceEnabled);
      return serviceEnabled;
    } catch (e) {
      _logError('خطأ في التحقق من حالة خدمة الموقع: $e');
      return false;
    }
  }

  /// تسجيل حالة خدمة الموقع (المعيار #5)
  void _logServiceStatus(bool isEnabled) {
    final String status = isEnabled ? 'مفعلة' : 'معطلة';
    print('📍 حالة خدمة الموقع: $status');
  }

  /// التحقق من أذونات الموقع (المعيار #5)
  ///
  /// يرجع حالة الإذن الحالية
  Future<LocationPermission> checkLocationPermission() async {
    try {
      final LocationPermission permission = await Geolocator.checkPermission();
      _logPermissionStatus(permission);
      return permission;
    } catch (e) {
      _logError('خطأ في التحقق من أذونات الموقع: $e');
      return LocationPermission.denied;
    }
  }

  /// تسجيل حالة الإذن (المعيار #5)
  void _logPermissionStatus(LocationPermission permission) {
    final String status = _getPermissionStatusText(permission);
    print('🔐 حالة إذن الموقع: $status');
  }

  /// الحصول على نص حالة الإذن (المعيار #5)
  String _getPermissionStatusText(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        return 'مرفوض';
      case LocationPermission.deniedForever:
        return 'مرفوض نهائياً';
      case LocationPermission.whileInUse:
        return 'مسموح أثناء الاستخدام';
      case LocationPermission.always:
        return 'مسموح دائماً';
      default:
        return 'غير محدد';
    }
  }

  /// طلب أذونات الموقع (المعيار #5)
  ///
  /// يرجع حالة الإذن بعد الطلب
  Future<LocationPermission> requestLocationPermission() async {
    try {
      final LocationPermission permission =
          await Geolocator.requestPermission();
      _logPermissionRequestResult(permission);
      return permission;
    } catch (e) {
      _logError('خطأ في طلب أذونات الموقع: $e');
      return LocationPermission.denied;
    }
  }

  /// تسجيل نتيجة طلب الإذن (المعيار #5)
  void _logPermissionRequestResult(LocationPermission permission) {
    final String status = _getPermissionStatusText(permission);
    print('🔐 نتيجة طلب إذن الموقع: $status');
  }

  /// فتح إعدادات الموقع (المعيار #5)
  ///
  /// يفتح إعدادات الموقع في النظام
  /// يرجع true إذا تم فتح الإعدادات بنجاح
  Future<bool> openLocationSettings() async {
    try {
      final bool opened = await Geolocator.openLocationSettings();
      _logSettingsResult('إعدادات الموقع', opened);
      return opened;
    } catch (e) {
      _logError('خطأ في فتح إعدادات الموقع: $e');
      return false;
    }
  }

  /// فتح إعدادات التطبيق (المعيار #5)
  ///
  /// يفتح إعدادات التطبيق في النظام
  /// يرجع true إذا تم فتح الإعدادات بنجاح
  Future<bool> openAppSettings() async {
    try {
      final bool opened = await Geolocator.openAppSettings();
      _logSettingsResult('إعدادات التطبيق', opened);
      return opened;
    } catch (e) {
      _logError('خطأ في فتح إعدادات التطبيق: $e');
      return false;
    }
  }

  /// تسجيل نتيجة فتح الإعدادات (المعيار #5)
  void _logSettingsResult(String settingsType, bool opened) {
    final String result = opened ? 'تم فتح' : 'فشل في فتح';
    print('⚙️ $result $settingsType');
  }

  /// تحديد الموقع مع إعادة المحاولة (المعيار #5)
  ///
  /// [maxRetries] عدد المحاولات القصوى (افتراضي: 3)
  /// [retryDelay] التأخير بين المحاولات بالثواني (افتراضي: 2)
  ///
  /// يرجع الموقع أو null في حالة فشل جميع المحاولات
  Future<Position?> getCurrentLocationWithRetry({
    int? maxRetries,
    int? retryDelay,
  }) async {
    final int attempts = maxRetries ?? _defaultMaxRetries;
    final int delay = retryDelay ?? _defaultRetryDelaySeconds;

    for (int attempt = 1; attempt <= attempts; attempt++) {
      _logRetryAttempt(attempt, attempts);

      final Position? position = await getCurrentLocation();
      if (position != null) {
        return position;
      }

      // انتظار قبل المحاولة التالية (إلا في المحاولة الأخيرة)
      if (attempt < attempts) {
        await _delayBeforeRetry(delay);
      }
    }

    _logRetryFailure(attempts);
    return null;
  }

  /// تسجيل محاولة إعادة التجربة (المعيار #5)
  void _logRetryAttempt(int attempt, int maxAttempts) {
    print('🔄 محاولة تحديد الموقع: $attempt/$maxAttempts');
  }

  /// انتظار قبل إعادة المحاولة (المعيار #5)
  Future<void> _delayBeforeRetry(int seconds) async {
    print('⏳ انتظار $seconds ثانية قبل المحاولة التالية...');
    await Future.delayed(Duration(seconds: seconds));
  }

  /// تسجيل فشل جميع المحاولات (المعيار #5)
  void _logRetryFailure(int attempts) {
    _logError('فشل في تحديد الموقع بعد $attempts محاولات');
  }

  /// حساب تكلفة النقل بناءً على المسافة (المعيار #4 - استخدام الثوابت)
  ///
  /// [farmLatitude] خط عرض المزرعة
  /// [farmLongitude] خط طول المزرعة
  /// [engineerLatitude] خط عرض المهندس (افتراضي: مركز صنعاء)
  /// [engineerLongitude] خط طول المهندس (افتراضي: مركز صنعاء)
  ///
  /// يرجع تكلفة النقل المقدرة بالريال اليمني
  double calculateTransportCost(
    double farmLatitude,
    double farmLongitude, {
    double? engineerLatitude,
    double? engineerLongitude,
  }) {
    try {
      // استخدام الإحداثيات الافتراضية إذا لم تُحدد (المعيار #4)
      final double engLat = engineerLatitude ?? _sanaaLatitude;
      final double engLng = engineerLongitude ?? _sanaaLongitude;

      // التحقق من صحة الإحداثيات
      if (!_areCoordinatesValid(farmLatitude, farmLongitude) ||
          !_areCoordinatesValid(engLat, engLng)) {
        _logError('إحداثيات غير صحيحة لحساب تكلفة النقل');
        return AgriculturalServicesConstants.baseCost;
      }

      // حساب المسافة والتكلفة
      final double distanceKm = calculateDistanceInKm(
        engLat,
        engLng,
        farmLatitude,
        farmLongitude,
      );
      final double totalCost = _calculateTotalTransportCost(distanceKm);

      _logTransportCostResult(totalCost, distanceKm);
      return totalCost;
    } catch (e) {
      _logError('خطأ في حساب تكلفة النقل: $e');
      return AgriculturalServicesConstants.baseCost;
    }
  }

  /// حساب التكلفة الإجمالية للنقل (المعيار #5)
  double _calculateTotalTransportCost(double distanceKm) {
    final double baseCost = AgriculturalServicesConstants.baseCost;
    final double distanceCost = distanceKm * _costPerKilometer;
    return baseCost + distanceCost;
  }

  /// تسجيل نتيجة حساب تكلفة النقل (المعيار #5)
  void _logTransportCostResult(double totalCost, double distanceKm) {
    print('💰 تكلفة النقل المقدرة: ${totalCost.toStringAsFixed(0)} ريال');
    print('📏 المسافة: ${distanceKm.toStringAsFixed(2)} كم');
    print(
      '💵 التكلفة الأساسية: ${AgriculturalServicesConstants.baseCost.toStringAsFixed(0)} ريال',
    );
    print(
      '🚗 تكلفة المسافة: ${(distanceKm * _costPerKilometer).toStringAsFixed(0)} ريال',
    );
  }

  /// التحقق من دقة الموقع (المعيار #4 - استخدام الثوابت)
  ///
  /// [position] الموقع المراد التحقق من دقته
  ///
  /// يرجع true إذا كانت الدقة مقبولة
  bool isLocationAccurate(Position position) {
    final bool isAccurate = position.accuracy <= _defaultAccuracyThreshold;
    _logAccuracyResult(position.accuracy, isAccurate);
    return isAccurate;
  }

  /// تسجيل نتيجة فحص الدقة (المعيار #5)
  void _logAccuracyResult(double accuracy, bool isAccurate) {
    if (isAccurate) {
      print('✅ دقة الموقع مقبولة: ${accuracy.toStringAsFixed(1)} متر');
    } else {
      print('⚠️ دقة الموقع منخفضة: ${accuracy.toStringAsFixed(1)} متر');
      print(
        '📏 الحد الأدنى المطلوب: ${_defaultAccuracyThreshold.toStringAsFixed(1)} متر',
      );
    }
  }

  /// إنشاء كائن GpsLocation من Position (المعيار #2 - Clean Architecture)
  ///
  /// [position] الموقع المراد تحويله
  ///
  /// يرجع كائن GpsLocation للاستخدام في طبقة Domain
  GpsLocation createGpsLocationFromPosition(Position position) {
    return GpsLocation(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      timestamp: position.timestamp ?? DateTime.now(),
    );
  }

  /// الحصول على معلومات شاملة عن الموقع (المعيار #5)
  ///
  /// يرجع معلومات مفصلة عن حالة خدمة الموقع والأذونات
  Future<Map<String, dynamic>> getLocationServiceInfo() async {
    try {
      final bool serviceEnabled = await isLocationServiceEnabled();
      final LocationPermission permission = await checkLocationPermission();
      final Position? lastKnown = await getLastKnownLocation();

      return {
        'serviceEnabled': serviceEnabled,
        'permission': permission.toString(),
        'permissionGranted': _isPermissionGranted(permission),
        'hasLastKnownLocation': lastKnown != null,
        'lastKnownAccuracy': lastKnown?.accuracy,
        'lastKnownAge':
            lastKnown?.timestamp != null
                ? DateTime.now().difference(lastKnown!.timestamp!).inMinutes
                : null,
        'isReady': serviceEnabled && _isPermissionGranted(permission),
      };
    } catch (e) {
      _logError('خطأ في جمع معلومات خدمة الموقع: $e');
      return {
        'serviceEnabled': false,
        'permission': 'unknown',
        'permissionGranted': false,
        'hasLastKnownLocation': false,
        'isReady': false,
        'error': e.toString(),
      };
    }
  }

  /// التحقق من منح الإذن (المعيار #5)
  bool _isPermissionGranted(LocationPermission permission) {
    return permission == LocationPermission.whileInUse ||
        permission == LocationPermission.always;
  }

  /// التحقق الشامل من جاهزية خدمة الموقع (المعيار #5)
  ///
  /// يتحقق من جميع المتطلبات ويرجع تقريراً مفصلاً
  /// يتبع المعيار #12 - تعليقات عربية شاملة
  Future<LocationReadinessReport> checkLocationReadiness() async {
    try {
      final bool serviceEnabled = await isLocationServiceEnabled();
      final LocationPermission permission = await checkLocationPermission();
      final Position? lastKnown = await getLastKnownLocation();

      final bool isReady = serviceEnabled && _isPermissionGranted(permission);

      return LocationReadinessReport(
        isReady: isReady,
        serviceEnabled: serviceEnabled,
        permission: permission,
        hasLastKnownLocation: lastKnown != null,
        lastKnownAccuracy: lastKnown?.accuracy,
        recommendations: _generateRecommendations(
          serviceEnabled,
          permission,
          lastKnown,
        ),
      );
    } catch (e) {
      _logError('خطأ في فحص جاهزية خدمة الموقع: $e');
      return LocationReadinessReport.error(e.toString());
    }
  }

  /// توليد التوصيات لتحسين خدمة الموقع (المعيار #5)
  List<String> _generateRecommendations(
    bool serviceEnabled,
    LocationPermission permission,
    Position? lastKnown,
  ) {
    final List<String> recommendations = [];

    if (!serviceEnabled) {
      recommendations.add('يرجى تفعيل خدمة الموقع (GPS) في إعدادات الجهاز');
    }

    if (!_isPermissionGranted(permission)) {
      if (permission == LocationPermission.denied) {
        recommendations.add('يرجى منح إذن الموقع للتطبيق');
      } else if (permission == LocationPermission.deniedForever) {
        recommendations.add('يرجى فتح إعدادات التطبيق ومنح إذن الموقع يدوياً');
      }
    }

    if (lastKnown != null && lastKnown.accuracy > _defaultAccuracyThreshold) {
      recommendations.add('للحصول على دقة أفضل، يرجى الانتقال إلى مكان مفتوح');
    }

    if (recommendations.isEmpty) {
      recommendations.add('خدمة الموقع جاهزة للاستخدام');
    }

    return recommendations;
  }

  /// إعادة تعيين إعدادات الموقع (المعيار #5)
  ///
  /// يحاول إعادة تهيئة خدمة الموقع من البداية
  Future<bool> resetLocationService() async {
    try {
      _logError('إعادة تعيين خدمة الموقع...');

      // محاولة فتح إعدادات الموقع
      await openLocationSettings();

      // انتظار قصير للسماح للمستخدم بتغيير الإعدادات
      await Future.delayed(const Duration(seconds: 2));

      // التحقق من الحالة الجديدة
      final LocationReadinessReport report = await checkLocationReadiness();

      if (report.isReady) {
        print('✅ تم إعادة تعيين خدمة الموقع بنجاح');
        return true;
      } else {
        print('⚠️ خدمة الموقع لا تزال غير جاهزة');
        return false;
      }
    } catch (e) {
      _logError('خطأ في إعادة تعيين خدمة الموقع: $e');
      return false;
    }
  }

  /// الحصول على الموقع مع استخدام الكاش (المعيار #5 - تحسين الأداء)
  ///
  /// يستخدم الموقع المحفوظ إذا كان حديثاً، وإلا يحدث الموقع
  /// يتبع المعيار #12 - تعليقات عربية شاملة
  Future<Position?> getCurrentLocationCached() async {
    try {
      // التحقق من صحة الكاش
      if (_isCacheValid()) {
        print('📍 استخدام الموقع المحفوظ من الكاش');
        return _cachedPosition;
      }

      // الحصول على موقع جديد
      final Position? newPosition = await getCurrentLocation();

      if (newPosition != null) {
        // حفظ الموقع الجديد في الكاش
        _updateCache(newPosition);
      }

      return newPosition;
    } catch (e) {
      _logError('خطأ في الحصول على الموقع مع الكاش: $e');
      return null;
    }
  }

  /// التحقق من صحة الكاش (المعيار #5)
  bool _isCacheValid() {
    if (_cachedPosition == null || _cacheTimestamp == null) {
      return false;
    }

    final Duration cacheAge = DateTime.now().difference(_cacheTimestamp!);
    return cacheAge.inMinutes < _cacheValidityMinutes;
  }

  /// تحديث الكاش (المعيار #5)
  void _updateCache(Position position) {
    _cachedPosition = position;
    _cacheTimestamp = DateTime.now();
    print('💾 تم حفظ الموقع في الكاش');
  }

  /// مسح الكاش (المعيار #5)
  ///
  /// يمسح الموقع المحفوظ لإجبار تحديث جديد
  void clearLocationCache() {
    _cachedPosition = null;
    _cacheTimestamp = null;
    print('🗑️ تم مسح كاش الموقع');
  }

  /// الحصول على معلومات الكاش (المعيار #5)
  Map<String, dynamic> getCacheInfo() {
    return {
      'hasCachedPosition': _cachedPosition != null,
      'cacheTimestamp': _cacheTimestamp?.toIso8601String(),
      'cacheAge':
          _cacheTimestamp != null
              ? DateTime.now().difference(_cacheTimestamp!).inMinutes
              : null,
      'isValid': _isCacheValid(),
      'validityMinutes': _cacheValidityMinutes,
    };
  }
}

/// تقرير جاهزية خدمة الموقع (المعيار #2 - Clean Architecture)
class LocationReadinessReport {
  /// هل الخدمة جاهزة للاستخدام
  final bool isReady;

  /// هل خدمة الموقع مفعلة
  final bool serviceEnabled;

  /// حالة إذن الموقع
  final LocationPermission permission;

  /// هل يوجد موقع معروف سابقاً
  final bool hasLastKnownLocation;

  /// دقة آخر موقع معروف
  final double? lastKnownAccuracy;

  /// التوصيات لتحسين الخدمة
  final List<String> recommendations;

  /// رسالة خطأ (إن وجدت)
  final String? errorMessage;

  /// منشئ التقرير العادي
  const LocationReadinessReport({
    required this.isReady,
    required this.serviceEnabled,
    required this.permission,
    required this.hasLastKnownLocation,
    this.lastKnownAccuracy,
    required this.recommendations,
    this.errorMessage,
  });

  /// منشئ تقرير الخطأ
  const LocationReadinessReport.error(String error)
    : isReady = false,
      serviceEnabled = false,
      permission = LocationPermission.denied,
      hasLastKnownLocation = false,
      lastKnownAccuracy = null,
      recommendations = const ['حدث خطأ في فحص خدمة الموقع'],
      errorMessage = error;

  /// تحويل إلى Map للتصدير
  Map<String, dynamic> toMap() {
    return {
      'isReady': isReady,
      'serviceEnabled': serviceEnabled,
      'permission': permission.toString(),
      'hasLastKnownLocation': hasLastKnownLocation,
      'lastKnownAccuracy': lastKnownAccuracy,
      'recommendations': recommendations,
      'errorMessage': errorMessage,
    };
  }

  @override
  String toString() {
    return 'LocationReadinessReport(isReady: $isReady, serviceEnabled: $serviceEnabled, permission: $permission)';
  }
}
