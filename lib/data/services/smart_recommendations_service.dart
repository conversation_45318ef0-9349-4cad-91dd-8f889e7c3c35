import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../core/utils/services/api_keys_service.dart';
import '../../core/shared/services/user/user_data_service.dart';
import 'ai_analysis_service.dart';

/// خدمة التوصيات الذكية
/// 
/// تجمع البيانات من مصادر متعددة لإنتاج توصيات مخصصة
/// - تحليل AI للنباتات
/// - بيانات الطقس
/// - تاريخ المزارع
/// - المعرفة الزراعية المحلية
/// 
/// وفق المعيار #2: Clean Architecture - Data Layer
/// وفق المعيار #5: تجزئة الوظائف في وحدات صغيرة
/// وفق المعيار #12: تعليقات عربية شاملة
class SmartRecommendationsService {
  /// خدمة تحليل AI
  final AIAnalysisService _aiAnalysisService;
  
  // /// خدمة الطقس
  // final WeatherService _weatherService;
  
  /// خدمة مفاتيح API
  final ApiKeysService? _apiKeysService;

  /// منشئ خدمة التوصيات الذكية
  ///
  /// المعلمات:
  /// - [aiAnalysisService]: خدمة تحليل AI
  /// - [apiKeysService]: خدمة مفاتيح API (اختياري)
  SmartRecommendationsService({
    required AIAnalysisService aiAnalysisService,
    ApiKeysService? apiKeysService,
  })  : _aiAnalysisService = aiAnalysisService,
        _apiKeysService = apiKeysService;

  /// إنتاج توصيات شاملة للمزارع
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [imageUrls]: صور النبات (اختياري)
  /// - [location]: الموقع الجغرافي (اختياري)
  /// - [farmSize]: مساحة المزرعة (اختياري)
  /// 
  /// الإرجاع: [Map<String, dynamic>] التوصيات الشاملة
  Future<Map<String, dynamic>> generateComprehensiveRecommendations({
    required String cropType,
    List<String>? imageUrls,
    String? location,
    double? farmSize,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🧠 بدء إنتاج التوصيات الذكية للمحصول: $cropType');
      }

      // جمع البيانات من مصادر متعددة
      final analysisData = await _gatherAnalysisData(
        cropType: cropType,
        imageUrls: imageUrls ?? [],
        location: location,
        farmSize: farmSize,
      );

      // إنتاج التوصيات الذكية
      final recommendations = await _generateSmartRecommendations(analysisData);

      // إضافة معلومات إضافية
      recommendations['metadata'] = {
        'generated_at': DateTime.now().toIso8601String(),
        'crop_type': cropType,
        'data_sources': analysisData['sources'],
        'confidence_score': _calculateOverallConfidence(analysisData),
      };

      if (kDebugMode) {
        debugPrint('✅ تم إنتاج التوصيات الذكية بنجاح');
      }

      return recommendations;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إنتاج التوصيات الذكية: $e');
      }
      return _getEmptyRecommendations();
    }
  }

  /// إنتاج توصيات يومية
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [location]: الموقع الجغرافي
  /// 
  /// الإرجاع: [Map<String, dynamic>] التوصيات اليومية
  Future<Map<String, dynamic>> generateDailyRecommendations({
    required String cropType,
    String? location,
  }) async {
    try {
      // الحصول على بيانات الطقس اليومية (محاكاة)
      final weatherData = _getMockWeatherData(location ?? 'الرياض');
      
      // إنتاج توصيات بناءً على الطقس
      final recommendations = _generateWeatherBasedRecommendations(cropType, weatherData);
      
      // إضافة توصيات عامة
      recommendations['general_tips'] = _getDailyGeneralTips(cropType);
      
      // إضافة تذكيرات
      recommendations['reminders'] = _getDailyReminders(cropType);
      
      recommendations['metadata'] = {
        'type': 'daily_recommendations',
        'generated_at': DateTime.now().toIso8601String(),
        'crop_type': cropType,
        'weather_based': weatherData != null,
      };

      return recommendations;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إنتاج التوصيات اليومية: $e');
      }
      return _getEmptyDailyRecommendations();
    }
  }

  /// إنتاج توصيات طوارئ
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [emergencyType]: نوع الطوارئ
  /// - [severity]: مستوى الخطورة
  /// 
  /// الإرجاع: [Map<String, dynamic>] توصيات الطوارئ
  Future<Map<String, dynamic>> generateEmergencyRecommendations({
    required String cropType,
    required String emergencyType,
    required String severity,
  }) async {
    try {
      final recommendations = <String, dynamic>{};

      switch (emergencyType) {
        case 'pest_outbreak':
          recommendations.addAll(_getPestEmergencyRecommendations(cropType, severity));
          break;
        case 'disease_outbreak':
          recommendations.addAll(_getDiseaseEmergencyRecommendations(cropType, severity));
          break;
        case 'weather_extreme':
          recommendations.addAll(_getWeatherEmergencyRecommendations(cropType, severity));
          break;
        case 'water_shortage':
          recommendations.addAll(_getWaterShortageRecommendations(cropType, severity));
          break;
        default:
          recommendations.addAll(_getGeneralEmergencyRecommendations(cropType));
      }

      recommendations['metadata'] = {
        'type': 'emergency_recommendations',
        'emergency_type': emergencyType,
        'severity': severity,
        'generated_at': DateTime.now().toIso8601String(),
        'urgency': 'high',
      };

      return recommendations;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إنتاج توصيات الطوارئ: $e');
      }
      return _getEmptyEmergencyRecommendations();
    }
  }

  /// جمع بيانات التحليل
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [imageUrls]: صور النبات
  /// - [location]: الموقع
  /// - [farmSize]: مساحة المزرعة
  /// 
  /// الإرجاع: [Map<String, dynamic>] البيانات المجمعة
  Future<Map<String, dynamic>> _gatherAnalysisData({
    required String cropType,
    required List<String> imageUrls,
    String? location,
    double? farmSize,
  }) async {
    final data = <String, dynamic>{
      'crop_type': cropType,
      'farm_size': farmSize,
      'location': location,
      'sources': <String>[],
    };

    // تحليل صحة النبات إذا توفرت صور
    if (imageUrls.isNotEmpty) {
      try {
        final healthAnalysis = await _aiAnalysisService.assessPlantHealth(imageUrls, cropType);
        data['health_analysis'] = healthAnalysis;
        data['sources'].add('plant_health_ai');
      } catch (e) {
        if (kDebugMode) {
          print('تعذر تحليل صحة النبات: $e');
        }
      }

      // تحليل الآفات
      try {
        final pestAnalysis = await _aiAnalysisService.identifyPests(imageUrls, cropType);
        data['pest_analysis'] = pestAnalysis;
        data['sources'].add('pest_detection_ai');
      } catch (e) {
        if (kDebugMode) {
          print('تعذر تحليل الآفات: $e');
        }
      }
    }

    // بيانات الطقس (محاكاة)
    if (location != null) {
      try {
        final weatherData = _getMockWeatherData(location);
        data['weather_data'] = weatherData;
        data['sources'].add('weather_mock');
      } catch (e) {
        if (kDebugMode) {
          debugPrint('تعذر الحصول على بيانات الطقس: $e');
        }
      }
    }

    // بيانات المستخدم
    try {
      final userData = UserDataService.getCurrentUserData();
      data['user_data'] = {
        'user_id': userData.userId,
        'user_name': userData.userName,
        'user_phone': userData.userPhone,
      };
      data['sources'].add('user_profile');
    } catch (e) {
      if (kDebugMode) {
        print('تعذر الحصول على بيانات المستخدم: $e');
      }
    }

    return data;
  }

  /// إنتاج التوصيات الذكية
  /// 
  /// المعلمات:
  /// - [analysisData]: البيانات المجمعة
  /// 
  /// الإرجاع: [Map<String, dynamic>] التوصيات الذكية
  Future<Map<String, dynamic>> _generateSmartRecommendations(
    Map<String, dynamic> analysisData,
  ) async {
    final recommendations = <String, dynamic>{};

    // توصيات الري
    recommendations['irrigation'] = _generateIrrigationRecommendations(analysisData);

    // توصيات التسميد
    recommendations['fertilization'] = _generateFertilizationRecommendations(analysisData);

    // توصيات مكافحة الآفات
    recommendations['pest_control'] = _generatePestControlRecommendations(analysisData);

    // توصيات العناية العامة
    recommendations['general_care'] = _generateGeneralCareRecommendations(analysisData);

    // توصيات الحصاد
    recommendations['harvest'] = _generateHarvestRecommendations(analysisData);

    // تحذيرات وتنبيهات
    recommendations['alerts'] = _generateAlerts(analysisData);

    return recommendations;
  }

  /// إنتاج توصيات الري
  Map<String, dynamic> _generateIrrigationRecommendations(Map<String, dynamic> data) {
    final weatherData = data['weather_data'] as Map<String, dynamic>?;
    final cropType = data['crop_type'] as String;

    final recommendations = <String, dynamic>{
      'frequency': 'يومياً',
      'amount': '2-3 لتر لكل متر مربع',
      'best_time': 'الصباح الباكر (6-8 صباحاً)',
      'method': 'الري بالتنقيط المفضل',
    };

    // تعديل بناءً على الطقس
    if (weatherData != null) {
      final temperature = weatherData['temperature'] as double?;
      final humidity = weatherData['humidity'] as double?;

      if (temperature != null && temperature > 35) {
        recommendations['frequency'] = 'مرتين يومياً';
        recommendations['amount'] = '3-4 لتر لكل متر مربع';
        recommendations['additional_tips'] = [
          'زيادة كمية الري في الطقس الحار',
          'تجنب الري في وقت الظهيرة',
        ];
      }

      if (humidity != null && humidity < 30) {
        recommendations['humidity_care'] = 'رش الأوراق بالماء في المساء';
      }
    }

    return recommendations;
  }

  /// إنتاج توصيات التسميد
  Map<String, dynamic> _generateFertilizationRecommendations(Map<String, dynamic> data) {
    final healthAnalysis = data['health_analysis'] as Map<String, dynamic>?;
    final cropType = data['crop_type'] as String;

    final recommendations = <String, dynamic>{
      'schedule': 'كل أسبوعين',
      'type': 'سماد مركب NPK (20-20-20)',
      'amount': '50 جرام لكل متر مربع',
      'application_method': 'خلط مع ماء الري',
    };

    // تعديل بناءً على تحليل الصحة
    if (healthAnalysis != null) {
      final issues = healthAnalysis['issues_detected'] as List?;
      if (issues != null && issues.isNotEmpty) {
        for (final issue in issues) {
          if (issue.toString().contains('نيتروجين')) {
            recommendations['nitrogen_boost'] = 'إضافة سماد نيتروجيني إضافي';
          }
          if (issue.toString().contains('فوسفور')) {
            recommendations['phosphorus_boost'] = 'إضافة سماد فوسفوري';
          }
        }
      }
    }

    return recommendations;
  }

  /// إنتاج توصيات مكافحة الآفات
  Map<String, dynamic> _generatePestControlRecommendations(Map<String, dynamic> data) {
    final pestAnalysis = data['pest_analysis'] as Map<String, dynamic>?;

    final recommendations = <String, dynamic>{
      'prevention': 'فحص أسبوعي للنباتات',
      'organic_methods': [
        'استخدام زيت النيم',
        'رش بمحلول الصابون الطبيعي',
      ],
      'monitoring': 'مراقبة يومية للأوراق والثمار',
    };

    // تعديل بناءً على تحليل الآفات
    if (pestAnalysis != null) {
      final pestsDetected = pestAnalysis['pests_detected'] as List?;
      if (pestsDetected != null && pestsDetected.isNotEmpty) {
        recommendations['immediate_action'] = 'علاج فوري مطلوب';
        recommendations['treatment'] = 'استخدام مبيد حشري مناسب';
        recommendations['urgency'] = 'عالي';
      }
    }

    return recommendations;
  }

  /// إنتاج توصيات العناية العامة
  Map<String, dynamic> _generateGeneralCareRecommendations(Map<String, dynamic> data) {
    return {
      'pruning': 'تقليم الأوراق الجافة أسبوعياً',
      'weeding': 'إزالة الأعشاب الضارة بانتظام',
      'soil_care': 'تهوية التربة كل أسبوعين',
      'support': 'توفير دعامات للنباتات الطويلة',
    };
  }

  /// إنتاج توصيات الحصاد
  Map<String, dynamic> _generateHarvestRecommendations(Map<String, dynamic> data) {
    final cropType = data['crop_type'] as String;

    return {
      'timing': _getHarvestTiming(cropType),
      'indicators': _getHarvestIndicators(cropType),
      'best_time': 'الصباح الباكر',
      'storage': 'حفظ في مكان بارد وجاف',
    };
  }

  /// إنتاج التحذيرات
  List<Map<String, dynamic>> _generateAlerts(Map<String, dynamic> data) {
    final alerts = <Map<String, dynamic>>[];

    final weatherData = data['weather_data'] as Map<String, dynamic>?;
    if (weatherData != null) {
      final temperature = weatherData['temperature'] as double?;
      if (temperature != null && temperature > 40) {
        alerts.add({
          'type': 'weather_warning',
          'severity': 'high',
          'message': 'درجة حرارة عالية جداً - زيادة الري مطلوبة',
        });
      }
    }

    return alerts;
  }

  /// حساب الثقة الإجمالية
  double _calculateOverallConfidence(Map<String, dynamic> data) {
    final sources = data['sources'] as List<String>;
    double confidence = 0.5; // ثقة أساسية

    if (sources.contains('plant_health_ai')) confidence += 0.2;
    if (sources.contains('pest_detection_ai')) confidence += 0.15;
    if (sources.contains('weather_api')) confidence += 0.1;
    if (sources.contains('user_profile')) confidence += 0.05;

    return confidence.clamp(0.0, 1.0);
  }

  /// توصيات بناءً على الطقس
  Map<String, dynamic> _generateWeatherBasedRecommendations(
    String cropType,
    Map<String, dynamic>? weatherData,
  ) {
    if (weatherData == null) {
      return _getDefaultDailyRecommendations(cropType);
    }

    final recommendations = <String, dynamic>{};
    final temperature = weatherData['temperature'] as double?;
    final humidity = weatherData['humidity'] as double?;

    if (temperature != null) {
      if (temperature > 35) {
        recommendations['heat_protection'] = [
          'زيادة كمية الري',
          'توفير ظل للنباتات',
          'رش الأوراق بالماء',
        ];
      } else if (temperature < 15) {
        recommendations['cold_protection'] = [
          'تقليل كمية الري',
          'حماية النباتات من البرد',
        ];
      }
    }

    return recommendations;
  }

  /// الحصول على توقيت الحصاد
  String _getHarvestTiming(String cropType) {
    switch (cropType.toLowerCase()) {
      case 'طماطم':
        return '70-80 يوم من الزراعة';
      case 'خيار':
        return '50-60 يوم من الزراعة';
      case 'خس':
        return '45-55 يوم من الزراعة';
      default:
        return '60-90 يوم من الزراعة';
    }
  }

  /// الحصول على مؤشرات الحصاد
  List<String> _getHarvestIndicators(String cropType) {
    switch (cropType.toLowerCase()) {
      case 'طماطم':
        return ['تغير اللون إلى الأحمر', 'ليونة طفيفة عند اللمس'];
      case 'خيار':
        return ['اللون الأخضر الداكن', 'الحجم المناسب'];
      case 'خس':
        return ['الأوراق الممتلئة', 'الحجم المناسب'];
      default:
        return ['النضج الظاهري', 'الحجم المناسب'];
    }
  }

  /// نصائح عامة يومية
  List<String> _getDailyGeneralTips(String cropType) {
    return [
      'فحص النباتات صباحاً',
      'مراقبة مستوى الرطوبة',
      'إزالة الأوراق الجافة',
      'تسجيل الملاحظات اليومية',
    ];
  }

  /// تذكيرات يومية
  List<String> _getDailyReminders(String cropType) {
    return [
      'وقت الري: 6-8 صباحاً',
      'فحص الآفات: مساءً',
      'تسجيل درجة الحرارة',
    ];
  }

  /// توصيات طوارئ الآفات
  Map<String, dynamic> _getPestEmergencyRecommendations(String cropType, String severity) {
    return {
      'immediate_actions': [
        'عزل النباتات المصابة',
        'تطبيق مبيد حشري فوري',
        'زيادة التهوية',
      ],
      'follow_up': [
        'مراقبة يومية لمدة أسبوع',
        'تكرار العلاج حسب الحاجة',
      ],
      'prevention': [
        'تحسين النظافة العامة',
        'تقليل الرطوبة الزائدة',
      ],
    };
  }

  /// توصيات طوارئ الأمراض
  Map<String, dynamic> _getDiseaseEmergencyRecommendations(String cropType, String severity) {
    return {
      'immediate_actions': [
        'إزالة الأجزاء المصابة',
        'تطبيق مبيد فطري',
        'تحسين التهوية',
      ],
      'quarantine': 'عزل النباتات المصابة',
      'monitoring': 'مراقبة انتشار المرض',
    };
  }

  /// توصيات طوارئ الطقس
  Map<String, dynamic> _getWeatherEmergencyRecommendations(String cropType, String severity) {
    return {
      'protection': [
        'توفير حماية من العوامل الجوية',
        'تعديل جدول الري',
        'تقوية دعامات النباتات',
      ],
      'monitoring': 'مراقبة مستمرة للنباتات',
    };
  }

  /// توصيات نقص المياه
  Map<String, dynamic> _getWaterShortageRecommendations(String cropType, String severity) {
    return {
      'water_conservation': [
        'استخدام نظام الري بالتنقيط',
        'إضافة مهاد للتربة',
        'الري في الصباح الباكر',
      ],
      'priority_plants': 'التركيز على النباتات الأهم',
    };
  }

  /// توصيات طوارئ عامة
  Map<String, dynamic> _getGeneralEmergencyRecommendations(String cropType) {
    return {
      'assessment': 'تقييم شامل للوضع',
      'documentation': 'توثيق المشكلة بالصور',
      'expert_consultation': 'استشارة خبير زراعي',
    };
  }

  /// توصيات يومية افتراضية
  Map<String, dynamic> _getDefaultDailyRecommendations(String cropType) {
    return {
      'irrigation': 'ري منتظم حسب الحاجة',
      'monitoring': 'فحص يومي للنباتات',
      'care': 'عناية عامة بالنباتات',
    };
  }

  /// توصيات فارغة
  Map<String, dynamic> _getEmptyRecommendations() {
    return {
      'error': 'لا توجد بيانات كافية لإنتاج التوصيات',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// توصيات يومية فارغة
  Map<String, dynamic> _getEmptyDailyRecommendations() {
    return {
      'error': 'لا توجد بيانات كافية للتوصيات اليومية',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// توصيات طوارئ فارغة
  Map<String, dynamic> _getEmptyEmergencyRecommendations() {
    return {
      'error': 'لا توجد بيانات كافية لتوصيات الطوارئ',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// محاكاة بيانات الطقس
  ///
  /// المعلمات:
  /// - [location]: الموقع
  ///
  /// الإرجاع: [Map<String, dynamic>] بيانات الطقس المحاكاة
  Map<String, dynamic> _getMockWeatherData(String location) {
    return {
      'location': location,
      'temperature': 28.5,
      'humidity': 65.0,
      'wind_speed': 12.0,
      'weather_condition': 'مشمس جزئياً',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
