import 'package:agriculture/core/utils/logging/logging.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/core/utils/firebase_auth_fix.dart';
import 'package:agriculture/data/datasources/local/shared_prefs.dart';
import 'package:agriculture/data/models/user.dart';
import 'package:agriculture/data/repositories/user_repository.dart';
import 'package:agriculture/domain/entities/user_entity.dart';
import 'package:agriculture/domain/repositories/auth_repository_interface.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../core/constants/constants_url.dart';

class AuthRepository implements AuthRepositoryInterface {
  static FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static UserAccount? curUser;

  // متغيرات للمصادقة برقم الهاتف
  String _verificationId = '';
  int? _resendToken;

  // Getters للوصول إلى المتغيرات من خارج الفئة
  String get verificationId => _verificationId;
  int? get resendToken => _resendToken;

  // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  @override
  Future<UserEntity?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      printInfo("محاولة تسجيل الدخول باستخدام البريد الإلكتروني: $email");

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء عملية تسجيل الدخول باستخدام البريد الإلكتروني: $email',
        tag: 'AuthRepository',
      );

      // استخدام الإصلاح الآمن لمشكلة PigeonUserDetails
      UserCredential? userCredential = await FirebaseAuthFix.signInWithEmailAndPasswordSafe(
        email,
        password,
      );

      if (userCredential?.user == null) {
        throw Exception('فشل في تسجيل الدخول: المستخدم فارغ');
      }

      // الآن نحن متأكدون أن userCredential و user ليسا null
      final user = userCredential!.user!;

      printInfo("تم تسجيل الدخول بنجاح: ${user.uid}");

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'تم تسجيل الدخول بنجاح في Firebase: ${user.uid}',
        tag: 'AuthRepository',
      );

      // تأكد من تعيين متغيرات الجلسة
      SharedPrefs.setBool('isAuth', true);
      SharedPrefs.setString('uid', user.uid);

      // تحديث المتغير العام uid
      updateUid(user.uid);

      printInfo(
          "تم تعيين متغيرات الجلسة: isAuth=true, uid=${user.uid}");

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'تم تعيين متغيرات الجلسة: isAuth=true, uid=${user.uid}',
        tag: 'AuthRepository',
      );

      // التحقق من وجود بيانات المستخدم في Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'التحقق من وجود بيانات المستخدم في Firestore: ${userDoc.exists}',
        tag: 'AuthRepository',
      );

      if (!userDoc.exists) {
        printWarning(
            "بيانات المستخدم غير موجودة في Firestore، إنشاء بيانات أولية");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'بيانات المستخدم غير موجودة في Firestore، إنشاء بيانات أولية',
          tag: 'AuthRepository',
        );

        // إنشاء بيانات أولية للمستخدم إذا لم تكن موجودة
        UserRepository userRepo = UserRepository();
        await userRepo.onUserCreated(UserEntity(
          id: user.uid,
          name: user.displayName ?? 'User',
          email: user.email ?? '',
          phone: user.phoneNumber ?? '',
          isVerified: user.emailVerified,
          address: '',
          image: user.photoURL ?? '',
          bio: '',
          createdAt: DateTime.now().toIso8601String(),
          lastUpdated: DateTime.now().toIso8601String(),
          theme: '',
          language: '',
        ));

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'تم إنشاء بيانات أولية للمستخدم في Firestore',
          tag: 'AuthRepository',
        );
      }

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'إرجاع بيانات المستخدم من Firebase',
        tag: 'AuthRepository',
      );

      return _getUserAccountFromFirebaseUser(user);
    } catch (e) {
      printError("خطأ في تسجيل الدخول: $e");

      // تسجيل إضافي للتصحيح
      LoggerService.error(
        'خطأ في تسجيل الدخول: $e',
        error: e,
        tag: 'AuthRepository',
      );

      // إعادة رمي الاستثناء ليتم معالجته في الطبقة العليا
      rethrow;
    }
  }

  // إنشاء حساب باستخدام البريد الإلكتروني وكلمة المرور
  @override
  Future<UserEntity?> createUserWithEmailAndPassword(
      String name, String email, String password, String phone) async {
    try {
      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء عملية إنشاء حساب جديد: البريد الإلكتروني = $email',
        tag: 'AuthRepository.createUserWithEmailAndPassword',
      );

      // استخدام الإصلاح الآمن لمشكلة PigeonUserDetails
      UserCredential? userCredential = await FirebaseAuthFix.createUserWithEmailAndPasswordSafe(
        email,
        password,
      );

      if (userCredential?.user == null) {
        throw Exception('فشل في إنشاء الحساب: المستخدم فارغ');
      }

      LoggerService.debug(
        'تم إنشاء الحساب بنجاح في Firebase Auth: ${userCredential!.user!.uid}',
        tag: 'AuthRepository.createUserWithEmailAndPassword',
      );

      // الآن نحن متأكدون أن userCredential و user ليسا null
      final user = userCredential!.user!;

      // إنشاء كائن UserAccount
      UserAccount userAccount = UserAccount(
        id: user.uid,
        name: user.displayName ?? 'User',
        email: user.email ?? '',
        phone: user.phoneNumber ?? '',
        isVerified: user.emailVerified,
        address: '',
        image: user.photoURL ?? '',
        bio: '',
        createdAt: '',
        lastUpdated: '',
        theme: '',
        language: '',
        predictionHistory: [],
      );

      // تأكد من تعيين متغيرات الجلسة
      SharedPrefs.setString('uid', user.uid);
      SharedPrefs.setBool('isAuth', true);

      // تحديث المتغير العام uid
      updateUid(user.uid);

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'تم تعيين متغيرات الجلسة بعد إنشاء الحساب: isAuth=true, uid=${user.uid}',
        tag: 'AuthRepository.createUserWithEmailAndPassword',
      );

      // استدعاء UserRepository.onUserCreated لحفظ بيانات المستخدم في Firestore
      UserRepository userRepo = UserRepository();
      await userRepo.onUserCreated(UserEntity(
        id: userAccount.id,
        name: userAccount.name,
        email: userAccount.email,
        phone: userAccount.phone,
        isVerified: userAccount.isVerified,
        address: userAccount.address,
        image: userAccount.image,
        bio: userAccount.bio ?? '',
        createdAt: userAccount.createdAt,
        lastUpdated: userAccount.lastUpdated,
        theme: userAccount.theme,
        language: userAccount.language,
      ));

      // تحويل UserAccount إلى UserEntity
      return UserEntity(
        id: userAccount.id,
        name: userAccount.name,
        email: userAccount.email,
        phone: userAccount.phone,
        isVerified: userAccount.isVerified,
        address: userAccount.address,
        image: userAccount.image,
        bio: userAccount.bio ?? '',
        createdAt: userAccount.createdAt,
        lastUpdated: userAccount.lastUpdated,
        theme: userAccount.theme,
        language: userAccount.language,
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء الحساب',
        error: e,
        tag: 'AuthRepository.createUserWithEmailAndPassword',
      );
      printError("Error creating user: $e");

      // إعادة رمي الاستثناء ليتم معالجته في الطبقة العليا
      rethrow;
    }
  }

  // تسجيل الدخول باستخدام Google
  @override
  Future<UserEntity?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user == null) {
        throw Exception('فشل في تسجيل الدخول بـ Google: المستخدم فارغ');
      }

      final user = userCredential.user!;

      // تأكد من تعيين متغيرات الجلسة
      SharedPrefs.setString('uid', user.uid);
      SharedPrefs.setBool('isAuth', true);

      // تحديث المتغير العام uid
      updateUid(user.uid);

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'تم تعيين متغيرات الجلسة بعد تسجيل الدخول بـ Google: isAuth=true, uid=${user.uid}',
        tag: 'AuthRepository.signInWithGoogle',
      );

      // إنشاء كائن UserAccount
      UserAccount userAccount = UserAccount(
        id: user.uid,
        name: user.displayName ?? 'User',
        email: user.email ?? '',
        phone: user.phoneNumber ?? '',
        isVerified: user.emailVerified,
        address: '',
        image: user.photoURL ?? '',
        bio: '',
        createdAt: '',
        lastUpdated: '',
        theme: '',
        language: '',
        specialty: '', // إضافة حقل التخصص
        predictionHistory: [],
      );
      // استدعاء UserRepository.onUserCreated لحفظ بيانات المستخدم في Firestore
      UserRepository userRepo = UserRepository();
      await userRepo.onUserCreated(UserEntity(
        id: userAccount.id,
        name: userAccount.name,
        email: userAccount.email,
        phone: userAccount.phone,
        isVerified: userAccount.isVerified,
        address: userAccount.address,
        image: userAccount.image,
        bio: userAccount.bio ?? '',
        createdAt: userAccount.createdAt,
        lastUpdated: userAccount.lastUpdated,
        theme: userAccount.theme,
        language: userAccount.language,
        specialty: userAccount.specialty, // إضافة حقل التخصص
      ));

      // تحويل UserAccount إلى UserEntity
      return UserEntity(
        id: userAccount.id,
        name: userAccount.name,
        email: userAccount.email,
        phone: userAccount.phone,
        isVerified: userAccount.isVerified,
        address: userAccount.address,
        image: userAccount.image,
        bio: userAccount.bio ?? '',
        createdAt: userAccount.createdAt,
        lastUpdated: userAccount.lastUpdated,
        theme: userAccount.theme,
        language: userAccount.language,
        specialty: userAccount.specialty,
      );
    } catch (e) {
      printError("Error signing in with Google: $e");
      return null;
    }
  }

  /// تسجيل الدخول برقم الهاتف - إرسال رمز التحقق
  @override
  Future<void> signInWithPhone(String phoneNumber) async {
    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // تستخدم للتحقق التلقائي على أجهزة Android
          // لن نستخدمها هنا لأننا نريد أن يدخل المستخدم الرمز يدويًا
        },
        verificationFailed: (FirebaseAuthException e) {
          throw Exception(e.message ?? 'حدث خطأ أثناء إرسال رمز التحقق');
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _resendToken = resendToken;
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      printError("Error sending verification code: $e");
      throw Exception('حدث خطأ أثناء إرسال رمز التحقق: $e');
    }
  }

  /// التحقق من رمز OTP
  @override
  Future<UserEntity?> verifyOTP(String verificationId, String otp) async {
    try {
      // استخدام verificationId المُمرر أو المتغير الداخلي إذا كان المُمرر فارغًا
      final String verId =
          verificationId.isNotEmpty ? verificationId : _verificationId;

      // إنشاء بيانات اعتماد المصادقة باستخدام رمز التحقق
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verId,
        smsCode: otp,
      );

      // تسجيل الدخول باستخدام بيانات الاعتماد
      final userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        // حفظ معرف المستخدم في SharedPrefs
        if (userCredential.user!.uid.isNotEmpty) {
          SharedPrefs.setString('uid', userCredential.user!.uid);
          SharedPrefs.setBool('isAuth', true);

          // تسجيل إضافي للتصحيح
          LoggerService.debug(
            'تم تعيين متغيرات الجلسة بعد التحقق من OTP: isAuth=true, uid=${userCredential.user!.uid}',
            tag: 'AuthRepository.verifyOTP',
          );
        }

        return _getUserAccountFromFirebaseUser(userCredential.user);
      } else {
        // تسجيل إضافي للتصحيح
        LoggerService.error(
          'فشل التحقق من OTP: المستخدم غير موجود',
          tag: 'AuthRepository.verifyOTP',
        );

        return null;
      }
    } catch (e) {
      printError("Error verifying OTP: $e");
      return null;
    }
  }

  /// التحقق مما إذا كان المستخدم قد أكمل ملفه الشخصي
  @override
  Future<bool> hasUserCompletedProfile(String userId) async {
    try {
      printInfo("التحقق من اكتمال ملف المستخدم: $userId");

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء التحقق من اكتمال ملف المستخدم: $userId',
        tag: 'AuthRepository.hasUserCompletedProfile',
      );

      final doc = await _firestore.collection('users').doc(userId).get();

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'نتيجة استعلام Firestore: ${doc.exists ? "الوثيقة موجودة" : "الوثيقة غير موجودة"}',
        tag: 'AuthRepository.hasUserCompletedProfile',
      );

      if (doc.exists && doc.data() != null) {
        final data = doc.data()!;
        printInfo("بيانات المستخدم: $data");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'بيانات المستخدم: $data',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // التحقق من وجود الاسم على الأقل
        bool hasName = data.containsKey('name') &&
            data['name'] != null &&
            data['name'].toString().isNotEmpty;
        printInfo("وجود الاسم: $hasName");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'وجود الاسم: $hasName (القيمة: ${data['name'] ?? 'غير موجود'})',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // التحقق من وجود التخصص
        bool hasSpecialty = data.containsKey('specialty') &&
            data['specialty'] != null &&
            data['specialty'].toString().isNotEmpty;
        printInfo(
            "وجود التخصص: $hasSpecialty (القيمة: ${data['specialty'] ?? 'غير موجود'})");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'وجود التخصص: $hasSpecialty (القيمة: ${data['specialty'] ?? 'غير موجود'})',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // التحقق من وجود المحافظة
        bool hasGovernorate = data.containsKey('governorate') &&
            data['governorate'] != null &&
            data['governorate'].toString().isNotEmpty;
        printInfo(
            "وجود المحافظة: $hasGovernorate (القيمة: ${data['governorate'] ?? 'غير موجود'})");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'وجود المحافظة: $hasGovernorate (القيمة: ${data['governorate'] ?? 'غير موجود'})',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // التحقق من وجود المدينة
        bool hasCity = data.containsKey('city') &&
            data['city'] != null &&
            data['city'].toString().isNotEmpty;
        printInfo(
            "وجود المدينة: $hasCity (القيمة: ${data['city'] ?? 'غير موجود'})");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'وجود المدينة: $hasCity (القيمة: ${data['city'] ?? 'غير موجود'})',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // يعتبر الملف مكتملًا إذا كان الاسم والتخصص والمحافظة والمدينة موجودين
        bool isComplete = hasName && hasSpecialty && hasGovernorate && hasCity;

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'تم تعديل شروط اكتمال الملف: الاسم والتخصص والمحافظة والمدينة فقط',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );
        printInfo("اكتمال ملف المستخدم: $isComplete");

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'نتيجة التحقق من اكتمال ملف المستخدم: $isComplete',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'نتيجة التحقق النهائية من اكتمال ملف المستخدم: $isComplete',
          tag: 'AuthRepository.hasUserCompletedProfile',
        );

        return isComplete;
      }
      printWarning("ملف المستخدم غير موجود");

      // تسجيل إضافي للتصحيح
      LoggerService.warning(
        'ملف المستخدم غير موجود',
        tag: 'AuthRepository.hasUserCompletedProfile',
      );

      // إذا كان ملف المستخدم غير موجود، نعتبره غير مكتمل
      return false;
    } catch (e) {
      printError("خطأ أثناء التحقق من ملف المستخدم: $e");

      // تسجيل إضافي للتصحيح
      LoggerService.error(
        'خطأ أثناء التحقق من ملف المستخدم: $e',
        error: e,
        tag: 'AuthRepository.hasUserCompletedProfile',
      );

      return false;
    }
  }

  // تسجيل الخروج
  @override
  Future<void> signOut() async {
    try {
      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء عملية تسجيل الخروج',
        tag: 'AuthRepository.signOut',
      );

      // تسجيل الخروج من Firebase
      await _firebaseAuth.signOut();

      // إزالة متغيرات الجلسة
      await SharedPrefs.remove('uid');
      await SharedPrefs.setBool('isAuth', false);

      // تحديث المتغير العام uid
      updateUid('');

      // تحديث المتغيرات العامة في constants_url.dart
      // يتم تحديثها تلقائيًا عند إعادة تشغيل التطبيق

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'تم تسجيل الخروج بنجاح وإزالة متغيرات الجلسة',
        tag: 'AuthRepository.signOut',
      );
    } catch (e) {
      // تسجيل إضافي للتصحيح
      LoggerService.error(
        'خطأ في تسجيل الخروج: $e',
        error: e,
        tag: 'AuthRepository.signOut',
      );

      rethrow;
    }
  }

  // تحويل Firebase User إلى UserEntity
  UserEntity? _getUserAccountFromFirebaseUser(User? user) {
    if (user == null) return null;

    // أولاً نقوم بإنشاء UserAccount
    UserAccount userAccount = UserAccount(
      id: user.uid,
      name: user.displayName ?? 'User',
      email: user.email ?? '',
      phone: user.phoneNumber ?? '',
      isVerified: user.emailVerified,
      address: '',
      image: user.photoURL ?? '',
      bio: '',
      createdAt: '',
      lastUpdated: '',
      theme: '',
      language: '',
      specialty: '', // إضافة حقل التخصص
      predictionHistory: [],
    );

    // ثم نقوم بتحويله إلى UserEntity
    return UserEntity(
      id: userAccount.id,
      name: userAccount.name,
      email: userAccount.email,
      phone: userAccount.phone,
      isVerified: userAccount.isVerified,
      address: userAccount.address,
      image: userAccount.image,
      bio: userAccount.bio ?? '',
      createdAt: userAccount.createdAt,
      lastUpdated: userAccount.lastUpdated,
      theme: userAccount.theme,
      language: userAccount.language,
      specialty: userAccount.specialty, // إضافة حقل التخصص
    );
  }
}
