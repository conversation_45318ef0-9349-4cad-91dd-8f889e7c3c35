import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../services/firestore_rest_service.dart';

import '../../core/utils/logging/logger_service.dart';
import '../../domain/repositories/advisor_repository_interface.dart';
import '../../domain/repositories/storage_repository_interface.dart';
import '../models/agricultural_advisor/advisor_model.dart';
import '../models/agricultural_advisor/consultation_model.dart';
import '../models/agricultural_advisor/appointment_model.dart';

/// تنفيذ مستودع المرشدين الزراعيين
class AdvisorRepository implements AdvisorRepositoryInterface {
  /// مثيل Firestore
  final FirebaseFirestore _firestore;

  /// خدمة Firestore REST (بديل لا يحتاج Google Play Services - للاستخدام المستقبلي)
  // final FirestoreRestService _firestoreRest = FirestoreRestService();

  /// مستودع التخزين
  final StorageRepositoryInterface _storageRepository;

  /// مولد المعرفات الفريدة
  final Uuid _uuid = const Uuid();

  /// منشئ مستودع المرشدين الزراعيين
  AdvisorRepository({
    required FirebaseFirestore firestore,
    required StorageRepositoryInterface storageRepository,
  })  : _firestore = firestore,
        _storageRepository = storageRepository;

  @override
  Future<List<AdvisorModel>> getAdvisors({int limit = 10, String? lastAdvisorId}) async {
    try {
      Query query = _firestore
          .collection('advisors')
          .orderBy('rating', descending: true)
          .limit(limit);

      if (lastAdvisorId != null) {
        // الحصول على المستند الأخير للتحميل التدريجي
        final lastDoc = await _firestore
            .collection('advisors')
            .doc(lastAdvisorId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => AdvisorModel.fromDocument(doc))
          .toList();
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على المرشدين الزراعيين',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<AdvisorModel?> getAdvisor(String advisorId) async {
    try {
      final doc = await _firestore
          .collection('advisors')
          .doc(advisorId)
          .get();

      if (doc.exists) {
        return AdvisorModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على المرشد الزراعي: $advisorId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return null;
    }
  }

  @override
  Future<List<AdvisorModel>> searchAdvisors({
    required String query,
    String? specialty,
    int limit = 20,
  }) async {
    try {
      LoggerService.info(
        'البحث عن المرشدين باستخدام المصطلح: $query',
        tag: 'AdvisorRepository',
      );

      // تنظيف مصطلح البحث
      final String cleanQuery = query.trim().toLowerCase();
      if (cleanQuery.isEmpty) {
        return [];
      }

      Query baseQuery = _firestore.collection('advisors');

      // إضافة فلتر التخصص إذا تم توفيره
      if (specialty != null && specialty.isNotEmpty) {
        baseQuery = baseQuery.where('specialty', isEqualTo: specialty);
      }

      // البحث في اسم المرشد
      final nameQuerySnapshot = await baseQuery
          .where('name', isGreaterThanOrEqualTo: cleanQuery)
          .where('name', isLessThanOrEqualTo: cleanQuery + '\uf8ff')
          .limit(limit)
          .get();

      // البحث في التخصص
      final specialtyQuerySnapshot = await baseQuery
          .where('specialty', isGreaterThanOrEqualTo: cleanQuery)
          .where('specialty', isLessThanOrEqualTo: cleanQuery + '\uf8ff')
          .limit(limit)
          .get();

      // البحث في التخصصات المتعددة
      final specialtiesQuerySnapshot = await baseQuery
          .where('specialties', arrayContains: cleanQuery)
          .limit(limit)
          .get();

      // جمع النتائج وإزالة التكرار
      final Set<String> advisorIds = {};
      final List<AdvisorModel> results = [];

      // إضافة نتائج البحث في الاسم
      for (final doc in nameQuerySnapshot.docs) {
        if (!advisorIds.contains(doc.id)) {
          advisorIds.add(doc.id);
          results.add(AdvisorModel.fromDocument(doc));
        }
      }

      // إضافة نتائج البحث في التخصص
      for (final doc in specialtyQuerySnapshot.docs) {
        if (!advisorIds.contains(doc.id)) {
          advisorIds.add(doc.id);
          results.add(AdvisorModel.fromDocument(doc));
        }
      }

      // إضافة نتائج البحث في التخصصات المتعددة
      for (final doc in specialtiesQuerySnapshot.docs) {
        if (!advisorIds.contains(doc.id)) {
          advisorIds.add(doc.id);
          results.add(AdvisorModel.fromDocument(doc));
        }
      }

      // ترتيب النتائج حسب التقييم
      results.sort((a, b) => b.rating.compareTo(a.rating));

      LoggerService.info(
        'تم العثور على ${results.length} مرشد',
        tag: 'AdvisorRepository',
      );

      return results;
    } catch (e) {
      LoggerService.error(
        'خطأ في البحث عن المرشدين',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<ConsultationModel?> createConsultation({
    required String userId,
    required String userName,
    String? userImage,
    required String advisorId,
    required String cropType,
    required String problemDescription,
    required String area,
    List<File>? imageFiles,
  }) async {
    try {
      debugPrint('=== Repository: بدء createConsultation ===');
      debugPrint('Repository: advisorId = $advisorId');
      debugPrint('Repository: cropType = $cropType');
      // التحقق من وجود المرشد أو إنشاء مرشد عام افتراضي
      AdvisorModel? advisor = await getAdvisor(advisorId);

      // إذا لم يوجد المرشد وكان مرشد عام، أنشئ مرشد افتراضي
      if (advisor == null && advisorId == 'general_advisor') {
        advisor = await _createDefaultGeneralAdvisor();
      }

      if (advisor == null) {
        LoggerService.warning(
          'محاولة إنشاء استشارة مع مرشد غير موجود: $advisorId',
          tag: 'AdvisorRepository',
        );
        return null;
      }

      // إنشاء معرف فريد للاستشارة
      final consultationId = _uuid.v4();

      // رفع الصور إذا وجدت (نفس طريقة المنتدى - بسيطة ومباشرة)
      List<String>? imageUrls;
      if (imageFiles != null && imageFiles.isNotEmpty) {
        imageUrls = [];
        for (final imageFile in imageFiles) {
          final imagePath = 'consultations/$consultationId/images/${_uuid.v4()}.jpg';
          final imageUrl = await _storageRepository.uploadFile(imageFile, imagePath);
          if (imageUrl != null) {
            imageUrls.add(imageUrl);
          }
        }
      }

      // إنشاء نموذج الاستشارة
      final now = DateTime.now().toIso8601String();
      final consultation = ConsultationModel(
        id: consultationId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        advisorId: advisorId,
        advisorName: advisor.name,
        cropType: cropType,
        problemDescription: problemDescription,
        area: area,
        images: imageUrls,
        status: ConsultationStatus.pending,
        createdAt: now,
        lastUpdated: now,
      );

      // حفظ الاستشارة في Firestore (نفس طريقة المنتدى مع timeout بسيط)
      await _firestore
          .collection('consultations')
          .doc(consultationId)
          .set(consultation.toMap())
          .timeout(const Duration(seconds: 30));

      LoggerService.info(
        'تم إنشاء استشارة جديدة: $consultationId',
        tag: 'AdvisorRepository',
      );

      return consultation;
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء استشارة جديدة',
        error: e,
        tag: 'AdvisorRepository',
      );
      return null;
    }
  }

  /// إنشاء مرشد عام افتراضي
  Future<AdvisorModel?> _createDefaultGeneralAdvisor() async {
    try {
      debugPrint('=== Repository: إنشاء مرشد عام افتراضي ===');

      final defaultAdvisor = AdvisorModel(
        id: 'general_advisor',
        name: 'المرشد الزراعي العام',
        specialty: 'استشارات زراعية عامة',
        image: null,
        phone: '+967700000000',
        email: '<EMAIL>',
        location: 'صنعاء، اليمن',
        rating: 4.5,
        reviewsCount: 0,
        clientsCount: 0,
        experienceYears: 10,
        successRate: 95.0,
        specialties: ['استشارات زراعية عامة', 'مكافحة الآفات', 'التسميد والري'],
        workingHours: {
          'السبت': '24/7',
          'الأحد': '24/7',
          'الاثنين': '24/7',
          'الثلاثاء': '24/7',
          'الأربعاء': '24/7',
          'الخميس': '24/7',
          'الجمعة': '24/7',
        },
        isAvailable: true,
        createdAt: DateTime.now().toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
      );

      // حفظ المرشد في Firestore
      await _firestore
          .collection('advisors')
          .doc('general_advisor')
          .set(defaultAdvisor.toMap());

      LoggerService.info(
        'تم إنشاء المرشد العام الافتراضي',
        tag: 'AdvisorRepository',
      );

      return defaultAdvisor;
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء المرشد العام الافتراضي',
        error: e,
        tag: 'AdvisorRepository',
      );
      return null;
    }
  }

  /// الحصول على استشارة محددة
  Future<ConsultationModel?> getConsultation(String consultationId) async {
    try {
      debugPrint('🔍 جلب الاستشارة: $consultationId');

      final docSnapshot = await _firestore
          .collection('consultations')
          .doc(consultationId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final consultation = ConsultationModel.fromDocument(docSnapshot);
        debugPrint('✅ تم جلب الاستشارة بنجاح: ${consultation.userName}');
        return consultation;
      } else {
        debugPrint('❌ لم يتم العثور على الاستشارة: $consultationId');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب الاستشارة: $e');
      return null;
    }
  }

  /// الحصول على جميع الاستشارات
  @override
  Future<List<ConsultationModel>> getAllConsultations({
    int limit = 50,
  }) async {
    try {
      debugPrint('🔍 تحميل جميع الاستشارات من Firebase...');

      // محاولة التحميل مع الترتيب أولاً
      QuerySnapshot querySnapshot;
      try {
        querySnapshot = await _firestore
            .collection('consultations')
            .orderBy('createdAt', descending: true)
            .limit(limit)
            .get();
        debugPrint('✅ تم تحميل الاستشارات مع الترتيب');
      } catch (e) {
        debugPrint('⚠️ فشل الترتيب، محاولة بدون ترتيب: $e');
        // إذا فشل الترتيب (مشكلة index)، حمل بدون ترتيب
        querySnapshot = await _firestore
            .collection('consultations')
            .limit(limit)
            .get();
        debugPrint('✅ تم تحميل الاستشارات بدون ترتيب');
      }

      final consultations = querySnapshot.docs
          .map((doc) {
            try {
              return ConsultationModel.fromDocument(doc);
            } catch (e) {
              debugPrint('⚠️ خطأ في تحويل الاستشارة ${doc.id}: $e');
              return null;
            }
          })
          .where((consultation) => consultation != null)
          .cast<ConsultationModel>()
          .toList();

      debugPrint('📊 تم تحميل ${consultations.length} استشارة من أصل ${querySnapshot.docs.length}');

      // ترتيب يدوي إذا لم يتم الترتيب في Firebase
      consultations.sort((a, b) {
        final aTime = DateTime.tryParse(a.createdAt ?? '') ?? DateTime.now();
        final bTime = DateTime.tryParse(b.createdAt ?? '') ?? DateTime.now();
        return bTime.compareTo(aTime); // الأحدث أولاً
      });

      return consultations;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على جميع الاستشارات',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<List<ConsultationModel>> getUserConsultations({
    required String userId,
    int limit = 20,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection('consultations')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => ConsultationModel.fromDocument(doc))
          .toList();
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على استشارات المستخدم: $userId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<List<ConsultationModel>> getAdvisorConsultations({
    required String advisorId,
    int limit = 20,
  }) async {
    try {
      debugPrint('🔍 البحث عن استشارات المرشد: $advisorId');

      // إذا كان المرشد العام، استخدم getAllConsultations
      if (advisorId == 'general_advisor') {
        debugPrint('📋 المرشد العام - تحميل جميع الاستشارات');
        return await getAllConsultations(limit: limit);
      }

      // للمرشدين المحددين، ابحث عن استشاراتهم
      QuerySnapshot querySnapshot;
      try {
        querySnapshot = await _firestore
            .collection('consultations')
            .where('advisorId', isEqualTo: advisorId)
            .orderBy('createdAt', descending: true)
            .limit(limit)
            .get();
        debugPrint('✅ تم البحث مع الترتيب');
      } catch (e) {
        debugPrint('⚠️ فشل الترتيب، محاولة بدون ترتيب: $e');
        querySnapshot = await _firestore
            .collection('consultations')
            .where('advisorId', isEqualTo: advisorId)
            .limit(limit)
            .get();
        debugPrint('✅ تم البحث بدون ترتيب');
      }

      final consultations = querySnapshot.docs
          .map((doc) {
            try {
              return ConsultationModel.fromDocument(doc);
            } catch (e) {
              debugPrint('⚠️ خطأ في تحويل الاستشارة ${doc.id}: $e');
              return null;
            }
          })
          .where((consultation) => consultation != null)
          .cast<ConsultationModel>()
          .toList();

      debugPrint('✅ تم العثور على ${consultations.length} استشارة للمرشد: $advisorId');

      // طباعة تفاصيل الاستشارات للتأكد
      for (int i = 0; i < consultations.length && i < 3; i++) {
        final consultation = consultations[i];
        debugPrint('📋 استشارة ${i + 1}: ${consultation.id} - ${consultation.cropType} - ${consultation.status.name}');
      }

      // ترتيب يدوي إذا لم يتم الترتيب في Firebase
      consultations.sort((a, b) {
        final aTime = DateTime.tryParse(a.createdAt ?? '') ?? DateTime.now();
        final bTime = DateTime.tryParse(b.createdAt ?? '') ?? DateTime.now();
        return bTime.compareTo(aTime); // الأحدث أولاً
      });

      return consultations;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على استشارات المرشد: $advisorId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<bool> updateConsultationStatus({
    required String consultationId,
    required ConsultationStatus status,
    String? advisorResponse,
  }) async {
    try {
      final updates = <String, dynamic>{
        'status': status.name,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      if (advisorResponse != null) {
        updates['advisorResponse'] = advisorResponse;
        updates['respondedAt'] = DateTime.now().toIso8601String();
      }

      await _firestore
          .collection('consultations')
          .doc(consultationId)
          .update(updates);

      LoggerService.info(
        'تم تحديث حالة الاستشارة: $consultationId إلى ${status.name}',
        tag: 'AdvisorRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث حالة الاستشارة: $consultationId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return false;
    }
  }

  @override
  Future<bool> rateConsultation({
    required String consultationId,
    required double rating,
    String? comment,
  }) async {
    try {
      final updates = <String, dynamic>{
        'rating': rating,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      if (comment != null) {
        updates['ratingComment'] = comment;
      }

      await _firestore
          .collection('consultations')
          .doc(consultationId)
          .update(updates);

      LoggerService.info(
        'تم تقييم الاستشارة: $consultationId بتقييم $rating',
        tag: 'AdvisorRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في تقييم الاستشارة: $consultationId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return false;
    }
  }

  @override
  Future<AppointmentModel?> bookAppointment({
    required String userId,
    required String userName,
    required String userPhone,
    required String advisorId,
    required String consultationType,
    required String appointmentDate,
    required String appointmentTime,
    required String problemDescription,
  }) async {
    try {
      // التحقق من وجود المرشد
      final advisor = await getAdvisor(advisorId);
      if (advisor == null) {
        LoggerService.warning(
          'محاولة حجز موعد مع مرشد غير موجود: $advisorId',
          tag: 'AdvisorRepository',
        );
        return null;
      }

      // إنشاء معرف فريد للموعد
      final appointmentId = _uuid.v4();

      // إنشاء نموذج الموعد
      final now = DateTime.now().toIso8601String();
      final appointment = AppointmentModel(
        id: appointmentId,
        userId: userId,
        userName: userName,
        userPhone: userPhone,
        advisorId: advisorId,
        advisorName: advisor.name,
        consultationType: consultationType,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        problemDescription: problemDescription,
        status: AppointmentStatus.pending,
        createdAt: now,
        lastUpdated: now,
      );

      // حفظ الموعد في Firestore
      await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .set(appointment.toMap());

      LoggerService.info(
        'تم حجز موعد جديد: $appointmentId',
        tag: 'AdvisorRepository',
      );

      return appointment;
    } catch (e) {
      LoggerService.error(
        'خطأ في حجز موعد جديد',
        error: e,
        tag: 'AdvisorRepository',
      );
      return null;
    }
  }

  @override
  Future<List<AppointmentModel>> getUserAppointments({
    required String userId,
    int limit = 20,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection('appointments')
          .where('userId', isEqualTo: userId)
          .orderBy('appointmentDate', descending: false)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => AppointmentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على مواعيد المستخدم: $userId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<List<AppointmentModel>> getAdvisorAppointments({
    required String advisorId,
    int limit = 20,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection('appointments')
          .where('advisorId', isEqualTo: advisorId)
          .orderBy('appointmentDate', descending: false)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => AppointmentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على مواعيد المرشد: $advisorId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return [];
    }
  }

  @override
  Future<bool> updateAppointmentStatus({
    required String appointmentId,
    required AppointmentStatus status,
    String? advisorNotes,
  }) async {
    try {
      final updates = <String, dynamic>{
        'status': status.name,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      if (advisorNotes != null) {
        updates['advisorNotes'] = advisorNotes;
      }

      if (status == AppointmentStatus.confirmed) {
        updates['confirmedAt'] = DateTime.now().toIso8601String();
      }

      await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .update(updates);

      LoggerService.info(
        'تم تحديث حالة الموعد: $appointmentId إلى ${status.name}',
        tag: 'AdvisorRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث حالة الموعد: $appointmentId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return false;
    }
  }

  @override
  Future<bool> cancelAppointment({
    required String appointmentId,
    required String userId,
  }) async {
    try {
      // التحقق من أن المستخدم هو صاحب الموعد
      final appointmentDoc = await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .get();

      if (!appointmentDoc.exists) {
        LoggerService.warning(
          'محاولة إلغاء موعد غير موجود: $appointmentId',
          tag: 'AdvisorRepository',
        );
        return false;
      }

      final appointment = AppointmentModel.fromDocument(appointmentDoc);
      if (appointment.userId != userId) {
        LoggerService.warning(
          'محاولة إلغاء موعد بواسطة مستخدم غير مصرح له: $userId',
          tag: 'AdvisorRepository',
        );
        return false;
      }

      await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .update({
        'status': AppointmentStatus.cancelled.name,
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      LoggerService.info(
        'تم إلغاء الموعد: $appointmentId',
        tag: 'AdvisorRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في إلغاء الموعد: $appointmentId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getAdvisorStats(String advisorId) async {
    try {
      // الحصول على عدد الاستشارات
      final consultationsSnapshot = await _firestore
          .collection('consultations')
          .where('advisorId', isEqualTo: advisorId)
          .get();

      // الحصول على عدد المواعيد
      final appointmentsSnapshot = await _firestore
          .collection('appointments')
          .where('advisorId', isEqualTo: advisorId)
          .get();

      // حساب الإحصائيات
      final totalConsultations = consultationsSnapshot.docs.length;
      final totalAppointments = appointmentsSnapshot.docs.length;

      final answeredConsultations = consultationsSnapshot.docs
          .where((doc) => doc.data()['status'] == 'answered')
          .length;

      final completedAppointments = appointmentsSnapshot.docs
          .where((doc) => doc.data()['status'] == 'completed')
          .length;

      // حساب متوسط التقييم
      final ratings = consultationsSnapshot.docs
          .where((doc) => doc.data()['rating'] != null)
          .map((doc) => (doc.data()['rating'] as num).toDouble())
          .toList();

      final averageRating = ratings.isNotEmpty
          ? ratings.reduce((a, b) => a + b) / ratings.length
          : 0.0;

      return {
        'totalConsultations': totalConsultations,
        'totalAppointments': totalAppointments,
        'answeredConsultations': answeredConsultations,
        'completedAppointments': completedAppointments,
        'averageRating': averageRating,
        'totalRatings': ratings.length,
      };
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على إحصائيات المرشد: $advisorId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return {};
    }
  }

  @override
  Future<bool> respondToConsultation({
    required String consultationId,
    required String advisorId,
    required String response,
    String? recommendations,
    bool followUpRequired = false,
  }) async {
    try {
      LoggerService.info(
        'الرد على الاستشارة: $consultationId من المرشد: $advisorId',
        tag: 'AdvisorRepository',
      );

      // تحديث الاستشارة بالرد والتوصيات
      await _firestore
          .collection('consultations')
          .doc(consultationId)
          .update({
        'advisorResponse': response,
        'recommendations': recommendations,
        'followUpRequired': followUpRequired,
        'status': ConsultationStatus.answered.name,
        'respondedAt': DateTime.now().toIso8601String(),
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      LoggerService.info(
        'تم الرد على الاستشارة بنجاح: $consultationId',
        tag: 'AdvisorRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في الرد على الاستشارة: $consultationId',
        error: e,
        tag: 'AdvisorRepository',
      );
      return false;
    }
  }
}
