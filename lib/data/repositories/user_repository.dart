import 'package:agriculture/core/constants/collections.dart';
import 'package:agriculture/core/utils/logging/logging.dart';
import 'package:agriculture/data/models/user.dart';
import 'package:agriculture/domain/entities/user_entity.dart';
import 'package:agriculture/domain/repositories/user_repository_interface.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
// import 'package:firebase_storage/firebase_storage.dart';

class UserRepository implements UserRepositoryInterface {
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  // final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  Future<bool> onUserCreated(UserEntity user) async {
    try {
      // تحويل UserEntity إلى UserAccount
      UserAccount acc = _convertToUserAccount(user);

      DocumentReference ref = _db.collection(Collections.users).doc(acc.id);
      DocumentSnapshot snapshot = await ref.get();

      if (!snapshot.exists) {
        await ref.set(acc.toMap());
      }
      return true;
    } on Exception catch (e) {
      printError("Error creating user: $e");
      return false;
    }
  }

  @override
  Future<UserEntity> getUserData(String id) async {
    try {
      DocumentReference ref = _db.collection(Collections.users).doc(id);
      DocumentSnapshot snapshot = await ref.get();

      if (snapshot.exists) {
        printInfo('User data found in Firestore');
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        UserAccount userAccount = UserAccount.fromMap(data);
        return _convertToUserEntity(userAccount);
      } else {
        printWarning('User data not found in Firestore');
        User? currentUser = _auth.currentUser;
        if (currentUser != null) {
          UserAccount acc = UserAccount(
            id: currentUser.uid,
            name: currentUser.displayName ?? 'User',
            email: currentUser.email ?? '',
            phone: currentUser.phoneNumber ?? '',
            isVerified: currentUser.emailVerified,
            address: '',
            image: currentUser.photoURL ?? '',
            bio: '',
            createdAt: DateTime.now().toString(),
            lastUpdated: DateTime.now().toString(),
            theme: '',
            language: '',
            specialty: '', // إضافة حقل التخصص
            predictionHistory: [],
          );
          return _convertToUserEntity(acc);
        } else {
          throw Exception('User not found and not authenticated');
        }
      }
    } catch (e) {
      printError("Error getting user data: $e");
      rethrow;
    }
  }

  @override
  Future<bool> updateUserData({required UserEntity user}) async {
    try {
      // تحويل UserEntity إلى UserAccount
      UserAccount acc = _convertToUserAccount(user);

      DocumentReference ref = _db.collection(Collections.users).doc(acc.id);
      DocumentSnapshot snapshot = await ref.get();

      if (snapshot.exists) {
        await ref.update(acc.toMap());
        return true;
      }
      return false;
    } catch (e) {
      printError("Error updating user data: $e");
      return false;
    }
  }

  // دالة مساعدة لتحويل UserEntity إلى UserAccount
  UserAccount _convertToUserAccount(UserEntity user) {
    return UserAccount(
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      isVerified: user.isVerified,
      address: user.address,
      image: user.image,
      bio: user.bio,
      createdAt: user.createdAt,
      lastUpdated: user.lastUpdated,
      theme: user.theme,
      language: user.language,
      specialty: user.specialty, // إضافة حقل التخصص
      predictionHistory: [], // TODO: تحويل التنبؤات
    );
  }

  // دالة مساعدة لتحويل UserAccount إلى UserEntity
  UserEntity _convertToUserEntity(UserAccount account) {
    return UserEntity(
      id: account.id,
      name: account.name,
      email: account.email,
      phone: account.phone,
      isVerified: account.isVerified,
      address: account.address,
      image: account.image,
      bio: account.bio ?? '',
      createdAt: account.createdAt,
      lastUpdated: account.lastUpdated,
      theme: account.theme,
      language: account.language,
      specialty: account.specialty, // إضافة حقل التخصص
    );
  }
}
