import 'dart:io';

import '../../../core/constants/app_constants.dart';
import '../../../core/utils/logging/logger_service.dart';
import '../../../core/utils/media/image_compression_service.dart';
import '../../../core/utils/media/video_compression_service.dart';
import '../../../core/utils/media/video_quality.dart';
import '../../domain/repositories/storage_repository_interface.dart';
import '../datasources/remote/google_drive/google_drive_service.dart';

/// مستودع التخزين
///
/// ينفذ هذا المستودع واجهة `StorageRepositoryInterface` باستخدام `GoogleDriveService`.
class StorageRepository implements StorageRepositoryInterface {
  final GoogleDriveService _googleDriveService;

  /// منشئ مستودع التخزين
  ///
  /// المعلمات:
  /// - [googleDriveService]: خدمة Google Drive
  StorageRepository(this._googleDriveService);

  @override
  Future<String?> uploadFile(File file, String path, {bool compress = true}) async {
    try {
      // تحديد نوع الملف بناءً على امتداده
      final String extension = path.split('.').last.toLowerCase();

      // إذا كان الملف صورة وتم تفعيل الضغط
      if (compress && ['jpg', 'jpeg', 'png', 'webp', 'heic'].contains(extension)) {
        return await uploadCompressedImage(file, path);
      }

      // إذا كان الملف فيديو وتم تفعيل الضغط
      if (compress && ['mp4', 'mov', 'avi', 'wmv', 'flv', 'webm', '3gp'].contains(extension)) {
        return await uploadCompressedVideo(file, path);
      }

      // إذا لم يكن الملف صورة أو فيديو أو تم تعطيل الضغط
      return await _googleDriveService.uploadFile(file, path);
    } catch (e) {
      LoggerService.error(
        'خطأ في رفع الملف',
        error: e,
        tag: 'StorageRepository',
      );
      return null;
    }
  }

  @override
  Future<bool> deleteFile(String path) async {
    try {
      return await _googleDriveService.deleteFile(path);
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف الملف',
        error: e,
        tag: 'StorageRepository',
      );
      return false;
    }
  }

  /// تحديث صورة البروفايل
  ///
  /// يقوم برفع الصورة الجديدة وحذف الصورة القديمة إذا كانت موجودة
  ///
  /// المعلمات:
  /// - [file]: ملف الصورة الجديدة
  /// - [path]: مسار الصورة الجديدة
  /// - [oldImageUrl]: رابط الصورة القديمة (اختياري)
  /// - [compress]: ما إذا كان يجب ضغط الصورة قبل رفعها
  ///
  /// يعيد رابط الصورة الجديدة أو null في حالة الفشل
  @override
  Future<String?> updateProfileImage(File file, String path,
      {String? oldImageUrl, bool compress = true}) async {
    try {
      // رفع الصورة الجديدة
      final newImageUrl = await uploadFile(file, path, compress: compress);

      // إذا نجح رفع الصورة الجديدة وكانت هناك صورة قديمة، نحذف الصورة القديمة
      if (newImageUrl != null &&
          oldImageUrl != null &&
          oldImageUrl.contains('drive.google.com')) {
        // تأكد من أن الصورة القديمة ليست الصورة الافتراضية
        if (!oldImageUrl.contains('ui-avatars.com')) {
          LoggerService.info(
            'محاولة حذف الصورة القديمة: $oldImageUrl',
            tag: 'StorageRepository',
          );

          // حذف الصورة القديمة بشكل غير متزامن (لا ننتظر النتيجة)
          deleteFile(oldImageUrl).then((success) {
            if (success) {
              LoggerService.info(
                'تم حذف الصورة القديمة بنجاح',
                tag: 'StorageRepository',
              );
            } else {
              LoggerService.warning(
                'فشل حذف الصورة القديمة',
                tag: 'StorageRepository',
              );
            }
          });
        }
      }

      return newImageUrl;
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث صورة البروفايل',
        error: e,
        tag: 'StorageRepository',
      );
      return null;
    }
  }

  /// رفع صورة مضغوطة إلى التخزين
  @override
  Future<String?> uploadCompressedImage(File file, String path, {
    int quality = AppConstants.defaultImageQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      LoggerService.info(
        'بدء ضغط ورفع الصورة: ${file.path}',
        tag: 'StorageRepository',
      );

      // ضغط الصورة
      final File? compressedFile = await ImageCompressionService.compressImage(
        file: file,
        quality: quality,
        maxWidth: maxWidth ?? AppConstants.defaultImageMaxWidth,
        maxHeight: maxHeight ?? AppConstants.defaultImageMaxHeight,
      );

      if (compressedFile == null) {
        LoggerService.warning(
          'فشل ضغط الصورة، سيتم رفع الصورة الأصلية',
          tag: 'StorageRepository',
        );
        return await _googleDriveService.uploadFile(file, path);
      }

      // رفع الصورة المضغوطة
      final String? imageUrl = await _googleDriveService.uploadFile(compressedFile, path);

      LoggerService.info(
        'تم رفع الصورة المضغوطة بنجاح: $imageUrl',
        tag: 'StorageRepository',
      );

      return imageUrl;
    } catch (e) {
      LoggerService.error(
        'خطأ في ضغط ورفع الصورة',
        error: e,
        tag: 'StorageRepository',
      );

      // في حالة الخطأ، نحاول رفع الصورة الأصلية
      try {
        return await _googleDriveService.uploadFile(file, path);
      } catch (e) {
        LoggerService.error(
          'خطأ في رفع الصورة الأصلية',
          error: e,
          tag: 'StorageRepository',
        );
        return null;
      }
    }
  }

  /// رفع فيديو مضغوط إلى التخزين
  @override
  Future<String?> uploadCompressedVideo(File file, String path, {
    VideoQuality quality = VideoQuality.MediumQuality,
  }) async {
    try {
      LoggerService.info(
        'بدء ضغط ورفع الفيديو: ${file.path}',
        tag: 'StorageRepository',
      );

      // ضغط الفيديو
      final File? compressedFile = await VideoCompressionService.compressVideo(
        file: file,
        quality: quality,
      );

      if (compressedFile == null) {
        LoggerService.warning(
          'فشل ضغط الفيديو، سيتم رفع الفيديو الأصلي',
          tag: 'StorageRepository',
        );
        return await _googleDriveService.uploadFile(file, path);
      }

      // رفع الفيديو المضغوط
      final String? videoUrl = await _googleDriveService.uploadFile(compressedFile, path);

      LoggerService.info(
        'تم رفع الفيديو المضغوط بنجاح: $videoUrl',
        tag: 'StorageRepository',
      );

      // تنظيف الذاكرة المؤقتة لضغط الفيديو
      VideoCompressionService.cleanCache();

      return videoUrl;
    } catch (e) {
      LoggerService.error(
        'خطأ في ضغط ورفع الفيديو',
        error: e,
        tag: 'StorageRepository',
      );

      // في حالة الخطأ، نحاول رفع الفيديو الأصلي
      try {
        return await _googleDriveService.uploadFile(file, path);
      } catch (e) {
        LoggerService.error(
          'خطأ في رفع الفيديو الأصلي',
          error: e,
          tag: 'StorageRepository',
        );
        return null;
      }
    }
  }
}

/// مصنع مستودع التخزين
///
/// يوفر هذا المصنع طريقة لإنشاء نسخة من مستودع التخزين.
class StorageRepositoryFactory {
  /// إنشاء نسخة من مستودع التخزين
  static StorageRepository create() {
    return StorageRepository(GoogleDriveServiceFactory.create());
  }
}
