import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/collections.dart';
import '../../core/utils/logging/logger_service.dart';
import '../../domain/repositories/post_repository_interface.dart';
import '../../domain/repositories/storage_repository_interface.dart';
import '../models/community_forum/comment_model.dart';
import '../models/community_forum/post_model.dart';

/// مستودع المنشورات
///
/// ينفذ هذا المستودع واجهة `PostRepositoryInterface` للتعامل مع المنشورات في Firestore.
class PostRepository implements PostRepositoryInterface {
  final FirebaseFirestore _firestore;
  final StorageRepositoryInterface _storageRepository;
  final FirebaseAuth _auth;
  final Uuid _uuid;

  /// منشئ مستودع المنشورات
  ///
  /// المعلمات:
  /// - [firestore]: مثيل Firestore
  /// - [storageRepository]: مستودع التخزين
  /// - [auth]: مثيل FirebaseAuth
  PostRepository({
    required FirebaseFirestore firestore,
    required StorageRepositoryInterface storageRepository,
    required FirebaseAuth auth,
  })  : _firestore = firestore,
        _storageRepository = storageRepository,
        _auth = auth,
        _uuid = const Uuid();

  @override
  Future<List<PostModel>> getPosts({int limit = 10, String? lastPostId}) async {
    try {
      Query query = _firestore
          .collection(Collections.posts)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastPostId != null) {
        // الحصول على المستند الأخير للتحميل التدريجي
        final lastDoc = await _firestore
            .collection(Collections.posts)
            .doc(lastPostId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على المنشورات',
        error: e,
        tag: 'PostRepository',
      );
      return [];
    }
  }

  @override
  Future<PostModel?> getPost(String postId) async {
    try {
      final doc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (doc.exists) {
        return PostModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على المنشور',
        error: e,
        tag: 'PostRepository',
      );
      return null;
    }
  }

  @override
  Future<PostModel?> createPost({
    required String userId,
    required String userName,
    required String userImage,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? hashtags,
  }) async {
    try {
      // التحقق من وجود محتوى للمنشور
      if ((text == null || text.isEmpty) &&
          (imageFiles == null || imageFiles.isEmpty) &&
          videoFile == null) {
        LoggerService.warning(
          'محاولة إنشاء منشور فارغ',
          tag: 'PostRepository',
        );
        return null;
      }

      // إنشاء معرف فريد للمنشور
      final postId = _uuid.v4();

      // رفع الصور إذا وجدت
      List<String>? imageUrls;
      if (imageFiles != null && imageFiles.isNotEmpty) {
        imageUrls = [];
        for (final imageFile in imageFiles) {
          final imagePath = 'posts/$postId/images/${_uuid.v4()}.jpg';
          final imageUrl =
              await _storageRepository.uploadFile(imageFile, imagePath);
          if (imageUrl != null) {
            imageUrls.add(imageUrl);
          }
        }
      }

      // رفع الفيديو إذا وجد
      String? videoUrl;
      if (videoFile != null) {
        final videoPath = 'posts/$postId/video/${_uuid.v4()}.mp4';
        videoUrl = await _storageRepository.uploadFile(videoFile, videoPath);
      }

      // استخراج الوسوم من النص إذا لم يتم توفيرها
      List<String> extractedHashtags = [];
      if (hashtags == null && text != null && text.isNotEmpty) {
        // استخراج الوسوم باستخدام تعبير نمطي
        final RegExp hashtagRegExp = RegExp(r'#(\w+)', multiLine: true);
        final matches = hashtagRegExp.allMatches(text);
        extractedHashtags = matches.map((match) => match.group(1)!).toSet().toList();
      }

      // إنشاء نموذج المنشور
      final now = DateTime.now().toIso8601String();
      final post = PostModel(
        id: postId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        text: text,
        images: imageUrls,
        video: videoUrl,
        createdAt: now,
        lastUpdated: now,
        likesCount: 0,
        commentsCount: 0,
        hashtags: hashtags ?? extractedHashtags,
        viewsCount: 0,
      );

      // حفظ المنشور في Firestore
      await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .set(post.toMap());

      LoggerService.info(
        'تم إنشاء منشور جديد: $postId',
        tag: 'PostRepository',
      );

      return post;
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء المنشور',
        error: e,
        tag: 'PostRepository',
      );
      return null;
    }
  }

  @override
  Future<PostModel?> updatePost({
    required String postId,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? deleteImages,
    bool deleteVideo = false,
  }) async {
    try {
      // الحصول على المنشور الحالي
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة تحديث منشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return null;
      }

      final post = PostModel.fromDocument(postDoc);

      // التحقق من أن المستخدم الحالي هو صاحب المنشور
      final currentUser = _auth.currentUser;
      if (currentUser == null || currentUser.uid != post.userId) {
        LoggerService.warning(
          'محاولة تحديث منشور بواسطة مستخدم غير مصرح له',
          tag: 'PostRepository',
        );
        return null;
      }

      // تحديث البيانات
      Map<String, dynamic> updates = {
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      // تحديث النص إذا تم توفيره
      if (text != null) {
        updates['text'] = text;
      }

      // حذف الصور المحددة
      List<String> updatedImages = List.from(post.images ?? []);
      if (deleteImages != null && deleteImages.isNotEmpty) {
        for (final imageUrl in deleteImages) {
          if (updatedImages.contains(imageUrl)) {
            await _storageRepository.deleteFile(imageUrl);
            updatedImages.remove(imageUrl);
          }
        }
        updates['images'] = updatedImages;
      }

      // حذف الفيديو إذا تم طلب ذلك
      if (deleteVideo && post.video != null) {
        await _storageRepository.deleteFile(post.video!);
        updates['video'] = null;
      }

      // رفع صور جديدة
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (final imageFile in imageFiles) {
          final imagePath = 'posts/$postId/images/${_uuid.v4()}.jpg';
          final imageUrl =
              await _storageRepository.uploadFile(imageFile, imagePath);
          if (imageUrl != null) {
            updatedImages.add(imageUrl);
          }
        }
        updates['images'] = updatedImages;
      }

      // رفع فيديو جديد
      if (videoFile != null) {
        // حذف الفيديو القديم إذا وجد
        if (post.video != null) {
          await _storageRepository.deleteFile(post.video!);
        }
        final videoPath = 'posts/$postId/video/${_uuid.v4()}.mp4';
        final videoUrl =
            await _storageRepository.uploadFile(videoFile, videoPath);
        updates['video'] = videoUrl;
      }

      // تحديث المنشور في Firestore
      await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .update(updates);

      // الحصول على المنشور المحدث
      final updatedDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      final updatedPost = PostModel.fromDocument(updatedDoc);

      LoggerService.info(
        'تم تحديث المنشور: $postId',
        tag: 'PostRepository',
      );

      return updatedPost;
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث المنشور',
        error: e,
        tag: 'PostRepository',
      );
      return null;
    }
  }

  // تم استبدال هذه الدالة بالدالة الجديدة التي تتطلب معرف المستخدم أيضًا

  @override
  Future<bool> likePost(String postId, String userId) async {
    try {
      // التحقق من وجود المنشور
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة الإعجاب بمنشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return false;
      }

      final post = PostModel.fromDocument(postDoc);

      // التحقق مما إذا كان المستخدم قد أعجب بالمنشور بالفعل
      if (post.likedBy.contains(userId)) {
        LoggerService.warning(
          'المستخدم $userId أعجب بالمنشور $postId بالفعل',
          tag: 'PostRepository',
        );
        return true; // نعتبرها عملية ناجحة لأن الحالة النهائية هي ما نريده
      }

      // إضافة المستخدم إلى قائمة المعجبين وزيادة عدد الإعجابات
      final updatedLikedBy = List<String>.from(post.likedBy)..add(userId);
      await _firestore.collection(Collections.posts).doc(postId).update({
        'likedBy': updatedLikedBy,
        'likesCount': post.likesCount + 1,
      });

      LoggerService.info(
        'المستخدم $userId أعجب بالمنشور $postId',
        tag: 'PostRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في الإعجاب بالمنشور',
        error: e,
        tag: 'PostRepository',
      );
      return false;
    }
  }

  @override
  Future<bool> unlikePost(String postId, String userId) async {
    try {
      // التحقق من وجود المنشور
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة إلغاء الإعجاب بمنشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return false;
      }

      final post = PostModel.fromDocument(postDoc);

      // التحقق مما إذا كان المستخدم قد أعجب بالمنشور
      if (!post.likedBy.contains(userId)) {
        LoggerService.warning(
          'المستخدم $userId لم يعجب بالمنشور $postId من قبل',
          tag: 'PostRepository',
        );
        return true; // نعتبرها عملية ناجحة لأن الحالة النهائية هي ما نريده
      }

      // إزالة المستخدم من قائمة المعجبين وتقليل عدد الإعجابات
      final updatedLikedBy = List<String>.from(post.likedBy)..remove(userId);
      await _firestore.collection(Collections.posts).doc(postId).update({
        'likedBy': updatedLikedBy,
        'likesCount': post.likesCount - 1,
      });

      LoggerService.info(
        'المستخدم $userId ألغى إعجابه بالمنشور $postId',
        tag: 'PostRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في إلغاء الإعجاب بالمنشور',
        error: e,
        tag: 'PostRepository',
      );
      return false;
    }
  }

  @override
  Future<CommentModel?> addComment({
    required String postId,
    required String userId,
    required String userName,
    required String userImage,
    required String text,
  }) async {
    try {
      // التحقق من وجود المنشور
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة إضافة تعليق على منشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return null;
      }

      final post = PostModel.fromDocument(postDoc);

      // إنشاء معرف فريد للتعليق
      final commentId = _uuid.v4();
      final now = DateTime.now().toIso8601String();

      // إنشاء نموذج التعليق
      final comment = CommentModel(
        id: commentId,
        postId: postId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        text: text,
        createdAt: now,
      );

      // حفظ التعليق في Firestore
      await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .set(comment.toMap());

      // تحديث عدد التعليقات في المنشور
      await _firestore.collection(Collections.posts).doc(postId).update({
        'commentsCount': post.commentsCount + 1,
      });

      LoggerService.info(
        'تم إضافة تعليق جديد: $commentId على المنشور: $postId',
        tag: 'PostRepository',
      );

      return comment;
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة تعليق',
        error: e,
        tag: 'PostRepository',
      );
      return null;
    }
  }

  @override
  Future<bool> incrementPostViews(String postId) async {
    try {
      // التحقق من وجود المنشور
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة زيادة عدد مشاهدات منشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return false;
      }

      final post = PostModel.fromDocument(postDoc);

      // زيادة عدد المشاهدات
      await _firestore.collection(Collections.posts).doc(postId).update({
        'viewsCount': post.viewsCount + 1,
      });

      LoggerService.info(
        'تم زيادة عدد مشاهدات المنشور: $postId',
        tag: 'PostRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في زيادة عدد مشاهدات المنشور',
        error: e,
        tag: 'PostRepository',
      );
      return false;
    }
  }

  @override
  Future<List<PostModel>> searchPosts({
    required String query,
    int limit = 20,
  }) async {
    try {
      LoggerService.info(
        'البحث عن المنشورات باستخدام المصطلح: $query',
        tag: 'PostRepository',
      );

      // تنظيف مصطلح البحث
      final String cleanQuery = query.trim().toLowerCase();
      if (cleanQuery.isEmpty) {
        return [];
      }

      // البحث في نص المنشور
      final textQuerySnapshot = await _firestore
          .collection(Collections.posts)
          .where('text', isGreaterThanOrEqualTo: cleanQuery)
          .where('text', isLessThanOrEqualTo: cleanQuery + '\uf8ff')
          .limit(limit)
          .get();

      // البحث في اسم المستخدم
      final userNameQuerySnapshot = await _firestore
          .collection(Collections.posts)
          .where('userName', isGreaterThanOrEqualTo: cleanQuery)
          .where('userName', isLessThanOrEqualTo: cleanQuery + '\uf8ff')
          .limit(limit)
          .get();

      // البحث في الوسوم
      final hashtagQuerySnapshot = await _firestore
          .collection(Collections.posts)
          .where('hashtags', arrayContains: cleanQuery)
          .limit(limit)
          .get();

      // جمع النتائج وإزالة التكرار
      final Set<String> postIds = {};
      final List<PostModel> results = [];

      // إضافة نتائج البحث في النص
      for (final doc in textQuerySnapshot.docs) {
        if (!postIds.contains(doc.id)) {
          postIds.add(doc.id);
          results.add(PostModel.fromDocument(doc));
        }
      }

      // إضافة نتائج البحث في اسم المستخدم
      for (final doc in userNameQuerySnapshot.docs) {
        if (!postIds.contains(doc.id)) {
          postIds.add(doc.id);
          results.add(PostModel.fromDocument(doc));
        }
      }

      // إضافة نتائج البحث في الوسوم
      for (final doc in hashtagQuerySnapshot.docs) {
        if (!postIds.contains(doc.id)) {
          postIds.add(doc.id);
          results.add(PostModel.fromDocument(doc));
        }
      }

      // ترتيب النتائج حسب تاريخ الإنشاء (الأحدث أولاً)
      results.sort((a, b) => DateTime.parse(b.createdAt).compareTo(DateTime.parse(a.createdAt)));

      LoggerService.info(
        'تم العثور على ${results.length} منشور للمصطلح: $query',
        tag: 'PostRepository',
      );

      return results;
    } catch (e) {
      LoggerService.error(
        'خطأ في البحث عن المنشورات',
        error: e,
        tag: 'PostRepository',
      );
      return [];
    }
  }

  @override
  Future<bool> deletePost(String postId, String userId) async {
    try {
      LoggerService.info(
        'محاولة حذف المنشور: $postId بواسطة المستخدم: $userId',
        tag: 'PostRepository',
      );

      // الحصول على المنشور
      final postDoc = await _firestore.collection(Collections.posts).doc(postId).get();

      // التحقق من وجود المنشور
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة حذف منشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return false;
      }

      // تحويل المستند إلى نموذج
      final post = PostModel.fromDocument(postDoc);

      // التحقق من أن المستخدم هو منشئ المنشور
      if (post.userId != userId) {
        LoggerService.warning(
          'محاولة حذف منشور بواسطة مستخدم غير مصرح له: $userId',
          tag: 'PostRepository',
        );
        return false;
      }

      // حذف الصور من Google Drive إذا وجدت
      if (post.images != null && post.images!.isNotEmpty) {
        for (final imageUrl in post.images!) {
          // استخراج معرف الملف من الرابط
          final fileId = _extractFileIdFromUrl(imageUrl);
          if (fileId != null) {
            try {
              await _storageRepository.deleteFile(fileId);
              LoggerService.info(
                'تم حذف الصورة من Google Drive: $fileId',
                tag: 'PostRepository',
              );
            } catch (e) {
              LoggerService.error(
                'خطأ في حذف الصورة من Google Drive: $fileId',
                error: e,
                tag: 'PostRepository',
              );
              // نستمر في حذف بقية الصور والمنشور حتى لو فشل حذف إحدى الصور
            }
          }
        }
      }

      // حذف الفيديو من Google Drive إذا وجد
      if (post.video != null && post.video!.isNotEmpty) {
        final fileId = _extractFileIdFromUrl(post.video!);
        if (fileId != null) {
          try {
            await _storageRepository.deleteFile(fileId);
            LoggerService.info(
              'تم حذف الفيديو من Google Drive: $fileId',
              tag: 'PostRepository',
            );
          } catch (e) {
            LoggerService.error(
              'خطأ في حذف الفيديو من Google Drive: $fileId',
              error: e,
              tag: 'PostRepository',
            );
            // نستمر في حذف المنشور حتى لو فشل حذف الفيديو
          }
        }
      }

      // حذف التعليقات المرتبطة بالمنشور
      final commentsSnapshot = await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .collection('comments')
          .get();

      // حذف كل تعليق في مجموعة التعليقات
      final batch = _firestore.batch();
      for (final commentDoc in commentsSnapshot.docs) {
        batch.delete(commentDoc.reference);
      }

      // حذف المنشور نفسه
      batch.delete(_firestore.collection(Collections.posts).doc(postId));

      // تنفيذ عمليات الحذف في دفعة واحدة
      await batch.commit();

      LoggerService.info(
        'تم حذف المنشور بنجاح: $postId',
        tag: 'PostRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف المنشور: $postId',
        error: e,
        tag: 'PostRepository',
      );
      return false;
    }
  }

  /// استخراج معرف الملف من رابط Google Drive
  String? _extractFileIdFromUrl(String url) {
    try {
      // نمط رابط Google Drive: https://drive.google.com/uc?id=FILE_ID
      final uri = Uri.parse(url);
      if (uri.host == 'drive.google.com' && uri.path == '/uc') {
        return uri.queryParameters['id'];
      }
      return null;
    } catch (e) {
      LoggerService.error(
        'خطأ في استخراج معرف الملف من الرابط: $url',
        error: e,
        tag: 'PostRepository',
      );
      return null;
    }
  }

  @override
  Future<List<CommentModel>> getComments({
    required String postId,
    int limit = 20,
    String? lastCommentId,
  }) async {
    try {
      print('جاري جلب التعليقات من Firestore للمنشور: $postId');
      Query query = _firestore
          .collection(Collections.posts)
          .doc(postId)
          .collection('comments')
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastCommentId != null) {
        // الحصول على المستند الأخير للتحميل التدريجي
        final lastDoc = await _firestore
            .collection(Collections.posts)
            .doc(postId)
            .collection('comments')
            .doc(lastCommentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      final comments = querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();

      print('تم جلب ${comments.length} تعليق من Firestore للمنشور: $postId');
      return comments;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على التعليقات',
        error: e,
        tag: 'PostRepository',
      );
      print('خطأ في جلب التعليقات للمنشور: $postId - ${e.toString()}');
      return [];
    }
  }

  @override
  Future<bool> deleteComment(String postId, String commentId) async {
    try {
      // التحقق من وجود المنشور
      final postDoc =
          await _firestore.collection(Collections.posts).doc(postId).get();
      if (!postDoc.exists) {
        LoggerService.warning(
          'محاولة حذف تعليق من منشور غير موجود: $postId',
          tag: 'PostRepository',
        );
        return false;
      }

      // التحقق من وجود التعليق
      final commentDoc = await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .get();
      if (!commentDoc.exists) {
        LoggerService.warning(
          'محاولة حذف تعليق غير موجود: $commentId',
          tag: 'PostRepository',
        );
        return false;
      }

      final comment = CommentModel.fromDocument(commentDoc);
      final post = PostModel.fromDocument(postDoc);

      // التحقق من أن المستخدم الحالي هو صاحب التعليق أو صاحب المنشور
      final currentUser = _auth.currentUser;
      if (currentUser == null ||
          (currentUser.uid != comment.userId &&
              currentUser.uid != post.userId)) {
        LoggerService.warning(
          'محاولة حذف تعليق بواسطة مستخدم غير مصرح له',
          tag: 'PostRepository',
        );
        return false;
      }

      // حذف التعليق
      await _firestore
          .collection(Collections.posts)
          .doc(postId)
          .collection('comments')
          .doc(commentId)
          .delete();

      // تحديث عدد التعليقات في المنشور
      await _firestore.collection(Collections.posts).doc(postId).update({
        'commentsCount': post.commentsCount - 1,
      });

      LoggerService.info(
        'تم حذف التعليق: $commentId من المنشور: $postId',
        tag: 'PostRepository',
      );

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف التعليق',
        error: e,
        tag: 'PostRepository',
      );
      return false;
    }
  }
}

/// مصنع مستودع المنشورات
///
/// يوفر هذا المصنع طريقة لإنشاء نسخة من مستودع المنشورات.
class PostRepositoryFactory {
  /// إنشاء نسخة من مستودع المنشورات
  static PostRepository create(StorageRepositoryInterface storageRepository) {
    return PostRepository(
      firestore: FirebaseFirestore.instance,
      storageRepository: storageRepository,
      auth: FirebaseAuth.instance,
    );
  }
}
