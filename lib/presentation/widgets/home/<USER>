import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:flutter/material.dart';

class CardHomeItem extends StatelessWidget {
  const CardHomeItem({
    super.key,
    required this.text,
    required this.icon,
    required this.router,
  });

  final String text;
  final IconData icon;
  final String router;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, router);
      },
      child: Card(
        color: Colors.white,
        elevation: 10,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: AssetsColors.dufaultGreencolor,
              ),
              const SizedBox(height: 10),
              Text(
                text,
                style: TextStyles.of(context).bodyMedium(
                    fontSize: 14,
                    // fontWeight: FontWeight.bold,
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.black),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
