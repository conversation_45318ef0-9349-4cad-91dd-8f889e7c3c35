import 'package:agriculture/imports.dart';
import 'package:flutter/material.dart';

class TextService extends StatelessWidget {
  const TextService({
    super.key,
    required this.textLef,
    this.textReit,
  });

  final String textLef;
  final String? textReit;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            textLef,
            style: TextStyles.of(context).headlineSmall(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          if (textReit != null)
            Text(
              textReit!,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
            ),
        ],
      ),
    );
  }
}
