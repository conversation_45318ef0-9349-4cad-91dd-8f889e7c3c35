import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../imports.dart';
import '../shared/user_profile_avatar.dart';

/// مكون الأبار الرئيسي لصفحة الهوم
///
/// يحتوي على شعار التطبيق وأيقونة الإشعارات وصورة المستخدم
/// ويمكن تخصيصه حسب الحاجة
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// إنشاء مكون الأبار الرئيسي
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: AssetsColors.dufaultGreencolor,
        statusBarIconBrightness: Brightness.light,
      ),
      leadingWidth: 120,
      backgroundColor: Colors.tealAccent.shade700,
      leading: _buildWeatherWidget(),
      title: _buildAppTitle(context),
      actions: _buildActions(context),
    );
  }

  /// بناء مكون الطقس في الجانب الأيسر
  Widget _buildWeatherWidget() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Container(
        clipBehavior: Clip.antiAlias,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(Icons.light_mode_outlined),
            const SizedBox(width: 10.0),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '20 C',
                  style: TextStyle(
                    fontSize: 13.0,
                    color: Colors.black,
                    height: .7,
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  'غائم جزئي',
                  style: TextStyle(
                    fontFamily: AssetsFonts.sultan,
                    fontSize: 13.0,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان التطبيق
  Widget _buildAppTitle(BuildContext context) {
    return Text(
      'الارض الطيبة',
      style: TextStyles.of(context).titleLarge().copyWith(
            color: Colors.white,
            fontFamily: AssetsFonts.sultan,
            fontSize: 22.0,
          ),
    );
  }

  /// بناء الإجراءات في الجانب الأيمن (الإشعارات وصورة المستخدم)
  List<Widget> _buildActions(BuildContext context) {
    return [
      // أيقونة الإشعارات
      IconButton(
        onPressed: () {
          // يمكن إضافة التنقل إلى صفحة الإشعارات هنا
        },
        icon: Icon(
          Icons.notifications_sharp,
          size: 30,
          color: Colors.white,
        ),
      ),

      // صورة المستخدم
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: UserProfileAvatar(
          onTap: () {
            // يمكن إضافة التنقل إلى صفحة الملف الشخصي هنا
          },
        ),
      ),
    ];
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
