import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';
import 'package:agriculture/presentation/widgets/shared/enhanced_video_player.dart';
import 'package:agriculture/presentation/widgets/shared/full_screen_image_viewer.dart';
import 'package:agriculture/presentation/widgets/shared/multiple_images_grid.dart';
import 'package:flutter/material.dart';

/// مكون عرض وسائط المنشور (صور وفيديو)
class PostMedia extends StatefulWidget {
  /// قائمة روابط الصور
  final List<String>? images;

  /// رابط الفيديو
  final String? video;

  const PostMedia({
    super.key,
    this.images,
    this.video,
  });

  @override
  State<PostMedia> createState() => _PostMediaState();
}

class _PostMediaState extends State<PostMedia> {
  @override
  Widget build(BuildContext context) {
    // إذا كان هناك فيديو، نعرضه
    if (widget.video != null && widget.video!.isNotEmpty) {
      return Container(
        height: 250, // ارتفاع ثابت للفيديو والصور
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.black,
          // إضافة ظل لتحسين المظهر
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        clipBehavior:
            Clip.antiAlias, // لقص المحتوى الذي يتجاوز الحواف المستديرة
        child: GestureDetector(
          onTap: () => context.openFullScreenVideo(widget.video!),
          child: Stack(
            fit: StackFit.expand, // لملء الحاوية بالكامل
            children: [
              // مشغل الفيديو المحسن - يملأ الحاوية بالكامل
              EnhancedVideoPlayer(
                videoUrl: widget.video!,
                autoPlay: false,
                showControls: true,
                allowFullScreen: true,
                fillContainer: true, // ملء الحاوية بالكامل
              ),

              // زر تشغيل الفيديو بملء الشاشة
              Positioned(
                bottom: 10,
                right: 10,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.fullscreen,
                      color: Colors.white,
                    ),
                    onPressed: () => context.openFullScreenVideo(widget.video!),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // إذا كان هناك صور، نعرضها
    if (widget.images != null && widget.images!.isNotEmpty) {
      return GestureDetector(
        onTap: () => context.openFullScreenImageGallery(widget.images!),
        child: MultipleImagesGrid(
          imageUrls: widget.images!,
          maxHeight: 250, // نفس ارتفاع الفيديو للتناسق
          borderRadius: 8,
          spacing: 2,
        ),
      );
    }

    // إذا لم يكن هناك صور أو فيديو
    return const SizedBox.shrink();
  }
}
