import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/utils/extensions/date_time_extensions.dart';
import '../../../data/models/community_forum/comment_model.dart';
import '../../bloc/community_forum/posts_cubit.dart';
import '../../bloc/community_forum/posts_state.dart';
import '../../pages/community_forum/comments_page.dart';
import '../shared/cachd_net_image.dart';

/// مكون عرض موجز للتعليقات
///
/// يستخدم هذا المكون لعرض موجز للتعليقات الأخيرة في بطاقة المنشور.
class CommentPreview extends StatelessWidget {
  /// معرف المنشور
  final String postId;

  /// عدد التعليقات الكلي
  final int commentsCount;

  /// عدد التعليقات المراد عرضها في الموجز
  final int previewCount;

  /// دالة يتم استدعاؤها عند النقر على زر "عرض المزيد"
  final VoidCallback? onViewMore;

  /// دالة يتم استدعاؤها عند النقر على زر "إضافة تعليق"
  final VoidCallback? onAddComment;

  /// منشئ مكون عرض موجز للتعليقات
  const CommentPreview({
    super.key,
    required this.postId,
    required this.commentsCount,
    this.previewCount = 2,
    this.onViewMore,
    this.onAddComment,
  });

  @override
  Widget build(BuildContext context) {
    // إذا لم يكن هناك تعليقات، نعرض زر إضافة تعليق فقط
    if (commentsCount == 0) {
      return Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: InkWell(
          onTap: onAddComment,
          child: Row(
            children: [
              const Icon(Icons.add_comment, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text(
                'إضافة تعليق',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
              ),
            ],
          ),
        ),
      );
    }

    // تحميل التعليقات إذا لم تكن محملة بالفعل
    return BlocBuilder<PostsCubit, PostsState>(
      builder: (context, state) {
        // إذا كانت التعليقات قيد التحميل، نعرض مؤشر تحميل
        if (state is CommentsLoading && state.postId == postId) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          );
        }

        // إذا حدث خطأ في تحميل التعليقات، نعرض رسالة الخطأ
        if (state is CommentsError && state.postId == postId) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'خطأ في تحميل التعليقات',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
            ),
          );
        }

        // إذا تم تحميل التعليقات بنجاح، نعرضها
        if (state is CommentsLoaded && state.postId == postId) {
          final comments = state.comments;

          // إذا لم يكن هناك تعليقات، نعرض زر إضافة تعليق فقط
          if (comments.isEmpty) {
            return Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: InkWell(
                onTap: onAddComment,
                child: Row(
                  children: [
                    const Icon(Icons.add_comment, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      'إضافة تعليق',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey,
                          ),
                    ),
                  ],
                ),
              ),
            );
          }

          // عرض موجز للتعليقات
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عرض التعليقات الأخيرة (حسب عدد previewCount)
              ...comments.take(previewCount).map((comment) => _buildCommentItem(context, comment)),

              // زر عرض المزيد من التعليقات
              if (commentsCount > previewCount)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: InkWell(
                    onTap: onViewMore,
                    child: Text(
                      'عرض جميع التعليقات ($commentsCount)',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AssetsColors.primary,
                          ),
                    ),
                  ),
                ),

              // زر إضافة تعليق
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: InkWell(
                  onTap: onAddComment,
                  child: Row(
                    children: [
                      const Icon(Icons.add_comment, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'إضافة تعليق',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        // إذا لم يتم تحميل التعليقات بعد، نعرض زر عرض التعليقات
        if (!(state is CommentsLoaded && state.postId == postId)) {
          // عرض زر عرض التعليقات بدلاً من تحميلها تلقائيًا
          return Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: InkWell(
              onTap: onViewMore,
              child: Text(
                'عرض جميع التعليقات ($commentsCount)',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AssetsColors.primary,
                    ),
              ),
            ),
          );
        }

        // حالة افتراضية (لن يتم الوصول إليها)
        return const SizedBox.shrink();
      },
    );
  }

  /// بناء عنصر تعليق
  Widget _buildCommentItem(BuildContext context, CommentModel comment) {
    // تحويل التاريخ إلى كائن DateTime
    final DateTime commentDate = DateTime.parse(comment.createdAt);

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم
          ClipOval(
            child: CachedNetImage(
              imageUrl: comment.userImage,
              width: 24,
              height: 24,
              fit: BoxFit.cover,
              lazyLoad: true,
              memCacheWidth: 48,
              memCacheHeight: 48,
            ),
          ),
          const SizedBox(width: 8),

          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم المستخدم والتاريخ
                Row(
                  children: [
                    Text(
                      comment.userName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      commentDate.dateTime,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey,
                            fontSize: 10,
                          ),
                    ),
                  ],
                ),

                // نص التعليق
                Text(
                  comment.text,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
