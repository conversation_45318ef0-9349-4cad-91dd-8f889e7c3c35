import 'package:flutter/material.dart';

class CommentAndLike extends StatelessWidget {
  /// عدد الإعجابات
  final int likesCount;

  /// عدد التعليقات
  final int commentsCount;

  /// ما إذا كان المستخدم قد أعجب بالمنشور
  final bool isLiked;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final VoidCallback? onLike;

  /// دالة يتم استدعاؤها عند النقر على زر التعليق
  final VoidCallback? onComment;

  /// دالة يتم استدعاؤها عند النقر على زر المشاركة
  final VoidCallback? onShare;

  const CommentAndLike({
    super.key,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.isLiked = false,
    this.onLike,
    this.onComment,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
        ),
      ),
      child: Row(
        children: [
          // زر الإعجاب
          Expanded(
            child: InkWell(
              onTap: onLike,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.thumb_up,
                    size: 22.0,
                    color: isLiked ? Colors.redAccent : Colors.grey,
                  ),
                  const SizedBox(width: 5.0),
                  Text(
                    '$likesCount اعجاب',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),

          // خط فاصل عمودي
          Container(
            height: 25,
            width: 1,
            color: Colors.grey.withOpacity(0.3),
          ),

          // زر التعليق
          Expanded(
            child: InkWell(
              onTap: onComment,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.chat,
                    size: 22.0,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 5.0),
                  Text(
                    '$commentsCount تعليق',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),

          // خط فاصل عمودي
          Container(
            height: 25,
            width: 1,
            color: Colors.grey.withOpacity(0.3),
          ),

          // زر المشاركة
          Expanded(
            child: InkWell(
              onTap: onShare,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.share,
                    size: 22.0,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 5.0),
                  Text(
                    'مشاركة',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
