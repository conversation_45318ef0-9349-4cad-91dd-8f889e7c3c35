import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

import '../../pages/home/<USER>';

class AppBottomNavigationBar extends StatefulWidget {
  const AppBottomNavigationBar({Key? key}) : super(key: key);

  @override
  State<AppBottomNavigationBar> createState() => _AppBottomNavigationBar();
}

class _AppBottomNavigationBar extends State<AppBottomNavigationBar> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: IndexedStack(
          index: _currentIndex,
          children: AppPages.bottomNavItems
              .map((item) => AppPages.routes[item['route']]!(context))
              .toList(),
        ),
        bottomNavigationBar: _buildCustomBottomNavBar(),
      ),
    );
  }

  Widget _buildCustomBottomNavBar() {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) => setState(() => _currentIndex = index),
          type: BottomNavigationBarType.fixed,
          selectedFontSize: 12,
          unselectedFontSize: 10,
          backgroundColor: Colors.white,
          selectedItemColor: AssetsColors.dufaultGreencolor,
          unselectedItemColor: Colors.grey[600],
          items: AppPages.bottomNavItems.map((item) {
            return BottomNavigationBarItem(
              icon: Icon(item['icon']),
              activeIcon: Icon(item['activeIcon']),
              label: item['label'],
            );
          }).toList(),
        ),
      ),
    );
  }
}
