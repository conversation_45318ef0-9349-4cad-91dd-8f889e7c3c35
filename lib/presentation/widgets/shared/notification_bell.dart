import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/services/notification_service.dart';
import 'package:agriculture/core/shared/services/notification_storage_service.dart';
import 'package:agriculture/core/shared/services/audio_notification_service.dart';
import 'package:agriculture/data/models/notification/notification_model.dart';

/// زر الجرس مع عداد الإشعارات
class NotificationBell extends StatefulWidget {
  /// معرف المستخدم الحالي
  final String? userId;
  
  /// دالة عند النقر على الإشعار
  final Function(NotificationModel)? onNotificationTap;

  const NotificationBell({
    super.key,
    this.userId,
    this.onNotificationTap,
  });

  @override
  State<NotificationBell> createState() => _NotificationBellState();
}

class _NotificationBellState extends State<NotificationBell> {
  final NotificationService _notificationService = NotificationService();
  int _unreadCount = 0;
  bool _soundEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  /// تهيئة خدمة الإشعارات المحسنة
  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
    await _loadStoredNotifications();
    await _loadSoundSettings();
    _updateUnreadCount();
  }

  /// تحميل الإشعارات المحفوظة
  Future<void> _loadStoredNotifications() async {
    // تحديث العداد فقط - لا نحتاج لحفظ القائمة
    await _updateUnreadCount();
  }

  /// تحميل إعدادات الصوت
  Future<void> _loadSoundSettings() async {
    final soundEnabled = await NotificationStorageService.isSoundEnabled();
    setState(() {
      _soundEnabled = soundEnabled;
    });
  }

  /// تحديث عداد الإشعارات غير المقروءة المحسن
  Future<void> _updateUnreadCount() async {
    final storedUnreadCount = await NotificationStorageService.getUnreadCount();
    final serviceUnreadCount = widget.userId != null
        ? _notificationService.getUnreadCountForUser(widget.userId!)
        : _notificationService.getUnreadCount();

    setState(() {
      _unreadCount = storedUnreadCount + serviceUnreadCount;
    });
  }

  /// تشغيل صوت الإشعار المحسن
  Future<void> _playNotificationSound() async {
    if (_soundEnabled) {
      await AudioNotificationService.playDefaultNotification();
    }
  }

  /// إضافة إشعار جديد مع صوت
  /// [notification] بيانات الإشعار
  Future<void> addNotificationWithSound(Map<String, dynamic> notification) async {
    // حفظ الإشعار
    await NotificationStorageService.saveNotification(notification);

    // تشغيل الصوت المناسب
    if (_soundEnabled) {
      await AudioNotificationService.playNotificationByType(
        notification['type'] ?? 'default'
      );
    }

    // تحديث العداد والواجهة
    await _loadStoredNotifications();
    await _updateUnreadCount();
  }



  /// تحديد جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    if (!mounted) return;

    await NotificationStorageService.markAllAsRead();
    await _updateUnreadCount();
    await _loadStoredNotifications();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديد جميع الإشعارات كمقروءة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// عرض قائمة الإشعارات
  void _showNotificationsList() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildNotificationsBottomSheet(),
    );
  }

  /// بناء قائمة الإشعارات
  Widget _buildNotificationsBottomSheet() {
    final notifications = widget.userId != null
        ? _notificationService.getNotificationsForUser(widget.userId!)
        : _notificationService.getAllNotifications();

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // رأس القائمة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AssetsColors.dufaultGreencolor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                Icon(Icons.notifications, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'الإشعارات',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (_unreadCount > 0) ...[
                  TextButton.icon(
                    onPressed: _markAllAsRead,
                    icon: const Icon(Icons.done_all, color: Colors.white, size: 18),
                    label: const Text(
                      'تحديد الكل كمقروء',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ],
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // قائمة الإشعارات
          Expanded(
            child: notifications.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notifications[index];
                      return _buildNotificationCard(notification);
                    },
                  ),
          ),

          // أزرار الإعدادات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(top: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showNotificationSettings,
                    icon: const Icon(Icons.settings, size: 18),
                    label: const Text('إعدادات الإشعارات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _clearAllNotifications,
                  icon: const Icon(Icons.clear_all, size: 18),
                  label: const Text('مسح الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الإشعار
  Widget _buildNotificationCard(NotificationModel notification) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: notification.isRead ? Colors.white : Colors.blue[50],
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getNotificationColor(notification.type),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.body,
              style: const TextStyle(fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(notification.timestamp),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: notification.isRead
            ? null
            : Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
        onTap: () => _onNotificationTap(notification),
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// النقر على الإشعار
  Future<void> _onNotificationTap(NotificationModel notification) async {
    // تحديث حالة القراءة
    if (!notification.isRead) {
      await _notificationService.markAsRead(notification.id);
      await _updateUnreadCount();
    }

    // استدعاء دالة المعالجة الخارجية
    if (widget.onNotificationTap != null) {
      widget.onNotificationTap!(notification);
    }

    // إغلاق القائمة
    if (mounted) {
      Navigator.pop(context);
    }
  }



  /// مسح جميع الإشعارات
  Future<void> _clearAllNotifications() async {
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المسح'),
        content: const Text('هل تريد مسح جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('مسح', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      await _notificationService.clearAllNotifications();
      await _updateUnreadCount();

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مسح جميع الإشعارات'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// إعدادات الإشعارات
  void _showNotificationSettings() {
    Navigator.pop(context);
    // يمكن إضافة شاشة إعدادات مفصلة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إعدادات الإشعارات قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// الحصول على لون الإشعار حسب النوع
  Color _getNotificationColor(String type) {
    switch (type) {
      case 'consultation_reply':
        return Colors.green;
      case 'new_consultation':
        return Colors.blue;
      case 'weather_alert':
        return Colors.orange;
      case 'pest_alert':
        return Colors.red;
      case 'irrigation_reminder':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'consultation_reply':
        return Icons.reply;
      case 'new_consultation':
        return Icons.help;
      case 'weather_alert':
        return Icons.cloud;
      case 'pest_alert':
        return Icons.bug_report;
      case 'irrigation_reminder':
        return Icons.water_drop;
      default:
        return Icons.notifications;
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            _playNotificationSound();
            _showNotificationsList();
          },
        ),
        if (_unreadCount > 0)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _unreadCount > 99 ? '99+' : _unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
