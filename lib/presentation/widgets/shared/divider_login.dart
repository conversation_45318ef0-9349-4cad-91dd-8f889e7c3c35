import 'package:flutter/material.dart';

import '../../../core/constants/assets_fonts.dart';

class Text<PERSON>ithDivider extends StatelessWidget {
  final String? text;

  const TextWithDivider({super.key, this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: Colors.grey.shade400,
            thickness: 1,
          ),
        ),
        SizedBox(width: 20),
        Text(
          text ?? 'أو',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade600,
            fontFamily: AssetsFonts.messiri,
          ),
        ),
        SizedBox(width: 20),
        Expanded(
          child: Divider(
            color: Colors.grey.shade400,
            thickness: 1,
          ),
        ),
      ],
    );
  }
}
