import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import 'cachd_net_image.dart';

/// شبكة تحميل كسول
///
/// مكون لعرض شبكة من العناصر مع تحميل كسول (تحميل العناصر عند الحاجة فقط).
class LazyLoadingGrid extends StatefulWidget {
  /// قائمة العناصر
  final List<String> items;

  /// عدد الأعمدة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;

  /// المسافة بين العناصر أفقيًا
  final double crossAxisSpacing;

  /// المسافة بين العناصر رأسيًا
  final double mainAxisSpacing;

  /// حجم الحشو
  final EdgeInsetsGeometry padding;

  /// دالة بناء العنصر
  final Widget Function(BuildContext, String, int)? itemBuilder;

  /// دالة عند النقر على العنصر
  final void Function(int)? onItemTap;

  /// عدد العناصر المحملة مسبقًا
  final int initialItemCount;

  /// عدد العناصر المحملة في كل مرة
  final int loadMoreCount;

  /// منشئ شبكة التحميل الكسول
  const LazyLoadingGrid({
    super.key,
    required this.items,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.padding = const EdgeInsets.all(8.0),
    this.itemBuilder,
    this.onItemTap,
    this.initialItemCount = 10,
    this.loadMoreCount = 10,
  });

  @override
  State<LazyLoadingGrid> createState() => _LazyLoadingGridState();
}

class _LazyLoadingGridState extends State<LazyLoadingGrid> {
  /// عدد العناصر المعروضة حاليًا
  late int _currentItemCount;

  /// تحكم في التمرير
  final ScrollController _scrollController = ScrollController();

  /// مؤشر التحميل
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentItemCount = widget.initialItemCount.clamp(0, widget.items.length);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// دالة التمرير
  void _onScroll() {
    if (_isLoading) return;

    // التحقق من أننا وصلنا إلى نهاية القائمة
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        _currentItemCount < widget.items.length) {
      _loadMore();
    }
  }

  /// تحميل المزيد من العناصر
  void _loadMore() {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _currentItemCount = (_currentItemCount + widget.loadMoreCount)
          .clamp(0, widget.items.length);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: _currentItemCount + (_currentItemCount < widget.items.length ? 1 : 0),
      itemBuilder: (context, index) {
        // إذا وصلنا إلى نهاية العناصر المحملة، نعرض مؤشر تحميل
        if (index == _currentItemCount) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        // بناء العنصر
        if (widget.itemBuilder != null) {
          return GestureDetector(
            onTap: () => widget.onItemTap?.call(index),
            child: widget.itemBuilder!(context, widget.items[index], index),
          );
        }

        // العنصر الافتراضي (صورة)
        return GestureDetector(
          onTap: () => widget.onItemTap?.call(index),
          child: CachedNetImage(
            imageUrl: widget.items[index],
            fit: BoxFit.cover,
            borderRadius: 8,
            lazyLoad: true,
          ),
        );
      },
    );
  }
}

/// شبكة تحميل كسول للصور
///
/// مكون متخصص لعرض شبكة من الصور مع تحميل كسول.
class LazyLoadingImageGrid extends StatelessWidget {
  /// قائمة روابط الصور
  final List<String> imageUrls;

  /// عدد الأعمدة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;

  /// المسافة بين العناصر أفقيًا
  final double crossAxisSpacing;

  /// المسافة بين العناصر رأسيًا
  final double mainAxisSpacing;

  /// حجم الحشو
  final EdgeInsetsGeometry padding;

  /// دالة عند النقر على الصورة
  final void Function(int)? onImageTap;

  /// نصف قطر الحواف
  final double borderRadius;

  /// عدد العناصر المحملة مسبقًا
  final int initialItemCount;

  /// عدد العناصر المحملة في كل مرة
  final int loadMoreCount;

  /// منشئ شبكة التحميل الكسول للصور
  const LazyLoadingImageGrid({
    super.key,
    required this.imageUrls,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.padding = const EdgeInsets.all(8.0),
    this.onImageTap,
    this.borderRadius = 8.0,
    this.initialItemCount = 10,
    this.loadMoreCount = 10,
  });

  @override
  Widget build(BuildContext context) {
    return LazyLoadingGrid(
      items: imageUrls,
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      padding: padding,
      initialItemCount: initialItemCount,
      loadMoreCount: loadMoreCount,
      onItemTap: onImageTap,
      itemBuilder: (context, imageUrl, index) {
        return CachedNetImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          borderRadius: borderRadius,
          lazyLoad: true,
          enableFullScreen: true,
        );
      },
    );
  }
}
