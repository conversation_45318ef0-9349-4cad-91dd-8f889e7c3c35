import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';

/// مكون بطاقة معلومات موحد
///
/// يستخدم لعرض المعلومات بشكل موحد في جميع أجزاء التطبيق
class InfoCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// تفاصيل البطاقة
  final String details;

  /// صورة البطاقة (اختياري)
  final String? imageBase64;

  /// دالة تنفذ عند النقر على البطاقة (اختياري)
  final VoidCallback? onTap;

  /// ما إذا كانت البطاقة قابلة للتوسعة
  final bool expandable;

  /// أيقونة البطاقة (اختياري)
  final IconData? icon;

  /// إنشاء بطاقة معلومات جديدة
  const InfoCard({
    super.key,
    required this.title,
    required this.details,
    this.imageBase64,
    this.onTap,
    this.expandable = true,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: imageBase64 != null && imageBase64!.isNotEmpty
          ? Clip.antiAliasWithSaveLayer
          : Clip.none,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض الصورة إذا كانت موجودة
            if (imageBase64 != null && imageBase64!.isNotEmpty) _buildImage(),

            // عرض العنوان والتفاصيل
            expandable
                ? _buildExpandableTile(context)
                : _buildNonExpandableTile(context),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر قابل للتوسعة
  Widget _buildExpandableTile(BuildContext context) {
    return ExpansionTile(
      leading: icon != null
          ? Icon(
              icon,
              color: AssetsColors.dufaultGreencolor,
              size: 28,
            )
          : null,
      title: Text(
        title,
        style: TextStyles.of(context).bodySmall(
          fontFamily: AssetsFonts.sultan,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AssetsColors.primary,
        ),
        textAlign: TextAlign.start,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            details,
            style: TextStyles.of(context).bodyMedium(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              height: 1.8,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }

  /// بناء عنصر غير قابل للتوسعة
  Widget _buildNonExpandableTile(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Icon(
                icon,
                color: AssetsColors.dufaultGreencolor,
                size: 28,
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyles.of(context).bodySmall(
                    fontFamily: AssetsFonts.sultan,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  details,
                  style: TextStyles.of(context).bodyMedium(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 16,
                    height: 1.8,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صورة البطاقة
  Widget _buildImage() {
    return FutureBuilder<Uint8List>(
      future: Future.microtask(() => base64Decode(imageBase64!)),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            height: 200,
            color: Colors.grey.shade300,
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.grey.shade500,
              ),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return Container(
            height: 200,
            color: Colors.grey.shade300,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 50,
                color: Colors.grey,
              ),
            ),
          );
        }

        return Image.memory(
          snapshot.data!,
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
          cacheWidth: 800,
          gaplessPlayback: true,
          errorBuilder: (context, error, stackTrace) => Container(
            height: 200,
            color: Colors.grey.shade300,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 50,
                color: Colors.grey,
              ),
            ),
          ),
        );
      },
    );
  }
}
