import 'package:flutter/material.dart';

/// فاصل مخصص للتطبيق
///
/// يعرض فاصلًا أفقيًا بتصميم موحد عبر التطبيق
/// يستخدم في فصل المحتوى بصرياً
Widget customDivider({
  Color? color,
  double? thickness,
  double? height,
  double? indent,
  double? endIndent,
}) =>
    Divider(
      color: color ?? Colors.grey[300],
      thickness: thickness ?? 3,
      height: height ?? 1,
      indent: indent,
      endIndent: endIndent,
    );

/// فاصل افتراضي للتطبيق
///
/// نسخة مبسطة من الفاصل المخصص بالإعدادات الافتراضية
Widget myDivider() => customDivider();
