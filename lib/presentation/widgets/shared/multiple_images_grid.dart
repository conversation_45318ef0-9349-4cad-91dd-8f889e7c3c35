import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import 'full_screen_image_viewer.dart';

/// شبكة الصور المتعددة
///
/// يستخدم هذا المكون لعرض مجموعة من الصور في شبكة بتخطيط مناسب.
class MultipleImagesGrid extends StatelessWidget {
  /// قائمة روابط الصور
  final List<String> imageUrls;

  /// الارتفاع الأقصى للشبكة
  final double maxHeight;

  /// نصف قطر الحواف
  final double borderRadius;

  /// المسافة بين الصور
  final double spacing;

  /// منشئ شبكة الصور المتعددة
  const MultipleImagesGrid({
    super.key,
    required this.imageUrls,
    this.maxHeight = 300,
    this.borderRadius = 8,
    this.spacing = 2,
  });

  @override
  Widget build(BuildContext context) {
    // إذا كانت القائمة فارغة، لا نعرض شيئًا
    if (imageUrls.isEmpty) {
      return const SizedBox.shrink();
    }

    // إذا كانت هناك صورة واحدة فقط
    if (imageUrls.length == 1) {
      return _buildSingleImage(context);
    }

    // إذا كان هناك صورتان
    if (imageUrls.length == 2) {
      return _buildTwoImages(context);
    }

    // إذا كان هناك ثلاث صور
    if (imageUrls.length == 3) {
      return _buildThreeImages(context);
    }

    // إذا كان هناك أربع صور أو أكثر
    return _buildFourOrMoreImages(context);
  }

  /// بناء عرض صورة واحدة
  Widget _buildSingleImage(BuildContext context) {
    return GestureDetector(
      onTap: () => context.openFullScreenImageGallery(imageUrls),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: CachedNetworkImage(
          imageUrl: imageUrls[0],
          height: maxHeight,
          width: double.infinity,
          fit: BoxFit.cover,
          placeholder: (context, url) => Shimmer.fromColors(
            baseColor: Colors.grey.shade300,
            highlightColor: Colors.grey.shade100,
            child: Container(
              height: maxHeight,
              width: double.infinity,
              color: Colors.white,
            ),
          ),
          errorWidget: (context, url, error) => Container(
            height: maxHeight,
            width: double.infinity,
            color: Colors.grey.shade300,
            child: const Icon(
              Icons.error,
              color: Colors.red,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عرض صورتين
  Widget _buildTwoImages(BuildContext context) {
    return SizedBox(
      height: maxHeight,
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(borderRadius),
                child: CachedNetworkImage(
                  imageUrl: imageUrls[0],
                  height: maxHeight,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildShimmer(),
                  errorWidget: (context, url, error) => _buildErrorWidget(),
                ),
              ),
            ),
          ),
          SizedBox(width: spacing),
          Expanded(
            child: GestureDetector(
              onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 1),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(borderRadius),
                child: CachedNetworkImage(
                  imageUrl: imageUrls[1],
                  height: maxHeight,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildShimmer(),
                  errorWidget: (context, url, error) => _buildErrorWidget(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض ثلاث صور
  Widget _buildThreeImages(BuildContext context) {
    return SizedBox(
      height: maxHeight,
      child: Row(
        children: [
          // الصورة الأولى (كبيرة على اليمين)
          Expanded(
            flex: 3,
            child: GestureDetector(
              onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(borderRadius),
                child: CachedNetworkImage(
                  imageUrl: imageUrls[0],
                  height: maxHeight,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildShimmer(),
                  errorWidget: (context, url, error) => _buildErrorWidget(),
                ),
              ),
            ),
          ),
          SizedBox(width: spacing),
          // الصورتان الثانية والثالثة (صغيرتان على اليسار)
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 1),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(borderRadius),
                      child: CachedNetworkImage(
                        imageUrl: imageUrls[1],
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildShimmer(),
                        errorWidget: (context, url, error) => _buildErrorWidget(),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: spacing),
                Expanded(
                  child: GestureDetector(
                    onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(borderRadius),
                      child: CachedNetworkImage(
                        imageUrl: imageUrls[2],
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildShimmer(),
                        errorWidget: (context, url, error) => _buildErrorWidget(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض أربع صور أو أكثر
  Widget _buildFourOrMoreImages(BuildContext context) {
    return SizedBox(
      height: maxHeight,
      child: Column(
        children: [
          // الصف الأول (صورتان)
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(borderRadius),
                      child: CachedNetworkImage(
                        imageUrl: imageUrls[0],
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildShimmer(),
                        errorWidget: (context, url, error) => _buildErrorWidget(),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: spacing),
                Expanded(
                  child: GestureDetector(
                    onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 1),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(borderRadius),
                      child: CachedNetworkImage(
                        imageUrl: imageUrls[1],
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildShimmer(),
                        errorWidget: (context, url, error) => _buildErrorWidget(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: spacing),
          // الصف الثاني (صورتان، مع إمكانية وجود المزيد)
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(borderRadius),
                      child: CachedNetworkImage(
                        imageUrl: imageUrls[2],
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildShimmer(),
                        errorWidget: (context, url, error) => _buildErrorWidget(),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: spacing),
                Expanded(
                  child: imageUrls.length > 4
                      ? _buildMoreImagesIndicator(context)
                      : GestureDetector(
                          onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 3),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(borderRadius),
                            child: CachedNetworkImage(
                              imageUrl: imageUrls[3],
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => _buildShimmer(),
                              errorWidget: (context, url, error) => _buildErrorWidget(),
                            ),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر وجود المزيد من الصور
  Widget _buildMoreImagesIndicator(BuildContext context) {
    return GestureDetector(
      onTap: () => context.openFullScreenImageGallery(imageUrls, initialIndex: 3),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // الصورة الرابعة كخلفية
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: CachedNetworkImage(
              imageUrl: imageUrls[3],
              width: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => _buildShimmer(),
              errorWidget: (context, url, error) => _buildErrorWidget(),
            ),
          ),
          // طبقة شفافة داكنة
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: Container(
              color: Colors.black.withOpacity(0.7),
              child: Center(
                child: Text(
                  '+${imageUrls.length - 4}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        color: Colors.white,
      ),
    );
  }

  /// بناء مؤشر الخطأ
  Widget _buildErrorWidget() {
    return Container(
      color: Colors.grey.shade300,
      child: const Icon(
        Icons.error,
        color: Colors.red,
      ),
    );
  }
}
