import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// حوار تأكيد عام
///
/// يستخدم هذا الحوار لعرض رسالة تأكيد للمستخدم قبل تنفيذ إجراء مهم.
class ConfirmationDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;

  /// محتوى الحوار
  final String content;

  /// نص زر التأكيد
  final String confirmButtonText;

  /// نص زر الإلغاء
  final String cancelButtonText;

  /// لون زر التأكيد
  final Color confirmButtonColor;

  /// أيقونة زر التأكيد
  final IconData? confirmButtonIcon;

  /// الإجراء الذي سيتم تنفيذه عند النقر على زر التأكيد
  final VoidCallback onConfirm;

  /// منشئ حوار التأكيد
  const ConfirmationDialog({
    Key? key,
    required this.title,
    required this.content,
    this.confirmButtonText = 'تأكيد',
    this.cancelButtonText = 'إلغاء',
    this.confirmButtonColor = Colors.red,
    this.confirmButtonIcon,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center, // توسيط العنوان
      ),
      content: Text(
        content,
        textAlign: TextAlign.center, // توسيط المحتوى
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      // توسيط الأزرار وتوزيعها بشكل متساوٍ
      actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      actions: [
        // زر الإلغاء
        SizedBox(
          width: 100, // عرض ثابت للزر
          child: TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AssetsColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: AssetsColors.primary.withOpacity(0.5)),
              ),
            ),
            child: Text(cancelButtonText),
          ),
        ),

        const SizedBox(width: 16), // مسافة متساوية بين الزرين

        // زر التأكيد
        SizedBox(
          width: 100, // عرض ثابت للزر
          child: TextButton.icon(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: confirmButtonColor,
              padding: const EdgeInsets.symmetric(vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: Icon(confirmButtonIcon ?? Icons.check, size: 18),
            label: Text(confirmButtonText),
          ),
        ),
      ],
    );
  }

  /// عرض حوار التأكيد
  static Future<void> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmButtonText = 'تأكيد',
    String cancelButtonText = 'إلغاء',
    Color confirmButtonColor = Colors.red,
    IconData? confirmButtonIcon,
    required VoidCallback onConfirm,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: title,
        content: content,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        confirmButtonColor: confirmButtonColor,
        confirmButtonIcon: confirmButtonIcon,
        onConfirm: onConfirm,
      ),
    );
  }
}
