import 'package:flutter/material.dart';
import 'package:flutter_conditional_rendering/conditional.dart';

//الاضافة هي flutter_conditional_rendering: ^2.1.0
Widget conditionalSingle({
  required BuildContext context,
  required bool Function(
    BuildContext context,
  ) conditionBuilder,
  required Widget Function(BuildContext context) widgetBuilder,
  Widget Function(BuildContext context)? fallbackBuilder,
}) =>
    Conditional.single(
        context: context,
        conditionBuilder: conditionBuilder,
        widgetBuilder: widgetBuilder,
        fallbackBuilder: fallbackBuilder);
