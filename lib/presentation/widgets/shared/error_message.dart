import 'package:flutter/material.dart';

/// مكون عرض رسائل الخطأ
///
/// يستخدم هذا المكون لعرض رسائل الخطأ بطريقة موحدة في جميع أنحاء التطبيق
class ErrorMessage extends StatelessWidget {
  /// رسالة الخطأ
  final String message;

  /// ما إذا كان يجب عرض أيقونة
  final bool showIcon;

  /// أيقونة الخطأ
  final IconData icon;

  /// لون الخطأ
  final Color color;

  /// حجم الخط
  final double fontSize;

  /// وزن الخط
  final FontWeight fontWeight;

  /// التباعد الداخلي
  final EdgeInsetsGeometry padding;

  /// دالة يتم استدعاؤها عند النقر على زر إعادة المحاولة
  final VoidCallback? onRetry;

  /// نص زر إعادة المحاولة
  final String retryText;

  /// منشئ مكون عرض رسائل الخطأ
  const ErrorMessage({
    super.key,
    required this.message,
    this.showIcon = true,
    this.icon = Icons.error_outline,
    this.color = Colors.red,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.padding = const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
    this.onRetry,
    this.retryText = 'إعادة المحاولة',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // أيقونة الخطأ
          if (showIcon) ...[
            Icon(icon, color: color, size: fontSize * 1.5),
            const SizedBox(width: 8.0),
          ],

          // رسالة الخطأ
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: color,
                fontSize: fontSize,
                fontWeight: fontWeight,
              ),
            ),
          ),

          // زر إعادة المحاولة
          if (onRetry != null) ...[
            const SizedBox(width: 8.0),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: color,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                minimumSize: const Size(0, 36),
              ),
              child: Text(retryText),
            ),
          ],
        ],
      ),
    );
  }
}
