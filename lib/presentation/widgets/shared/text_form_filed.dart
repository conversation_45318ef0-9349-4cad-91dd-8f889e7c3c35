import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../bloc/text_field/text_field_cubit.dart';

/// حقل إدخال للمصادقة
///
/// مكون محسن لحقول الإدخال في صفحات المصادقة
/// تم تحويله إلى StatelessWidget مع استخدام Cubit لإدارة الحالة
// ignore: must_be_immutable
class AuthField extends StatefulWidget {
  AuthField({
    super.key,
    required this.controller,
    required this.keyboardType,
    this.onFieldSubmitted,
    required this.validator,
    this.onChanged,
    required this.labelText,
    required this.prefixIcon,
    this.suffixIcon,
    this.borderRadius = 50.0,
    this.isPassword = false,
    this.colorBorder,
    this.onTap,
    this.maxLines,
    this.textInputAction,
  });
  final TextInputAction? textInputAction;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onFieldSubmitted;
  final FormFieldValidator? validator;
  final ValueChanged<String>? onChanged;
  GestureTapCallback? onTap;
  final String labelText;
  final IconData prefixIcon;
  final IconData? suffixIcon;

  final double borderRadius;
  final bool isPassword;
  final Color? colorBorder;
  final int? maxLines;
  @override
  State<AuthField> createState() => _AuthFieldState();
}

/// حالة حقل إدخال المصادقة
class _AuthFieldState extends State<AuthField> {
  late TextFieldCubit _textFieldCubit;

  @override
  void initState() {
    super.initState();
    _textFieldCubit = TextFieldCubit();
  }

  @override
  void dispose() {
    _textFieldCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _textFieldCubit,
      child: BlocBuilder<TextFieldCubit, TextFieldState>(
        builder: (context, state) {
          if (state is TextFieldLoaded) {
            return _AuthFieldContent(
              widget: widget,
              state: state,
              cubit: _textFieldCubit,
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

/// محتوى حقل إدخال المصادقة
class _AuthFieldContent extends StatelessWidget {
  final AuthField widget;
  final TextFieldLoaded state;
  final TextFieldCubit cubit;

  const _AuthFieldContent({
    required this.widget,
    required this.state,
    required this.cubit,
  });

  @override
  Widget build(BuildContext context) {
    final color = state.isFocused ? AssetsColors.blue : Colors.grey;
    return TextFormField(
      //حفظ ماداخل المربع الى هاذا المتغير
      controller: widget.controller,
      //انواع الكيبورد
      keyboardType: widget.keyboardType,
      //عند النقر خارج التكست
      onTapOutside: (event) {
        // لا حاجة لـ setState، الـ Cubit يدير الحالة
      },
      //دالة تنفيذ عند النقر على تم بواسطة الكيبورد
      onFieldSubmitted: widget.onFieldSubmitted,
      //عند النقر على المربع النصي
      onTap: widget.onTap,
      textInputAction: widget.textInputAction,

      //****دالة التحق
      validator: widget.validator,
      //دالة معرفة كل حرف يقوم بادخالة المربع
      onChanged: widget.onChanged,
      maxLines: widget.isPassword ? 1 : widget.maxLines,
      // ignore: avoid_bool_literals_in_conditional_expressions

      decoration: InputDecoration(
        isDense: true,
        filled: true,

        //جعل النص الذي في اعلى الشريط بالمنصف
        // floatingLabelAlignment: FloatingLabelAlignment.center,
        labelStyle: TextStyle(),
        label: Text(
          widget.labelText,
          style: TextStyle(
            fontSize: 14,
          ),
        ),
        // contentPadding: EdgeInsets.symmetric(horizontal: 20.0),
        //الايقونة الجهة اليسرى
        prefixIcon: Icon(widget.prefixIcon),
        //ايقونة اضهار واخفاء
        suffixIcon: widget.isPassword
            ? IconButton(
                onPressed: () {
                  cubit.togglePasswordVisibility();
                },
                icon: Icon(
                  state.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                  color: const Color(0xFF171725),
                ),
              )
            : null,
        //جعل الحواف دائرية
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        //حدود الحقل بالوضع الافتراضي
        // enabledBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(widget.borderRadius),
        //   borderSide: BorderSide(
        //     color: Colors.grey.shade700,
        //   ),
        // ),
        //الون الحواف عند الكتابة
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: BorderSide(
            color: widget.colorBorder ?? AssetsColors.blue,
            width: 3.0,
          ),
        ),
        // // لون النص الذي يظهر في الاعلى التلميح
        floatingLabelStyle: TextStyles.of(context).headlineSmall(
          fontWeight: FontWeight.bold,
          fontSize: 12,
          color: AssetsColors.blue,
        ),
        // TextStyle(color: color, fontWeight: FontWeight.w600),
        // جعل النص الذي يظهر في الاعلى يبعد قليل
        alignLabelWithHint: true,
        // iconColor: Colors.red,
        //icon
        prefixIconColor: color,
        suffixIconColor: color,
      ),
      //اظهار واخفاء كلمة المرور
      obscureText: widget.isPassword ? !state.isPasswordVisible : false,
      // cursorColor: Colors.red,
    );
  }
}
