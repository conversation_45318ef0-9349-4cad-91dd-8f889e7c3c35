import 'package:flutter/material.dart';
import '../../../core/shared/services/notification_storage_service.dart';
import '../../../core/shared/services/audio_notification_service.dart';
import '../../../core/constants/notification_constants.dart';

/// واجهة إعدادات الإشعارات
/// 
/// توفر للمستخدم إمكانية التحكم في:
/// - تفعيل/إلغاء تفعيل الأصوات
/// - اختبار الأصوات المختلفة
/// - إدارة الإشعارات المحفوظة
class NotificationSettingsWidget extends StatefulWidget {
  const NotificationSettingsWidget({super.key});

  @override
  State<NotificationSettingsWidget> createState() => _NotificationSettingsWidgetState();
}

class _NotificationSettingsWidgetState extends State<NotificationSettingsWidget> {
  bool _soundEnabled = true;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات الحالية
  Future<void> _loadSettings() async {
    final soundEnabled = await NotificationStorageService.isSoundEnabled();
    final unreadCount = await NotificationStorageService.getUnreadCount();
    
    setState(() {
      _soundEnabled = soundEnabled;
      _unreadCount = unreadCount;
    });
  }

  /// تبديل حالة الصوت
  Future<void> _toggleSound() async {
    await NotificationStorageService.setSoundEnabled(!_soundEnabled);
    setState(() {
      _soundEnabled = !_soundEnabled;
    });
    
    // تشغيل صوت تجريبي إذا تم التفعيل
    if (_soundEnabled) {
      await AudioNotificationService.playDefaultNotification();
    }
  }

  /// تشغيل صوت تجريبي
  Future<void> _testSound(String soundType) async {
    if (_soundEnabled) {
      await AudioNotificationService.playNotificationByType(soundType);
    }
  }

  /// مسح جميع الإشعارات
  Future<void> _clearAllNotifications() async {
    await NotificationStorageService.clearAllNotifications();
    await _loadSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم مسح جميع الإشعارات'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    await NotificationStorageService.markAllAsRead();
    await _loadSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديد جميع الإشعارات كمقروءة'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الإعدادات
            Row(
              children: [
                Icon(Icons.settings, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الإشعارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // إحصائيات الإشعارات
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.notifications, color: Colors.blue.shade600),
                  const SizedBox(width: 8),
                  Text(
                    'الإشعارات غير المقروءة: $_unreadCount',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // تفعيل الصوت
            SwitchListTile(
              title: const Text('تفعيل الأصوات'),
              subtitle: const Text('تشغيل الأصوات عند وصول الإشعارات'),
              value: _soundEnabled,
              onChanged: (value) => _toggleSound(),
              secondary: Icon(
                _soundEnabled ? Icons.volume_up : Icons.volume_off,
                color: _soundEnabled ? Colors.green : Colors.grey,
              ),
            ),
            
            const Divider(),
            
            // اختبار الأصوات
            const Text(
              'اختبار الأصوات:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              children: [
                _buildSoundTestButton('افتراضي', NotificationConstants.typeDefault),
                _buildSoundTestButton('استشارة', NotificationConstants.typeConsultation),
                _buildSoundTestButton('رد مرشد', NotificationConstants.typeAdvisorResponse),
                _buildSoundTestButton('عاجل', NotificationConstants.typeUrgent),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            
            // إدارة الإشعارات
            const Text(
              'إدارة الإشعارات:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _markAllAsRead,
                    icon: const Icon(Icons.done_all),
                    label: const Text('تحديد الكل كمقروء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearAllNotifications,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('مسح الكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر اختبار الصوت
  Widget _buildSoundTestButton(String label, String soundType) {
    return ElevatedButton(
      onPressed: _soundEnabled ? () => _testSound(soundType) : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green.shade100,
        foregroundColor: Colors.green.shade800,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }
}
