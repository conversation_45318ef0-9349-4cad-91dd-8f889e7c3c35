import 'package:flutter/material.dart';

/// مكون عنصر التبويب المحسن
///
/// يستخدم لعرض عناصر التبويب بشكل جذاب مع دعم الأيقونات وعدد العناصر
class TabItem extends StatelessWidget {
  /// عنوان التبويب
  final String title;

  /// عدد العناصر في التبويب (اختياري)
  final int? count;

  /// أيقونة التبويب (اختياري)
  final IconData? icon;

  /// إنشاء عنصر تبويب جديد
  const TabItem({
    super.key,
    required this.title,
    this.count,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // عرض الأيقونة إذا كانت موجودة
          if (icon != null) ...[
            Icon(icon, size: 18),
            const SizedBox(width: 8),
          ],

          // عرض العنوان
          Text(title),

          // عرض العدد إذا كان موجودًا
          if (count != null && count! > 0) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(77),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
