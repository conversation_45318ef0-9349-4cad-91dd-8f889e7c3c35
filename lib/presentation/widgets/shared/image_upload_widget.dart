import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'dart:io';

/// Widget رفع الصور المشترك
/// 
/// يُستخدم في جميع صفحات الخدمات لرفع الصور
/// يدعم الكاميرا والمعرض مع معاينة الصور
class ImageUploadWidget extends StatelessWidget {
  /// عنوان القسم
  final String title;
  
  /// وصف القسم
  final String description;
  
  /// قائمة الصور المختارة
  final List<File> selectedImages;
  
  /// الحد الأقصى لعدد الصور
  final int maxImages;
  
  /// دالة اختيار صورة من الكاميرا
  final VoidCallback onCameraPressed;
  
  /// دالة اختيار صور من المعرض
  final VoidCallback onGalleryPressed;
  
  /// دالة حذف صورة
  final Function(int index) onRemoveImage;
  
  /// لون الحدود والأيقونات
  final Color? accentColor;
  
  /// إظهار معلومات إضافية
  final bool showImageInfo;
  
  /// نص إضافي للمساعدة
  final String? helpText;

  /// منشئ Widget رفع الصور
  const ImageUploadWidget({
    Key? key,
    required this.title,
    required this.description,
    required this.selectedImages,
    required this.maxImages,
    required this.onCameraPressed,
    required this.onGalleryPressed,
    required this.onRemoveImage,
    this.accentColor,
    this.showImageInfo = true,
    this.helpText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color effectiveAccentColor = accentColor ?? AssetsColors.dufaultGreencolor;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // منطقة رفع الصور
            _buildUploadArea(effectiveAccentColor),
            
            // معلومات الصور المختارة
            if (selectedImages.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSelectedImagesInfo(effectiveAccentColor),
              const SizedBox(height: 8),
              _buildImagePreview(),
            ],
            
            // نص المساعدة
            if (helpText != null) ...[
              const SizedBox(height: 12),
              _buildHelpText(),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء منطقة رفع الصور
  Widget _buildUploadArea(Color accentColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: accentColor.withValues(alpha: 0.5)),
        borderRadius: BorderRadius.circular(8),
        color: accentColor.withValues(alpha: 0.1),
      ),
      child: Column(
        children: [
          // الأيقونة والعنوان
          Icon(
            Icons.camera_alt,
            size: 48,
            color: accentColor,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: accentColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              color: accentColor.withValues(alpha: 0.8),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          
          // أزرار الكاميرا والمعرض
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                icon: Icons.camera,
                label: 'كاميرا',
                onPressed: selectedImages.length < maxImages ? onCameraPressed : null,
                accentColor: accentColor,
              ),
              _buildActionButton(
                icon: Icons.photo_library,
                label: 'المعرض',
                onPressed: selectedImages.length < maxImages ? onGalleryPressed : null,
                accentColor: accentColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر العمل
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color accentColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: accentColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// بناء معلومات الصور المختارة
  Widget _buildSelectedImagesInfo(Color accentColor) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: accentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: accentColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'الصور المحددة: ${selectedImages.length}',
              style: TextStyle(
                color: accentColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (showImageInfo)
            Text(
              '(حد أقصى: $maxImages)',
              style: TextStyle(
                color: accentColor.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء معاينة الصور
  Widget _buildImagePreview() {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: selectedImages.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: Stack(
              children: [
                // الصورة
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    selectedImages[index],
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
                
                // زر الحذف
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => onRemoveImage(index),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
                
                // رقم الصورة
                if (showImageInfo)
                  Positioned(
                    bottom: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء نص المساعدة
  Widget _buildHelpText() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.blue[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              helpText!,
              style: TextStyle(
                color: Colors.blue[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget رفع صور النبات المخصص
class PlantImageUploadWidget extends StatelessWidget {
  /// قائمة الصور المختارة
  final List<File> selectedImages;
  
  /// دالة اختيار صورة من الكاميرا
  final VoidCallback onCameraPressed;
  
  /// دالة اختيار صور من المعرض
  final VoidCallback onGalleryPressed;
  
  /// دالة حذف صورة
  final Function(int index) onRemoveImage;

  const PlantImageUploadWidget({
    Key? key,
    required this.selectedImages,
    required this.onCameraPressed,
    required this.onGalleryPressed,
    required this.onRemoveImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ImageUploadWidget(
      title: '🔥 رفع صور حقيقي مفعل',
      description: 'الصور سيتم رفعها إلى Firebase Storage مع ضغط تلقائي',
      selectedImages: selectedImages,
      maxImages: 10,
      onCameraPressed: onCameraPressed,
      onGalleryPressed: onGalleryPressed,
      onRemoveImage: onRemoveImage,
      accentColor: Colors.blue[600],
      helpText: 'سيتم تحليل الصور تلقائياً بالذكاء الاصطناعي لتشخيص الأمراض والآفات',
    );
  }
}

/// Widget رفع صور الاستشارة المخصص
class ConsultationImageUploadWidget extends StatelessWidget {
  /// قائمة الصور المختارة
  final List<File> selectedImages;
  
  /// دالة اختيار صورة من الكاميرا
  final VoidCallback onCameraPressed;
  
  /// دالة اختيار صور من المعرض
  final VoidCallback onGalleryPressed;
  
  /// دالة حذف صورة
  final Function(int index) onRemoveImage;

  const ConsultationImageUploadWidget({
    Key? key,
    required this.selectedImages,
    required this.onCameraPressed,
    required this.onGalleryPressed,
    required this.onRemoveImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ImageUploadWidget(
      title: '🔥 إرفاق صور حقيقية',
      description: 'الصور سيتم رفعها إلى Firebase Storage مع ضغط تلقائي',
      selectedImages: selectedImages,
      maxImages: 5,
      onCameraPressed: onCameraPressed,
      onGalleryPressed: onGalleryPressed,
      onRemoveImage: onRemoveImage,
      accentColor: AssetsColors.dufaultGreencolor,
      helpText: 'يمكنك إرفاق صور للمحصول أو المشكلة لمساعدة المرشد في التشخيص',
    );
  }
}

/// Widget عرض حالة رفع الصور
class ImageUploadProgressWidget extends StatelessWidget {
  /// عدد الصور المرفوعة
  final int uploadedCount;
  
  /// العدد الإجمالي للصور
  final int totalCount;
  
  /// رسالة الحالة
  final String? statusMessage;

  const ImageUploadProgressWidget({
    Key? key,
    required this.uploadedCount,
    required this.totalCount,
    this.statusMessage,
  }) : super(key: key);

  /// نسبة التقدم
  double get progress => totalCount > 0 ? uploadedCount / totalCount : 0.0;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.cloud_upload,
                color: Colors.blue[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      statusMessage ?? 'جاري رفع الصور...',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$uploadedCount من $totalCount صورة',
                      style: TextStyle(
                        color: Colors.blue[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.blue[100],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
          ),
        ],
      ),
    );
  }
}
