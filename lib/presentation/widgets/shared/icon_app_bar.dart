import 'package:flutter/material.dart';

/// مكون أيقونة شريط التطبيق
/// 
/// مكون قابل لإعادة الاستخدام لعرض أيقونة في شريط التطبيق
/// مع إمكانية تخصيص اللون والخلفية والحدث
class IconAppBar extends StatelessWidget {
  /// إنشاء مكون أيقونة شريط التطبيق
  const IconAppBar({
    super.key,
    required this.backgroundColor,
    required this.colorIcon,
    required this.onTap,
    required this.icon,
    this.size = 40,
    this.iconSize = 30,
    this.borderRadius = 12,
  });

  /// لون خلفية الأيقونة
  final Color backgroundColor;
  
  /// حدث النقر على الأيقونة
  final GestureTapCallback onTap;
  
  /// الأيقونة المراد عرضها
  final IconData icon;
  
  /// لون الأيقونة
  final Color colorIcon;
  
  /// حجم الحاوية
  final double size;
  
  /// حجم الأيقونة
  final double iconSize;
  
  /// نصف قطر الحواف
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Icon(
            icon, 
            color: colorIcon, 
            size: iconSize,
          ),
        ),
      ),
    );
  }
}
