import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:flutter/material.dart';

class DropDown extends StatelessWidget {
  const DropDown({super.key, required this.titel, required this.details});

  final String titel;
  final String details;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Card(
        child: ExpansionTile(
          leading: Icon(
            Icons.insert_drive_file,
            color: AssetsColors.dufaultGreencolor,
            size: 35,
          ),
          title: Center(
            child: Text(
              titel,
              style: TextStyle(
                fontFamily: 'sultan',
                fontStyle: FontStyle.italic,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AssetsColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                details.toString(),
                style: TextStyles.of(context).bodyMedium().copyWith(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 16,
                      height: 2,
                      fontWeight: FontWeight.w800,
                      color: Colors.black87,
                    ),
                textAlign: TextAlign.start,
                textWidthBasis: TextWidthBasis.longestLine,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
