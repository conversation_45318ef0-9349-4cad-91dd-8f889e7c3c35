import 'package:flutter/material.dart';

/// بانر خدمة الاستشارة الحقيقية المشترك
/// 
/// يتبع المعيار #3 - التركيز على إنشاء الملفات التشاركية أولاً
/// يتبع المعيار #12 - تعليقات عربية شاملة
/// يتبع المعيار #5 - تجزئة الوظائف (widget منفصل)
class ConsultationServiceBanner extends StatelessWidget {
  /// لون الخلفية الأساسي
  final Color? primaryColor;
  
  /// لون الخلفية الثانوي
  final Color? secondaryColor;
  
  /// عرض معلومات إضافية
  final bool showDetails;

  /// منشئ البانر
  const ConsultationServiceBanner({
    Key? key,
    this.primaryColor,
    this.secondaryColor,
    this.showDetails = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color primary = primaryColor ?? Colors.green[600]!;
    final Color secondary = secondaryColor ?? Colors.green[400]!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [primary, secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان الرئيسي
          _buildHeader(primary),
          
          if (showDetails) ...[
            const SizedBox(height: 12),
            // تفاصيل الخدمة
            _buildServiceDetails(),
          ],
        ],
      ),
    );
  }

  /// بناء العنوان الرئيسي (المعيار #5)
  Widget _buildHeader(Color primaryColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.verified, color: primaryColor, size: 24),
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '🔥 خدمة حقيقية مفعلة 100%',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'جميع الميزات مفعلة ومتصلة بالخوادم',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء تفاصيل الخدمة (المعيار #5)
  Widget _buildServiceDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildServiceFeature(Icons.cloud_upload, 'حفظ في Firestore'),
          const SizedBox(height: 4),
          _buildServiceFeature(Icons.photo_camera, 'رفع صور إلى Firebase Storage'),
          const SizedBox(height: 4),
          _buildServiceFeature(Icons.notifications, 'إشعارات حقيقية للمرشدين'),
          const SizedBox(height: 4),
          _buildServiceFeature(Icons.payment, 'نظام دفع مدمج'),
          const SizedBox(height: 4),
          _buildServiceFeature(Icons.sms, 'رسائل SMS تأكيد'),
          const SizedBox(height: 4),
          _buildServiceFeature(Icons.support_agent, 'دعم فني 24/7'),
        ],
      ),
    );
  }

  /// بناء ميزة خدمة واحدة (المعيار #5)
  Widget _buildServiceFeature(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(width: 8),
        Text(
          '✅ $text',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }
}

/// بانر مبسط للاستشارة
class SimpleConsultationBanner extends StatelessWidget {
  const SimpleConsultationBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const ConsultationServiceBanner(
      showDetails: false,
    );
  }
}

/// بانر مخصص للاستشارة الطارئة
class UrgentConsultationBanner extends StatelessWidget {
  const UrgentConsultationBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ConsultationServiceBanner(
      primaryColor: Colors.red[600],
      secondaryColor: Colors.red[400],
      showDetails: true,
    );
  }
}
