import 'package:flutter/material.dart';
import '../../../../core/constants/smart_farming_constants.dart';

/// بطاقة عرض بيانات المستشعرات المشتركة
/// تستخدم لعرض قراءات المستشعرات المختلفة بتصميم موحد
/// وفق المعايير الـ18 - إنشاء المكونات المشتركة أولاً
class SensorCard extends StatelessWidget {
  /// عنوان المستشعر
  final String title;
  
  /// قيمة القراءة
  final String value;
  
  /// أيقونة المستشعر
  final IconData icon;
  
  /// لون البطاقة
  final Color color;
  
  /// وحدة القياس (اختيارية)
  final String? unit;
  
  /// حالة المستشعر (طبيعي، تحذير، خطر)
  final SensorCardStatus status;
  
  /// دالة النقر على البطاقة (اختيارية)
  final VoidCallback? onTap;
  
  /// إظهار مؤشر التحديث
  final bool showUpdateIndicator;
  
  /// وقت آخر تحديث
  final DateTime? lastUpdate;

  /// منشئ بطاقة المستشعر
  const SensorCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.unit,
    this.status = SensorCardStatus.normal,
    this.onTap,
    this.showUpdateIndicator = false,
    this.lastUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: SmartFarmingConstants.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SmartFarmingConstants.cardBorderRadius),
        side: _getBorderSide(),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SmartFarmingConstants.cardBorderRadius),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(SmartFarmingConstants.defaultPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SmartFarmingConstants.cardBorderRadius),
            gradient: _getGradient(),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة المستشعر مع مؤشر الحالة
              Stack(
                children: [
                  Icon(
                    icon,
                    size: SmartFarmingConstants.largeIconSize,
                    color: _getIconColor(),
                  ),
                  if (showUpdateIndicator)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: SmartFarmingConstants.smallPadding),
              
              // عنوان المستشعر
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getTextColor(),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: SmartFarmingConstants.smallPadding / 2),
              
              // قيمة القراءة مع الوحدة
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    value,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (unit != null) ...[
                    const SizedBox(width: 4),
                    Text(
                      unit!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getTextColor(),
                      ),
                    ),
                  ],
                ],
              ),
              
              // مؤشر الحالة
              if (status != SensorCardStatus.normal) ...[
                const SizedBox(height: SmartFarmingConstants.smallPadding / 2),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: SmartFarmingConstants.smallPadding,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              
              // وقت آخر تحديث
              if (lastUpdate != null) ...[
                const SizedBox(height: SmartFarmingConstants.smallPadding / 2),
                Text(
                  _formatLastUpdate(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على حدود البطاقة حسب الحالة
  BorderSide _getBorderSide() {
    switch (status) {
      case SensorCardStatus.warning:
        return BorderSide(color: Colors.orange, width: 2);
      case SensorCardStatus.danger:
        return BorderSide(color: Colors.red, width: 2);
      case SensorCardStatus.normal:
        return BorderSide.none;
    }
  }

  /// الحصول على التدرج اللوني للبطاقة
  LinearGradient? _getGradient() {
    if (status == SensorCardStatus.normal) return null;
    
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white,
        _getStatusColor().withOpacity(0.05),
      ],
    );
  }

  /// الحصول على لون الأيقونة
  Color _getIconColor() {
    switch (status) {
      case SensorCardStatus.warning:
        return Colors.orange;
      case SensorCardStatus.danger:
        return Colors.red;
      case SensorCardStatus.normal:
        return color;
    }
  }

  /// الحصول على لون النص
  Color _getTextColor() {
    return Colors.grey[700] ?? Colors.black87;
  }

  /// الحصول على لون الحالة
  Color _getStatusColor() {
    switch (status) {
      case SensorCardStatus.warning:
        return Colors.orange;
      case SensorCardStatus.danger:
        return Colors.red;
      case SensorCardStatus.normal:
        return Colors.green;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText() {
    switch (status) {
      case SensorCardStatus.warning:
        return 'تحذير';
      case SensorCardStatus.danger:
        return 'خطر';
      case SensorCardStatus.normal:
        return 'طبيعي';
    }
  }

  /// تنسيق وقت آخر تحديث
  String _formatLastUpdate() {
    if (lastUpdate == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(lastUpdate!);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}

/// حالات بطاقة المستشعر
enum SensorCardStatus {
  /// حالة طبيعية
  normal,
  
  /// حالة تحذير
  warning,
  
  /// حالة خطر
  danger,
}
