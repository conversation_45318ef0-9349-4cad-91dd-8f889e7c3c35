import 'package:flutter/material.dart';
import '../../../../data/models/smart_farming/index.dart';
import '../../../../core/constants/smart_farming_constants.dart';

/// بطاقة عرض سجل المحصول المشتركة
/// تستخدم لعرض معلومات المحصول بتصميم موحد
/// وفق المعايير الـ18 - إنشاء المكونات المشتركة أولاً
class CropRecordCard extends StatelessWidget {
  /// بيانات المحصول
  final CropRecordModel cropRecord;
  
  /// دالة النقر على البطاقة
  final VoidCallback? onTap;
  
  /// دالة تعديل المحصول
  final VoidCallback? onEdit;
  
  /// دالة حذف المحصول
  final VoidCallback? onDelete;
  
  /// دالة عرض التفاصيل
  final VoidCallback? onViewDetails;
  
  /// إظهار أزرار الإجراءات
  final bool showActions;
  
  /// إظهار مؤشر التقدم
  final bool showProgress;
  
  /// إظهار معلومات الموقع
  final bool showLocation;

  /// منشئ بطاقة سجل المحصول
  const CropRecordCard({
    super.key,
    required this.cropRecord,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onViewDetails,
    this.showActions = true,
    this.showProgress = true,
    this.showLocation = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: SmartFarmingConstants.cardElevation,
      margin: const EdgeInsets.only(bottom: SmartFarmingConstants.defaultPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SmartFarmingConstants.cardBorderRadius),
      ),
      child: InkWell(
        onTap: onTap ?? onViewDetails,
        borderRadius: BorderRadius.circular(SmartFarmingConstants.cardBorderRadius),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(SmartFarmingConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // الصف الأول: الأيقونة والاسم والحالة
              Row(
                children: [
                  // أيقونة المحصول
                  Container(
                    padding: const EdgeInsets.all(SmartFarmingConstants.smallPadding),
                    decoration: BoxDecoration(
                      color: _getCropColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getCropIcon(),
                      color: _getCropColor(),
                      size: SmartFarmingConstants.iconSize,
                    ),
                  ),
                  
                  const SizedBox(width: SmartFarmingConstants.defaultPadding),
                  
                  // اسم المحصول ونوعه
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cropRecord.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          cropRecord.type,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // شارة الحالة
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SmartFarmingConstants.smallPadding,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      cropRecord.status.displayName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: SmartFarmingConstants.defaultPadding),
              
              // الصف الثاني: المعلومات الأساسية
              Row(
                children: [
                  // المساحة
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      icon: Icons.crop_landscape,
                      label: SmartFarmingConstants.cropAreaLabel,
                      value: '${cropRecord.area} ${SmartFarmingConstants.hectareUnit}',
                    ),
                  ),
                  
                  const SizedBox(width: SmartFarmingConstants.defaultPadding),
                  
                  // تاريخ الزراعة
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      icon: Icons.calendar_today,
                      label: SmartFarmingConstants.plantingDateLabel,
                      value: _formatDate(cropRecord.plantingDate),
                    ),
                  ),
                ],
              ),
              
              // الموقع (إذا كان مطلوباً)
              if (showLocation) ...[
                const SizedBox(height: SmartFarmingConstants.smallPadding),
                _buildInfoItem(
                  context,
                  icon: Icons.location_on,
                  label: SmartFarmingConstants.cropLocationLabel,
                  value: '${cropRecord.location} - ${cropRecord.province}',
                ),
              ],
              
              // مؤشر التقدم (إذا كان مطلوباً)
              if (showProgress) ...[
                const SizedBox(height: SmartFarmingConstants.defaultPadding),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          SmartFarmingConstants.growthProgressLabel,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${cropRecord.growthProgress.toInt()}%',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: _getCropColor(),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: SmartFarmingConstants.smallPadding),
                    LinearProgressIndicator(
                      value: cropRecord.growthProgress / 100,
                      backgroundColor: Colors.grey[300],
                      color: _getCropColor(),
                      minHeight: 8,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ],
              
              // الملاحظات (إذا وجدت)
              if (cropRecord.notes != null && cropRecord.notes!.isNotEmpty) ...[
                const SizedBox(height: SmartFarmingConstants.smallPadding),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(SmartFarmingConstants.smallPadding),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    cropRecord.notes!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[700],
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
              
              // أزرار الإجراءات (إذا كانت مطلوبة)
              if (showActions) ...[
                const SizedBox(height: SmartFarmingConstants.defaultPadding),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (onViewDetails != null)
                      _buildActionButton(
                        context,
                        icon: Icons.visibility,
                        label: 'عرض',
                        onPressed: onViewDetails!,
                        color: Colors.blue,
                      ),
                    if (onEdit != null)
                      _buildActionButton(
                        context,
                        icon: Icons.edit,
                        label: 'تعديل',
                        onPressed: onEdit!,
                        color: Colors.orange,
                      ),
                    if (onDelete != null)
                      _buildActionButton(
                        context,
                        icon: Icons.delete,
                        label: 'حذف',
                        onPressed: onDelete!,
                        color: Colors.red,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: color),
      label: Text(
        label,
        style: TextStyle(color: color),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// الحصول على لون المحصول حسب النوع
  Color _getCropColor() {
    switch (cropRecord.type.toLowerCase()) {
      case 'قمح':
        return Colors.amber[700]!;
      case 'أرز':
        return Colors.green[600]!;
      case 'طماطم':
        return Colors.red[600]!;
      case 'ذرة':
      case 'ذرة رفيعة':
      case 'ذرة شامية':
        return Colors.yellow[700]!;
      case 'بطاطس':
        return Colors.brown[600]!;
      case 'خضروات':
        return Colors.green[500]!;
      case 'فواكه':
        return Colors.orange[600]!;
      default:
        return Colors.green[600]!;
    }
  }

  /// الحصول على أيقونة المحصول حسب النوع
  IconData _getCropIcon() {
    switch (cropRecord.type.toLowerCase()) {
      case 'قمح':
        return Icons.grass;
      case 'أرز':
        return Icons.rice_bowl;
      case 'طماطم':
        return Icons.local_florist;
      case 'ذرة':
      case 'ذرة رفيعة':
      case 'ذرة شامية':
        return Icons.agriculture;
      case 'بطاطس':
        return Icons.eco;
      case 'خضروات':
        return Icons.local_grocery_store;
      case 'فواكه':
        return Icons.apple;
      default:
        return Icons.eco;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor() {
    switch (cropRecord.status) {
      case CropStatus.planting:
        return Colors.blue[600]!;
      case CropStatus.growing:
        return Colors.green[600]!;
      case CropStatus.flowering:
        return Colors.pink[600]!;
      case CropStatus.readyToHarvest:
        return Colors.orange[600]!;
      case CropStatus.harvested:
        return Colors.brown[600]!;
      case CropStatus.damaged:
        return Colors.red[600]!;
      case CropStatus.failed:
        return Colors.grey[600]!;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final parts = dateString.split('-');
      if (parts.length == 3) {
        return '${parts[2]}/${parts[1]}/${parts[0]}';
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }
}
