import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../imports.dart';

/// شريط التطبيق الافتراضي
///
/// يستخدم في جميع صفحات التطبيق مع إمكانية تخصيص اللون والعنوان والإجراءات وغيرها
PreferredSizeWidget defaultAppBar({
  /// لون خلفية شريط التطبيق
  Color? color,

  /// عنوان شريط التطبيق (اختياري)
  String? titel,

  /// قائمة الإجراءات في الجانب الأيمن
  List<Widget>? actions,

  /// الزر في الجانب الأيسر
  Widget? leading,

  /// سياق البناء (مطلوب)
  required BuildContext context,

  /// شريط سفلي إضافي
  PreferredSizeWidget? bottom,

  /// ما إذا كان يجب عرض زر الرجوع تلقائيًا
  bool automaticallyImplyLeading = false,

  /// ما إذا كان العنوان يجب أن يكون في المنتصف
  bool centerTitle = true,
}) =>
    AppBar(
      // إعدادات زر الرجوع
      automaticallyImplyLeading: automaticallyImplyLeading,
      foregroundColor: Colors.white,

      // إعدادات شريط الحالة
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.light,
      ),

      // إعدادات العنوان والتخطيط
      centerTitle: centerTitle,
      backgroundColor: color ?? AppTheme.primaryColor,
      leading: leading,

      // عنوان شريط التطبيق
      title: titel != null
          ? Text(
              titel,
              style: TextStyles.of(context).titleLarge().copyWith(
                    fontFamily: AssetsFonts.messiri,
                    color: Colors.white,
                    fontSize: 20.0,
                  ),
            )
          : null,

      // الإجراءات والشريط السفلي
      actions: actions,
      bottom: bottom,

      // الارتفاع والظل
      elevation: 4.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(10),
        ),
      ),
    );
