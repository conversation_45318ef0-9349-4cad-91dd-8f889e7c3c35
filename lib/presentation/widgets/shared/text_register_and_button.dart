import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';

import '../../../core/constants/assets_colors.dart';

Widget textRegister({required GestureTapCallback? onTap}) => RichText(
      text: TextSpan(
        text: 'Don’t have an account? ',
        style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: AssetsColors.kGrey70),
        children: [
          TextSpan(
            text: 'Sign Up',
            recognizer: TapGestureRecognizer()..onTap = onTap,
            style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AssetsColors.kPrimary),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
