import 'package:flutter/material.dart';

/// مؤشر تقدم مع طبقة شفافة
///
/// يستخدم هذا المكون لعرض مؤشر تقدم مع طبقة شفافة فوق المحتوى
/// مفيد لعرض حالة التحميل أثناء العمليات التي تستغرق وقتًا
class ProgressIndicatorOverlay extends StatelessWidget {
  /// ما إذا كان المؤشر مرئيًا
  final bool isVisible;

  /// رسالة لعرضها مع المؤشر
  final String? message;

  /// لون الخلفية
  final Color backgroundColor;

  /// لون المؤشر
  final Color indicatorColor;

  /// حجم المؤشر
  final double indicatorSize;

  /// حجم الخط
  final double fontSize;

  /// المحتوى الذي سيتم عرض المؤشر فوقه
  final Widget child;

  /// منشئ مؤشر التقدم مع طبقة شفافة
  const ProgressIndicatorOverlay({
    super.key,
    required this.isVisible,
    required this.child,
    this.message,
    this.backgroundColor = Colors.black54,
    this.indicatorColor = Colors.white,
    this.indicatorSize = 50.0,
    this.fontSize = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // المحتوى الأساسي
        child,

        // طبقة المؤشر
        if (isVisible)
          Container(
            color: backgroundColor,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // مؤشر التقدم الدائري
                  SizedBox(
                    width: indicatorSize,
                    height: indicatorSize,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
                      strokeWidth: 4.0,
                    ),
                  ),

                  // رسالة (إذا كانت موجودة)
                  if (message != null) ...[
                    const SizedBox(height: 16.0),
                    Text(
                      message!,
                      style: TextStyle(
                        color: indicatorColor,
                        fontSize: fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }
}
