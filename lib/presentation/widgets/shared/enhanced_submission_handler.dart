import 'dart:async';
import 'package:flutter/material.dart';

/// معالج إرسال محسن للطلبات
/// 
/// يوفر واجهة موحدة لجميع عمليات الإرسال في التطبيق
/// مع معالجة أخطاء متقدمة ورسائل نجاح جميلة
class EnhancedSubmissionHandler {
  
  /// عرض حوار تحميل محسن
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                message ?? 'جاري الإرسال...',
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
        );
      },
    );
  }

  /// عرض حوار نجاح محسن
  static void showSuccessDialog(
    BuildContext context, {
    required String title,
    required String message,
    Map<String, String>? details,
    VoidCallback? onViewDetails,
    VoidCallback? onClose,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          icon: const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48,
          ),
          title: Text(
            title,
            style: const TextStyle(
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(message),
                if (details != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل الطلب:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        ...details.entries.map((entry) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text('${entry.key}: ${entry.value}'),
                        )),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: const Text(
                    'سيتم التواصل معك قريباً لمتابعة طلبك',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            if (onViewDetails != null)
              TextButton(
                child: const Text('عرض التفاصيل'),
                onPressed: onViewDetails,
              ),
            ElevatedButton(
              child: const Text('موافق'),
              onPressed: onClose ?? () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار خطأ محسن
  static void showErrorDialog(
    BuildContext context, {
    required String message,
    VoidCallback? onRetry,
  }) {
    String solution = _getSolutionForError(message);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          icon: const Icon(
            Icons.error,
            color: Colors.red,
            size: 48,
          ),
          title: const Text(
            'فشل في الإرسال',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'السبب:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Text(
                  'الحلول المقترحة:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  solution,
                  style: const TextStyle(fontSize: 14, height: 1.4),
                ),
              ],
            ),
          ),
          actions: [
            if (onRetry != null)
              TextButton(
                child: const Text('إعادة المحاولة'),
                onPressed: () {
                  Navigator.pop(context);
                  onRetry();
                },
              ),
            TextButton(
              child: const Text('حسناً'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  /// معالج إرسال شامل مع timeout
  static Future<void> handleSubmission(
    BuildContext context, {
    required Future<void> Function() submitFunction,
    required String loadingMessage,
    required String successTitle,
    required String successMessage,
    Map<String, String>? successDetails,
    VoidCallback? onSuccess,
    int timeoutSeconds = 60,
  }) async {
    // عرض حوار التحميل
    showLoadingDialog(context, message: loadingMessage);

    try {
      // تنفيذ العملية مع timeout
      await Future.any([
        submitFunction(),
        Future.delayed(Duration(seconds: timeoutSeconds), () {
          throw TimeoutException('انتهت مهلة الإرسال', Duration(seconds: timeoutSeconds));
        }),
      ]);

      // إغلاق حوار التحميل
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      // تأخير بسيط قبل عرض رسالة النجاح
      Future.delayed(const Duration(milliseconds: 300), () {
        if (context.mounted) {
          showSuccessDialog(
            context,
            title: successTitle,
            message: successMessage,
            details: successDetails,
            onClose: onSuccess,
          );
        }
      });

    } catch (e) {
      // إغلاق حوار التحميل
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      // تأخير بسيط قبل عرض رسالة الخطأ
      Future.delayed(const Duration(milliseconds: 300), () {
        if (context.mounted) {
          String errorMessage = e is TimeoutException
              ? 'انتهت مهلة الإرسال. يرجى المحاولة مرة أخرى.'
              : e.toString();

          showErrorDialog(
            context,
            message: errorMessage,
            onRetry: () => handleSubmission(
              context,
              submitFunction: submitFunction,
              loadingMessage: loadingMessage,
              successTitle: successTitle,
              successMessage: successMessage,
              successDetails: successDetails,
              onSuccess: onSuccess,
              timeoutSeconds: timeoutSeconds,
            ),
          );
        }
      });
    }
  }

  /// الحصول على حل مقترح للخطأ
  static String _getSolutionForError(String error) {
    if (error.contains('خدمة قاعدة البيانات غير متاحة')) {
      return '• تأكد من اتصالك بالإنترنت\n• حاول مرة أخرى بعد دقائق قليلة\n• إذا استمرت المشكلة، تواصل مع الدعم الفني';
    } else if (error.contains('انتهت مهلة الاتصال') || error.contains('انتهت مهلة الإرسال')) {
      return '• تحقق من قوة إشارة الإنترنت\n• جرب الاتصال بشبكة Wi-Fi أقوى\n• أعد المحاولة بعد قليل\n• تم رفع الصورة بنجاح، المشكلة في حفظ البيانات فقط';
    } else if (error.contains('ليس لديك صلاحية')) {
      return '• تأكد من تسجيل الدخول بحسابك\n• تواصل مع الدعم الفني للمساعدة';
    } else if (error.contains('فشل في إنشاء') || error.contains('فشل في إرسال')) {
      return '• تم رفع الصورة بنجاح إلى Google Drive\n• المشكلة في حفظ البيانات\n• جرب إعادة المحاولة\n• أو استخدم جهاز آخر';
    } else {
      return '• تأكد من اتصالك بالإنترنت\n• أعد المحاولة بعد قليل\n• إذا استمرت المشكلة، تواصل معنا';
    }
  }
}
