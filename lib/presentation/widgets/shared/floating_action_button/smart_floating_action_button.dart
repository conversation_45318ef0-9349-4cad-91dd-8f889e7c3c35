import 'package:flutter/material.dart';
import 'package:agriculture/data/models/floating_action/fab_action_model.dart';
import 'package:agriculture/core/services/floating_action_button_service.dart';
import 'package:agriculture/core/constants/floating_action_button_constants.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// الزر العائم الذكي للاستشاري الزراعي
/// وفق المعيار #6: عدم استخدام StatefulWidget - استخدام StatelessWidget
/// وفق المعيار #15: مجلد widgets للصفحة
class SmartFloatingActionButton extends StatelessWidget {
  /// قائمة الإجراءات المخصصة (اختيارية)
  final List<FabActionModel>? customActions;
  
  /// هل يتم عرض الإجراءات في شكل قائمة أم شبكة
  final bool showAsGrid;
  
  /// عدد الأعمدة في الشبكة
  final int gridColumns;
  
  /// دالة مخصصة عند النقر على الزر الرئيسي
  final VoidCallback? onMainButtonTap;

  const SmartFloatingActionButton({
    super.key,
    this.customActions,
    this.showAsGrid = false,
    this.gridColumns = 2,
    this.onMainButtonTap,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onMainButtonTap ?? () => _showActionsBottomSheet(context),
      backgroundColor: AssetsColors.dufaultGreencolor,
      elevation: 8,
      child: const Icon(
        Icons.psychology,
        color: Colors.white,
        size: 28,
      ),
    );
  }

  /// عرض قائمة الإجراءات في BottomSheet
  void _showActionsBottomSheet(BuildContext context) {
    final actions = customActions ?? 
        FloatingActionButtonService().createAdvisorActions(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildActionsBottomSheet(context, actions),
    );
  }

  /// بناء BottomSheet الإجراءات
  Widget _buildActionsBottomSheet(BuildContext context, List<FabActionModel> actions) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // رأس القائمة
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AssetsColors.dufaultGreencolor,
                  AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المساعد الذكي للاستشاري',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'اختر الإجراء المناسب لتحسين خدماتك',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // قائمة الإجراءات
          Expanded(
            child: showAsGrid 
                ? _buildActionsGrid(context, actions)
                : _buildActionsList(context, actions),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الإجراءات
  Widget _buildActionsList(BuildContext context, List<FabActionModel> actions) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return _buildActionCard(context, action, index);
      },
    );
  }

  /// بناء شبكة الإجراءات
  Widget _buildActionsGrid(BuildContext context, List<FabActionModel> actions) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridColumns,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return _buildActionGridCard(context, action, index);
      },
    );
  }

  /// بناء بطاقة الإجراء (للقائمة)
  Widget _buildActionCard(BuildContext context, FabActionModel action, int index) {
    return TweenAnimationBuilder<double>(
      duration: FloatingActionButtonConstants.animationDuration,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Card(
              margin: const EdgeInsets.only(bottom: 12),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: InkWell(
                onTap: () {
                  Navigator.pop(context);
                  action.onTap?.call();
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white,
                        action.color.withValues(alpha: 0.05),
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      // أيقونة الإجراء
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: action.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: action.color.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Icon(
                          action.icon,
                          color: action.color,
                          size: 24,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // معلومات الإجراء
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              action.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              action.description,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      // سهم الانتقال
                      Icon(
                        Icons.arrow_forward_ios,
                        color: action.color,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء بطاقة الإجراء (للشبكة)
  Widget _buildActionGridCard(BuildContext context, FabActionModel action, int index) {
    return TweenAnimationBuilder<double>(
      duration: FloatingActionButtonConstants.animationDuration,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: InkWell(
              onTap: () {
                Navigator.pop(context);
                action.onTap?.call();
              },
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      Colors.white,
                      action.color.withValues(alpha: 0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // أيقونة الإجراء
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: action.color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        action.icon,
                        color: action.color,
                        size: 20,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // عنوان الإجراء
                    Text(
                      action.title,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
