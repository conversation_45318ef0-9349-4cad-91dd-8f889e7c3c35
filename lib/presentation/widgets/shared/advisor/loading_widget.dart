import 'package:flutter/material.dart';

/// مكون التحميل المشترك للاستشاري الزراعي
/// 
/// يستخدم لعرض مؤشرات التحميل المختلفة
class AdvisorLoadingWidget extends StatelessWidget {
  /// رسالة التحميل
  final String? message;
  
  /// حجم مؤشر التحميل
  final double size;
  
  /// لون مؤشر التحميل
  final Color? color;
  
  /// نوع مؤشر التحميل
  final AdvisorLoadingType type;
  
  /// منشئ مكون التحميل
  const AdvisorLoadingWidget({
    super.key,
    this.message,
    this.size = 40,
    this.color,
    this.type = AdvisorLoadingType.circular,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = color ?? Theme.of(context).primaryColor;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // مؤشر التحميل
          _buildLoadingIndicator(themeColor),
          
          // رسالة التحميل إذا كانت متوفرة
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
  
  /// بناء مؤشر التحميل حسب النوع
  Widget _buildLoadingIndicator(Color themeColor) {
    switch (type) {
      case AdvisorLoadingType.circular:
        return SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(themeColor),
            strokeWidth: 3,
          ),
        );
        
      case AdvisorLoadingType.linear:
        return SizedBox(
          width: size * 2,
          child: LinearProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(themeColor),
            backgroundColor: themeColor.withValues(alpha: 0.2),
          ),
        );
        
      case AdvisorLoadingType.dots:
        return _buildDotsIndicator(themeColor);
        
      case AdvisorLoadingType.shimmer:
        return _buildShimmerIndicator();
    }
  }
  
  /// بناء مؤشر النقاط
  Widget _buildDotsIndicator(Color themeColor) {
    return SizedBox(
      width: size,
      height: size / 4,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 600 + (index * 200)),
            width: size / 8,
            height: size / 8,
            decoration: BoxDecoration(
              color: themeColor,
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }
  
  /// بناء مؤشر الشيمر
  Widget _buildShimmerIndicator() {
    return Container(
      width: size * 2,
      height: size / 2,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          colors: [
            Colors.grey[300]!,
            Colors.grey[100]!,
            Colors.grey[300]!,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
}

/// أنواع مؤشرات التحميل
enum AdvisorLoadingType {
  /// مؤشر دائري
  circular,
  
  /// مؤشر خطي
  linear,
  
  /// مؤشر النقاط
  dots,
  
  /// مؤشر الشيمر
  shimmer,
}

/// مكونات التحميل المحددة مسبقاً للاستشاري الزراعي
class AdvisorLoadingStates {
  /// تحميل الاستشارات
  static Widget consultations() {
    return const AdvisorLoadingWidget(
      message: 'جاري تحميل الاستشارات...',
      color: Colors.blue,
    );
  }
  
  /// تحميل المواعيد
  static Widget appointments() {
    return const AdvisorLoadingWidget(
      message: 'جاري تحميل المواعيد...',
      color: Colors.orange,
    );
  }
  
  /// تحميل مراقبة النبات
  static Widget plantMonitoring() {
    return const AdvisorLoadingWidget(
      message: 'جاري تحميل بيانات المراقبة...',
      color: Colors.green,
    );
  }
  
  /// تحميل التقارير
  static Widget reports() {
    return const AdvisorLoadingWidget(
      message: 'جاري إنشاء التقارير...',
      color: Colors.purple,
    );
  }
  
  /// تحميل البحث
  static Widget search() {
    return const AdvisorLoadingWidget(
      message: 'جاري البحث...',
      type: AdvisorLoadingType.dots,
      color: Colors.grey,
    );
  }
  
  /// تحميل عام
  static Widget general({String? message}) {
    return AdvisorLoadingWidget(
      message: message ?? 'جاري التحميل...',
    );
  }
}
