import 'package:flutter/material.dart';

/// بطاقة إحصائية مشتركة للاستشاري الزراعي
/// 
/// تستخدم لعرض الإحصائيات المختلفة مثل عدد الاستشارات الجديدة،
/// المواعيد المؤكدة، الحالات العاجلة، إلخ
class AdvisorStatCard extends StatelessWidget {
  /// تسمية الإحصائية
  final String label;
  
  /// قيمة الإحصائية
  final String count;
  
  /// لون البطاقة
  final Color color;
  
  /// أيقونة البطاقة
  final IconData? icon;
  
  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;
  
  /// حجم البطاقة
  final Size? size;
  
  /// منشئ بطاقة الإحصائيات
  const AdvisorStatCard({
    super.key,
    required this.label,
    required this.count,
    required this.color,
    this.icon,
    this.onTap,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size?.width,
        height: size?.height ?? 120,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // تقليل الحجم للحد الأدنى
          children: [
            // الأيقونة إذا كانت متوفرة
            if (icon != null) ...[
              Container(
                padding: const EdgeInsets.all(6), // تقليل الحشو
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18, // تقليل حجم الأيقونة
                ),
              ),
              const SizedBox(height: 6), // تقليل المسافة
            ],

            // قيمة الإحصائية
            Flexible( // استخدام Flexible
              child: Text(
                count,
                style: TextStyle(
                  fontSize: 18, // تقليل حجم الخط
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: 2), // تقليل المسافة

            // تسمية الإحصائية
            Flexible( // استخدام Flexible
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 10, // تقليل حجم الخط
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// صف من بطاقات الإحصائيات
/// 
/// يستخدم لعرض مجموعة من الإحصائيات في صف واحد
class AdvisorStatsRow extends StatelessWidget {
  /// قائمة بيانات الإحصائيات
  final List<AdvisorStatData> stats;
  
  /// المسافة بين البطاقات
  final double spacing;
  
  /// منشئ صف الإحصائيات
  const AdvisorStatsRow({
    super.key,
    required this.stats,
    this.spacing = 12,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: stats.asMap().entries.map((entry) {
        final index = entry.key;
        final stat = entry.value;
        
        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              left: index > 0 ? spacing / 2 : 0,
              right: index < stats.length - 1 ? spacing / 2 : 0,
            ),
            child: AdvisorStatCard(
              label: stat.label,
              count: stat.count,
              color: stat.color,
              icon: stat.icon,
              onTap: stat.onTap,
            ),
          ),
        );
      }).toList(),
    );
  }
}

/// نموذج بيانات الإحصائية
class AdvisorStatData {
  /// تسمية الإحصائية
  final String label;
  
  /// قيمة الإحصائية
  final String count;
  
  /// لون الإحصائية
  final Color color;
  
  /// أيقونة الإحصائية
  final IconData? icon;
  
  /// دالة النقر
  final VoidCallback? onTap;
  
  /// منشئ بيانات الإحصائية
  const AdvisorStatData({
    required this.label,
    required this.count,
    required this.color,
    this.icon,
    this.onTap,
  });
}
