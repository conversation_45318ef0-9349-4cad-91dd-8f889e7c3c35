import 'package:flutter/material.dart';

/// مكون عرض الحالة الفارغة للاستشاري الزراعي
/// 
/// يستخدم لعرض رسالة عندما لا توجد بيانات للعرض
/// مثل عدم وجود استشارات أو مواعيد
class AdvisorEmptyStateWidget extends StatelessWidget {
  /// الأيقونة المعروضة
  final IconData icon;
  
  /// العنوان الرئيسي
  final String title;
  
  /// الوصف التفصيلي
  final String description;
  
  /// نص الزر (اختياري)
  final String? buttonText;
  
  /// دالة النقر على الزر (اختياري)
  final VoidCallback? onButtonPressed;
  
  /// لون الأيقونة والزر
  final Color? color;
  
  /// حجم الأيقونة
  final double iconSize;
  
  /// منشئ مكون الحالة الفارغة
  const AdvisorEmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.buttonText,
    this.onButtonPressed,
    this.color,
    this.iconSize = 64,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = color ?? Theme.of(context).primaryColor;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // الأيقونة
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: themeColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: themeColor.withValues(alpha: 0.6),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // العنوان
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // الوصف
            Text(
              description,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            // الزر إذا كان متوفراً
            if (buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onButtonPressed,
                icon: const Icon(Icons.add),
                label: Text(buttonText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// مكونات الحالة الفارغة المحددة مسبقاً للاستشاري الزراعي
class AdvisorEmptyStates {
  /// حالة فارغة للاستشارات
  static Widget consultations({
    VoidCallback? onAddConsultation,
  }) {
    return AdvisorEmptyStateWidget(
      icon: Icons.chat_bubble_outline,
      title: 'لا توجد استشارات',
      description: 'لم يتم العثور على أي استشارات حتى الآن.\nستظهر الاستشارات الجديدة هنا.',
      buttonText: onAddConsultation != null ? 'إضافة استشارة' : null,
      onButtonPressed: onAddConsultation,
      color: Colors.blue,
    );
  }
  
  /// حالة فارغة للمواعيد
  static Widget appointments({
    VoidCallback? onAddAppointment,
  }) {
    return AdvisorEmptyStateWidget(
      icon: Icons.calendar_today_outlined,
      title: 'لا توجد مواعيد',
      description: 'لم يتم حجز أي مواعيد حتى الآن.\nستظهر المواعيد المحجوزة هنا.',
      buttonText: onAddAppointment != null ? 'حجز موعد' : null,
      onButtonPressed: onAddAppointment,
      color: Colors.orange,
    );
  }
  
  /// حالة فارغة لمراقبة النبات
  static Widget plantMonitoring({
    VoidCallback? onAddMonitoring,
  }) {
    return AdvisorEmptyStateWidget(
      icon: Icons.eco_outlined,
      title: 'لا توجد مراقبة نباتات',
      description: 'لم يتم إضافة أي نباتات للمراقبة حتى الآن.\nستظهر النباتات المراقبة هنا.',
      buttonText: onAddMonitoring != null ? 'إضافة مراقبة' : null,
      onButtonPressed: onAddMonitoring,
      color: Colors.green,
    );
  }
  
  /// حالة فارغة للتقارير
  static Widget reports({
    VoidCallback? onGenerateReport,
  }) {
    return AdvisorEmptyStateWidget(
      icon: Icons.bar_chart_outlined,
      title: 'لا توجد تقارير',
      description: 'لم يتم إنشاء أي تقارير حتى الآن.\nستظهر التقارير المنشأة هنا.',
      buttonText: onGenerateReport != null ? 'إنشاء تقرير' : null,
      onButtonPressed: onGenerateReport,
      color: Colors.purple,
    );
  }
  
  /// حالة فارغة للبحث
  static Widget searchResults({
    required String searchQuery,
  }) {
    return AdvisorEmptyStateWidget(
      icon: Icons.search_off,
      title: 'لا توجد نتائج',
      description: 'لم يتم العثور على نتائج للبحث عن "$searchQuery".\nجرب استخدام كلمات مختلفة.',
      color: Colors.grey,
    );
  }
}
