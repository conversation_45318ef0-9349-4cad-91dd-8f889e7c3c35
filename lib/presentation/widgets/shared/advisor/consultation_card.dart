import 'package:flutter/material.dart';

import '../../../../core/constants/advisor_constants.dart';
import '../../../../data/models/agricultural_advisor/consultation_model.dart';

/// بطاقة الاستشارة المشتركة
/// 
/// تستخدم لعرض معلومات الاستشارة في قوائم مختلفة
/// مع تصميم موحد ومتجاوب
class AdvisorConsultationCard extends StatelessWidget {
  /// نموذج الاستشارة
  final ConsultationModel consultation;
  
  /// دالة النقر على البطاقة
  final VoidCallback? onTap;
  
  /// دالة النقر على زر الرد (للمرشدين)
  final VoidCallback? onReply;
  
  /// ما إذا كان المستخدم مرشد
  final bool isAdvisor;
  
  /// إظهار صورة المستخدم
  final bool showUserImage;
  
  /// إظهار حالة الاستشارة
  final bool showStatus;
  
  /// منشئ بطاقة الاستشارة
  const AdvisorConsultationCard({
    super.key,
    required this.consultation,
    this.onTap,
    this.onReply,
    this.isAdvisor = false,
    this.showUserImage = true,
    this.showStatus = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              _buildHeader(),
              
              const SizedBox(height: 12),
              
              // محتوى الاستشارة
              _buildContent(),
              
              const SizedBox(height: 12),
              
              // تذييل البطاقة
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }
  
  /// بناء رأس البطاقة
  Widget _buildHeader() {
    return Row(
      children: [
        // صورة المستخدم
        if (showUserImage) ...[
          CircleAvatar(
            radius: 20,
            backgroundImage: consultation.userImage != null
                ? NetworkImage(consultation.userImage!)
                : null,
            child: consultation.userImage == null
                ? const Icon(Icons.person, size: 20)
                : null,
          ),
          const SizedBox(width: 12),
        ],
        
        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                consultation.userName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'نوع المحصول: ${consultation.cropType}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        
        // حالة الاستشارة
        if (showStatus) _buildStatusChip(),
      ],
    );
  }
  
  /// بناء محتوى الاستشارة
  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // وصف المشكلة
        Text(
          consultation.problemDescription,
          style: const TextStyle(fontSize: 14),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        
        // المساحة إذا كانت متوفرة
        if (consultation.area.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.crop_landscape,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                'المساحة: ${consultation.area}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
        
        // الصور إذا كانت متوفرة
        if (consultation.images?.isNotEmpty == true) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.image,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                '${consultation.images?.length ?? 0} صورة مرفقة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
  
  /// بناء تذييل البطاقة
  Widget _buildFooter(BuildContext context) {
    return Row(
      children: [
        // تاريخ الإنشاء
        Icon(
          Icons.access_time,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          _formatDate(consultation.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        
        const Spacer(),
        
        // زر الرد للمرشدين
        if (isAdvisor && onReply != null && 
            consultation.status == ConsultationStatus.pending) ...[
          ElevatedButton.icon(
            onPressed: onReply,
            icon: const Icon(Icons.reply, size: 16),
            label: const Text('رد'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              minimumSize: const Size(0, 32),
            ),
          ),
        ],
        
        // تقييم الاستشارة إذا كانت مكتملة
        if (consultation.rating != null) ...[
          Row(
            children: [
              const Icon(
                Icons.star,
                size: 16,
                color: Colors.amber,
              ),
              const SizedBox(width: 4),
              Text(
                consultation.rating!.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
  
  /// بناء شريحة الحالة
  Widget _buildStatusChip() {
    final statusInfo = _getStatusInfo(consultation.status);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusInfo.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusInfo.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        statusInfo.label,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: statusInfo.color,
        ),
      ),
    );
  }
  
  /// الحصول على معلومات الحالة
  _StatusInfo _getStatusInfo(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return _StatusInfo(
          label: AdvisorConstants.consultationStatusLabels[
              AdvisorConstants.consultationStatusPending]!,
          color: Color(AdvisorConstants.consultationStatusColors[
              AdvisorConstants.consultationStatusPending]!),
        );
      case ConsultationStatus.inProgress:
        return _StatusInfo(
          label: AdvisorConstants.consultationStatusLabels[
              AdvisorConstants.consultationStatusInProgress]!,
          color: Color(AdvisorConstants.consultationStatusColors[
              AdvisorConstants.consultationStatusInProgress]!),
        );
      case ConsultationStatus.answered:
        return _StatusInfo(
          label: AdvisorConstants.consultationStatusLabels[
              AdvisorConstants.consultationStatusCompleted]!,
          color: Color(AdvisorConstants.consultationStatusColors[
              AdvisorConstants.consultationStatusCompleted]!),
        );
      case ConsultationStatus.cancelled:
        return _StatusInfo(
          label: AdvisorConstants.consultationStatusLabels[
              AdvisorConstants.consultationStatusCancelled]!,
          color: Color(AdvisorConstants.consultationStatusColors[
              AdvisorConstants.consultationStatusCancelled]!),
        );
      case ConsultationStatus.closed:
        return _StatusInfo(
          label: AdvisorConstants.consultationStatusLabels[
              AdvisorConstants.consultationStatusCancelled]!,
          color: Color(AdvisorConstants.consultationStatusColors[
              AdvisorConstants.consultationStatusCancelled]!),
        );
    }
  }
  
  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays == 0) {
        return 'اليوم ${_formatTime(date)}';
      } else if (difference.inDays == 1) {
        return 'أمس ${_formatTime(date)}';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} أيام';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}

/// معلومات الحالة
class _StatusInfo {
  final String label;
  final Color color;
  
  const _StatusInfo({
    required this.label,
    required this.color,
  });
}
