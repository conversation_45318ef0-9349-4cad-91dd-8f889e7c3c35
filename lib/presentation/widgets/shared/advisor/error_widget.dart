import 'package:flutter/material.dart';

/// مكون عرض الأخطاء للاستشاري الزراعي
/// 
/// يستخدم لعرض رسائل الأخطاء مع إمكانية إعادة المحاولة
class AdvisorErrorWidget extends StatelessWidget {
  /// رسالة الخطأ
  final String message;
  
  /// تفاصيل الخطأ (اختياري)
  final String? details;
  
  /// أيقونة الخطأ
  final IconData icon;
  
  /// نص زر إعادة المحاولة
  final String? retryButtonText;
  
  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;
  
  /// لون الخطأ
  final Color? color;
  
  /// إظهار تفاصيل الخطأ
  final bool showDetails;
  
  /// منشئ مكون الأخطاء
  const AdvisorErrorWidget({
    super.key,
    required this.message,
    this.details,
    this.icon = Icons.error_outline,
    this.retryButtonText,
    this.onRetry,
    this.color,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final errorColor = color ?? Colors.red;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الخطأ
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                icon,
                size: 64,
                color: errorColor,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // رسالة الخطأ
            Text(
              message,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            // تفاصيل الخطأ إذا كانت متوفرة ومطلوبة
            if (showDetails && details != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل الخطأ:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      details!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // زر إعادة المحاولة إذا كان متوفراً
            if (retryButtonText != null && onRetry != null) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryButtonText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: errorColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
            
            // زر إظهار/إخفاء التفاصيل إذا كانت متوفرة
            if (details != null) ...[
              const SizedBox(height: 16),
              TextButton.icon(
                onPressed: () {
                  // يمكن تنفيذ هذا باستخدام StatefulWidget أو Cubit
                  // هنا نعرض حوار بالتفاصيل
                  _showDetailsDialog(context, details!);
                },
                icon: Icon(
                  showDetails ? Icons.visibility_off : Icons.visibility,
                  size: 16,
                ),
                label: Text(
                  showDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// عرض حوار تفاصيل الخطأ
  void _showDetailsDialog(BuildContext context, String details) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الخطأ'),
        content: SingleChildScrollView(
          child: Text(
            details,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

/// مكونات الأخطاء المحددة مسبقاً للاستشاري الزراعي
class AdvisorErrorStates {
  /// خطأ في تحميل الاستشارات
  static Widget consultationsError({
    VoidCallback? onRetry,
    String? details,
  }) {
    return AdvisorErrorWidget(
      message: 'فشل في تحميل الاستشارات',
      details: details,
      icon: Icons.chat_bubble_outline,
      retryButtonText: 'إعادة المحاولة',
      onRetry: onRetry,
      color: Colors.blue,
    );
  }
  
  /// خطأ في تحميل المواعيد
  static Widget appointmentsError({
    VoidCallback? onRetry,
    String? details,
  }) {
    return AdvisorErrorWidget(
      message: 'فشل في تحميل المواعيد',
      details: details,
      icon: Icons.calendar_today_outlined,
      retryButtonText: 'إعادة المحاولة',
      onRetry: onRetry,
      color: Colors.orange,
    );
  }
  
  /// خطأ في تحميل مراقبة النبات
  static Widget plantMonitoringError({
    VoidCallback? onRetry,
    String? details,
  }) {
    return AdvisorErrorWidget(
      message: 'فشل في تحميل بيانات المراقبة',
      details: details,
      icon: Icons.eco_outlined,
      retryButtonText: 'إعادة المحاولة',
      onRetry: onRetry,
      color: Colors.green,
    );
  }
  
  /// خطأ في الشبكة
  static Widget networkError({
    VoidCallback? onRetry,
  }) {
    return AdvisorErrorWidget(
      message: 'لا يوجد اتصال بالإنترنت',
      details: 'تأكد من اتصالك بالإنترنت وحاول مرة أخرى',
      icon: Icons.wifi_off,
      retryButtonText: 'إعادة المحاولة',
      onRetry: onRetry,
      color: Colors.grey,
    );
  }
  
  /// خطأ عام
  static Widget general({
    required String message,
    VoidCallback? onRetry,
    String? details,
  }) {
    return AdvisorErrorWidget(
      message: message,
      details: details,
      retryButtonText: onRetry != null ? 'إعادة المحاولة' : null,
      onRetry: onRetry,
    );
  }
}
