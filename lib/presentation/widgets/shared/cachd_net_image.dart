import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/constants/app_constants.dart';
import 'full_screen_image_viewer.dart';

//الاضافة  shimmer: ^3.0.0 and  cached_network_image: ^3.4.1
class CachedNetImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final double? borderRadius;
  final BoxFit? fit;
  final bool enableFullScreen;
  final String? heroTag;
  final bool lazyLoad;
  final int? memCacheWidth;
  final int? memCacheHeight;

  const CachedNetImage({
    super.key,
    required this.imageUrl,
    this.width = double.infinity,
    this.height = double.infinity,
    this.borderRadius,
    this.fit,
    this.enableFullScreen = false,
    this.heroTag,
    this.lazyLoad = true,
    this.memCacheWidth,
    this.memCacheHeight,
  });

  @override
  Widget build(BuildContext context) {
    final Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      memCacheWidth: memCacheWidth ?? AppConstants.defaultImageMaxWidth,
      memCacheHeight: memCacheHeight ?? AppConstants.defaultImageMaxHeight,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      placeholderFadeInDuration: const Duration(milliseconds: 300),
      maxWidthDiskCache: AppConstants.defaultImageMaxWidth * 2,
      maxHeightDiskCache: AppConstants.defaultImageMaxHeight * 2,
      // التحميل الكسول - لا يتم تحميل الصورة إلا عند ظهورها في الشاشة
      cacheKey: imageUrl,
      useOldImageOnUrlChange: true,
      placeholder: (context, url) => Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
          width: width,
          height: height,
          color: Colors.white,
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: width,
        height: height,
        color: Colors.grey.shade300,
        child: const Icon(
          Icons.error,
          color: Colors.red,
        ),
      ),
    );

    // إنشاء الصورة النهائية مع مراعاة نصف القطر
    final Widget finalImageWidget = borderRadius != null
        ? ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius!),
            child: imageWidget,
          )
        : imageWidget;

    // إذا كان تمكين العرض بملء الشاشة مفعلاً، نضيف GestureDetector
    if (enableFullScreen) {
      return GestureDetector(
        onTap: () => context.openFullScreenImage(imageUrl, tag: heroTag),
        child: finalImageWidget,
      );
    }

    // إذا لم يكن تمكين العرض بملء الشاشة مفعلاً، نعيد الصورة كما هي
    return finalImageWidget;
  }
}
