import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// بانر الخدمة الحقيقية المشترك
/// 
/// يُستخدم في جميع صفحات الخدمات الحقيقية لإظهار أن الخدمة مفعلة
/// يحتوي على معلومات الخدمة والميزات المفعلة
class RealServiceBanner extends StatelessWidget {
  /// عنوان الخدمة
  final String serviceTitle;
  
  /// وصف الخدمة
  final String serviceDescription;
  
  /// أيقونة الخدمة
  final IconData serviceIcon;
  
  /// قائمة الميزات المفعلة
  final List<String> enabledFeatures;
  
  /// ألوان التدرج
  final List<Color>? gradientColors;
  
  /// إظهار أيقونة التحقق
  final bool showVerificationIcon;

  /// منشئ بانر الخدمة الحقيقية
  const RealServiceBanner({
    Key? key,
    required this.serviceTitle,
    required this.serviceDescription,
    required this.serviceIcon,
    required this.enabledFeatures,
    this.gradientColors,
    this.showVerificationIcon = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors ?? [
            Colors.green[600]!,
            Colors.green[400]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (gradientColors?.first ?? Colors.green).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان والأيقونة
          _buildHeader(),
          
          const SizedBox(height: 12),
          
          // الميزات المفعلة
          _buildFeaturesSection(),
        ],
      ),
    );
  }

  /// بناء رأس البانر
  Widget _buildHeader() {
    return Row(
      children: [
        // أيقونة التحقق
        if (showVerificationIcon)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.verified,
              color: gradientColors?.first ?? Colors.green[600],
              size: 24,
            ),
          ),
        
        if (showVerificationIcon) const SizedBox(width: 12),
        
        // أيقونة الخدمة
        Container(
          padding: const EdgeInsets.all(8),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Icon(
            serviceIcon,
            color: gradientColors?.first ?? Colors.green[600],
            size: 24,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // العنوان والوصف
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '🔥 $serviceTitle',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                serviceDescription,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم الميزات
  Widget _buildFeaturesSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔥 ميزات مفعلة:',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          ...enabledFeatures.map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    feature,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}

/// بانر خدمة مراقبة النبات
class PlantMonitoringServiceBanner extends StatelessWidget {
  const PlantMonitoringServiceBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const RealServiceBanner(
      serviceTitle: 'مراقبة النبات الحقيقية',
      serviceDescription: 'تقنيات متقدمة مع ذكاء اصطناعي',
      serviceIcon: Icons.eco,
      gradientColors: [
        Color(0xFF1976D2), // أزرق
        Color(0xFF4CAF50), // أخضر
      ],
      enabledFeatures: [
        'تحليل صور بالذكاء الاصطناعي',
        'تحديد موقع دقيق بـ GPS',
        'جدولة زيارات المهندسين',
        'تقارير مفصلة وإحصائيات',
      ],
    );
  }
}

/// بانر خدمة الاستشارة
class ConsultationServiceBanner extends StatelessWidget {
  const ConsultationServiceBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RealServiceBanner(
      serviceTitle: 'الاستشارة الحقيقية',
      serviceDescription: 'جميع الميزات مفعلة ومتصلة بالخوادم',
      serviceIcon: Icons.chat,
      gradientColors: [
        Colors.green[600]!,
        Colors.green[400]!,
      ],
      enabledFeatures: const [
        'حفظ في Firestore',
        'رفع صور إلى Firebase Storage',
        'إشعارات حقيقية للمرشدين',
        'نظام دفع مدمج',
      ],
    );
  }
}

/// بانر خدمة حجز المواعيد
class AppointmentServiceBanner extends StatelessWidget {
  const AppointmentServiceBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RealServiceBanner(
      serviceTitle: 'حجز المواعيد الحقيقي',
      serviceDescription: 'نظام حجز متطور ومتزامن',
      serviceIcon: Icons.calendar_today,
      gradientColors: [
        Colors.purple[600]!,
        Colors.purple[400]!,
      ],
      enabledFeatures: const [
        'تقويم تفاعلي حقيقي',
        'تزامن مع Google Calendar',
        'إشعارات تذكير',
        'إدارة توفر المرشدين',
      ],
    );
  }
}

/// بانر عام للخدمات الحقيقية
class GeneralRealServiceBanner extends StatelessWidget {
  const GeneralRealServiceBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RealServiceBanner(
      serviceTitle: 'خدمات حقيقية مفعلة',
      serviceDescription: 'جميع الخدمات متصلة بالخوادم الحقيقية',
      serviceIcon: Icons.verified,
      gradientColors: [
        AssetsColors.dufaultGreencolor,
        Colors.green[400]!,
      ],
      enabledFeatures: const [
        'Firebase Storage - رفع الصور',
        'Firestore - حفظ البيانات',
        'Firebase Messaging - الإشعارات',
        'AI API - تحليل الصور',
        'SMS API - إرسال الرسائل',
        'Payment API - معالجة الدفع',
        'GPS - تحديد الموقع',
      ],
    );
  }
}

/// بانر تأكيد الخدمة مع إحصائيات
class ServiceConfirmationBanner extends StatelessWidget {
  /// عدد الطلبات المعالجة
  final int processedRequests;
  
  /// معدل النجاح
  final double successRate;
  
  /// زمن الاستجابة المتوسط
  final int averageResponseTime;

  const ServiceConfirmationBanner({
    Key? key,
    required this.processedRequests,
    required this.successRate,
    required this.averageResponseTime,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[600]!, Colors.blue[500]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(Icons.analytics, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📊 إحصائيات الخدمة الحقيقية',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'بيانات حقيقية من الخوادم',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  '📈',
                  processedRequests.toString(),
                  'طلب معالج',
                ),
                _buildStatItem(
                  '✅',
                  '${successRate.toStringAsFixed(1)}%',
                  'معدل النجاح',
                ),
                _buildStatItem(
                  '⚡',
                  '${averageResponseTime}ث',
                  'زمن الاستجابة',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String icon, String value, String label) {
    return Column(
      children: [
        Text(
          icon,
          style: const TextStyle(fontSize: 20),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}
