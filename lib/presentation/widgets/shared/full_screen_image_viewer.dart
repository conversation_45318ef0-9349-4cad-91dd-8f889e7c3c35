import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:shimmer/shimmer.dart';

/// عارض الصور بحجم كامل مع إمكانية التكبير
///
/// يستخدم هذا المكون لعرض صورة أو مجموعة من الصور بحجم كامل مع إمكانية التكبير والتصغير والتمرير.
class FullScreenImageViewer extends StatefulWidget {
  /// قائمة روابط الصور
  final List<String> imageUrls;

  /// مؤشر الصورة الحالية
  final int initialIndex;

  /// عنوان الصفحة
  final String? title;

  /// منشئ عارض الصور بحجم كامل
  const FullScreenImageViewer({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
    this.title,
  });

  @override
  State<FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<FullScreenImageViewer> {
  /// مؤشر الصفحة الحالية
  late int _currentIndex;

  /// تحكم في الصفحة
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          widget.title ?? 'صورة ${_currentIndex + 1} من ${widget.imageUrls.length}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر المشاركة
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: تنفيذ مشاركة الصورة
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // معرض الصور
          PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: CachedNetworkImageProvider(widget.imageUrls[index]),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained * 0.8,
                maxScale: PhotoViewComputedScale.covered * 2,
                heroAttributes: PhotoViewHeroAttributes(tag: 'image_${widget.imageUrls[index]}'),
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey[900],
                  child: const Center(
                    child: Icon(
                      Icons.error,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                ),
              );
            },
            itemCount: widget.imageUrls.length,
            loadingBuilder: (context, event) => Center(
              child: CircularProgressIndicator(
                value: event == null
                    ? 0
                    : event.cumulativeBytesLoaded / event.expectedTotalBytes!,
              ),
            ),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
            pageController: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
          ),

          // مؤشرات الصور (تظهر فقط إذا كان هناك أكثر من صورة)
          if (widget.imageUrls.length > 1)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  widget.imageUrls.length,
                  (index) => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentIndex == index ? Colors.white : Colors.grey,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// امتداد لفتح عارض الصور بحجم كامل
extension FullScreenImageViewerExtension on BuildContext {
  /// فتح عارض الصور بحجم كامل
  void openFullScreenImage(String imageUrl, {String? tag}) {
    Navigator.push(
      this,
      MaterialPageRoute(
        builder: (context) => FullScreenImageViewer(
          imageUrls: [imageUrl],
          initialIndex: 0,
        ),
      ),
    );
  }

  /// فتح معرض الصور بحجم كامل
  void openFullScreenImageGallery(List<String> imageUrls, {int initialIndex = 0, String? title}) {
    Navigator.push(
      this,
      MaterialPageRoute(
        builder: (context) => FullScreenImageViewer(
          imageUrls: imageUrls,
          initialIndex: initialIndex,
          title: title,
        ),
      ),
    );
  }
}
