import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';

import '../../../data/models/crops/plant_referral_data_model.dart';

import '../shared/info_card.dart';

/// مكون عرض معلومات المحصول الزراعي
///
/// يعرض معلومات مفصلة عن المحصول الزراعي
class InfDescription extends StatelessWidget {
  /// بيانات الإحالة للمحصول
  final List<PlantReferralData> model;

  /// تفاصيل المحصول
  final String details;

  /// إنشاء مكون عرض معلومات المحصول
  const InfDescription({
    super.key,
    required this.model,
    required this.details,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض وصف المحصول
            _buildDescriptionSection(context),

            // عرض بيانات الإحالة (إذا وجدت)
            _buildReferralDataSection(context),
          ],
        ),
      ),
    );
  }

  /// بناء قسم وصف المحصول
  Widget _buildDescriptionSection(BuildContext context) {
    if (details.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            'معلومات المحصول',
            style: TextStyles.of(context).titleSmall(
              fontFamily: AssetsFonts.cairo,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),
        ),

        // وصف المحصول
        InfoCard(
          title: 'الوصف',
          details: details,
          expandable: false,
          icon: Icons.feed_outlined,
        ),

        // عنوان بيانات الإحالة
        if (model.isNotEmpty) ...[
          const SizedBox(height: 24),
          Text(
            'بيانات مرجعية',
            style: TextStyles.of(context).titleSmall(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),
          const SizedBox(height: 8),
        ],
      ],
    );
  }

  /// بناء قسم بيانات الإحالة
  Widget _buildReferralDataSection(BuildContext context) {
    // التحقق من وجود بيانات إحالة صالحة
    if (model.isEmpty) {
      return const SizedBox();
    }

    // تصفية البيانات غير الصالحة
    final validData = model
        .where((item) => item.name.isNotEmpty && item.details.isNotEmpty)
        .toList();

    if (validData.isEmpty) {
      return const SizedBox();
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) => InfoCard(
        title: validData[index].name,
        details: validData[index].details,
        expandable: false,
        icon: Icons.insert_drive_file_sharp,
      ),
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemCount: validData.length,
    );
  }
}
