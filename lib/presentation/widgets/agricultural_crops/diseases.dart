import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_model.dart';
import '../../../data/models/crops/disease_referral_Info_model.dart';
import '../../bloc/agricultural_crops/crops_cubit.dart';
import '../shared/info_card.dart';
import 'show_diseases.dart';

/// مكون عرض أمراض المحصول الزراعي
///
/// يعرض قائمة الأمراض المرتبطة بالمحصول الزراعي
class Diseases extends StatelessWidget {
  /// قائمة أمراض المحصول
  final List<Disease> disease;

  /// إنشاء مكون عرض الأمراض
  const Diseases({
    super.key,
    required this.disease,
  });

  @override
  Widget build(BuildContext context) {
    // التحقق من وجود أمراض للعرض
    if (disease.isEmpty) {
      return _buildEmptyDiseases(context);
    }

    return BlocConsumer<CropsCubit, CropsState>(
      listener: (context, state) {},
      builder: (context, state) {
        final cubit = CropsCubit.get(context);

        // تصفية الأمراض غير الصالحة (التي تحتوي على تفاصيل فارغة)
        final validDiseases = disease
            .where((dis) => dis.name.isNotEmpty && dis.details.isNotEmpty)
            .toList();

        // التحقق مرة أخرى بعد التصفية
        if (validDiseases.isEmpty) {
          return _buildEmptyDiseases(context);
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القسم
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'الآفات والأمراض',
                  style: TextStyles.of(context).titleSmall(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.primary,
                  ),
                ),
              ),
              const Divider(height: 1),
              const SizedBox(height: 16),
              ListView.builder(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                itemBuilder: (context, index) {
                  final currentDisease = validDiseases[index];
                  final diseaseInfo = cubit.diseaseReferralInfo
                      .where((dis) => dis.diseaseId == currentDisease.id)
                      .toList();

                  return _buildDiseaseItem(
                    context,
                    currentDisease,
                    diseaseInfo,
                  );
                },
                itemCount: validDiseases.length,
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر المرض
  Widget _buildDiseaseItem(
    BuildContext context,
    Disease disease,
    List<DiseaseReferralInfo> info,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: InfoCard(
        title: disease.name,
        details: disease.details,
        imageBase64: disease.mainPhoto,
        expandable: false,
        icon: Icons.bug_report_outlined,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ShowDiseases(
                image: disease.mainPhoto,
                name: disease.name,
                details: disease.details,
                info: info,
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء عرض فارغ عندما لا توجد أمراض
  Widget _buildEmptyDiseases(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bug_report_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد آفات أو أمراض مسجلة لهذا المحصول',
            style: TextStyles.of(context).bodyMedium(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
