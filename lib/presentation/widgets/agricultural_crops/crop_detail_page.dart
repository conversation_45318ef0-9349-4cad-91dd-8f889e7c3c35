import 'dart:convert';

import 'package:buttons_tabbar/buttons_tabbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_model.dart';
import '../../../data/models/crops/plant_operation_model.dart';
import '../../../data/models/crops/plant_referral_data_model.dart';
import '../../../data/models/crops/plants_model.dart';
import 'diseases.dart';
import 'inf_description.dart';
import 'operations.dart';

/// صفحة تفاصيل المحصول الزراعي
///
/// تعرض معلومات مفصلة عن المحصول الزراعي المحدد
class CropDetailPage extends StatelessWidget {
  /// بيانات المحصول الزراعي
  final Plant crop;

  /// بيانات الإحالة للمحصول
  final List<PlantReferralData> referralData;

  /// عمليات المحصول
  final List<PlantOperation> operations;

  /// أمراض المحصول
  final List<Disease> diseases;

  /// إنشاء صفحة تفاصيل المحصول الزراعي
  const CropDetailPage({
    super.key,
    required this.crop,
    required this.referralData,
    required this.operations,
    required this.diseases,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        floatHeaderSlivers: true,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) =>
            [
          _buildAppBar(context),
        ],
        body: _buildTabContent(context),
      ),
    );
  }

  /// بناء شريط التطبيق
  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      foregroundColor: Colors.white,
      backgroundColor: AssetsColors.dufaultGreencolor,
      centerTitle: true,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.indigo,
        statusBarIconBrightness: Brightness.light,
      ),
      pinned: true,
      snap: false,
      expandedHeight: 300.0,
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        title: Text(
          crop.name,
          style: TextStyles.of(context).titleSmall(
            fontSize: 18,
            fontFamily: AssetsFonts.sultan,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        background: _buildHeaderImage(),
      ),
    );
  }

  /// بناء صورة رأس الصفحة
  Widget _buildHeaderImage() {
    try {
      // استخدام FutureBuilder لتحميل الصورة بشكل غير متزامن
      return FutureBuilder<Uint8List>(
        // استخدام Future.microtask لتحسين الأداء
        future: Future.microtask(() => base64Decode(crop.mainPhoto)),
        builder: (context, snapshot) {
          // عرض مؤشر التحميل أثناء فك تشفير الصورة
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: Colors.grey.shade700,
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white70,
                ),
              ),
            );
          }

          // عرض صورة الخطأ في حالة وجود خطأ
          if (snapshot.hasError || !snapshot.hasData) {
            return _buildErrorImage();
          }

          // عرض الصورة بعد فك تشفيرها
          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            // تخزين الصورة مؤقتاً لتحسين الأداء
            cacheWidth: 1200,
            gaplessPlayback: true,
            errorBuilder: (context, error, stackTrace) => _buildErrorImage(),
          );
        },
      );
    } catch (e) {
      return _buildErrorImage();
    }
  }

  /// بناء صورة الخطأ (تظهر عند فشل تحميل الصورة)
  Widget _buildErrorImage() {
    return Container(
      color: Colors.grey.shade700,
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          size: 80,
          color: Colors.white54,
        ),
      ),
    );
  }

  /// بناء محتوى التبويبات
  Widget _buildTabContent(BuildContext context) {
    // استخدام مكتبة التسجيل للتحقق من البيانات
    // يمكن استخدام printInfo من مكتبة التسجيل الخاصة بالتطبيق

    return DefaultTabController(
      length: 3,
      child: Column(
        children: <Widget>[
          _buildTabBar(context),
          Expanded(
            child: TabBarView(
              physics: const BouncingScrollPhysics(),
              children: [
                // قسم المعلومات
                InfDescription(
                  model: referralData,
                  details: crop.details,
                ),

                // قسم العمليات
                operations.isEmpty
                    ? _buildEmptyOperations(context)
                    : Operations(
                        operation: operations,
                      ),

                // قسم الأمراض
                diseases.isEmpty
                    ? _buildEmptyDiseases(context)
                    : Diseases(
                        disease: diseases,
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض فارغ عندما لا توجد عمليات
  Widget _buildEmptyOperations(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grass_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عمليات متاحة لهذا المحصول',
            style: TextStyles.of(context).bodyMedium(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عرض فارغ عندما لا توجد أمراض
  Widget _buildEmptyDiseases(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bug_report_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد آفات أو أمراض مسجلة لهذا المحصول',
            style: TextStyles.of(context).bodyMedium(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      color: AssetsColors.dufaultGreencolor.withAlpha(25),
      child: ButtonsTabBar(
        backgroundColor: AssetsColors.primary,
        unselectedBackgroundColor: const Color.fromARGB(255, 213, 212, 212),
        elevation: 3,
        borderWidth: 1,
        borderColor: Colors.transparent,
        unselectedBorderColor: Colors.transparent,
        radius: 8,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        buttonMargin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        labelStyle: TextStyles.of(context).bodySmall(
          fontFamily: AssetsFonts.cairo,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        unselectedLabelStyle: TextStyles.of(context).bodySmall(
          fontFamily: AssetsFonts.cairo,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        contentCenter: true,
        physics: const BouncingScrollPhysics(),
        height: 80,
        width: screenWidth / 3,
        tabs: [
          _buildTabItem(context, 'معلومات', Icons.info_outline),
          _buildTabItem(context, 'العمليات', Icons.grass_outlined),
          _buildTabItem(context, 'الآفات', Icons.bug_report_outlined),
        ],
      ),
    );
  }

  /// بناء عنصر التبويب
  Widget _buildTabItem(BuildContext context, String text, IconData icon) {
    return Tab(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(text),
        ],
      ),
    );
  }
}
