import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/crops/plants_model.dart';
import '../../bloc/agricultural_crops/crops_cubit.dart';
import 'crop_card.dart';

/// مكون قائمة المحاصيل الزراعية
///
/// يعرض قائمة من المحاصيل الزراعية المنتمية لفئة معينة
class CropList extends StatelessWidget {
  /// قائمة المحاصيل الزراعية
  final List<Plant> crops;

  /// إنشاء قائمة محاصيل زراعية
  const CropList({
    super.key,
    required this.crops,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CropsCubit, CropsState>(
      listener: (context, state) {
        // يمكن إضافة استجابة للتغييرات في الحالة هنا
      },
      builder: (context, state) {
        final cubit = CropsCubit.get(context);

        return ListView.separated(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          itemBuilder: (context, index) => _buildCropCard(cubit, index),
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemCount: crops.length,
        );
      },
    );
  }

  /// بناء بطاقة المحصول الزراعي
  Widget _buildCropCard(CropsCubit cubit, int index) {
    final crop = crops[index];

    // الحصول على بيانات الإحالة للمحصول (استبعاد البيانات غير الصالحة)
    final referralData = cubit.plantReferralData
        .where((ref) => ref.plantId == crop.id && ref.name.isNotEmpty)
        .toList();

    // الحصول على عمليات المحصول
    final operations = cubit.plantOperations
        .where((op) => op.plantId == crop.id && op.name.isNotEmpty)
        .toList();

    // الحصول على أمراض المحصول (استبعاد الأمراض غير الصالحة)
    final diseases = cubit.diseases
        .where((dis) => dis.plantId == crop.id && dis.name.isNotEmpty)
        .toList();

    // استخدام مكتبة التسجيل للتحقق من البيانات
    // يمكن استخدام printInfo من مكتبة التسجيل الخاصة بالتطبيق

    return CropCard(
      crop: crop,
      referralData: referralData,
      operations: operations,
      diseases: diseases,
    );
  }
}
