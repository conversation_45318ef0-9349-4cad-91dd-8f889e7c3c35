import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_referral_Info_model.dart';

/// صفحة عرض تفاصيل المرض
///
/// تعرض معلومات مفصلة عن المرض المحدد
class ShowDiseases extends StatelessWidget {
  /// صورة المرض
  final String image;

  /// اسم المرض
  final String name;

  /// تفاصيل المرض
  final String details;

  /// معلومات إضافية عن المرض
  final List<DiseaseReferralInfo> info;

  /// إنشاء صفحة عرض تفاصيل المرض
  const ShowDiseases({
    super.key,
    required this.image,
    required this.name,
    required this.details,
    required this.info,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        floatHeaderSlivers: true,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) =>
            [
          _buildAppBar(context),
        ],
        body: _buildBody(context),
      ),
    );
  }

  /// بناء شريط التطبيق
  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      foregroundColor: Colors.white,
      backgroundColor: AssetsColors.dufaultGreencolor,
      centerTitle: true,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.indigo,
        statusBarIconBrightness: Brightness.light,
      ),
      pinned: true,
      snap: false,
      expandedHeight: 300.0,
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        title: Text(
          name,
          style: TextStyles.of(context).bodySmall(
            fontFamily: AssetsFonts.sultan,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        background: _buildHeaderImage(),
      ),
    );
  }

  /// بناء صورة رأس الصفحة
  Widget _buildHeaderImage() {
    try {
      // استخدام FutureBuilder لتحميل الصورة بشكل غير متزامن
      return FutureBuilder<Uint8List>(
        // استخدام Future.microtask لتحسين الأداء
        future: Future.microtask(() => base64Decode(image)),
        builder: (context, snapshot) {
          // عرض مؤشر التحميل أثناء فك تشفير الصورة
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: Colors.grey.shade700,
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white70,
                ),
              ),
            );
          }

          // عرض صورة الخطأ في حالة وجود خطأ
          if (snapshot.hasError || !snapshot.hasData) {
            return _buildErrorImage();
          }

          // عرض الصورة بعد فك تشفيرها
          return Stack(
            fit: StackFit.expand,
            children: [
              // الصورة الأساسية
              Image.memory(
                snapshot.data!,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                // تخزين الصورة مؤقتاً لتحسين الأداء
                cacheWidth: 1200,
                gaplessPlayback: true,
                errorBuilder: (context, error, stackTrace) =>
                    _buildErrorImage(),
              ),
              // طبقة تظليل لتحسين قراءة النص
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withAlpha(25),
                      Colors.black.withAlpha(128),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      );
    } catch (e) {
      return _buildErrorImage();
    }
  }

  /// بناء صورة الخطأ (تظهر عند فشل تحميل الصورة)
  Widget _buildErrorImage() {
    return Container(
      color: Colors.grey.shade700,
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          size: 80,
          color: Colors.white54,
        ),
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عرض تفاصيل المرض
              _buildDiseaseDetails(context),

              // عرض معلومات إضافية عن المرض
              _buildDiseaseInfo(context),

              // مساحة إضافية في الأسفل
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم تفاصيل المرض
  Widget _buildDiseaseDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            'معلومات المرض',
            style: TextStyles.of(context).titleSmall(
              fontFamily: AssetsFonts.cairo,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),
        ),

        // استخدام نفس تصميم InfoCard تمامًا
        Card(
          elevation: 4,
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          clipBehavior: Clip.none,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.bug_report_outlined,
                  color: AssetsColors.dufaultGreencolor,
                  size: 28,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوصف',
                        style: TextStyles.of(context).bodySmall(
                          fontFamily: AssetsFonts.sultan,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AssetsColors.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        details,
                        style: TextStyles.of(context).bodyMedium(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 16,
                          height: 1.8,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // عنوان قسم المعلومات الإضافية (إذا وجدت)
        if (info.isNotEmpty) ...[
          const SizedBox(height: 24),
          Text(
            'معلومات إضافية',
            style: TextStyles.of(context).titleSmall(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),
          const SizedBox(height: 8),
        ],
      ],
    );
  }

  /// بناء قسم معلومات إضافية عن المرض
  Widget _buildDiseaseInfo(BuildContext context) {
    if (info.isEmpty) {
      return const SizedBox();
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) => _buildInfoItem(context, info[index]),
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemCount: info.length,
      padding: EdgeInsets.zero,
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(BuildContext context, DiseaseReferralInfo infoItem) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.none,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.pest_control_outlined,
              color: AssetsColors.dufaultGreencolor,
              size: 28,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    infoItem.title,
                    style: TextStyles.of(context).bodySmall(
                      fontFamily: AssetsFonts.sultan,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    infoItem.details,
                    style: TextStyles.of(context).bodyMedium(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 16,
                      height: 1.8,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
