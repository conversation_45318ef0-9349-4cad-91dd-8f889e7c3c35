import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';

import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_model.dart';
import '../../../data/models/crops/plant_operation_model.dart';
import '../../../data/models/crops/plant_referral_data_model.dart';
import '../../../data/models/crops/plants_model.dart';
import 'crop_detail_page.dart';

/// مكون بطاقة المحصول الزراعي
///
/// يستخدم لعرض معلومات المحصول الزراعي في واجهة المستخدم
class CropCard extends StatelessWidget {
  /// بيانات المحصول الزراعي
  final Plant crop;

  /// بيانات الإحالة للمحصول
  final List<PlantReferralData> referralData;

  /// عمليات المحصول
  final List<PlantOperation> operations;

  /// أمراض المحصول
  final List<Disease> diseases;

  /// إنشاء بطاقة محصول زراعي جديدة
  const CropCard({
    super.key,
    required this.crop,
    required this.referralData,
    required this.operations,
    required this.diseases,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        elevation: 8, // تقليل الظل قليلاً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12), // تحسين شكل الحواف
        ),
        clipBehavior: Clip.antiAliasWithSaveLayer,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _navigateToDetailPage(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المحصول
              _buildCropImage(),

              // اسم المحصول
              Padding(
                padding: const EdgeInsets.all(16.0), // زيادة التباعد
                child: Text(
                  crop.name,
                  style: TextStyles.of(context).bodySmall(
                    color: Colors.black87,
                    fontFamily: AssetsFonts.sultan,
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ),

              const SizedBox(height: 8), // تباعد إضافي في النهاية
            ],
          ),
        ),
      ),
    );
  }

  /// بناء صورة المحصول
  Widget _buildCropImage() {
    return SizedBox(
      width: double.infinity,
      height: 220, // تقليل الارتفاع قليلاً
      child: _getImage(),
    );
  }

  /// الحصول على صورة المحصول
  Widget _getImage() {
    try {
      // استخدام FutureBuilder لتحميل الصورة بشكل غير متزامن
      return FutureBuilder<Uint8List>(
        // استخدام Future.microtask لتحسين الأداء
        future: Future.microtask(() => base64Decode(crop.mainPhoto)),
        builder: (context, snapshot) {
          // عرض مؤشر التحميل أثناء فك تشفير الصورة
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.grey.shade400,
              ),
            );
          }

          // عرض صورة الخطأ في حالة وجود خطأ
          if (snapshot.hasError || !snapshot.hasData) {
            return _buildErrorImage();
          }

          // عرض الصورة بعد فك تشفيرها
          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            // تخزين الصورة مؤقتاً لتحسين الأداء
            cacheWidth: 800,
            gaplessPlayback: true,
            errorBuilder: (context, error, stackTrace) => _buildErrorImage(),
          );
        },
      );
    } catch (e) {
      return _buildErrorImage();
    }
  }

  /// بناء صورة الخطأ (تظهر عند فشل تحميل الصورة)
  Widget _buildErrorImage() {
    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Icon(
          Icons.image_not_supported,
          size: 50,
          color: Colors.grey.shade400,
        ),
      ),
    );
  }

  /// الانتقال إلى صفحة تفاصيل المحصول
  void _navigateToDetailPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CropDetailPage(
          crop: crop,
          referralData: referralData,
          operations: operations,
          diseases: diseases,
        ),
      ),
    );
  }
}
