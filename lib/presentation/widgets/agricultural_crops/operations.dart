import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/plant_operation_model.dart';
import '../shared/info_card.dart';

/// مكون عرض عمليات المحصول الزراعي
///
/// يعرض قائمة العمليات المرتبطة بالمحصول الزراعي
class Operations extends StatelessWidget {
  /// قائمة عمليات المحصول
  final List<PlantOperation> operation;

  /// إنشاء مكون عرض العمليات
  const Operations({super.key, required this.operation});

  @override
  Widget build(BuildContext context) {
    // التحقق من وجود عمليات للعرض
    if (operation.isEmpty) {
      return _buildEmptyOperations(context);
    }

    // تصفية العمليات غير الصالحة (التي تحتوي على تفاصيل فارغة)
    final validOperations =
        operation.where((op) => op.details.isNotEmpty).toList();

    // التحقق مرة أخرى بعد التصفية
    if (validOperations.isEmpty) {
      return _buildEmptyOperations(context);
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'العمليات الزراعية',
              style: TextStyles.of(context).titleSmall(
                fontFamily: AssetsFonts.cairo,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AssetsColors.primary,
              ),
            ),
          ),
          const Divider(height: 1),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemBuilder: (context, index) =>
                _buildOperationItem(context, validOperations[index]),
            itemCount: validOperations.length,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر العملية
  Widget _buildOperationItem(BuildContext context, PlantOperation operation) {
    return InfoCard(
      title: operation.name,
      details: operation.details,
      expandable: true,
      icon: Icons.grass_outlined,
    );
  }

  /// بناء عرض فارغ عندما لا توجد عمليات
  Widget _buildEmptyOperations(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grass_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عمليات متاحة لهذا المحصول',
            style: TextStyles.of(context).bodyMedium(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
