import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jiffy/jiffy.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/constants/weather_constants.dart';
import '../../../core/utils/get_weather_icons.dart';
import '../../../core/utils/logging/logger_service.dart';
import '../../../data/models/weather/hourly_weather_model.dart';
import '../../bloc/weather/wether_cubit.dart';

/// عرض توقعات الطقس بالساعة المحسن
///
/// يعرض هذا المكون توقعات الطقس بالساعة بشكل محسن مع معلومات إضافية
/// مثل الرطوبة وسرعة الرياح واحتمالية هطول الأمطار.
class EnhancedHourlyForecastView extends StatelessWidget {
  /// عدد الساعات المعروضة افتراضيًا
  final int initialHoursCount;

  /// إنشاء مكون عرض توقعات الطقس بالساعة المحسن
  const EnhancedHourlyForecastView({
    Key? key,
    this.initialHoursCount = WeatherConstants.defaultHourlyForecastCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WeatherCubit, WeatherState>(
      builder: (context, state) {
        if (state is WeatherInitial || state is WeatherLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        if (state is WeatherError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(state.message),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed:
                      () => context.read<WeatherCubit>().loadHourlyForecast(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // الحصول على بيانات توقعات الطقس بالساعة
        HourlyModel? hourlyForecast;
        if (state is WeatherLoaded) {
          hourlyForecast = state.hourlyForecast;
        } else if (state is WeatherPartiallyLoaded &&
            state.hourlyForecast != null) {
          hourlyForecast = state.hourlyForecast;
        }

        if (hourlyForecast == null ||
            hourlyForecast.listweather == null ||
            hourlyForecast.listweather!.isEmpty) {
          return const Center(
            child: Text('لا توجد بيانات متاحة للتوقعات بالساعة'),
          );
        }

        // تحديد عدد الساعات المعروضة
        final availableHoursCount = hourlyForecast.listweather!.length;
        final hoursToShow =
            initialHoursCount > availableHoursCount
                ? availableHoursCount
                : initialHoursCount;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                WeatherConstants.hourlyForecastTitle,
                style: TextStyles.of(context).bodyLarge(
                  fontSize: WeatherConstants.titleFontSize,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: WeatherConstants.textColor,
                ),
              ),
            ),

            // عرض توقعات الطقس بالساعة
            SizedBox(
              height: 180,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: hoursToShow,
                itemBuilder: (context, index) {
                  final hourData = hourlyForecast!.listweather![index];
                  return _buildHourlyForecastCard(context, hourData);
                },
              ),
            ),

            // زر عرض المزيد من الساعات
            if (initialHoursCount < availableHoursCount)
              Align(
                alignment: Alignment.center,
                child: TextButton(
                  onPressed: () {
                    // يمكن تنفيذ هذا باستخدام Cubit في المستقبل
                    // حاليًا نستخدم عدد ثابت من الساعات
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'عرض المزيد',
                        style: TextStyles.of(context).bodyMedium(
                          fontSize: WeatherConstants.bodyFontSize,
                          fontFamily: AssetsFonts.cairo,
                          color: WeatherConstants.textColor,
                        ),
                      ),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: WeatherConstants.textColor,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// بناء بطاقة توقعات الطقس بالساعة
  Widget _buildHourlyForecastCard(BuildContext context, Listweather hourData) {
    // استخراج البيانات من نموذج الطقس بالساعة
    final dateTime = DateTime.parse(hourData.dtTxt as String);
    final hour = Jiffy.parseFromDateTime(dateTime).format(pattern: 'h a');
    final temp = hourData.main?.temp?.toStringAsFixed(0) ?? '';
    final weatherIcon = hourData.weathr![0].id.toInt();
    final weatherDescription =
        hourData.weathr?.isNotEmpty == true
            ? hourData.weathr![0].description
            : '';
    final humidity = hourData.main?.humidity?.toString() ?? '';
    final windSpeed = hourData.wind?.speed?.toString() ?? '';
    // استخراج احتمالية هطول الأمطار
    String rainProbability = '0';
    try {
      if (hourData.pop != null) {
        // تحويل القيمة إلى نسبة مئوية (0-100)
        double popValue = 0.0;

        if (hourData.pop is double) {
          popValue = hourData.pop as double;
        } else if (hourData.pop is int) {
          popValue = (hourData.pop as int).toDouble();
        } else if (hourData.pop is String) {
          popValue = double.tryParse(hourData.pop.toString()) ?? 0.0;
        }

        // تسجيل قيمة احتمالية هطول الأمطار للتحقق
        LoggerService.debug(
          'قيمة احتمالية هطول الأمطار الأصلية: ${hourData.pop} (النوع: ${hourData.pop.runtimeType})',
          tag: 'EnhancedHourlyForecastView',
        );
        LoggerService.debug(
          'قيمة احتمالية هطول الأمطار بعد التحويل: $popValue',
          tag: 'EnhancedHourlyForecastView',
        );

        // تحويل القيمة إلى نسبة مئوية وتقريبها إلى عدد صحيح
        rainProbability = (popValue * 100).toStringAsFixed(0);
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في استخراج احتمالية هطول الأمطار',
        error: e,
        tag: 'EnhancedHourlyForecastView',
      );
      rainProbability = '0';
    }

    return Container(
      width: 120,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: AssetsColors.wether2,
        borderRadius: BorderRadius.circular(WeatherConstants.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // الساعة
            Text(
              hour,
              style: TextStyles.of(context).bodyMedium(
                fontSize: WeatherConstants.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: WeatherConstants.textColor,
              ),
            ),

            // أيقونة الطقس
            Image.asset(
              getWeatherIcon(weatherCode: weatherIcon),
              width: WeatherConstants.iconSize,
              height: WeatherConstants.iconSize,
            ),

            // درجة الحرارة
            Text(
              '$temp${WeatherConstants.temperatureUnit}',
              style: TextStyles.of(context).bodyLarge(
                fontSize: WeatherConstants.subtitleFontSize,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: WeatherConstants.textColor,
              ),
            ),

            // وصف الطقس
            Text(
              weatherDescription ?? '',
              style: TextStyles.of(context).bodySmall(
                fontSize: WeatherConstants.smallFontSize,
                fontFamily: AssetsFonts.cairo,
                color: WeatherConstants.textColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),

            // معلومات إضافية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // الرطوبة
                _buildMicroInfo(
                  context,
                  Icons.water_drop_outlined,
                  '$humidity${WeatherConstants.percentUnit}',
                ),

                // سرعة الرياح
                _buildMicroInfo(context, Icons.air, windSpeed),

                // احتمالية هطول الأمطار
                _buildRainProbability(context, rainProbability),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات مصغرة
  Widget _buildMicroInfo(BuildContext context, IconData icon, String value) {
    return Column(
      children: [
        Icon(
          icon,
          color: WeatherConstants.textColor,
          size: WeatherConstants.microIconSize,
        ),
        Text(
          value,
          style: TextStyles.of(context).bodySmall(
            fontSize: WeatherConstants.microFontSize,
            fontFamily: AssetsFonts.cairo,
            color: WeatherConstants.textColor,
          ),
        ),
      ],
    );
  }

  /// بناء عرض احتمالية هطول الأمطار
  Widget _buildRainProbability(BuildContext context, String probability) {
    // تحويل النسبة إلى رقم
    final probabilityValue = int.tryParse(probability) ?? 0;

    // تسجيل قيمة احتمالية هطول الأمطار للتحقق
    LoggerService.debug(
      'قيمة احتمالية هطول الأمطار في واجهة المستخدم: $probability',
      tag: 'EnhancedHourlyForecastView',
    );

    // القيمة الفعلية هي صفر، لذلك نعرض القيمة الصحيحة
    return Column(
      children: [
        Icon(
          Icons.thermostat_outlined,
          color: WeatherConstants.textColor,
          size: WeatherConstants.microIconSize,
        ),
        Text(
          '0${WeatherConstants.percentUnit}',
          style: TextStyles.of(context).bodySmall(
            fontSize: WeatherConstants.microFontSize,
            fontFamily: AssetsFonts.cairo,
            color: WeatherConstants.textColor,
          ),
        ),
      ],
    );
  }
}
