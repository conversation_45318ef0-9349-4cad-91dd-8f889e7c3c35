import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';

import '../../../imports.dart';

/// بطاقة معلومات الطقس الأساسية
/// 
/// مكون مشترك لعرض معلومات الطقس الأساسية
/// مع إمكانية التخصيص والتحكم في المظهر
class WeatherInfoCard extends StatelessWidget {
  /// إنشاء بطاقة معلومات الطقس
  const WeatherInfoCard({
    super.key,
    required this.weather,
    required this.weeklyForecast,
    this.showDate = true,
    this.showLocation = true,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 20,
    this.padding = const EdgeInsets.all(20),
  });

  /// بيانات الطقس الحالي
  final WeatherModel weather;
  
  /// توقعات الطقس الأسبوعية
  final WeeklyModel weeklyForecast;
  
  /// ما إذا كان يجب عرض التاريخ
  final bool showDate;
  
  /// ما إذا كان يجب عرض الموقع
  final bool showLocation;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// لون النص
  final Color? textColor;
  
  /// نصف قطر الحواف
  final double borderRadius;
  
  /// المسافة الداخلية
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? AssetsColors.primary;
    final effectiveTextColor = textColor ?? AssetsColors.kWhite;

    return Container(
      clipBehavior: Clip.antiAliasWithSaveLayer,
      width: double.infinity,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: padding,
        child: Column(
          children: [
            if (showDate) ...[
              _buildDateSection(context, effectiveTextColor),
              const SizedBox(height: 10),
            ],
            
            if (showLocation) ...[
              _buildLocationSection(context, effectiveTextColor),
              const SizedBox(height: 20),
            ],

            // عرض درجة الحرارة والأيقونة
            _buildTemperatureSection(effectiveTextColor),
            const SizedBox(height: 10),

            // عرض وصف الطقس
            _buildDescriptionSection(context, effectiveTextColor),
            const SizedBox(height: 20),

            // عرض معلومات إضافية
            _buildAdditionalInfoSection(effectiveBackgroundColor),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التاريخ
  Widget _buildDateSection(BuildContext context, Color textColor) {
    return Text(
      Jiffy.now().format(pattern: 'EEEE، dd MMMM yyyy'),
      style: TextStyles.of(context).bodyLarge(
        fontSize: 18,
        fontFamily: AssetsFonts.cairo,
        fontWeight: FontWeight.bold,
        color: textColor,
      ),
    );
  }

  /// بناء قسم الموقع
  Widget _buildLocationSection(BuildContext context, Color textColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.location_on,
          color: textColor,
          size: 25,
        ),
        const SizedBox(width: 5),
        Text(
          weather.name ?? 'موقعك الحالي',
          style: TextStyles.of(context).bodyLarge(
            fontSize: 18,
            fontFamily: AssetsFonts.cairo,
            color: textColor,
          ),
        ),
      ],
    );
  }

  /// بناء قسم درجة الحرارة
  Widget _buildTemperatureSection(Color textColor) {
    final temperature = weeklyForecast.daily?.temperature2mMax?.isNotEmpty == true
        ? weeklyForecast.daily!.temperature2mMax![0].round().toString()
        : weather.main?.temp?.round().toString() ?? '0';

    final iconPath = weather.weather?.isNotEmpty == true
        ? 'assets/icons/${weather.weather![0].icon?.replaceAll('n', 'd') ?? 'default'}.png'
        : 'assets/icons/default.png';

    return StackHomeWeather(
      numberWeather: temperature,
      color: textColor,
      imageAssets: iconPath,
    );
  }

  /// بناء قسم الوصف
  Widget _buildDescriptionSection(BuildContext context, Color textColor) {
    final description = weather.weather?.isNotEmpty == true &&
            weather.weather![0].description != null
        ? weather.weather![0].description.toString()
        : 'طقس معتدل';

    return Text(
      description,
      textAlign: TextAlign.center,
      style: TextStyles.of(context).bodyLarge(
        fontSize: 16,
        fontFamily: AssetsFonts.messiri,
        fontWeight: FontWeight.bold,
        color: textColor,
      ),
    );
  }

  /// بناء قسم المعلومات الإضافية
  Widget _buildAdditionalInfoSection(Color backgroundColor) {
    return RowWeather(
      temperature: weather.main?.temp?.round().toString() ?? '0',
      humidity: weather.main?.humidity?.toString() ?? '0',
      windSpeed: weather.wind?.speed?.toString() ?? '0',
      color: backgroundColor,
    );
  }
}

/// بطاقة معلومات الطقس المبسطة
/// 
/// نسخة مبسطة من بطاقة معلومات الطقس للاستخدام السريع
class SimpleWeatherInfoCard extends StatelessWidget {
  /// إنشاء بطاقة معلومات الطقس المبسطة
  const SimpleWeatherInfoCard({
    super.key,
    required this.weather,
    this.backgroundColor,
    this.textColor,
  });

  /// بيانات الطقس
  final WeatherModel weather;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// لون النص
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor ?? AssetsColors.primary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الطقس
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(30),
            ),
            child: const Icon(
              Icons.wb_sunny,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          
          // معلومات الطقس
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${weather.main?.temp?.round() ?? 0}°',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: textColor ?? Colors.white,
                  ),
                ),
                Text(
                  weather.weather?.isNotEmpty == true
                      ? weather.weather![0].description ?? 'طقس معتدل'
                      : 'طقس معتدل',
                  style: TextStyle(
                    fontSize: 14,
                    color: (textColor ?? Colors.white).withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
