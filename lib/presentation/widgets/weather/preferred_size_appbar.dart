import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';

PreferredSize buildPrefSizeAppBar(
    {required Color backgroundColor,
    required Color groundColorIcon,
    required GestureTapCallback onTapLading,
    required String text,
    required IconData iconLading,
    required Color colorIcon,
    required BuildContext context,
    required GestureTapCallback onTapAction,
    required IconData iconAction}) {
  return PreferredSize(
    preferredSize: Size.fromHeight(60.0),
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25.0, vertical: 10),
      child: AppBar(
        backgroundColor: backgroundColor,
        elevation: 0,
        leadingWidth: 40,
        centerTitle: true,
        toolbarHeight: 40,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: groundColorIcon,
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: onTapAction,
            child: Icon(
              iconAction,
              color: colorIcon,
              size: 30,
            ),
          ),
        ),
        title: Text(
          text,
          style: TextStyles.of(context).bodyLarge(
            fontSize: 17,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kWhite,
          ),
        ),
        actions: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: groundColorIcon,
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: onTapAction,
              child: Icon(
                iconAction,
                color: colorIcon,
                size: 30,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
