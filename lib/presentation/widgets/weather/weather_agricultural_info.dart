import 'package:flutter/material.dart';

import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/constants/weather_constants.dart';
import '../../../core/index.dart';
import '../../../data/models/weather/weather_model.dart';

/// مكون معلومات الطقس الزراعية
///
/// يعرض هذا المكون معلومات الطقس المهمة للمزارعين مثل شروق وغروب الشمس،
/// الضغط الجوي، اتجاه الرياح، ومؤشر ملاءمة الطقس للأنشطة الزراعية.
class WeatherAgriculturalInfo extends StatelessWidget {
  /// بيانات الطقس
  final WeatherModel weather;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color textColor;

  /// إنشاء مكون معلومات الطقس الزراعية
  const WeatherAgriculturalInfo({
    Key? key,
    required this.weather,
    this.backgroundColor,
    this.textColor = WeatherConstants.textColor,
  }) : super(key: key);

  /// الحصول على لون الخلفية الافتراضي
  Color get defaultBackgroundColor => WeatherConstants.cardBackgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: backgroundColor ?? defaultBackgroundColor,
        borderRadius: BorderRadius.circular(WeatherConstants.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(WeatherConstants.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              WeatherConstants.agriculturalInfoTitle,
              style: TextStyles.of(context).bodyLarge(
                fontSize: WeatherConstants.titleFontSize,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 16),

            // معلومات الشروق والغروب
            _buildSunriseSunsetInfo(context),
            Divider(color: WeatherConstants.dividerColor, height: 24),

            // معلومات الضغط الجوي والرياح
            _buildPressureWindInfo(context),
            Divider(color: WeatherConstants.dividerColor, height: 24),

            // مؤشر ملاءمة الطقس للأنشطة الزراعية
            _buildAgriculturalActivityIndicator(context),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الشروق والغروب
  Widget _buildSunriseSunsetInfo(BuildContext context) {
    // تحويل توقيت الشروق والغروب من يونكس تايم إلى وقت محلي
    DateTime? sunriseTime;
    if (weather.sys?.sunrise != null) {
      try {
        // التحقق من نوع البيانات وتحويلها بشكل صحيح
        final sunriseValue = weather.sys!.sunrise;
        if (sunriseValue is int) {
          sunriseTime = DateTime.fromMillisecondsSinceEpoch(sunriseValue * 1000);
        } else if (sunriseValue is String) {
          final sunriseInt = int.tryParse(sunriseValue);
          if (sunriseInt != null) {
            sunriseTime = DateTime.fromMillisecondsSinceEpoch(sunriseInt * 1000);
          }
        } else if (sunriseValue is double) {
          sunriseTime = DateTime.fromMillisecondsSinceEpoch((sunriseValue.toInt()) * 1000);
        }
      } catch (e) {
        LoggerService.error(
          'خطأ في تحويل وقت الشروق',
          error: e,
          tag: 'WeatherAgriculturalInfo',
        );
        sunriseTime = null;
      }
    }

    DateTime? sunsetTime;
    if (weather.sys?.sunset != null) {
      try {
        // التحقق من نوع البيانات وتحويلها بشكل صحيح
        final sunsetValue = weather.sys!.sunset;
        if (sunsetValue is int) {
          sunsetTime = DateTime.fromMillisecondsSinceEpoch(sunsetValue * 1000);
        } else if (sunsetValue is String) {
          final sunsetInt = int.tryParse(sunsetValue);
          if (sunsetInt != null) {
            sunsetTime = DateTime.fromMillisecondsSinceEpoch(sunsetInt * 1000);
          }
        } else if (sunsetValue is double) {
          sunsetTime = DateTime.fromMillisecondsSinceEpoch((sunsetValue.toInt()) * 1000);
        }
      } catch (e) {
        LoggerService.error(
          'خطأ في تحويل وقت الغروب',
          error: e,
          tag: 'WeatherAgriculturalInfo',
        );
        sunsetTime = null;
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // معلومات الشروق
        _buildInfoItem(
          context,
          WeatherConstants.sunriseInfo,
          sunriseTime != null
              ? '${sunriseTime.hour}:${sunriseTime.minute.toString().padLeft(2, '0')}'
              : '--:--',
          Icons.wb_sunny_outlined,
        ),

        // معلومات الغروب
        _buildInfoItem(
          context,
          WeatherConstants.sunsetInfo,
          sunsetTime != null
              ? '${sunsetTime.hour}:${sunsetTime.minute.toString().padLeft(2, '0')}'
              : '--:--',
          Icons.nightlight_round,
        ),

        // طول النهار
        _buildInfoItem(
          context,
          WeatherConstants.dayLengthInfo,
          _calculateDayLength(sunriseTime, sunsetTime),
          Icons.access_time,
        ),
      ],
    );
  }

  /// بناء معلومات الضغط الجوي والرياح
  Widget _buildPressureWindInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // معلومات الضغط الجوي
        _buildInfoItem(
          context,
          WeatherConstants.pressureInfo,
          weather.main?.pressure != null
              ? '${weather.main!.pressure} ${WeatherConstants.pressureUnit}'
              : '--',
          Icons.compress,
        ),

        // معلومات سرعة الرياح
        _buildInfoItem(
          context,
          WeatherConstants.windSpeedInfo,
          weather.wind?.speed != null
              ? '${weather.wind!.speed} ${WeatherConstants.windSpeedUnit}'
              : '--',
          Icons.air,
        ),

        // معلومات اتجاه الرياح
        _buildInfoItem(
          context,
          WeatherConstants.windDirectionInfo,
          weather.wind?.deg != null ? _getWindDirection(weather.wind!.deg) : '--',
          Icons.navigation,
        ),
      ],
    );
  }

  /// بناء مؤشر ملاءمة الطقس للأنشطة الزراعية
  Widget _buildAgriculturalActivityIndicator(BuildContext context) {
    // تحديد ملاءمة الطقس للأنشطة الزراعية المختلفة
    final irrigationSuitability = _calculateIrrigationSuitability();
    final sprayingSuitability = _calculateSprayingSuitability();
    final harvestingSuitability = _calculateHarvestingSuitability();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          WeatherConstants.activitySuitabilityTitle,
          style: TextStyles.of(context).bodyMedium(
            fontSize: WeatherConstants.subtitleFontSize,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // مؤشر ملاءمة الري
            _buildSuitabilityIndicator(
              context,
              WeatherConstants.irrigationActivity,
              irrigationSuitability,
              Icons.water_drop_outlined,
            ),

            // مؤشر ملاءمة الرش
            _buildSuitabilityIndicator(
              context,
              WeatherConstants.sprayingActivity,
              sprayingSuitability,
              Icons.sanitizer_outlined,
            ),

            // مؤشر ملاءمة الحصاد
            _buildSuitabilityIndicator(
              context,
              WeatherConstants.harvestingActivity,
              harvestingSuitability,
              Icons.agriculture_outlined,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: textColor,
          size: WeatherConstants.smallIconSize,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyles.of(context).bodyMedium(
            fontSize: WeatherConstants.bodyFontSize,
            fontFamily: AssetsFonts.cairo,
            color: textColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyles.of(context).bodyMedium(
            fontSize: WeatherConstants.bodyFontSize,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ],
    );
  }

  /// بناء مؤشر ملاءمة
  Widget _buildSuitabilityIndicator(
    BuildContext context,
    String activity,
    SuitabilityLevel suitability,
    IconData icon,
  ) {
    // تحديد لون المؤشر بناءً على مستوى الملاءمة
    Color indicatorColor;
    String suitabilityText;

    switch (suitability) {
      case SuitabilityLevel.high:
        indicatorColor = WeatherConstants.suitabilityHighColor;
        suitabilityText = WeatherConstants.suitabilityHighText;
        break;
      case SuitabilityLevel.medium:
        indicatorColor = WeatherConstants.suitabilityMediumColor;
        suitabilityText = WeatherConstants.suitabilityMediumText;
        break;
      case SuitabilityLevel.low:
        indicatorColor = WeatherConstants.suitabilityLowColor;
        suitabilityText = WeatherConstants.suitabilityLowText;
        break;
    }

    return Column(
      children: [
        Icon(
          icon,
          color: indicatorColor,
          size: WeatherConstants.smallIconSize,
        ),
        const SizedBox(height: 8),
        Text(
          activity,
          style: TextStyles.of(context).bodyMedium(
            fontSize: WeatherConstants.bodyFontSize,
            fontFamily: AssetsFonts.cairo,
            color: textColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: indicatorColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            suitabilityText,
            style: TextStyles.of(context).bodyMedium(
              fontSize: WeatherConstants.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: indicatorColor,
            ),
          ),
        ),
      ],
    );
  }

  /// حساب طول النهار
  String _calculateDayLength(DateTime? sunrise, DateTime? sunset) {
    if (sunrise == null || sunset == null) {
      return '--:--';
    }

    final difference = sunset.difference(sunrise);
    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;

    return '$hours:${minutes.toString().padLeft(2, '0')}';
  }

  /// الحصول على اتجاه الرياح
  String _getWindDirection(dynamic degrees) {
    if (degrees == null) return '--';

    final deg = (degrees as num).toDouble();

    if (deg >= 337.5 || deg < 22.5) {
      return 'شمال';
    } else if (deg >= 22.5 && deg < 67.5) {
      return 'شمال شرق';
    } else if (deg >= 67.5 && deg < 112.5) {
      return 'شرق';
    } else if (deg >= 112.5 && deg < 157.5) {
      return 'جنوب شرق';
    } else if (deg >= 157.5 && deg < 202.5) {
      return 'جنوب';
    } else if (deg >= 202.5 && deg < 247.5) {
      return 'جنوب غرب';
    } else if (deg >= 247.5 && deg < 292.5) {
      return 'غرب';
    } else {
      return 'شمال غرب';
    }
  }

  /// حساب ملاءمة الري
  SuitabilityLevel _calculateIrrigationSuitability() {
    // التحقق من توفر البيانات اللازمة
    if (weather.main?.humidity == null || weather.wind?.speed == null) {
      return SuitabilityLevel.medium;
    }

    final humidity = (weather.main!.humidity as num?)?.toDouble() ?? 0.0;
    final windSpeed = (weather.wind!.speed as num?)?.toDouble() ?? 0.0;

    // الري غير مناسب إذا كانت الرطوبة عالية أو كانت الرياح قوية
    if (humidity > 80 || windSpeed > 8) {
      return SuitabilityLevel.low;
    }
    // الري مناسب بشكل متوسط إذا كانت الرطوبة متوسطة
    else if (humidity > 60) {
      return SuitabilityLevel.medium;
    }
    // الري مناسب إذا كانت الرطوبة منخفضة والرياح خفيفة
    else {
      return SuitabilityLevel.high;
    }
  }

  /// حساب ملاءمة الرش
  SuitabilityLevel _calculateSprayingSuitability() {
    // التحقق من توفر البيانات اللازمة
    if (weather.main?.temp == null || weather.wind?.speed == null || weather.main?.humidity == null) {
      return SuitabilityLevel.medium;
    }

    final temperature = (weather.main!.temp as num?)?.toDouble() ?? 0.0;
    final windSpeed = (weather.wind!.speed as num?)?.toDouble() ?? 0.0;
    final humidity = (weather.main!.humidity as num?)?.toDouble() ?? 0.0;

    // الرش غير مناسب إذا كانت درجة الحرارة عالية أو الرياح قوية أو الرطوبة منخفضة جدًا
    if (temperature > 30 || windSpeed > 15 || humidity < 30) {
      return SuitabilityLevel.low;
    }
    // الرش مناسب بشكل متوسط إذا كانت الظروف متوسطة
    else if (temperature > 25 || windSpeed > 10 || humidity < 40) {
      return SuitabilityLevel.medium;
    }
    // الرش مناسب إذا كانت الظروف مثالية
    else {
      return SuitabilityLevel.high;
    }
  }

  /// حساب ملاءمة الحصاد
  SuitabilityLevel _calculateHarvestingSuitability() {
    // التحقق من توفر البيانات اللازمة
    if (weather.main?.temp == null || weather.main?.humidity == null) {
      return SuitabilityLevel.medium;
    }

    final temperature = (weather.main!.temp as num?)?.toDouble() ?? 0.0;
    final humidity = (weather.main!.humidity as num?)?.toDouble() ?? 0.0;

    // الحصاد غير مناسب إذا كانت درجة الحرارة عالية جدًا أو الرطوبة عالية جدًا
    if (temperature > 35 || humidity > 85) {
      return SuitabilityLevel.low;
    }
    // الحصاد مناسب بشكل متوسط إذا كانت الظروف متوسطة
    else if (temperature > 30 || humidity > 70) {
      return SuitabilityLevel.medium;
    }
    // الحصاد مناسب إذا كانت الظروف مثالية
    else {
      return SuitabilityLevel.high;
    }
  }
}

/// مستويات ملاءمة الطقس للأنشطة الزراعية
enum SuitabilityLevel {
  /// ملاءمة عالية
  high,

  /// ملاءمة متوسطة
  medium,

  /// ملاءمة منخفضة
  low,
}
