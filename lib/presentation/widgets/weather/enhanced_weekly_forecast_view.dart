import 'package:agriculture/core/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/weather_constants.dart';
import '../../../core/utils/get_weather_icons.dart';
import '../../../data/models/weather/weekly_model.dart';
import '../../bloc/weather/wether_cubit.dart';
import 'temperature_display.dart';

/// عرض توقعات الطقس الأسبوعية المحسن
///
/// يعرض هذا المكون توقعات الطقس الأسبوعية بشكل محسن مع معلومات إضافية
/// مثل الرطوبة وسرعة الرياح واحتمالية هطول الأمطار.
class EnhancedWeeklyForecastView extends StatelessWidget {
  /// إنشاء مكون عرض توقعات الطقس الأسبوعية المحسن
  const EnhancedWeeklyForecastView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WeatherCubit, WeatherState>(
      listener: (context, state) {
        if (state is WeatherError) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state is WeatherInitial || state is WeatherLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        if (state is WeatherError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(state.message),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed:
                      () => context.read<WeatherCubit>().loadWeeklyForecast(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // الحصول على بيانات توقعات الطقس الأسبوعية
        WeeklyModel? weeklyForecast;
        if (state is WeatherLoaded) {
          weeklyForecast = state.weeklyForecast;
        } else if (state is WeatherPartiallyLoaded &&
            state.weeklyForecast != null) {
          weeklyForecast = state.weeklyForecast;
        }

        if (weeklyForecast == null ||
            weeklyForecast.daily == null ||
            weeklyForecast.daily!.time == null ||
            weeklyForecast.daily!.time!.isEmpty) {
          return const Center(
            child: Text('لا توجد بيانات متاحة للتوقعات الأسبوعية'),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                WeatherConstants.weeklyForecastTitle,
                style: TextStyles.of(context).bodyLarge(
                  fontSize: WeatherConstants.titleFontSize,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: WeatherConstants.textColor,
                ),
              ),
            ),

            // عرض توقعات الطقس الأسبوعية
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: weeklyForecast.daily!.weatherCode!.length,
              separatorBuilder: (context, index) => const SizedBox(height: 10),
              itemBuilder: (context, index) {
                // استخراج البيانات من نموذج الطقس الأسبوعي
                final dayName =
                    index > 0
                        ? DateTime.parse(
                          weeklyForecast!.daily!.time![index],
                        ).dayOfWeek
                        : "";
                final dayNow =
                    index == 0
                        ? DateTime.parse(
                          weeklyForecast!.daily!.time![0],
                        ).dayOfWeek
                        : "";

                final weatherCode = weeklyForecast!.daily!.weatherCode![index];
                final tempMax =
                    weeklyForecast.daily!.temperature2mMax![index].round();
                final tempMin =
                    weeklyForecast.daily!.temperature2mMin![index].round();

                return _buildWeeklyForecastCard(
                  context,
                  dayName,
                  weatherCode,
                  tempMax,
                  tempMin,
                  dayNow, // اليوم الحالي
                );
              },
            ),
          ],
        );
      },
    );
  }

  /// بناء بطاقة توقعات الطقس الأسبوعية
  Widget _buildWeeklyForecastCard(
    BuildContext context,
    String dayName,
    int weatherCode,
    int tempMax,
    int tempMin,
    String? isToday,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AssetsColors.primary,
        borderRadius: BorderRadius.circular(WeatherConstants.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(WeatherConstants.cardPadding),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // اليوم
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (isToday != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: WeatherConstants.textColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'اليوم',
                      style: TextStyles.of(context).bodySmall(
                        fontSize: WeatherConstants.smallFontSize,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: WeatherConstants.textColor,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                if (dayName != null)
                  Text(
                    dayName,
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: WeatherConstants.bodyFontSize,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: WeatherConstants.textColor,
                    ),
                  ),
              ],
            ),

            // أيقونة الطقس
            Image.asset(
              getWeatherIcon2(weatherCode),
              height: WeatherConstants.iconSize,
            ),

            // درجات الحرارة
            TemperatureDisplay(
              maxTemp: tempMax,
              minTemp: tempMin,
              color: WeatherConstants.textColor,
              maxTempFontSize: WeatherConstants.bodyFontSize,
              minTempFontSize: WeatherConstants.smallFontSize,
            ),
          ],
        ),
      ),
    );
  }

  // /// الحصول على اسم اليوم بالعربية
  // String _getDayName(DateTime date) {
  //   final now = DateTime.now();
  //   final today = DateTime(now.year, now.month, now.day);
  //   final tomorrow = today.add(const Duration(days: 1));
  //
  //   if (date.year == today.year &&
  //       date.month == today.month &&
  //       date.day == today.day) {
  //     return 'اليوم';
  //   } else if (date.year == tomorrow.year &&
  //       date.month == tomorrow.month &&
  //       date.day == tomorrow.day) {
  //     return 'غداً';
  //   }
  //
  //   switch (date.weekday) {
  //     case 1:
  //       return 'الإثنين';
  //     case 2:
  //       return 'الثلاثاء';
  //     case 3:
  //       return 'الأربعاء';
  //     case 4:
  //       return 'الخميس';
  //     case 5:
  //       return 'الجمعة';
  //     case 6:
  //       return 'السبت';
  //     case 7:
  //       return 'الأحد';
  //     default:
  //       return '';
  //   }
  // }
}
