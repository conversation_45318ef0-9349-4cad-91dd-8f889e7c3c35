import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/constants/weather_constants.dart';

/// عرض درجات الحرارة
///
/// يعرض هذا المكون درجات الحرارة العظمى والصغرى بشكل متناسق
/// حيث تظهر درجة الحرارة العظمى أعلى وبخط أكبر
/// ودرجة الحرارة الصغرى أسفل وبخط أصغر
class TemperatureDisplay extends StatelessWidget {
  /// درجة الحرارة العظمى
  final int maxTemp;
  
  /// درجة الحرارة الصغرى
  final int minTemp;
  
  /// لون النص
  final Color color;
  
  /// حجم خط درجة الحرارة العظمى
  final double maxTempFontSize;
  
  /// حجم خط درجة الحرارة الصغرى
  final double minTempFontSize;
  
  /// إنشاء مكون عرض درجات الحرارة
  const TemperatureDisplay({
    Key? key,
    required this.maxTemp,
    required this.minTemp,
    this.color = AssetsColors.kWhite,
    this.maxTempFontSize = 24,
    this.minTempFontSize = 16,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 5,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // درجة الحرارة العظمى
        Text(
          '$maxTemp${WeatherConstants.temperatureUnit}',
          style: TextStyles.of(context).bodyLarge(
            fontSize: maxTempFontSize,
            fontWeight: FontWeight.bold,
            fontFamily: AssetsFonts.cairo,
            color: color,
          ),
        ),
        const SizedBox(width: 4),
        // درجة الحرارة الصغرى
        Padding(
          padding: const EdgeInsets.only(bottom: 2),
          child: Text(
            '$minTemp${WeatherConstants.temperatureUnit}',
            style: TextStyles.of(context).bodyMedium(
              fontSize: minTempFontSize,
              fontFamily: AssetsFonts.cairo,
              color: color.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
