import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../imports.dart';

/// معالج حالات الطقس
/// 
/// مكون مشترك لمعالجة حالات الطقس المختلفة
/// يقلل من تكرار الكود في صفحات الطقس
class WeatherStateHandler extends StatelessWidget {
  /// إنشاء معالج حالات الطقس
  const WeatherStateHandler({
    super.key,
    required this.loadedBuilder,
    this.showRefreshButton = true,
    this.backgroundColor,
    this.loadingWidget,
    this.errorWidget,
  });

  /// بناء المحتوى عند تحميل البيانات بنجاح
  final Widget Function(BuildContext context, WeatherLoaded state) loadedBuilder;
  
  /// ما إذا كان يجب عرض زر التحديث في حالة الخطأ
  final bool showRefreshButton;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// ويدجت التحميل المخصص
  final Widget? loadingWidget;
  
  /// ويدجت الخطأ المخصص
  final Widget Function(BuildContext context, WeatherError state)? errorWidget;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WeatherCubit, WeatherState>(
      listener: (BuildContext context, WeatherState state) {
        if (state is WeatherError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: () {
                  context.read<WeatherCubit>().refreshWeatherData();
                },
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        // حالة التحميل الأولي
        if (state is WeatherInitial || state is WeatherLoading) {
          return _buildLoadingWidget();
        }
        
        // حالة الخطأ
        if (state is WeatherError) {
          return _buildErrorWidget(context, state);
        }
        
        // حالة تحميل البيانات بنجاح
        if (state is WeatherLoaded) {
          return loadedBuilder(context, state);
        }
        
        // حالة غير متوقعة
        return _buildUnexpectedErrorWidget(context);
      },
    );
  }

  /// بناء ويدجت التحميل
  Widget _buildLoadingWidget() {
    return loadingWidget ?? 
      Container(
        color: backgroundColor,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 16),
              Text(
                'جاري تحميل بيانات الطقس...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
  }

  /// بناء ويدجت الخطأ
  Widget _buildErrorWidget(BuildContext context, WeatherError state) {
    if (errorWidget != null) {
      return errorWidget!(context, state);
    }

    return Container(
      color: backgroundColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                state.isConnectionError 
                  ? Icons.wifi_off 
                  : Icons.error_outline,
                size: 64,
                color: Colors.white70,
              ),
              const SizedBox(height: 16),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              if (showRefreshButton) ...[
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<WeatherCubit>().refreshWeatherData();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء ويدجت الخطأ غير المتوقع
  Widget _buildUnexpectedErrorWidget(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_outlined,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'حدث خطأ غير متوقع',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// معالج حالات الطقس المبسط
/// 
/// نسخة مبسطة من معالج حالات الطقس للاستخدام السريع
class SimpleWeatherStateHandler extends StatelessWidget {
  /// إنشاء معالج حالات الطقس المبسط
  const SimpleWeatherStateHandler({
    super.key,
    required this.builder,
  });

  /// بناء المحتوى
  final Widget Function(BuildContext context, WeatherState state) builder;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WeatherCubit, WeatherState>(
      builder: builder,
    );
  }
}
