import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/assets_weather_png.dart';
import '../../../core/constants/text_styles.dart';

class RowWeather extends StatelessWidget {
  const RowWeather({
    super.key,
    required this.temperature,
    required this.humidity,
    required this.windSpeed,
    required this.color,
  });
  final String temperature;
  final String humidity;
  final String windSpeed;
  final Color color;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 350,
      height: 120,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),

        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color, color.withOpacity(0.8)],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // التنبؤ
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Image(
                  image: AssetImage(AssetsWeatherPng.temperature),
                  height: 35,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$temperature°',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kWhite,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'درجة الحرارة',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 12,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kWhite,
                ),
              ),
            ],
          ),

          // الرطوبة
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Image(
                  image: AssetImage(AssetsWeatherPng.weter),
                  height: 35,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$humidity%',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kWhite,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'الرطوبة',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 12,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kWhite,
                ),
              ),
            ],
          ),

          // سرعة الرياح
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Image(
                  image: AssetImage(AssetsWeatherPng.ruah),
                  height: 35,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$windSpeed كم/س',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kWhite,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'سرعة الرياح',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 12,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kWhite,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
