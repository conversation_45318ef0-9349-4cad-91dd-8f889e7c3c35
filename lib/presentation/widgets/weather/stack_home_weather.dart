import 'package:flutter/material.dart';

import '../../../core/constants/text_styles.dart';

class StackHomeWeather extends StatelessWidget {
  const StackHomeWeather(
      {super.key,
      required this.numberWeather,
      required this.color,
      required this.imageAssets});
  final String numberWeather;
  final Color color;
  final String imageAssets;
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Center(
          child: Text(
            '$numberWeather°',
            style: TextStyles.of(context).headlineSmall(
              fontSize: 100,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ),
        Center(
          child: Opacity(
            opacity: 0.9,
            child: Padding(
              padding: const EdgeInsets.only(top: 50.0),
              child: Image(
                image: AssetImage(imageAssets),
                height: 200,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
