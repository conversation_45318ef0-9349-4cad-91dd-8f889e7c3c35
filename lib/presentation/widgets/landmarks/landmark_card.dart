import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/assets_images.dart';
import '../../../domain/entities/landmark_entity.dart';
import 'landmark_detail_page.dart';

/// أنماط عرض بطاقة المعلم الزراعي
enum LandmarkCardDisplayMode {
  /// عرض أفقي (للقائمة الدوارة)
  horizontal,

  /// عرض رأسي (للقائمة الرئيسية)
  vertical
}

/// مكون بطاقة المعلم الزراعي
///
/// يستخدم لعرض معلومات المعلم الزراعي في واجهة المستخدم
/// يمكن تخصيصه للعرض بشكل أفقي أو رأسي
class LandmarkCard extends StatelessWidget {
  /// بيانات المعلم الزراعي
  final LandmarkEntity landmark;

  /// نمط عرض البطاقة (أفقي أو رأسي)
  final LandmarkCardDisplayMode displayMode;

  /// إنشاء بطاقة معلم زراعي جديدة
  const LandmarkCard({
    super.key,
    required this.landmark,
    this.displayMode = LandmarkCardDisplayMode.vertical,
  });

  @override
  Widget build(BuildContext context) {
    return displayMode == LandmarkCardDisplayMode.horizontal
        ? _buildHorizontalCard(context)
        : _buildVerticalCard(context);
  }

  /// بناء بطاقة أفقية (للقائمة الدوارة)
  Widget _buildHorizontalCard(BuildContext context) {
    return InkWell(
      onTap: () => _navigateToDetailPage(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 5.0,
              spreadRadius: 1.0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        clipBehavior: Clip.antiAliasWithSaveLayer,
        width: 170.0, // زيادة العرض
        height: 190.0, // زيادة الارتفاع
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            // صورة المعلم
            _buildLandmarkImage(
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),

            // اسم المعلم
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(
                        204), // استخدام withAlpha بدلاً من withOpacity
                  ],
                ),
              ),
              padding: const EdgeInsetsDirectional.only(
                top: 20.0,
                bottom: 15.0,
              ),
              width: double.infinity,
              child: Text(
                landmark.name,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16, // زيادة حجم الخط
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold, // جعل الخط أكثر سمكاً
                ),
                maxLines: 1,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة رأسية (للقائمة الرئيسية)
  Widget _buildVerticalCard(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 3.0),
      child: SizedBox(
        child: ColoredBox(
          color: Colors.grey.shade200,
          child: InkWell(
            onTap: () => _navigateToDetailPage(context),
            splashColor: Colors.red,
            highlightColor: Colors.yellow,
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 10.0, horizontal: 5.0),
              child: Row(
                mainAxisAlignment:
                    MainAxisAlignment.center, // توسيط العناصر أفقياً
                crossAxisAlignment:
                    CrossAxisAlignment.center, // توسيط العناصر رأسياً
                children: [
                  // صورة المعلم
                  Container(
                    height: 70.0,
                    width: 70.0,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      image: DecorationImage(
                        image: _getImageProvider(),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20.0),

                  // اسم المعلم
                  Expanded(
                    child: SizedBox(
                      height: 90.0,
                      child: Column(
                        mainAxisAlignment:
                            MainAxisAlignment.center, // توسيط رأسي
                        crossAxisAlignment:
                            CrossAxisAlignment.center, // توسيط أفقي
                        children: [
                          Text(
                            landmark.name,
                            style: TextStyle(
                              fontSize: 18, // زيادة حجم الخط
                              fontFamily: 'sultan',
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87, // تحسين لون النص
                            ),
                            maxLines: 1,
                            textAlign: TextAlign.center, // توسيط النص
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على مزود الصورة (من الإنترنت أو من الأصول المحلية)
  ImageProvider _getImageProvider() {
    if (landmark.imageUrl != null && landmark.imageUrl!.isNotEmpty) {
      return NetworkImage(landmark.imageUrl!);
    }
    return const AssetImage(AssetsImages.imagesLandmarks);
  }

  /// بناء صورة المعلم (مع دعم الصور من الإنترنت)
  Widget _buildLandmarkImage({
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    if (landmark.imageUrl != null && landmark.imageUrl!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: landmark.imageUrl!,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => Container(
          color: Colors.grey.shade300,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Image.asset(
          AssetsImages.imagesLandmarks,
          width: width,
          height: height,
          fit: fit,
        ),
      );
    }

    return Image.asset(
      AssetsImages.imagesLandmarks,
      width: width,
      height: height,
      fit: fit,
    );
  }

  /// الانتقال إلى صفحة تفاصيل المعلم
  void _navigateToDetailPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LandmarkDetailPage(landmark: landmark),
      ),
    );
  }
}
