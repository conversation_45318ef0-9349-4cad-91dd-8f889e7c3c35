import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_fonts.dart';
import '../../../core/utils/calendar_helper.dart';
import '../../bloc/landmarks/information_cubit.dart';

/// مكون عرض التقويم
///
/// يستخدم لعرض تواريخ البداية والنهاية بتقويم معين
class CalendarDisplay extends StatelessWidget {
  /// اسم التقويم
  final String calendarName;

  /// تاريخ البداية
  final DateTime startDate;

  /// تاريخ النهاية
  final DateTime endDate;

  /// نوع التقويم
  final CalendarType calendarType;

  /// لون خلفية العنوان
  final Color titleBackgroundColor;

  /// لون نص العنوان
  final Color titleTextColor;

  /// إنشاء مكون عرض التقويم
  const CalendarDisplay({
    super.key,
    required this.calendarName,
    required this.startDate,
    required this.endDate,
    required this.calendarType,
    this.titleBackgroundColor = Colors.teal,
    this.titleTextColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على كيوبت المعلومات
    final informationCubit = context.read<InformationCubit>();

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(77),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // عنوان التقويم
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              decoration: BoxDecoration(
                color: titleBackgroundColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              child: Text(
                calendarName,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: titleTextColor,
                  fontSize: 16,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // تفاصيل التواريخ
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildDateColumn('من', startDate, informationCubit),
                  _buildDateColumn('إلى', endDate, informationCubit),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عمود التاريخ (العنوان والقيمة)
  Widget _buildDateColumn(String title, DateTime date, InformationCubit cubit) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 5),
        Text(
          _formatDate(date, cubit),
          style: TextStyle(
            fontSize: 16,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// تنسيق التاريخ حسب نوع التقويم
  String _formatDate(DateTime date, InformationCubit cubit) {
    // استخدام مساعد التقويم للحصول على اسم الشهر
    final String monthName =
        CalendarHelper.getMonthName(date, calendarType, cubit);

    // عرض اسم الشهر أولاً ثم رقم اليوم (بدون السنة)
    return '$monthName ${date.day}';
  }
}
