import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:list_wheel_scroll_view_nls/list_wheel_scroll_view_nls.dart';

import '../../../domain/entities/landmark_entity.dart';
import '../../bloc/landmarks/information_cubit.dart';
import '../shared/custom_loading_animation.dart';
import 'landmark_card.dart';

/// مكون محتوى صفحة المعالم الزراعية
///
/// يعرض قائمة المعالم الزراعية بتنسيقين مختلفين:
/// 1. قائمة أفقية دوارة في الأعلى
/// 2. قائمة رأسية في الأسفل
class LandmarksContent extends StatelessWidget {
  /// إنشاء مكون محتوى المعالم الزراعية
  const LandmarksContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<InformationCubit, InformationState>(
      listener: (context, state) {
        if (state is InformationInitial) {
          CustomLoadingAnimation();
        }
      },
      builder: (context, state) {
        // الحصول على كيوبت المعلومات
        final cubit = InformationCubit.get(context);

        // تحميل بيانات الشهور إذا لم تكن محملة بالفعل
        _loadMonthsData(cubit);

        // الحصول على قائمة المعالم
        final landmarks = cubit.landmarks;

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(5.0),
            child: Column(
              children: [
                // القائمة الأفقية الدوارة
                _buildHorizontalCarousel(landmarks),

                // القائمة الرأسية
                _buildVerticalList(landmarks),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء القائمة الأفقية الدوارة
  Widget _buildHorizontalCarousel(List<LandmarkEntity> landmarks) {
    return SizedBox(
      height: 200, // زيادة الارتفاع لعرض أفضل
      width: double.infinity,
      child: ListWheelScrollViewX(
        scrollDirection: Axis.horizontal,
        diameterRatio: 1.8, // تعديل نسبة القطر لتحسين المظهر
        itemExtent: 180, // زيادة عرض العنصر
        magnification: 1.2, // تعديل التكبير لمظهر أفضل
        perspective: 0.002, // إضافة منظور ثلاثي الأبعاد خفيف
        children: landmarks
            .map(
              (landmark) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                child: LandmarkCard(
                  landmark: landmark,
                  displayMode: LandmarkCardDisplayMode.horizontal,
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  /// بناء القائمة الرأسية
  Widget _buildVerticalList(List<LandmarkEntity> landmarks) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (context, index) => LandmarkCard(
          landmark: landmarks[index],
          displayMode: LandmarkCardDisplayMode.vertical,
        ),
        itemCount: landmarks.length,
        separatorBuilder: (BuildContext context, int index) => const SizedBox(
          height: 10.0,
        ),
      ),
    );
  }

  /// تحميل بيانات الشهور من قاعدة البيانات
  void _loadMonthsData(InformationCubit cubit) {
    // تحميل جميع أنواع الشهور إذا لم تكن محملة بالفعل
    if (cubit.crestMonths.isEmpty ||
        cubit.hemiariMonths.isEmpty ||
        cubit.gorianMonths.isEmpty) {
      cubit.loadMonthsData();
    }
  }
}
