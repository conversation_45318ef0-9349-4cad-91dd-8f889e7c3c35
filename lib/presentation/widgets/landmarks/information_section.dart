import 'package:flutter/material.dart';

import '../../../core/constants/assets_fonts.dart';

/// مكون قسم المعلومات
///
/// يستخدم لعرض قسم من المعلومات مع عنوان وتفاصيل
class InformationSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// تفاصيل القسم
  final String details;

  /// لون خلفية العنوان
  final Color titleBackgroundColor;

  /// لون نص العنوان
  final Color titleTextColor;

  /// إنشاء قسم معلومات جديد
  const InformationSection({
    super.key,
    required this.title,
    required this.details,
    this.titleBackgroundColor = Colors.teal,
    this.titleTextColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              decoration: BoxDecoration(
                color: titleBackgroundColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: titleTextColor,
                  fontSize: 16,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            // تفاصيل القسم
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Text(
                details,
                style: TextStyle(
                  fontSize: 15,
                  fontFamily: AssetsFonts.cairo,
                  height: 1.5,
                ),
                textAlign: TextAlign.justify,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
