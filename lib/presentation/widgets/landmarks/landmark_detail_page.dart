import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/assets_images.dart';
import '../../../core/utils/calendar_helper.dart';
import '../../../domain/entities/landmark_entity.dart';

import 'calendar_display.dart';
import 'information_section.dart';

/// صفحة تفاصيل المعلم الزراعي
///
/// تعرض معلومات مفصلة عن المعلم الزراعي المحدد
class LandmarkDetailPage extends StatelessWidget {
  /// بيانات المعلم الزراعي
  final LandmarkEntity landmark;

  /// إنشاء صفحة تفاصيل المعلم الزراعي
  const LandmarkDetailPage({
    super.key,
    required this.landmark,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        // تمكين تعويم الرأس فوق المحتوى الداخلي
        floatHeaderSlivers: true,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              foregroundColor: Colors.white,
              backgroundColor: AssetsColors.dufaultGreencolor,
              centerTitle: true,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.indigo,
                statusBarIconBrightness: Brightness.light,
              ),
              pinned: true,
              snap: false,
              expandedHeight: 250.0,
              flexibleSpace: FlexibleSpaceBar(
                centerTitle: true,
                title: Text(
                  landmark.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                background: _buildHeaderImage(),
              ),
            ),
          ];
        },
        body: SingleChildScrollView(
          child: Column(
            children: [
              // عرض التقويمات
              CalendarDisplay(
                calendarName: 'التقويم القمري',
                startDate: landmark.gregorianStartDate,
                endDate: landmark.gregorianEndDate,
                calendarType: CalendarType.gregorian,
                titleBackgroundColor: Colors.indigo,
              ),

              CalendarDisplay(
                calendarName: 'التقويم الحميري',
                startDate: landmark.himyarStartDate,
                endDate: landmark.himyarEndDate,
                calendarType: CalendarType.himyar,
                titleBackgroundColor: Colors.amber.shade800,
              ),

              CalendarDisplay(
                calendarName: 'التقويم الميلادي',
                startDate: landmark.crestStartDate,
                endDate: landmark.crestEndDate,
                calendarType: CalendarType.crest,
                titleBackgroundColor: Colors.teal.shade700,
              ),

              // عرض التفاصيل والعمليات
              InformationSection(
                title: 'التفاصيل',
                details: landmark.details,
                titleBackgroundColor: Colors.teal.shade700,
              ),

              InformationSection(
                title: 'العمليات',
                details: landmark.operations,
                titleBackgroundColor: Colors.amber.shade800,
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء صورة رأس الصفحة
  Widget _buildHeaderImage() {
    if (landmark.imageUrl != null && landmark.imageUrl!.isNotEmpty) {
      return Image.network(
        landmark.imageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Image.asset(
            AssetsImages.imagesLandmarks,
            fit: BoxFit.cover,
          );
        },
      );
    }

    return Image.asset(
      AssetsImages.imagesLandmarks,
      fit: BoxFit.cover,
    );
  }
}
