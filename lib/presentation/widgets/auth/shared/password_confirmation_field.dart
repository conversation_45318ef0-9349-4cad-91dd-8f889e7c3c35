import 'package:flutter/material.dart';

import '../../../../core/utils/validators/form_validators.dart';
import '../login/fixed_size_text_field.dart';

/// حقل تأكيد كلمة المرور
///
/// يستخدم هذا المكون لإدخال تأكيد كلمة المرور مع التحقق من تطابقها مع كلمة المرور الأصلية
class PasswordConfirmationField extends StatefulWidget {
  /// متحكم حقل تأكيد كلمة المرور
  final TextEditingController controller;

  /// متحكم حقل كلمة المرور الأصلية
  final TextEditingController passwordController;

  /// نص التسمية
  final String labelText;

  /// نص التلميح
  final String hintText;

  /// دالة التحقق من الصحة
  final String? Function(String?)? validator;

  /// منشئ حقل تأكيد كلمة المرور
  const PasswordConfirmationField({
    super.key,
    required this.controller,
    required this.passwordController,
    this.labelText = 'تأكيد كلمة المرور',
    this.hintText = 'أدخل تأكيد كلمة المرور',
    this.validator,
  });

  @override
  State<PasswordConfirmationField> createState() =>
      _PasswordConfirmationFieldState();
}

class _PasswordConfirmationFieldState extends State<PasswordConfirmationField> {
  // متغير لتخزين كلمة المرور الحالية
  String _currentPassword = '';

  @override
  void initState() {
    super.initState();
    // تحديث كلمة المرور الحالية عند بدء المكون
    _currentPassword = widget.passwordController.text;

    // إضافة مستمع للتغييرات في كلمة المرور
    widget.passwordController.addListener(_updatePassword);
  }

  @override
  void dispose() {
    // إزالة المستمع عند التخلص من المكون
    widget.passwordController.removeListener(_updatePassword);
    super.dispose();
  }

  // تحديث كلمة المرور الحالية عند تغييرها
  void _updatePassword() {
    setState(() {
      _currentPassword = widget.passwordController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    return FixedSizeTextField(
      controller: widget.controller,
      labelText: widget.labelText,
      hintText: widget.hintText,
      isPassword: true,
      prefixIcon: Icons.lock,
      height: 56.0,
      validator: (value) {
        // استخدام دالة التحقق المخصصة إذا كانت موجودة
        if (widget.validator != null) {
          final customValidation = widget.validator!(value);
          if (customValidation != null) {
            return customValidation;
          }
        }

        // استخدام دالة التحقق الافتراضية
        return FormValidators.validatePasswordConfirmation(
            value, _currentPassword);
      },
    );
  }
}
