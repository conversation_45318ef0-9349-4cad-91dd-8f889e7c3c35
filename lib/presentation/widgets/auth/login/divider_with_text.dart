import 'package:flutter/material.dart';

import '../../../../core/constants/assets_fonts.dart';

/// فاصل مع نص
///
/// يعرض فاصلًا أفقيًا مع نص في المنتصف
class DividerWithText extends StatelessWidget {
  final String text;

  const DividerWithText({
    super.key,
    this.text = 'أو',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 10.0, right: 15.0),
            child: Divider(
              color: Colors.grey.shade400,
              height: 36,
            ),
          ),
        ),
        Text(
          text,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontFamily: AssetsFonts.messiri,
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 15.0, right: 10.0),
            child: Divider(
              color: Colors.grey.shade400,
              height: 36,
            ),
          ),
        ),
      ],
    );
  }
}
