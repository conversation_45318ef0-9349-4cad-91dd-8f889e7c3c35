import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../../core/constants/colors.dart';

/// نص غني مع رابط
///
/// يوفر هذا الويدجت نصًا غنيًا مع جزء قابل للنقر.
/// يستخدم عادة في نماذج تسجيل الدخول والتسجيل.
class RichTextLink extends StatelessWidget {
  /// النص الأساسي
  final String mainText;

  /// نص الرابط
  final String linkText;

  /// دالة يتم استدعاؤها عند النقر على الرابط
  final GestureTapCallback? onTap;

  /// لون النص الأساسي
  final Color mainTextColor;

  /// لون نص الرابط
  final Color linkTextColor;

  /// حجم النص
  final double fontSize;

  /// وزن الخط
  final FontWeight fontWeight;

  /// محاذاة النص
  final TextAlign textAlign;

  /// منشئ النص الغني مع الرابط
  const RichTextLink({
    super.key,
    required this.mainText,
    required this.linkText,
    required this.onTap,
    this.mainTextColor = Colors.grey,
    this.linkTextColor = Colors.green,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.w700,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: mainText,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontFamily: AssetsFonts.messiri,
          color: mainTextColor,
        ),
        children: [
          TextSpan(
            text: linkText,
            recognizer: TapGestureRecognizer()..onTap = onTap,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: fontWeight,
              fontFamily: AssetsFonts.messiri,
              color: linkTextColor,
            ),
          ),
        ],
      ),
      textAlign: textAlign,
    );
  }

  /// إنشاء نص تسجيل باللغة العربية
  ///
  /// طريقة مساعدة لإنشاء نص تسجيل باللغة العربية.
  static Widget registerTextArabic({
    required GestureTapCallback? onTap,
    String? link,
    String? mainTxt,
  }) {
    return RichTextLink(
      mainText: mainTxt ?? 'ليس لديك حساب؟ ',
      linkText: link ?? 'إنشاء حساب',
      onTap: onTap,
      mainTextColor: Colors.grey,
      linkTextColor: AppColors.primary,
    );
  }

  /// إنشاء نص تسجيل باللغة الإنجليزية
  ///
  /// طريقة مساعدة لإنشاء نص تسجيل باللغة الإنجليزية.
  static Widget registerTextEnglish({required GestureTapCallback? onTap}) {
    return RichTextLink(
      mainText: 'Don\'t have an account? ',
      linkText: 'Sign Up',
      onTap: onTap,
      mainTextColor: Colors.grey,
      linkTextColor: AppColors.primary,
    );
  }
}
