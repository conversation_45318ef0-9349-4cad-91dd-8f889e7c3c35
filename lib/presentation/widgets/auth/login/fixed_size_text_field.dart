import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../imports.dart';
import '../../../bloc/text_field/text_field_cubit.dart';

/// مكون حقل إدخال ذو حجم ثابت
///
/// يقدم هذا المكون حقل إدخال بحجم ثابت لا يتغير عند ظهور رسائل التحذير.
/// يستخدم نفس خصائص OptimizedTextField ولكن مع تحسين للحفاظ على الحجم الثابت.
/// تم تحويله إلى StatelessWidget مع استخدام Cubit لإدارة الحالة.
class FixedSizeTextField extends StatefulWidget {
  /// متحكم النص
  final TextEditingController? controller;

  /// نص التلميح
  final String? hintText;

  /// نص التسمية
  final String? labelText;

  /// نص المساعدة
  final String? helperText;

  /// أيقونة البداية
  final IconData? prefixIcon;

  /// أيقونة النهاية
  final IconData? suffixIcon;

  /// دالة يتم استدعاؤها عند النقر على أيقونة النهاية
  final VoidCallback? onSuffixIconTap;

  /// دالة يتم استدعاؤها عند تغيير النص
  final ValueChanged<String>? onChanged;

  /// دالة يتم استدعاؤها عند الإرسال
  final ValueChanged<String>? onSubmitted;

  /// دالة يتم استدعاؤها عند التركيز
  final VoidCallback? onFocus;

  /// دالة التحقق من الصحة
  final FormFieldValidator<String>? validator;

  /// نوع لوحة المفاتيح
  final TextInputType keyboardType;

  /// هل حقل الإدخال لكلمة مرور
  final bool isPassword;

  /// هل حقل الإدخال للقراءة فقط
  final bool readOnly;

  /// هل حقل الإدخال معطل
  final bool disabled;

  /// هل حقل الإدخال مطلوب
  final bool required;

  /// هل حقل الإدخال ممتد بعرض الشاشة
  final bool isFullWidth;

  /// ارتفاع حقل الإدخال
  final double height;

  /// الحد الأقصى لعدد الأحرف
  final int? maxLength;

  /// قائمة المدققين اللغويين
  final List<TextInputFormatter>? inputFormatters;

  /// منشئ مكون حقل الإدخال ذو الحجم الثابت
  const FixedSizeTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocus,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.isPassword = false,
    this.readOnly = false,
    this.disabled = false,
    this.required = false,
    this.isFullWidth = true,
    this.height = 56.0,
    this.maxLength,
    this.inputFormatters,
  });

  @override
  State<FixedSizeTextField> createState() => _FixedSizeTextFieldState();
}

/// حالة مكون حقل الإدخال ذو الحجم الثابت
class _FixedSizeTextFieldState extends State<FixedSizeTextField> {
  late TextFieldCubit _textFieldCubit;

  @override
  void initState() {
    super.initState();
    _textFieldCubit = TextFieldCubit();
  }

  @override
  void dispose() {
    _textFieldCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _textFieldCubit,
      child: BlocBuilder<TextFieldCubit, TextFieldState>(
        builder: (context, state) {
          if (state is TextFieldLoaded) {
            return _FixedSizeTextFieldContent(
              widget: widget,
              state: state,
              cubit: _textFieldCubit,
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

/// محتوى مكون حقل الإدخال ذو الحجم الثابت
class _FixedSizeTextFieldContent extends StatelessWidget {
  final FixedSizeTextField widget;
  final TextFieldLoaded state;
  final TextFieldCubit cubit;

  const _FixedSizeTextFieldContent({
    required this.widget,
    required this.state,
    required this.cubit,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // حقل الإدخال بحجم ثابت
        Container(
          width: widget.isFullWidth ? double.infinity : null,
          height: widget.height,
          margin: const EdgeInsets.only(bottom: 4.0),
          child: TextFormField(
            controller: widget.controller,
            focusNode: state.focusNode,
            keyboardType: widget.keyboardType,
            obscureText: widget.isPassword && !state.isPasswordVisible,
            maxLines: 1,
            maxLength: widget.maxLength,
            readOnly: widget.readOnly,
            enabled: !widget.disabled,
            inputFormatters: widget.inputFormatters,
            style: TextStyle(
              fontSize: 20,
              color: widget.disabled ? Colors.grey : Colors.black,
              fontFamily: AssetsFonts.sultan,
            ),
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontFamily: AssetsFonts.sultan,
              ),
              labelText: widget.required ? '${widget.labelText} *' : widget.labelText,
              helperText: widget.helperText,
              // لا نعرض رسالة الخطأ هنا، سنعرضها أسفل الحقل
              errorStyle: const TextStyle(height: 0, fontSize: 0),
              prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
              suffixIcon: _buildSuffixIcon(),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: Dimensions.paddingM,
                vertical: Dimensions.paddingS,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: Colors.grey,
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: Colors.grey,
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: AppColors.dufaultGreencolor,
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: Colors.red,
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: Colors.red,
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(Dimensions.radiusM),
                borderSide: BorderSide(
                  color: Color.fromRGBO(128, 128, 128, 0.5),
                  width: Dimensions.borderWidthRegular,
                ),
              ),
              filled: true,
              fillColor: widget.disabled
                  ? Color.fromRGBO(128, 128, 128, 0.1)
                  : Colors.white,
              counterText: "", // إخفاء عداد الأحرف
            ),
            onChanged: widget.onChanged,
            onFieldSubmitted: widget.onSubmitted,
            validator: (value) {
              // حفظ رسالة الخطأ لعرضها أسفل الحقل
              final errorMessage = widget.validator?.call(value);
              cubit.updateErrorMessage(errorMessage);
              return errorMessage;
            },
          ),
        ),
        
        // عرض رسالة الخطأ أسفل الحقل
        if (state.errorMessage != null && state.errorMessage!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0, right: 12.0),
            child: Text(
              state.errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12.0,
                fontFamily: AssetsFonts.messiri,
              ),
            ),
          ),
      ],
    );
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon() {
    // إذا كان حقل كلمة مرور، نعرض أيقونة إظهار/إخفاء كلمة المرور
    if (widget.isPassword) {
      return IconButton(
        icon: Icon(
          state.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          color: state.isFocused ? AppColors.dufaultGreencolor : Colors.grey,
        ),
        onPressed: () {
          cubit.togglePasswordVisibility();
        },
      );
    }

    // إذا كان هناك أيقونة نهاية مخصصة، نعرضها
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          color: state.isFocused ? AppColors.dufaultGreencolor : Colors.grey,
        ),
        onPressed: widget.onSuffixIconTap,
      );
    }

    return null;
  }
}
