import 'package:flutter/material.dart';

import '../../../../imports.dart';

/// مربع اختيار تذكرني
///
/// يستخدم هذا المكون لإضافة خيار "تذكرني" في نموذج تسجيل الدخول
class RememberMeCheckbox extends StatelessWidget {
  /// حالة الاختيار
  final bool value;

  /// دالة يتم استدعاؤها عند تغيير الحالة
  final ValueChanged<bool?> onChanged;

  /// منشئ مربع اختيار تذكرني
  const RememberMeCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // مربع الاختيار
        SizedBox(
          height: 24,
          width: 24,
          child: Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: 8),

        // نص "تذكرني"
        Text(
          'تذكرني',
          style: TextStyle(
            color: Colors.grey.shade700,
            fontSize: 14,
            fontFamily: AssetsFonts.messiri,
          ),
        ),
      ],
    );
  }
}
