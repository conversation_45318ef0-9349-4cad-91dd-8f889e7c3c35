import 'package:agriculture/imports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// أنواع حقول الإدخال المحسنة
enum OptimizedTextFieldType {
  /// حقل نص عادي
  text,

  /// حقل كلمة مرور
  password,

  /// حقل بريد إلكتروني
  email,

  /// حقل رقم
  number,

  /// حقل هاتف
  phone,

  /// حقل بحث
  search,

  /// حقل متعدد الأسطر
  multiline,
}

/// أحجام حقول الإدخال المحسنة
enum OptimizedTextFieldSize {
  /// حقل صغير
  small,

  /// حقل متوسط
  medium,

  /// حقل كبير
  large,
}

/// مكون حقل إدخال محسن
///
/// يقدم هذا المكون حقل إدخال محسنًا يمكن استخدامه في جميع أنحاء التطبيق.
/// يدعم أنواعًا وأحجامًا مختلفة من حقول الإدخال، بالإضافة إلى خيارات التخصيص.
class OptimizedTextField extends StatefulWidget {
  /// متحكم النص
  final TextEditingController? controller;

  /// نص التلميح
  final String? hintText;

  /// نص التسمية
  final String? labelText;

  /// نص المساعدة
  final String? helperText;

  /// نص الخطأ
  final String? errorText;

  /// أيقونة البداية
  final IconData? prefixIcon;

  /// أيقونة النهاية
  final IconData? suffixIcon;

  /// دالة يتم استدعاؤها عند النقر على أيقونة النهاية
  final VoidCallback? onSuffixIconTap;

  /// دالة يتم استدعاؤها عند تغيير النص
  final ValueChanged<String>? onChanged;

  /// دالة يتم استدعاؤها عند الإرسال
  final ValueChanged<String>? onSubmitted;

  /// دالة يتم استدعاؤها عند التركيز
  final VoidCallback? onFocus;

  /// دالة التحقق من الصحة
  final FormFieldValidator<String>? validator;

  /// نوع حقل الإدخال
  final OptimizedTextFieldType type;

  /// حجم حقل الإدخال
  final OptimizedTextFieldSize size;

  /// هل حقل الإدخال للقراءة فقط
  final bool readOnly;

  /// هل حقل الإدخال معطل
  final bool disabled;

  /// هل حقل الإدخال مطلوب
  final bool required;

  /// هل حقل الإدخال ممتد بعرض الشاشة
  final bool isFullWidth;

  /// عدد الأسطر (للحقول متعددة الأسطر)
  final int? maxLines;

  /// الحد الأقصى لعدد الأحرف
  final int? maxLength;

  /// قائمة المدققين اللغويين
  final List<TextInputFormatter>? inputFormatters;

  /// لون حقل الإدخال المخصص
  final Color? customColor;

  /// لون النص المخصص
  final Color? customTextColor;

  /// منشئ مكون حقل الإدخال المحسن
  const OptimizedTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocus,
    this.validator,
    this.type = OptimizedTextFieldType.text,
    this.size = OptimizedTextFieldSize.medium,
    this.readOnly = false,
    this.disabled = false,
    this.required = false,
    this.isFullWidth = true,
    this.maxLines,
    this.maxLength,
    this.inputFormatters,
    this.customColor,
    this.customTextColor,
  });

  @override
  State<OptimizedTextField> createState() => _OptimizedTextFieldState();
}

class _OptimizedTextFieldState extends State<OptimizedTextField> {
  /// هل كلمة المرور مرئية
  bool _isPasswordVisible = false;

  /// هل الحقل مركز
  bool _isFocused = false;

  /// متحكم التركيز
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  /// معالجة تغيير التركيز
  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused && widget.onFocus != null) {
      widget.onFocus!();
    }
  }

  /// تبديل رؤية كلمة المرور
  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    // تحديد نوع لوحة المفاتيح
    final TextInputType keyboardType = _getKeyboardType();

    // تحديد ما إذا كان النص مخفيًا
    final bool obscureText =
        widget.type == OptimizedTextFieldType.password && !_isPasswordVisible;

    // تحديد عدد الأسطر
    final int maxLines = widget.maxLines ?? _getMaxLines();

    // تحديد المدققين اللغويين
    final List<TextInputFormatter> formatters =
        widget.inputFormatters ?? _getInputFormatters();

    // تحديد لون الحقل
    final Color fieldColor = widget.customColor ?? AppColors.dufaultGreencolor;

    // تحديد لون النص
    final Color textColor = widget.customTextColor ?? Colors.black;

    // تحديد حجم الحقل
    final double fieldHeight = _getFieldHeight();

    // إنشاء حقل الإدخال
    return Container(
      width: widget.isFullWidth ? double.infinity : null,
      // إزالة تحديد الارتفاع الثابت للسماح بعرض رسائل الخطأ دون تغيير حجم الحقل
      // height: widget.type == OptimizedTextFieldType.multiline ? null : fieldHeight,
      margin: EdgeInsets.symmetric(vertical: Dimensions.marginS),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: widget.type == OptimizedTextFieldType.multiline
                ? null
                : fieldHeight,
            child: TextFormField(
              controller: widget.controller,
              focusNode: _focusNode,
              keyboardType: keyboardType,
              obscureText: obscureText,
              maxLines: obscureText ? 1 : maxLines,
              minLines: widget.type == OptimizedTextFieldType.multiline ? 3 : 1,
              maxLength: widget.maxLength,
              readOnly: widget.readOnly,
              enabled: !widget.disabled,
              inputFormatters: formatters,
              style: TextStyle(
                fontSize: 20,
                color: widget.disabled ? Colors.grey : textColor,
                fontFamily: AssetsFonts.sultan,
              ),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(
                  fontSize: 18,
                  color: widget.disabled ? Colors.grey : Colors.grey,
                  fontFamily: AssetsFonts.sultan,
                ),
                labelText: widget.required
                    ? '${widget.labelText} *'
                    : widget.labelText,
                helperText: widget.helperText,
                errorText: widget.errorText,
                prefixIcon:
                    widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
                suffixIcon: _buildSuffixIcon(),
                contentPadding: _getContentPadding(),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: Colors.grey, width: Dimensions.borderWidthRegular),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: Colors.grey, width: Dimensions.borderWidthRegular),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: fieldColor, width: Dimensions.borderWidthRegular),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: Colors.red, width: Dimensions.borderWidthRegular),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: Colors.red, width: Dimensions.borderWidthRegular),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  borderSide: BorderSide(
                      color: Color.fromRGBO(128, 128, 128, 0.5),
                      width: Dimensions.borderWidthRegular),
                ),
                filled: true,
                fillColor: widget.disabled
                    ? Color.fromRGBO(128, 128, 128, 0.1)
                    : Colors.white,
              ),
              onChanged: widget.onChanged,
              onFieldSubmitted: widget.onSubmitted,
              validator: widget.validator,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon() {
    // إذا كان حقل كلمة مرور، نعرض أيقونة إظهار/إخفاء كلمة المرور
    if (widget.type == OptimizedTextFieldType.password) {
      return IconButton(
        icon: Icon(
          _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          color: _isFocused ? AppColors.dufaultGreencolor : Colors.grey,
        ),
        onPressed: _togglePasswordVisibility,
      );
    }

    // إذا كان هناك أيقونة نهاية مخصصة، نعرضها
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          color: _isFocused ? AppColors.dufaultGreencolor : Colors.grey,
        ),
        onPressed: widget.onSuffixIconTap,
      );
    }

    // إذا كان حقل بحث، نعرض أيقونة البحث
    if (widget.type == OptimizedTextFieldType.search) {
      return IconButton(
        icon: Icon(
          Icons.search,
          color: _isFocused ? AppColors.dufaultGreencolor : Colors.grey,
        ),
        onPressed: widget.onSuffixIconTap,
      );
    }

    return null;
  }

  /// الحصول على نوع لوحة المفاتيح حسب نوع الحقل
  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case OptimizedTextFieldType.email:
        return TextInputType.emailAddress;
      case OptimizedTextFieldType.number:
        return TextInputType.number;
      case OptimizedTextFieldType.phone:
        return TextInputType.phone;
      case OptimizedTextFieldType.multiline:
        return TextInputType.multiline;
      case OptimizedTextFieldType.search:
      case OptimizedTextFieldType.text:
      case OptimizedTextFieldType.password:
        return TextInputType.text;
    }
  }

  /// الحصول على عدد الأسطر حسب نوع الحقل
  int _getMaxLines() {
    switch (widget.type) {
      case OptimizedTextFieldType.multiline:
        return 5;
      case OptimizedTextFieldType.text:
      case OptimizedTextFieldType.email:
      case OptimizedTextFieldType.number:
      case OptimizedTextFieldType.phone:
      case OptimizedTextFieldType.search:
      case OptimizedTextFieldType.password:
        return 1;
    }
  }

  /// الحصول على المدققين اللغويين حسب نوع الحقل
  List<TextInputFormatter> _getInputFormatters() {
    switch (widget.type) {
      case OptimizedTextFieldType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case OptimizedTextFieldType.phone:
        return [FilteringTextInputFormatter.digitsOnly];
      case OptimizedTextFieldType.text:
      case OptimizedTextFieldType.email:
      case OptimizedTextFieldType.password:
      case OptimizedTextFieldType.search:
      case OptimizedTextFieldType.multiline:
        return [];
    }
  }

  /// الحصول على ارتفاع الحقل حسب الحجم
  double _getFieldHeight() {
    switch (widget.size) {
      case OptimizedTextFieldSize.small:
        return Dimensions.inputHeightSmall;
      case OptimizedTextFieldSize.medium:
        return Dimensions.inputHeight;
      case OptimizedTextFieldSize.large:
        return Dimensions.inputHeightLarge;
    }
  }

  /// الحصول على حجم الخط حسب حجم الحقل
  // double _getFontSize() {
  //   switch (widget.size) {
  //     case OptimizedTextFieldSize.small:
  //       return Dimensions.fontSizeS;
  //     case OptimizedTextFieldSize.medium:
  //       return Dimensions.fontSizeM;
  //     case OptimizedTextFieldSize.large:
  //       return Dimensions.fontSizeL;
  //   }
  // }

  /// الحصول على نصف قطر الحدود حسب حجم الحقل
  double _getBorderRadius() {
    switch (widget.size) {
      case OptimizedTextFieldSize.small:
        return Dimensions.radiusS;
      case OptimizedTextFieldSize.medium:
        return Dimensions.radiusM;
      case OptimizedTextFieldSize.large:
        return Dimensions.radiusL;
    }
  }

  /// الحصول على المسافة الداخلية حسب حجم الحقل
  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case OptimizedTextFieldSize.small:
        return EdgeInsets.symmetric(
          horizontal: Dimensions.paddingM,
          vertical: Dimensions.paddingXS,
        );
      case OptimizedTextFieldSize.medium:
        return EdgeInsets.symmetric(
          horizontal: Dimensions.paddingM,
          vertical: Dimensions.paddingS,
        );
      case OptimizedTextFieldSize.large:
        return EdgeInsets.symmetric(
          horizontal: Dimensions.paddingL,
          vertical: Dimensions.paddingM,
        );
    }
  }
}
