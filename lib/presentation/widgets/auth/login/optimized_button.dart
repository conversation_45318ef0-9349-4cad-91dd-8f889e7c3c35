import 'package:flutter/material.dart';

import '../../../../core/constants/colors.dart';
import '../../../../core/constants/dimensions.dart';

/// أنواع الأزرار المحسنة
enum OptimizedButtonType {
  /// زر أساسي (ملون بالكامل)
  primary,

  /// زر ثانوي (حدود فقط)
  secondary,

  /// زر خطر (أحمر)
  danger,

  /// زر نجاح (أخضر)
  success,

  /// زر معلومات (أزرق)
  info,

  /// زر تحذير (برتقالي)
  warning,
}

/// أحجام الأزرار المحسنة
enum OptimizedButtonSize {
  /// زر صغير
  small,

  /// زر متوسط
  medium,

  /// زر كبير
  large,
}

/// مكون زر محسن
///
/// يقدم هذا المكون زرًا محسنًا يمكن استخدامه في جميع أنحاء التطبيق.
/// يدعم أنواعًا وأحجامًا مختلفة من الأزرار، بالإضافة إلى خيارات التخصيص.
class OptimizedButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onPressed;

  /// نوع الزر
  final OptimizedButtonType type;

  /// حجم الزر
  final OptimizedButtonSize size;

  /// عرض الزر (اختياري)
  final double? width;

  /// ارتفاع الزر (اختياري)
  final double? height;

  /// هل الزر ممتد بعرض الشاشة
  final bool isFullWidth;

  /// هل الزر مستدير
  final bool isRounded;

  /// هل الزر قيد التحميل
  final bool isLoading;

  /// هل الزر معطل
  final bool isDisabled;

  /// لون الزر المخصص (اختياري)
  final Color? customColor;

  /// لون النص المخصص (اختياري)
  final Color? customTextColor;

  /// منشئ مكون الزر المحسن
  const OptimizedButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.type = OptimizedButtonType.primary,
    this.size = OptimizedButtonSize.medium,
    this.width,
    this.height,
    this.isFullWidth = false,
    this.isRounded = false,
    this.isLoading = false,
    this.isDisabled = false,
    this.customColor,
    this.customTextColor,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد لون الزر
    final Color buttonColor = _getButtonColor();

    // تحديد لون النص
    final Color textColor = customTextColor ?? _getTextColor();

    // تحديد حجم الزر
    final double buttonHeight = height ?? _getButtonHeight();
    final double buttonWidth = _getButtonWidth(context);

    // تحديد نصف قطر الزر
    final double borderRadius =
        isRounded ? Dimensions.radiusCircular : _getBorderRadius();

    // إنشاء الزر حسب النوع
    return SizedBox(
      width: buttonWidth,
      height: buttonHeight,
      child: _buildButton(buttonColor, textColor, borderRadius, context),
    );
  }

  /// بناء الزر حسب النوع
  Widget _buildButton(Color buttonColor, Color textColor, double borderRadius,
      BuildContext context) {
    // إذا كان الزر من النوع الثانوي، نستخدم زر حدود
    if (type == OptimizedButtonType.secondary) {
      return OutlinedButton(
        onPressed: (isDisabled || isLoading) ? null : onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(
              color: buttonColor, width: Dimensions.borderWidthRegular),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: _buildButtonContent(textColor, context),
      );
    }

    // غير ذلك، نستخدم زر ملون
    return ElevatedButton(
      onPressed: (isDisabled || isLoading) ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonColor,
        foregroundColor: textColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        elevation: 2,
      ),
      child: _buildButtonContent(textColor, context),
    );
  }

  /// بناء محتوى الزر (نص وأيقونة)
  Widget _buildButtonContent(Color textColor, BuildContext context) {
    // إذا كان الزر قيد التحميل، نعرض مؤشر التحميل
    if (isLoading) {
      return SizedBox(
        width: _getLoadingSize(),
        height: _getLoadingSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    // إذا كان هناك أيقونة، نعرض الأيقونة مع النص
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: _getIconSize(),
            color: textColor,
          ),
          SizedBox(width: Dimensions.spacingS),
          Text(
            text,
            style: TextStyle(
              fontSize: _getFontSize(),
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      );
    }

    // غير ذلك، نعرض النص فقط
    return Text(
      text,
      style: TextStyle(
        fontSize: _getFontSize(),
        fontWeight: FontWeight.bold,
        color: textColor,
      ),
    );
  }

  /// الحصول على لون الزر حسب النوع
  Color _getButtonColor() {
    if (customColor != null) {
      return customColor!;
    }

    if (isDisabled) {
      return Colors.grey;
    }

    switch (type) {
      case OptimizedButtonType.primary:
        return AppColors.dufaultGreencolor;
      case OptimizedButtonType.secondary:
        return Colors.transparent;
      case OptimizedButtonType.danger:
        return Colors.red;
      case OptimizedButtonType.success:
        return Colors.green;
      case OptimizedButtonType.info:
        return Colors.blue;
      case OptimizedButtonType.warning:
        return Colors.orange;
    }
  }

  /// الحصول على لون النص حسب النوع
  Color _getTextColor() {
    if (type == OptimizedButtonType.secondary) {
      return _getButtonColor();
    }

    return Colors.white;
  }

  /// الحصول على ارتفاع الزر حسب الحجم
  double _getButtonHeight() {
    switch (size) {
      case OptimizedButtonSize.small:
        return Dimensions.buttonHeightS;
      case OptimizedButtonSize.medium:
        return Dimensions.buttonHeightM;
      case OptimizedButtonSize.large:
        return Dimensions.buttonHeightL;
    }
  }

  /// الحصول على عرض الزر
  double _getButtonWidth(BuildContext context) {
    if (isFullWidth) {
      return double.infinity;
    }

    if (width != null) {
      return width!;
    }

    switch (size) {
      case OptimizedButtonSize.small:
        return Dimensions.buttonWidthS;
      case OptimizedButtonSize.medium:
        return Dimensions.buttonWidthM;
      case OptimizedButtonSize.large:
        return Dimensions.buttonWidthL;
    }
  }

  /// الحصول على نصف قطر الزر حسب الحجم
  double _getBorderRadius() {
    switch (size) {
      case OptimizedButtonSize.small:
        return Dimensions.radiusS;
      case OptimizedButtonSize.medium:
        return Dimensions.radiusM;
      case OptimizedButtonSize.large:
        return Dimensions.radiusL;
    }
  }

  /// الحصول على حجم الخط حسب حجم الزر
  double _getFontSize() {
    switch (size) {
      case OptimizedButtonSize.small:
        return Dimensions.fontSizeS;
      case OptimizedButtonSize.medium:
        return Dimensions.fontSizeM;
      case OptimizedButtonSize.large:
        return Dimensions.fontSizeL;
    }
  }

  /// الحصول على حجم الأيقونة حسب حجم الزر
  double _getIconSize() {
    switch (size) {
      case OptimizedButtonSize.small:
        return Dimensions.iconSizeS;
      case OptimizedButtonSize.medium:
        return Dimensions.iconSizeM;
      case OptimizedButtonSize.large:
        return Dimensions.iconSizeL;
    }
  }

  /// الحصول على حجم مؤشر التحميل حسب حجم الزر
  double _getLoadingSize() {
    switch (size) {
      case OptimizedButtonSize.small:
        return 16.0;
      case OptimizedButtonSize.medium:
        return 20.0;
      case OptimizedButtonSize.large:
        return 24.0;
    }
  }
}
