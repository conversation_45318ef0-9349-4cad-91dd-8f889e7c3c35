import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../bloc/auth/phone/phone_auth_cubit.dart';

/// زر إعادة إرسال رمز التحقق
///
/// يعرض زر إعادة إرسال رمز التحقق مع مؤقت
class ResendOtpButton extends StatelessWidget {
  final VoidCallback onResend;

  const ResendOtpButton({
    super.key,
    required this.onResend,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PhoneAuthCubit, PhoneAuthState>(
      builder: (context, state) {
        final bool canResend = state is PhoneAuthResendAvailable;
        final int remainingTime = state is PhoneAuthResendTimerTick
            ? state.remainingTime
            : (state is PhoneAuthResendTimerStarted ? state.remainingTime : 60);

        return ElevatedButton.icon(
          onPressed: canResend ? onResend : null,
          icon: Icon(
            Icons.refresh,
            color: canResend ? Colors.indigo : Colors.grey,
            size: 20,
          ),
          label: Text(
            canResend
                ? 'إعادة إرسال الرمز'
                : 'إعادة الإرسال بعد $remainingTime ثانية',
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontWeight: FontWeight.bold,
              color: canResend ? Colors.indigo : Colors.grey,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            disabledBackgroundColor: Colors.grey.shade200,
          ),
        );
      },
    );
  }
}
