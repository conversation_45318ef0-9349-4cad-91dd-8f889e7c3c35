import 'package:flutter/material.dart';

import '../../../../core/constants/assets_fonts.dart';

/// زر المصادقة بالهاتف
///
/// زر مخصص للمصادقة بالهاتف مع تأثيرات حركية
class PhoneAuthButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;

  const PhoneAuthButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  State<PhoneAuthButton> createState() => _PhoneAuthButtonState();
}

class _PhoneAuthButtonState extends State<PhoneAuthButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: widget.isLoading
          ? null
          : (_) {
              setState(() {
                _isPressed = true;
              });
            },
      onTapUp: widget.isLoading
          ? null
          : (_) {
              setState(() {
                _isPressed = false;
              });
              widget.onPressed();
            },
      onTapCancel: widget.isLoading
          ? null
          : () {
              setState(() {
                _isPressed = false;
              });
            },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        height: 55,
        width: double.infinity,
        decoration: BoxDecoration(
          color: widget.isLoading
              ? Colors.grey.shade400
              : (_isPressed ? Colors.green.shade800 : Colors.green),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: _isPressed
                  ? Colors.green.withAlpha(51)
                  : Colors.green.withAlpha(102),
              blurRadius: _isPressed ? 4 : 8,
              offset: _isPressed ? const Offset(0, 2) : const Offset(0, 4),
            ),
          ],
        ),
        // تأثير التصغير عند الضغط
        transform: Matrix4.identity()..scale(_isPressed ? 0.98 : 1.0),
        child: Center(
          child: widget.isLoading
              ? const CircularProgressIndicator(color: Colors.white)
              : Text(
                  widget.text,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: AssetsFonts.messiri,
                  ),
                ),
        ),
      ),
    );
  }
}
