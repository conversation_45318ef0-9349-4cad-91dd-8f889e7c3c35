import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../bloc/auth/phone/phone_auth_cubit.dart';
import 'otp_digit_field.dart';

/// نموذج إدخال رمز التحقق
///
/// يعرض حقول إدخال رمز التحقق المكون من 6 أرقام
class OtpInputForm extends StatelessWidget {
  final String phoneNumber;

  const OtpInputForm({
    super.key,
    required this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PhoneAuthCubit>();

    return Column(
      children: [
        // نص توضيحي لرمز التحقق
        Text(
          'أدخل رمز التحقق المكون من 6 أرقام',
          style: TextStyle(
            fontSize: 16,
            fontFamily: AssetsFonts.messiri,
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // مربعات إدخال رمز التحقق
        Container(
          width: MediaQuery.of(context).size.width *
              0.9, // استخدام 90% من عرض الشاشة
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(6, (index) {
                return Row(
                  children: [
                    OtpDigitField(
                      controller: cubit.otpControllers[index],
                      focusNode: cubit.otpFocusNodes[index],
                      onChanged: (value) {
                        // إذا تم إدخال رقم، انتقل إلى الحقل التالي
                        if (value.isNotEmpty && index < 5) {
                          cubit.otpFocusNodes[index].unfocus();
                          FocusScope.of(context)
                              .requestFocus(cubit.otpFocusNodes[index + 1]);
                        }

                        // إذا تم حذف الرقم، انتقل إلى الحقل السابق
                        if (value.isEmpty && index > 0) {
                          cubit.otpFocusNodes[index].unfocus();
                          FocusScope.of(context)
                              .requestFocus(cubit.otpFocusNodes[index - 1]);
                        }

                        // إذا تم إدخال جميع الأرقام، قم بالتحقق
                        if (index == 5 && value.isNotEmpty) {
                          final otpCode = cubit.getOtpCode();
                          if (otpCode.length == 6) {
                            cubit.verifyOTP(phoneNumber);
                          }
                        }
                      },
                    ),
                    if (index < 5) const SizedBox(width: 6),
                  ],
                );
              }),
            ),
          ),
        ),
      ],
    );
  }
}
