import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../bloc/auth/register_profile/register_profile_cubit.dart';

/// مكون حقول نموذج الملف الشخصي
class ProfileFormFields extends StatelessWidget {
  const ProfileFormFields({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<RegisterProfileCubit>();

    return Column(
      children: [
        // الاسم الأول
        _buildTextField(
          controller: cubit.firstNameController,
          labelText: 'الاسم الأول',
          prefixIcon: Icons.person,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى إدخال الاسم الأول';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // الاسم الأخير
        _buildTextField(
          controller: cubit.lastNameController,
          labelText: 'الاسم الأخير',
          prefixIcon: Icons.person,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى إدخال الاسم الأخير';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // اختيار التخصص
        _buildDropdownField(
          value: cubit.selectedSpecialization,
          items: cubit.specializations,
          labelText: 'اختر التخصص',
          prefixIcon: Icons.work,
          onChanged: (value) {
            if (value != null) {
              cubit.updateSpecialization(value);
            }
          },
        ),
        const SizedBox(height: 16),

        // اختيار المحافظة
        _buildDropdownField(
          value: cubit.selectedGovernorate,
          items: cubit.governorates,
          labelText: 'اختر المدينة',
          prefixIcon: Icons.location_city,
          onChanged: (value) {
            if (value != null) {
              cubit.updateGovernorate(value);
            }
          },
        ),
        const SizedBox(height: 16),

        // اختيار المدينة (إذا كانت متوفرة)
        BlocBuilder<RegisterProfileCubit, RegisterProfileState>(
          buildWhen: (previous, current) =>
              current is RegisterProfileGovernorateChanged,
          builder: (context, state) {
            if (cubit.cities.isNotEmpty) {
              return Column(
                children: [
                  _buildDropdownField(
                    value: cubit.selectedCity,
                    items: cubit.cities,
                    labelText: 'اختر المنطقة',
                    prefixIcon: Icons.location_on,
                    onChanged: (value) {
                      if (value != null) {
                        cubit.updateCity(value);
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),

        // الملف الشخصي (البايو)
        _buildTextField(
          controller: cubit.bioController,
          labelText: 'الملف الشخصي',
          hintText: 'اكتب نبذة عن نفسك...',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
      ],
    );
  }

  /// بناء حقل نصي
  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required IconData prefixIcon,
    String? hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        textAlign: TextAlign.right,
        maxLines: maxLines,
        style: TextStyle(
          color: AssetsColors.selectColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFamily: AssetsFonts.sultan,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          labelStyle: TextStyle(
              fontFamily: AssetsFonts.sultan, color: AssetsColors.kPrimary),
          hintStyle: hintText != null
              ? TextStyle(
                  fontFamily: AssetsFonts.messiri,
                  color: AssetsColors.kPrimary.withOpacity(0.5),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          prefixIcon: Icon(
            prefixIcon,
            color: AssetsColors.kPrimary,
          ),
        ),
        validator: validator,
      ),
    );
  }

  /// بناء حقل قائمة منسدلة
  Widget _buildDropdownField({
    required String? value,
    required List<String> items,
    required String labelText,
    required IconData prefixIcon,
    required void Function(String?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(51),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withAlpha(77),
          width: 1,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        dropdownColor: Colors.tealAccent.shade700,
        style: TextStyle(
          color: AssetsColors.selectColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFamily: AssetsFonts.sultan,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          labelStyle: TextStyle(
            fontFamily: AssetsFonts.sultan,
            color: AssetsColors.kPrimary,
          ),
          hintStyle: TextStyle(
            fontFamily: AssetsFonts.sultan,
            fontWeight: FontWeight.w400,
            color: AssetsColors.kPrimary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          prefixIcon: Icon(
            prefixIcon,
            color: AssetsColors.kPrimary,
          ),
        ),
        selectedItemBuilder: (BuildContext context) {
          return items.map<Widget>((String item) {
            return Container(
              alignment: Alignment.center,
              child: Text(
                item,
                style: TextStyle(
                  color: AssetsColors.selectColor, // لون النص المختار (أحمر)
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: AssetsFonts.sultan,
                ),
              ),
            );
          }).toList();
        },
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            alignment: Alignment.center,
            value: item,
            onTap: () {},
            child: Text(
              item,
              style: TextStyle(
                fontFamily: AssetsFonts.messiri,
                color: AssetsColors.kWhite,
              ),
              textAlign: TextAlign.right,
            ),
          );
        }).toList(),
        onChanged: onChanged,
        isExpanded: true,
        icon: Icon(
          Icons.arrow_drop_down,
          color: AssetsColors.kPrimary,
        ),
        iconSize: 30,
      ),
    );
  }
}
