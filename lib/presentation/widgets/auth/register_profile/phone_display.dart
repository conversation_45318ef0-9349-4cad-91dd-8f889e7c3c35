import 'package:flutter/material.dart';

import '../../../../core/constants/assets_fonts.dart';

/// عرض رقم الهاتف
class PhoneDisplay extends StatelessWidget {
  final String phoneNumber;

  const PhoneDisplay({
    super.key,
    required this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.phone_android,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 10),
          Text(
            phoneNumber,
            style: TextStyle(
              fontSize: 18,
              fontFamily: AssetsFonts.messiri,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
