import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../../core/constants/assets_login_image.dart';
import '../../../bloc/auth/register_profile/register_profile_cubit.dart';

/// مكون اختيار صورة الملف الشخصي
class ProfileImagePicker extends StatelessWidget {
  const ProfileImagePicker({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<RegisterProfileCubit>();

    return BlocBuilder<RegisterProfileCubit, RegisterProfileState>(
      buildWhen: (previous, current) => current is RegisterProfileImageChanged,
      builder: (context, state) {
        final File? profileImage = cubit.profileImage;

        return Stack(
          alignment: Alignment.bottomRight,
          children: [
            Column(
              children: [
                CircleAvatar(
                  radius: 60,
                  backgroundColor: Colors.white.withAlpha(77),
                  backgroundImage:
                      profileImage != null ? FileImage(profileImage) : null,
                  child: profileImage == null
                      ? Image.asset(
                          AssetsLoginImage.profileRegister,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.person,
                              size: 80,
                              color: Colors.white,
                            );
                          },
                        )
                      : null,
                ),
                const SizedBox(height: 8),
                Text(
                  profileImage != null
                      ? 'تم اختيار الصورة'
                      : 'اختيار صورة (اختياري)',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: AssetsFonts.messiri,
                  ),
                ),
              ],
            ),
            Positioned(
              right: 0,
              bottom: 40, // تعديل الموضع ليكون بجانب الصورة وليس أسفل النص
              child: GestureDetector(
                onTap: () => _showImageSourceOptions(context),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.tealAccent.shade700,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.edit,
                    color: Colors.tealAccent.shade700,
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// عرض خيارات اختيار الصورة
  void _showImageSourceOptions(BuildContext context) {
    final cubit = context.read<RegisterProfileCubit>();

    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'اختيار صورة شخصية',
              style: TextStyle(
                fontFamily: AssetsFonts.messiri,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: Text(
              'اختيار من المعرض',
              style: TextStyle(fontFamily: AssetsFonts.messiri),
            ),
            onTap: () {
              Navigator.pop(context);
              cubit.pickImage();
            },
          ),
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: Text(
              'التقاط صورة',
              style: TextStyle(fontFamily: AssetsFonts.messiri),
            ),
            onTap: () {
              Navigator.pop(context);
              cubit.takePhoto();
            },
          ),
          const Divider(),
          Text(
            'الصورة اختيارية، سيتم استخدام صورة افتراضية في حال عدم اختيار صورة',
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
