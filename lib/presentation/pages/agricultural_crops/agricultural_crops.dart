import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
// تم حذف استيراد headings غير المستخدم
import '../../bloc/agricultural_crops/crops_cubit.dart';
import '../../widgets/agricultural_crops/crop_list.dart';
import '../../widgets/shared/appbar.dart';

/// صفحة المحاصيل الزراعية
///
/// تعرض المحاصيل الزراعية مقسمة حسب الفئات
class AgriculturalCrops extends StatefulWidget {
  /// إنشاء صفحة المحاصيل الزراعية
  const AgriculturalCrops({super.key});

  @override
  State<AgriculturalCrops> createState() => _AgriculturalCropsState();
}

class _AgriculturalCropsState extends State<AgriculturalCrops> {
  /// تحديد الأيقونة المناسبة لفئة المحاصيل بناءً على اسمها
  IconData _getCategoryIcon(String categoryName) {
    final name = categoryName.toLowerCase();

    // الحبوب والبقوليات
    if (name.contains('حبوب') ||
        name.contains('بقول') ||
        name.contains('قمح') ||
        name.contains('شعير') ||
        name.contains('ذرة')) {
      return Icons.grain;
    }

    // الفواكه
    if (name.contains('فواكه') ||
        name.contains('فاكهة') ||
        name.contains('تفاح') ||
        name.contains('برتقال') ||
        name.contains('موز')) {
      return Icons.apple;
    }

    // الخضروات
    if (name.contains('خضر') ||
        name.contains('خضار') ||
        name.contains('طماطم') ||
        name.contains('بطاطس') ||
        name.contains('بصل')) {
      return Icons.eco;
    }

    // النباتات الزيتية
    if (name.contains('زيت') ||
        name.contains('زيتون') ||
        name.contains('سمسم')) {
      return Icons.water_drop;
    }

    // النباتات الطبية والعطرية
    if (name.contains('طبي') ||
        name.contains('عطري') ||
        name.contains('أعشاب')) {
      return Icons.spa;
    }

    // المحاصيل السكرية
    if (name.contains('سكر') || name.contains('قصب') || name.contains('بنجر')) {
      return Icons.cake;
    }

    // الألياف
    if (name.contains('ألياف') ||
        name.contains('قطن') ||
        name.contains('كتان')) {
      return Icons.texture;
    }

    // الأيقونة الافتراضية
    return Icons.grass;
  }

  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند إنشاء الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRequiredData(CropsCubit.get(context));
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CropsCubit, CropsState>(
      listener: (context, state) {
        // يمكن إضافة استجابة للتغييرات في الحالة هنا
      },
      builder: (context, state) {
        final cubit = CropsCubit.get(context);

        // عرض مؤشر التحميل إذا كانت البيانات غير جاهزة
        if (cubit.plantCategories.isEmpty || cubit.plants.isEmpty) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return DefaultTabController(
          length: cubit.plantCategories.length,
          child: Scaffold(
            appBar: _buildAppBar(context, cubit),
            body: Column(
              children: [
                // شريط التبويبات
                Material(
                  color: AssetsColors.dufaultGreencolor,
                  child: TabBar(
                    isScrollable: true,
                    indicatorColor: Colors.white,
                    indicatorWeight: 3,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.white70,
                    labelPadding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    indicator: BoxDecoration(
                      color: Colors.white.withAlpha(51),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    tabs: cubit.plantCategories.map((category) {
                      // حساب عدد المحاصيل في الفئة
                      final count = cubit.plants
                          .where((plant) => plant.categoryId == category.id)
                          .length;

                      // تحديد الأيقونة المناسبة للفئة
                      final icon = _getCategoryIcon(category.name);

                      return Tab(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(icon, size: 18),
                            const SizedBox(width: 8),
                            Text(category.name),
                            if (count > 0) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(77),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  count.toString(),
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
                // محتوى التبويبات
                Expanded(
                  child: _buildTabBarView(cubit),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// تحميل البيانات اللازمة إذا لم تكن محملة بالفعل
  Future<void> _loadRequiredData(CropsCubit cubit) async {
    // تحميل البيانات بالترتيب لتجنب إعادة بناء واجهة المستخدم بشكل متكرر

    // تحميل فئات المحاصيل أولاً
    if (cubit.plantCategories.isEmpty) {
      await cubit.getPlantCategories();
    }

    // تحميل المحاصيل بعد فئات المحاصيل
    if (cubit.plants.isEmpty) {
      await cubit.getPlants();
    }

    // تحميل باقي البيانات بالتوازي لتحسين الأداء
    await Future.wait([
      // تحميل بيانات الإحالة
      if (cubit.plantReferralData.isEmpty) cubit.getPlantReferralData(),

      // تحميل فئات العمليات
      if (cubit.plantOperationCategories.isEmpty)
        cubit.getPlantOperationCategories(),

      // تحميل العمليات
      if (cubit.plantOperations.isEmpty) cubit.getPlantOperations(),

      // تحميل الأمراض
      if (cubit.diseases.isEmpty) cubit.getDiseases(),

      // تحميل معلومات الإحالة للأمراض
      if (cubit.diseaseReferralInfo.isEmpty) cubit.getDiseaseReferralInfo(),
    ]);
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(BuildContext context, CropsCubit cubit) {
    return defaultAppBar(
      color: AssetsColors.dufaultGreencolor,
      context: context,
      titel: 'النباتات',
    );
  }

  /// بناء عرض شريط التبويبات
  Widget _buildTabBarView(CropsCubit cubit) {
    return TabBarView(
      children: cubit.plantCategories
          .map((category) => CropList(
                crops: cubit.plants
                    .where((plant) => plant.categoryId == category.id)
                    .toList(),
              ))
          .toList(),
    );
  }
}
