import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import '../../bloc/home/<USER>';
import '../education/education_updated.dart';
import '../weather/weather/weather_card.dart';
import '../agricultural_crops/agricultural_crops.dart';
import '../camera/camera_page.dart';
import '../community_forum/comunity_forum.dart';

/// الصفحة الرئيسية للتطبيق
///
/// تحتوي على:
/// - بطاقة الطقس
/// - الأخبار الزراعية
/// - الخدمات السريعة
/// - الإحصائيات اليومية
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

/// حالة الصفحة الرئيسية
class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late HomeCubit _homeCubit;

  @override
  void initState() {
    super.initState();
    _homeCubit = HomeCubit(vsync: this)..initialize();
  }

  @override
  void dispose() {
    _homeCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _homeCubit,
      child: BlocBuilder<HomeCubit, HomeState>(
        builder: (context, state) {
          if (state is HomeLoading) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          if (state is HomeError) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                    const SizedBox(height: 16),
                    Text(
                      state.message,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _homeCubit.refresh(),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is HomeLoaded) {
            return _HomeContent(state: state);
          }

          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        },
      ),
    );
  }
}

/// محتوى الصفحة الرئيسية
class _HomeContent extends StatelessWidget {
  final HomeLoaded state;

  const _HomeContent({required this.state});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AssetsColors.dufaultGreencolor.withOpacity(0.8),
              Colors.white,
              Colors.grey[50]!,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: state.fadeAnimation,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const HomeHeader(),
                  const SizedBox(height: 24),
                  WeatherCard(
                    location: state.weatherData.location,
                    temperature: state.weatherData.temperature,
                    condition: state.weatherData.condition,
                    humidity: state.weatherData.humidity,
                    windSpeed: state.weatherData.windSpeed,
                    uvIndex: state.weatherData.uvIndex,
                  ),
                  //  SizedBox(height: 24),
                  //  QuickServicesSection(),
                  //  SizedBox(height: 24),
                  //  QuickStatsSection(),
                  //  SizedBox(height: 24),
                  // NewsSection(newsItems: state.newsItems),
                  //  SizedBox(height: 24),
                  //  DailyTips(),
                  //  SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// فئة إدارة صفحات التطبيق وعناصر التنقل السفلي
///
/// تحتوي على:
/// - مسارات الصفحات
/// - عناصر شريط التنقل السفلي

class AppPages {
  static final Map<String, WidgetBuilder> routes = {
    '/home': (context) => const HomeScreen(),
    '/forum': (context) => const ComunityForum(),
    '/camera': (context) => const CameraPage(),
    '/education': (context) => const Education(),
    '/crops': (context) => const AgriculturalCrops(),
  };

  static final List<Map<String, dynamic>> bottomNavItems = [
    {
      'route': '/home',
      'icon': Icons.home_outlined,
      'activeIcon': Icons.home,
      'label': 'الرئيسية',
    },
    {
      'route': '/forum',
      'icon': Icons.forum_outlined,
      'activeIcon': Icons.forum,
      'label': 'المنتدى',
    },
    {
      'route': '/camera',
      'icon': Icons.camera_alt_outlined,
      'activeIcon': Icons.camera_alt,
      'label': 'الكاميرا',
    },
    {
      'route': '/education',
      'icon': Icons.school_outlined,
      'activeIcon': Icons.school,
      'label': 'التعليم',
    },
    {
      'route': '/crops',
      'icon': Icons.eco_outlined,
      'activeIcon': Icons.eco,
      'label': 'المحاصيل',
    },
  ];
}
