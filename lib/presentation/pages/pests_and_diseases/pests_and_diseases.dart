// ignore_for_file: use_super_parameters

import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/headings.dart';
import 'package:agriculture/presentation/widgets/shared/appbar.dart';
import 'package:flutter/material.dart';

class PestsAndDiseases extends StatelessWidget {
  const PestsAndDiseases({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: defaultAppBar(
        color: AssetsColors.dufaultGreencolor,
        context: context,
        titel: Headings.pestsAndDiseases,
      ),
      body: Center(
        child: Text(Headings.pestsAndDiseases),
      ),
    );
  }
}
