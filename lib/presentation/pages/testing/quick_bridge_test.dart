import 'package:flutter/material.dart';
import 'package:agriculture/core/shared/index.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'bridge_test_page.dart';

/// اختبار سريع لجسر التواصل
/// يمكن استدعاؤه من أي مكان في التطبيق لاختبار الجسر
class QuickBridgeTest {
  static final FarmerAdvisorBridge _bridge = FarmerAdvisorBridge();
  static final UnifiedNotificationService _notifications = UnifiedNotificationService();
  static final UnifiedConsultationService _consultationService = UnifiedConsultationService();

  /// تشغيل اختبار سريع للجسر
  /// [context] السياق لعرض النتائج
  static Future<void> runQuickTest(BuildContext context) async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                Sized<PERSON><PERSON>(height: 16),
                Text('جاري اختبار الجسر...'),
              ],
            ),
          ),
        ),
      ),
    );

    final results = <String>[];
    
    try {
      // تهيئة الخدمات
      await _bridge.initialize();
      await _notifications.initialize();
      results.add('✅ تم تهيئة الخدمات');

      // اختبار إرسال استشارة
      final consultation = await _consultationService.createConsultation(
        farmerId: 'test_farmer',
        cropType: 'طماطم',
        problemDescription: 'اختبار سريع للجسر',
        priority: 'normal',
      );

      if (consultation != null) {
        final success = await _bridge.sendInstantConsultation(
          farmerId: 'test_farmer',
          consultation: consultation,
          priority: 'normal',
        );
        
        if (success) {
          results.add('✅ اختبار الاستشارة: نجح');
        } else {
          results.add('❌ اختبار الاستشارة: فشل');
        }
      } else {
        results.add('❌ فشل في إنشاء الاستشارة');
      }

      // اختبار الإشعارات
      await _notifications.sendToAdvisor(
        title: 'اختبار سريع',
        body: 'هذا اختبار سريع للإشعارات',
        data: {'type': 'test'},
      );
      results.add('✅ اختبار الإشعارات: نجح');

    } catch (e) {
      results.add('❌ خطأ في الاختبار: $e');
    }

    // إغلاق مؤشر التحميل
    Navigator.of(context).pop();

    // عرض النتائج
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتائج الاختبار السريع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: results.map((result) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(result),
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BridgeTestPage(),
                ),
              );
            },
            child: const Text('اختبار مفصل'),
          ),
        ],
      ),
    );
  }

  /// إضافة زر اختبار سريع لأي صفحة
  static Widget buildQuickTestButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => runQuickTest(context),
      backgroundColor: AssetsColors.dufaultGreencolor,
      icon: const Icon(Icons.bug_report),
      label: const Text('اختبار الجسر'),
    );
  }

  /// إضافة عنصر قائمة للاختبار السريع
  static PopupMenuItem<String> buildQuickTestMenuItem() {
    return const PopupMenuItem<String>(
      value: 'quick_test',
      child: Row(
        children: [
          Icon(Icons.bug_report),
          SizedBox(width: 8),
          Text('اختبار سريع للجسر'),
        ],
      ),
    );
  }
}

/// ودجت اختبار مدمج يمكن إضافته لأي صفحة
class EmbeddedBridgeTest extends StatefulWidget {
  const EmbeddedBridgeTest({super.key});

  @override
  State<EmbeddedBridgeTest> createState() => _EmbeddedBridgeTestState();
}

class _EmbeddedBridgeTestState extends State<EmbeddedBridgeTest> {
  String _lastTestResult = 'لم يتم تشغيل اختبار بعد';

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: ExpansionTile(
        title: const Text('🧪 اختبار الجسر'),
        subtitle: Text(_lastTestResult),
        leading: Icon(
          Icons.science,
          color: AssetsColors.dufaultGreencolor,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _runQuickTest,
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('اختبار سريع'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _openDetailedTest,
                        icon: const Icon(Icons.settings),
                        label: const Text('اختبار مفصل'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'آخر نتيجة: $_lastTestResult',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تشغيل اختبار سريع
  Future<void> _runQuickTest() async {
    setState(() {
      _lastTestResult = 'جاري التشغيل...';
    });

    try {
      final bridge = FarmerAdvisorBridge();
      await bridge.initialize();
      
      final consultation = await UnifiedConsultationService().createConsultation(
        farmerId: 'embedded_test',
        cropType: 'اختبار',
        problemDescription: 'اختبار مدمج',
      );

      if (consultation != null) {
        final success = await bridge.sendInstantConsultation(
          farmerId: 'embedded_test',
          consultation: consultation,
        );
        
        setState(() {
          _lastTestResult = success ? '✅ نجح الاختبار' : '❌ فشل الاختبار';
        });
      } else {
        setState(() {
          _lastTestResult = '❌ فشل في إنشاء الاستشارة';
        });
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '❌ خطأ: ${e.toString().substring(0, 30)}...';
      });
    }
  }

  /// فتح الاختبار المفصل
  void _openDetailedTest() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BridgeTestPage(),
      ),
    );
  }
}


