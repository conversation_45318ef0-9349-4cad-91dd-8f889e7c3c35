import 'package:flutter/material.dart';
import 'package:agriculture/core/shared/index.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/data/models/agricultural_advisor/appointment_model.dart';

/// صفحة اختبار جسر التواصل بين المزارع والاستشاري
/// تتيح اختبار جميع وظائف الجسر والخدمات المرتبطة
class BridgeTestPage extends StatefulWidget {
  const BridgeTestPage({super.key});

  @override
  State<BridgeTestPage> createState() => _BridgeTestPageState();
}

class _BridgeTestPageState extends State<BridgeTestPage> {
  // الخدمات
  final FarmerAdvisorBridge _bridge = FarmerAdvisorBridge();
  final UnifiedNotificationService _notifications = UnifiedNotificationService();
  final UnifiedAnalyticsService _analytics = UnifiedAnalyticsService();
  final UnifiedConsultationService _consultationService = UnifiedConsultationService();

  // حالة الاختبار
  bool _isInitialized = false;
  bool _isLoading = false;
  String _testResults = '';
  List<String> _testLogs = [];

  // بيانات الاختبار
  final String _testFarmerId = 'farmer_test_001';
  final String _testAdvisorId = 'advisor_test_001';

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// تهيئة جميع الخدمات
  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('🚀 بدء تهيئة الخدمات...');
      
      await _bridge.initialize();
      _addLog('✅ تم تهيئة جسر التواصل');
      
      await _notifications.initialize();
      _addLog('✅ تم تهيئة خدمة الإشعارات');
      
      await _analytics.initialize();
      _addLog('✅ تم تهيئة خدمة التحليلات');
      
      // إضافة مستمعين للاختبار
      _bridge.addConsultationListener(_onConsultationReceived);
      _bridge.addAppointmentListener(_onAppointmentReceived);
      _notifications.addNotificationListener(_onNotificationReceived);
      
      _addLog('✅ تم إضافة المستمعين');
      
      setState(() {
        _isInitialized = true;
        _testResults = 'تم تهيئة جميع الخدمات بنجاح ✅';
      });
      
    } catch (e) {
      _addLog('❌ خطأ في التهيئة: $e');
      setState(() {
        _testResults = 'فشل في التهيئة: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إضافة سجل جديد
  void _addLog(String message) {
    setState(() {
      _testLogs.add('${DateTime.now().toLocal().toString().substring(11, 19)} - $message');
    });
    debugPrint(message);
  }

  /// معالجة استلام استشارة جديدة
  void _onConsultationReceived(ConsultationModel consultation) {
    _addLog('📨 تم استلام استشارة: ${consultation.cropType}');
    _showSnackBar('استشارة جديدة: ${consultation.cropType}', Colors.green);
  }

  /// معالجة استلام موعد جديد
  void _onAppointmentReceived(AppointmentModel appointment) {
    _addLog('📅 تم استلام موعد: ${appointment.id}');
    _showSnackBar('موعد جديد: ${appointment.id}', Colors.blue);
  }

  /// معالجة استلام إشعار
  void _onNotificationReceived(Map<String, dynamic> notification) {
    _addLog('🔔 تم استلام إشعار: ${notification['type']}');
    _showSnackBar('إشعار جديد', Colors.orange);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار جسر التواصل'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حالة التهيئة
            _buildInitializationStatus(),
            
            const SizedBox(height: 20),
            
            // أزرار الاختبار
            if (_isInitialized) ...[
              _buildTestButtons(),
              const SizedBox(height: 20),
            ],
            
            // نتائج الاختبار
            _buildTestResults(),
            
            const SizedBox(height: 20),
            
            // سجل الأحداث
            _buildEventLogs(),
          ],
        ),
      ),
    );
  }

  /// بناء حالة التهيئة
  Widget _buildInitializationStatus() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.hourglass_empty,
                  color: _isInitialized ? Colors.green : Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة التهيئة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (_isLoading)
              const Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 12),
                  Text('جاري التهيئة...'),
                ],
              )
            else
              Text(
                _testResults.isEmpty ? 'في انتظار التهيئة' : _testResults,
                style: TextStyle(
                  fontSize: 14,
                  color: _isInitialized ? Colors.green : Colors.grey[600],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الاختبار
  Widget _buildTestButtons() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختبارات الجسر',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
            ),
            const SizedBox(height: 16),
            
            // اختبار الاستشارة الفورية
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testInstantConsultation,
                icon: const Icon(Icons.chat_bubble),
                label: const Text('اختبار الاستشارة الفورية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار حجز الموعد
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testAppointmentBooking,
                icon: const Icon(Icons.calendar_today),
                label: const Text('اختبار حجز الموعد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار مراقبة النبات
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testPlantMonitoring,
                icon: const Icon(Icons.eco),
                label: const Text('اختبار مراقبة النبات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار رد الاستشاري
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testAdvisorResponse,
                icon: const Icon(Icons.reply),
                label: const Text('اختبار رد الاستشاري'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار الإشعارات العاجلة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testUrgentNotification,
                icon: const Icon(Icons.notification_important),
                label: const Text('اختبار الإشعار العاجل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار شامل
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _runFullTest,
                icon: const Icon(Icons.play_arrow),
                label: const Text('تشغيل اختبار شامل'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AssetsColors.dufaultGreencolor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: BorderSide(color: AssetsColors.dufaultGreencolor),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نتائج الاختبار
  Widget _buildTestResults() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'نتائج الاختبار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
                IconButton(
                  onPressed: _clearResults,
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح النتائج',
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Container(
              width: double.infinity,
              height: 150,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _testResults.isEmpty ? 'لا توجد نتائج بعد...' : _testResults,
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء سجل الأحداث
  Widget _buildEventLogs() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'سجل الأحداث',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
                IconButton(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.delete),
                  tooltip: 'مسح السجل',
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Container(
              width: double.infinity,
              height: 200,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                reverse: true,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _testLogs.map((log) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      log,
                      style: const TextStyle(
                        color: Colors.green,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                  )).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ===== دوال الاختبار =====

  /// اختبار الاستشارة الفورية
  Future<void> _testInstantConsultation() async {
    _addLog('🧪 بدء اختبار الاستشارة الفورية...');

    try {
      // إنشاء استشارة تجريبية
      final consultation = await _consultationService.createConsultation(
        farmerId: _testFarmerId,
        cropType: 'طماطم',
        problemDescription: 'أوراق النبات تتحول للون الأصفر وتسقط',
        area: '500',
        priority: 'urgent',
      );

      if (consultation != null) {
        _addLog('✅ تم إنشاء الاستشارة: ${consultation.id}');

        // إرسال عبر الجسر
        final success = await _bridge.sendInstantConsultation(
          farmerId: _testFarmerId,
          consultation: consultation,
          priority: 'urgent',
        );

        if (success) {
          _addLog('✅ تم إرسال الاستشارة عبر الجسر بنجاح');
          _updateTestResults('✅ اختبار الاستشارة الفورية: نجح');
        } else {
          _addLog('❌ فشل في إرسال الاستشارة عبر الجسر');
          _updateTestResults('❌ اختبار الاستشارة الفورية: فشل');
        }
      } else {
        _addLog('❌ فشل في إنشاء الاستشارة');
        _updateTestResults('❌ اختبار الاستشارة الفورية: فشل في الإنشاء');
      }
    } catch (e) {
      _addLog('❌ خطأ في اختبار الاستشارة: $e');
      _updateTestResults('❌ اختبار الاستشارة الفورية: خطأ - $e');
    }
  }

  /// اختبار حجز الموعد
  Future<void> _testAppointmentBooking() async {
    _addLog('🧪 بدء اختبار حجز الموعد...');

    try {
      // إنشاء موعد تجريبي
      final appointment = await _consultationService.bookAppointment(
        farmerId: _testFarmerId,
        advisorId: _testAdvisorId,
        scheduledDate: DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        type: 'استشارة ميدانية',
        notes: 'زيارة لفحص النباتات المريضة',
      );

      if (appointment != null) {
        _addLog('✅ تم إنشاء الموعد: ${appointment.id}');

        // إرسال عبر الجسر
        final success = await _bridge.bookAppointment(
          farmerId: _testFarmerId,
          appointment: appointment,
        );

        if (success) {
          _addLog('✅ تم إرسال الموعد عبر الجسر بنجاح');
          _updateTestResults('✅ اختبار حجز الموعد: نجح');
        } else {
          _addLog('❌ فشل في إرسال الموعد عبر الجسر');
          _updateTestResults('❌ اختبار حجز الموعد: فشل');
        }
      } else {
        _addLog('❌ فشل في إنشاء الموعد');
        _updateTestResults('❌ اختبار حجز الموعد: فشل في الإنشاء');
      }
    } catch (e) {
      _addLog('❌ خطأ في اختبار الموعد: $e');
      _updateTestResults('❌ اختبار حجز الموعد: خطأ - $e');
    }
  }

  /// اختبار مراقبة النبات
  Future<void> _testPlantMonitoring() async {
    _addLog('🧪 بدء اختبار مراقبة النبات...');

    try {
      final plantData = {
        'type': 'خيار',
        'area': '300',
        'age': '45 يوم',
        'location': 'الحقل الشمالي',
        'symptoms': 'بقع بنية على الأوراق',
      };

      final success = await _bridge.sendPlantMonitoringRequest(
        farmerId: _testFarmerId,
        plantData: plantData,
        monitoringType: 'فحص الأمراض',
      );

      if (success) {
        _addLog('✅ تم إرسال طلب مراقبة النبات بنجاح');
        _updateTestResults('✅ اختبار مراقبة النبات: نجح');
      } else {
        _addLog('❌ فشل في إرسال طلب مراقبة النبات');
        _updateTestResults('❌ اختبار مراقبة النبات: فشل');
      }
    } catch (e) {
      _addLog('❌ خطأ في اختبار مراقبة النبات: $e');
      _updateTestResults('❌ اختبار مراقبة النبات: خطأ - $e');
    }
  }

  /// اختبار رد الاستشاري
  Future<void> _testAdvisorResponse() async {
    _addLog('🧪 بدء اختبار رد الاستشاري...');

    try {
      final success = await _bridge.sendAdvisorResponse(
        advisorId: _testAdvisorId,
        farmerId: _testFarmerId,
        response: 'هذه مشكلة شائعة في نباتات الطماطم. أنصح بتقليل الري وتحسين التهوية.',
        consultationId: 'test_consultation_001',
        attachments: ['نصائح_العناية.pdf', 'جدول_الري.jpg'],
      );

      if (success) {
        _addLog('✅ تم إرسال رد الاستشاري بنجاح');
        _updateTestResults('✅ اختبار رد الاستشاري: نجح');
      } else {
        _addLog('❌ فشل في إرسال رد الاستشاري');
        _updateTestResults('❌ اختبار رد الاستشاري: فشل');
      }
    } catch (e) {
      _addLog('❌ خطأ في اختبار رد الاستشاري: $e');
      _updateTestResults('❌ اختبار رد الاستشاري: خطأ - $e');
    }
  }

  /// اختبار الإشعار العاجل
  Future<void> _testUrgentNotification() async {
    _addLog('🧪 بدء اختبار الإشعار العاجل...');

    try {
      await _notifications.sendUrgentNotification(
        title: 'تحذير عاجل',
        body: 'تم اكتشاف آفة خطيرة في المنطقة - يرجى اتخاذ الإجراءات فوراً',
        targetType: 'farmer',
        data: {
          'type': 'urgent_alert',
          'severity': 'high',
          'action_required': true,
        },
      );

      _addLog('✅ تم إرسال الإشعار العاجل بنجاح');
      _updateTestResults('✅ اختبار الإشعار العاجل: نجح');
    } catch (e) {
      _addLog('❌ خطأ في اختبار الإشعار العاجل: $e');
      _updateTestResults('❌ اختبار الإشعار العاجل: خطأ - $e');
    }
  }

  /// تشغيل اختبار شامل
  Future<void> _runFullTest() async {
    _addLog('🚀 بدء الاختبار الشامل...');
    _clearResults();

    await _testInstantConsultation();
    await Future.delayed(const Duration(seconds: 1));

    await _testAppointmentBooking();
    await Future.delayed(const Duration(seconds: 1));

    await _testPlantMonitoring();
    await Future.delayed(const Duration(seconds: 1));

    await _testAdvisorResponse();
    await Future.delayed(const Duration(seconds: 1));

    await _testUrgentNotification();

    _addLog('🏁 انتهى الاختبار الشامل');
    _updateTestResults('\n🏁 تم الانتهاء من جميع الاختبارات');
  }

  // ===== دوال مساعدة =====

  /// تحديث نتائج الاختبار
  void _updateTestResults(String result) {
    setState(() {
      _testResults += '$result\n';
    });
  }

  /// مسح النتائج
  void _clearResults() {
    setState(() {
      _testResults = '';
    });
  }

  /// مسح السجل
  void _clearLogs() {
    setState(() {
      _testLogs.clear();
    });
  }

  /// عرض رسالة سريعة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    // تنظيف المستمعين
    _bridge.removeConsultationListener(_onConsultationReceived);
    _bridge.removeAppointmentListener(_onAppointmentReceived);
    _notifications.removeNotificationListener(_onNotificationReceived);

    // تنظيف الخدمات
    _bridge.dispose();
    _notifications.dispose();
    _analytics.dispose();
    _consultationService.dispose();

    super.dispose();
  }
}
