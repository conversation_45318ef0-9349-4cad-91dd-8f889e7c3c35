# 🧪 دليل اختبار جسر التواصل

## نظرة عامة
هذا الدليل يشرح كيفية اختبار جسر التواصل بين المزارع والاستشاري الزراعي.

## 🚀 طرق الاختبار

### 1. الاختبار السريع
```dart
// في أي صفحة، أضف هذا الكود:
import 'package:agriculture/presentation/pages/testing/quick_bridge_test.dart';

// في build method:
floatingActionButton: QuickBridgeTest.buildQuickTestButton(context),

// أو في قائمة:
PopupMenuButton(
  itemBuilder: (context) => [
    QuickBridgeTest.buildQuickTestMenuItem(),
  ],
  onSelected: (value) {
    if (value == 'quick_test') {
      QuickBridgeTest.runQuickTest(context);
    }
  },
)
```

### 2. الاختبار المدمج
```dart
// أضف هذا الودجت في أي صفحة:
EmbeddedBridgeTest()
```

### 3. الاختبار المفصل
```dart
// انتقل إلى صفحة الاختبار الكاملة:
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => BridgeTestPage(),
  ),
);
```

## 🔧 الاختبارات المتاحة

### ✅ اختبار الاستشارة الفورية
- إنشاء استشارة تجريبية
- إرسالها عبر الجسر
- التحقق من وصولها للاستشاري
- اختبار الإشعارات

### ✅ اختبار حجز المواعيد
- إنشاء موعد تجريبي
- إرسال طلب الحجز
- التحقق من التأكيد
- اختبار التذكيرات

### ✅ اختبار مراقبة النبات
- إرسال طلب مراقبة
- تضمين بيانات النبات
- اختبار التحليلات
- التحقق من الاستجابة

### ✅ اختبار رد الاستشاري
- محاكاة رد الاستشاري
- إرسال الرد للمزارع
- اختبار المرفقات
- التحقق من الوصول

### ✅ اختبار الإشعارات العاجلة
- إرسال إشعار عاجل
- اختبار الأولوية العالية
- التحقق من العرض
- اختبار الصوت والاهتزاز

## 📊 مراقبة النتائج

### سجل الأحداث
- يعرض جميع الأحداث بالوقت الفعلي
- يمكن مسح السجل
- يحفظ تاريخ كل حدث

### نتائج الاختبار
- عرض نتيجة كل اختبار
- تفاصيل الأخطاء إن وجدت
- إمكانية مسح النتائج

### الإشعارات المباشرة
- SnackBar للنتائج السريعة
- حالة مرئية للاختبارات
- ألوان مختلفة للحالات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. فشل التهيئة
```
❌ خطأ في التهيئة: ...
```
**الحل:** تأكد من تهيئة Firebase وإعدادات الإشعارات

#### 2. فشل إرسال الاستشارة
```
❌ فشل في إرسال الاستشارة عبر الجسر
```
**الحل:** تحقق من اتصال الشبكة وحالة الخدمات

#### 3. عدم وصول الإشعارات
```
❌ فشل في إرسال الإشعار
```
**الحل:** تحقق من أذونات الإشعارات في النظام

### نصائح للاختبار:

1. **ابدأ بالاختبار السريع** للتحقق من الوظائف الأساسية
2. **استخدم الاختبار المفصل** لفحص شامل
3. **راقب سجل الأحداث** لفهم تدفق البيانات
4. **اختبر على أجهزة مختلفة** للتأكد من التوافق

## 🔄 التكامل مع الصفحات الموجودة

### في صفحة المزارع (reach_engineer):
```dart
// أضف زر اختبار في AppBar:
actions: [
  PopupMenuButton(
    itemBuilder: (context) => [
      QuickBridgeTest.buildQuickTestMenuItem(),
    ],
    onSelected: (value) {
      if (value == 'quick_test') {
        QuickBridgeTest.runQuickTest(context);
      }
    },
  ),
],
```

### في صفحة الاستشاري (advisor_virtual_interface):
```dart
// أضف الاختبار المدمج في لوحة التحكم:
children: [
  // ... باقي الودجات
  EmbeddedBridgeTest(),
],
```

## 📈 مراقبة الأداء

### مؤشرات الأداء:
- وقت الاستجابة للاستشارات
- معدل نجاح الإرسال
- عدد الإشعارات المرسلة
- حالة الاتصال

### التحليلات:
- يتم تسجيل جميع الأحداث في `UnifiedAnalyticsService`
- يمكن عرض التقارير من صفحة التقارير
- تتبع أداء الجسر بالوقت الفعلي

## 🎯 الخطوات التالية

1. **اختبر الجسر** باستخدام الطرق المختلفة
2. **راقب النتائج** وسجل أي مشاكل
3. **حسن الأداء** بناءً على النتائج
4. **أضف اختبارات جديدة** حسب الحاجة
5. **وثق أي تغييرات** في هذا الملف

---

**ملاحظة:** هذا الاختبار مخصص للتطوير فقط. لا تستخدمه في الإنتاج النهائي.
