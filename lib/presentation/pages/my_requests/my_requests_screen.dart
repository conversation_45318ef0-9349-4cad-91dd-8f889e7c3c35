import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/core/shared/services/user/user_data_service.dart';
// تم حذف استيراد appointment_model غير المستخدم

import '../../bloc/advisor/advisor_cubit.dart';
import '../../bloc/advisor/advisor_state.dart';

/// شاشة طلبات المستخدم
class MyRequestsScreen extends StatefulWidget {
  const MyRequestsScreen({super.key});

  @override
  State<MyRequestsScreen> createState() => _MyRequestsScreenState();
}

class _MyRequestsScreenState extends State<MyRequestsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserRequests() {
    // الحصول على معرف المستخدم الحالي من SharedPrefs
    final userData = UserDataService.getCurrentUserData();
    context.read<AdvisorCubit>().getUserConsultations(userId: userData.userId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: const Text(
          'طلباتي',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.chat), text: 'الاستشارات'),
            Tab(icon: Icon(Icons.calendar_today), text: 'المواعيد'),
            Tab(icon: Icon(Icons.visibility), text: 'المراقبة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildConsultationsTab(),
          _buildAppointmentsTab(),
          _buildMonitoringTab(),
        ],
      ),
    );
  }

  /// تبويب الاستشارات
  Widget _buildConsultationsTab() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        if (state is ConsultationsLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ConsultationsLoaded) {
          if (state.consultations.isEmpty) {
            return _buildEmptyState(
              icon: Icons.chat_bubble_outline,
              title: 'لا توجد استشارات',
              subtitle: 'لم تقم بإرسال أي استشارات بعد',
            );
          }
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.consultations.length,
            itemBuilder: (context, index) {
              return _buildConsultationCard(state.consultations[index]);
            },
          );
        } else if (state is AdvisorError) {
          return _buildErrorState(state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// تبويب المواعيد
  Widget _buildAppointmentsTab() {
    return _buildEmptyState(
      icon: Icons.calendar_today,
      title: 'لا توجد مواعيد',
      subtitle: 'لم تقم بحجز أي مواعيد بعد',
    );
  }

  /// تبويب المراقبة
  Widget _buildMonitoringTab() {
    return _buildEmptyState(
      icon: Icons.visibility,
      title: 'لا توجد طلبات مراقبة',
      subtitle: 'لم تقم بإرسال أي طلبات مراقبة بعد',
    );
  }

  /// بناء بطاقة استشارة
  Widget _buildConsultationCard(ConsultationModel consultation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الهيدر
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      consultation.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.chat,
                    color: _getStatusColor(consultation.status),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'استشارة ${consultation.cropType}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'المرشد: ${consultation.advisorName}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(consultation.status),
              ],
            ),
            const SizedBox(height: 12),

            // وصف المشكلة
            Text(
              consultation.problemDescription,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            // الصور (إن وجدت)
            if (consultation.images != null && consultation.images!.isNotEmpty)
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: consultation.images!.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 60,
                      height: 60,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(consultation.images![index]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ),
            const SizedBox(height: 12),

            // رد المرشد (إن وجد)
            if (consultation.advisorResponse != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.reply, color: Colors.green, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'رد المرشد:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      consultation.advisorResponse!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 12),

            // التاريخ والإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDate(consultation.createdAt),
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
                if (consultation.status == ConsultationStatus.answered &&
                    consultation.rating == null)
                  TextButton(
                    onPressed: () => _showRatingDialog(consultation),
                    child: Text(
                      'تقييم',
                      style: TextStyle(
                        color: AssetsColors.dufaultGreencolor,
                        fontSize: 14,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(ConsultationStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getStatusText(status),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة خطأ
  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadUserRequests,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار التقييم
  void _showRatingDialog(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تقييم الاستشارة'),
            content: const Text('كيف تقيم جودة الاستشارة المقدمة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: تنفيذ التقييم
                },
                child: const Text('تقييم'),
              ),
            ],
          ),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return Colors.orange;
      case ConsultationStatus.inProgress:
        return Colors.blue;
      case ConsultationStatus.answered:
        return Colors.green;
      case ConsultationStatus.closed:
        return Colors.grey;
      case ConsultationStatus.cancelled:
        return Colors.red;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'بانتظار الرد';
      case ConsultationStatus.inProgress:
        return 'جاري المعالجة';
      case ConsultationStatus.answered:
        return 'تمت الإجابة';
      case ConsultationStatus.closed:
        return 'مغلقة';
      case ConsultationStatus.cancelled:
        return 'ملغاة';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }
}
