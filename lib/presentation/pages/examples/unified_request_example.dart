import 'dart:io';
import 'package:flutter/material.dart';
import 'package:agriculture/core/shared/services/enhanced_unified_submission_service.dart';
import 'package:agriculture/data/models/unified_request/unified_request_model.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// مثال لاستخدام الخدمة الموحدة لإرسال الطلبات
/// 
/// يوضح كيفية استخدام الخدمة الموحدة لجميع أنواع الطلبات
/// بطريقة متسقة وموحدة
class UnifiedRequestExample extends StatelessWidget {
  const UnifiedRequestExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال الخدمة الموحدة'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // مقدمة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الخدمة الموحدة لإرسال الطلبات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.dufaultGreencolor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'تدعم الخدمة الموحدة جميع أنواع الطلبات بطريقة متسقة:\n'
                      '• الاستشارات الزراعية\n'
                      '• حجز المواعيد\n'
                      '• مراقبة النبات',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // أزرار الأمثلة
            _buildExampleButton(
              context,
              title: 'مثال إرسال استشارة',
              description: 'إرسال استشارة زراعية موحدة',
              icon: Icons.chat_bubble,
              color: Colors.blue,
              onPressed: () => _sendConsultationExample(context),
            ),
            
            const SizedBox(height: 12),
            
            _buildExampleButton(
              context,
              title: 'مثال حجز موعد',
              description: 'حجز موعد مع المرشد الزراعي',
              icon: Icons.calendar_today,
              color: Colors.green,
              onPressed: () => _sendAppointmentExample(context),
            ),
            
            const SizedBox(height: 12),
            
            _buildExampleButton(
              context,
              title: 'مثال مراقبة النبات',
              description: 'طلب مراقبة النبات موحد',
              icon: Icons.eco,
              color: Colors.orange,
              onPressed: () => _sendPlantMonitoringExample(context),
            ),
            
            const SizedBox(height: 20),
            
            // معلومات إضافية
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue),
                        const SizedBox(width: 8),
                        const Text(
                          'مميزات الخدمة الموحدة',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '✅ تحقق موحد من صحة البيانات\n'
                      '✅ رسائل خطأ ونجاح متسقة\n'
                      '✅ معالجة موحدة للأخطاء\n'
                      '✅ واجهة مستخدم متسقة\n'
                      '✅ سهولة الصيانة والتطوير',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر المثال
  Widget _buildExampleButton(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: color,
                child: Icon(icon, color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  /// مثال إرسال استشارة
  Future<void> _sendConsultationExample(BuildContext context) async {
    final success = await EnhancedUnifiedSubmissionService.submitConsultation(
      context: context,
      cropType: 'طماطم',
      problemDescription: 'أوراق النبات تصفر وتذبل، أحتاج مساعدة في تشخيص المشكلة وإيجاد الحل المناسب.',
      area: '100',
      priority: RequestPriority.normal,
    );

    if (success) {
      debugPrint('✅ تم إرسال الاستشارة بنجاح');
    } else {
      debugPrint('❌ فشل في إرسال الاستشارة');
    }
  }

  /// مثال حجز موعد
  Future<void> _sendAppointmentExample(BuildContext context) async {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final appointmentDate = '${tomorrow.year}-${tomorrow.month.toString().padLeft(2, '0')}-${tomorrow.day.toString().padLeft(2, '0')}';
    
    final success = await EnhancedUnifiedSubmissionService.submitAppointment(
      context: context,
      consultationType: 'استشارة زراعية',
      appointmentDate: appointmentDate,
      appointmentTime: const TimeOfDay(hour: 10, minute: 0),
      problemDescription: 'أحتاج استشارة حول أفضل طرق الري للمحاصيل في فصل الصيف.',
      priority: RequestPriority.normal,
    );

    if (success) {
      debugPrint('✅ تم حجز الموعد بنجاح');
    } else {
      debugPrint('❌ فشل في حجز الموعد');
    }
  }

  /// مثال مراقبة النبات
  Future<void> _sendPlantMonitoringExample(BuildContext context) async {
    final success = await EnhancedUnifiedSubmissionService.submitPlantMonitoring(
      context: context,
      plantType: 'خيار',
      location: 'الرياض - مزرعة الخير',
      monitoringType: 'مراقبة صحة النبات',
      notes: 'أحتاج مراقبة دورية لحالة النبات والتأكد من عدم وجود آفات أو أمراض.',
      priority: RequestPriority.high,
      estimatedCost: 150.0,
    );

    if (success) {
      debugPrint('✅ تم إرسال طلب مراقبة النبات بنجاح');
    } else {
      debugPrint('❌ فشل في إرسال طلب مراقبة النبات');
    }
  }
}
