import 'package:flutter/material.dart';
import '../../../core/constants/assets_colors.dart';
// إدارة النظام والمرشدين مهممممممممممممممممممممم
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لوحة تحكم الأدمن',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              'إدارة النظام والمرشدين',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: AssetsColors.dufaultGreencolor,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الإحصائيات'),
            Tab(text: 'المرشدين'),
            Tab(text: 'الطلبات'),
            Tab(text: 'الإعدادات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStatisticsTab(),
          _buildAdvisorsTab(),
          _buildRequestsTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة
          _buildStatsGrid(),
          const SizedBox(height: 24),

          // الرسوم البيانية
          _buildChartsSection(),
          const SizedBox(height: 24),

          // النشاط الأخير
          _buildRecentActivity(),
        ],
      ),
    );
  }

  /// شبكة الإحصائيات
  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'إجمالي المرشدين',
          value: '25',
          icon: Icons.people,
          color: Colors.blue,
          trend: '+3 هذا الشهر',
        ),
        _buildStatCard(
          title: 'الاستشارات اليوم',
          value: '47',
          icon: Icons.chat,
          color: Colors.green,
          trend: '+12 من أمس',
        ),
        _buildStatCard(
          title: 'المواعيد المعلقة',
          value: '18',
          icon: Icons.schedule,
          color: Colors.orange,
          trend: '-5 من أمس',
        ),
        _buildStatCard(
          title: 'المزارعين النشطين',
          value: '342',
          icon: Icons.agriculture,
          color: Colors.purple,
          trend: '+28 هذا الأسبوع',
        ),
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            trend,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// قسم الرسوم البيانية
  Widget _buildChartsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'نشاط النظام - آخر 7 أيام',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            child: const Center(
              child: Text(
                'الرسم البياني سيتم إضافته لاحقاً',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// النشاط الأخير
  Widget _buildRecentActivity() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'النشاط الأخير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...List.generate(5, (index) => _buildActivityItem(index)),
        ],
      ),
    );
  }

  /// عنصر نشاط
  Widget _buildActivityItem(int index) {
    final activities = [
      {'text': 'مرشد جديد انضم للنظام', 'time': 'منذ 5 دقائق', 'icon': Icons.person_add, 'color': Colors.green},
      {'text': 'تم الرد على استشارة جديدة', 'time': 'منذ 15 دقيقة', 'icon': Icons.chat, 'color': Colors.blue},
      {'text': 'تم حجز موعد جديد', 'time': 'منذ 30 دقيقة', 'icon': Icons.schedule, 'color': Colors.orange},
      {'text': 'مزارع جديد سجل في التطبيق', 'time': 'منذ ساعة', 'icon': Icons.agriculture, 'color': Colors.purple},
      {'text': 'تم إكمال طلب مراقبة', 'time': 'منذ ساعتين', 'icon': Icons.visibility, 'color': Colors.teal},
    ];

    final activity = activities[index];

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: (activity['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity['icon'] as IconData,
              color: activity['color'] as Color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity['text'] as String,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  activity['time'] as String,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب المرشدين
  Widget _buildAdvisorsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات المرشدين
          Row(
            children: [
              Expanded(
                child: _buildAdvisorStatCard(
                  title: 'المرشدين النشطين',
                  value: '23',
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAdvisorStatCard(
                  title: 'في انتظار الموافقة',
                  value: '2',
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // قائمة المرشدين
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Text(
                        'قائمة المرشدين',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton.icon(
                        onPressed: () {
                          // إضافة مرشد جديد
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة مرشد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AssetsColors.dufaultGreencolor,
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                ...List.generate(5, (index) => _buildAdvisorItem(index)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة إحصائية للمرشدين
  Widget _buildAdvisorStatCard({
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عنصر مرشد
  Widget _buildAdvisorItem(int index) {
    final advisors = [
      {'name': 'د. أحمد محمد', 'specialty': 'محاصيل حقلية', 'rating': '4.8', 'consultations': '156'},
      {'name': 'م. فاطمة علي', 'specialty': 'خضروات', 'rating': '4.9', 'consultations': '203'},
      {'name': 'د. محمد حسن', 'specialty': 'فواكه', 'rating': '4.7', 'consultations': '134'},
      {'name': 'م. سارة أحمد', 'specialty': 'نباتات زينة', 'rating': '4.6', 'consultations': '89'},
      {'name': 'د. عبدالله سالم', 'specialty': 'أمراض النبات', 'rating': '4.9', 'consultations': '267'},
    ];

    final advisor = advisors[index];

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AssetsColors.dufaultGreencolor.withOpacity(0.1),
        child: Text(
          advisor['name']!.substring(0, 2),
          style: TextStyle(
            color: AssetsColors.dufaultGreencolor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        advisor['name']!,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(advisor['specialty']!),
          Row(
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 16),
              Text(' ${advisor['rating']} • ${advisor['consultations']} استشارة'),
            ],
          ),
        ],
      ),
      trailing: PopupMenuButton(
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'view',
            child: Text('عرض التفاصيل'),
          ),
          const PopupMenuItem(
            value: 'edit',
            child: Text('تعديل'),
          ),
          const PopupMenuItem(
            value: 'suspend',
            child: Text('إيقاف مؤقت'),
          ),
        ],
      ),
    );
  }

  /// تبويب الطلبات
  Widget _buildRequestsTab() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            color: Colors.white,
            child: const TabBar(
              labelColor: Colors.black87,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.green,
              tabs: [
                Tab(text: 'الاستشارات'),
                Tab(text: 'المواعيد'),
                Tab(text: 'المراقبة'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildConsultationsList(),
                _buildAppointmentsList(),
                _buildMonitoringList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// قائمة الاستشارات
  Widget _buildConsultationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withOpacity(0.1),
              child: const Icon(Icons.chat, color: Colors.blue),
            ),
            title: Text('استشارة #${1000 + index}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المزارع: أحمد محمد'),
                Text('المرشد: د. فاطمة علي'),
                Text('الحالة: ${index % 3 == 0 ? 'معلقة' : index % 3 == 1 ? 'مجابة' : 'مغلقة'}'),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              // عرض تفاصيل الاستشارة
            },
          ),
        );
      },
    );
  }

  /// قائمة المواعيد
  Widget _buildAppointmentsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 8,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.orange.withOpacity(0.1),
              child: const Icon(Icons.schedule, color: Colors.orange),
            ),
            title: Text('موعد #${2000 + index}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المزارع: سارة أحمد'),
                Text('المرشد: د. محمد حسن'),
                Text('التاريخ: ${DateTime.now().add(Duration(days: index)).toString().split(' ')[0]}'),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              // عرض تفاصيل الموعد
            },
          ),
        );
      },
    );
  }

  /// قائمة المراقبة
  Widget _buildMonitoringList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.purple.withOpacity(0.1),
              child: const Icon(Icons.visibility, color: Colors.purple),
            ),
            title: Text('مراقبة #${3000 + index}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المزارع: عبدالله سالم'),
                Text('النبات: طماطم'),
                Text('النوع: ${index % 2 == 0 ? 'أسبوعية' : 'شهرية'}'),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              // عرض تفاصيل المراقبة
            },
          ),
        );
      },
    );
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSettingsSection(
          title: 'إعدادات النظام',
          items: [
            _buildSettingsItem(
              icon: Icons.notifications,
              title: 'إعدادات الإشعارات',
              subtitle: 'إدارة إشعارات النظام',
              onTap: () {},
            ),
            _buildSettingsItem(
              icon: Icons.security,
              title: 'الأمان والخصوصية',
              subtitle: 'إعدادات الأمان',
              onTap: () {},
            ),
            _buildSettingsItem(
              icon: Icons.backup,
              title: 'النسخ الاحتياطي',
              subtitle: 'إدارة النسخ الاحتياطية',
              onTap: () {},
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSettingsSection(
          title: 'إدارة المحتوى',
          items: [
            _buildSettingsItem(
              icon: Icons.article,
              title: 'إدارة الأخبار',
              subtitle: 'إضافة وتعديل الأخبار الزراعية',
              onTap: () {},
            ),
            _buildSettingsItem(
              icon: Icons.help,
              title: 'الأسئلة الشائعة',
              subtitle: 'إدارة الأسئلة والأجوبة',
              onTap: () {},
            ),
            _buildSettingsItem(
              icon: Icons.feedback,
              title: 'التقييمات والمراجعات',
              subtitle: 'إدارة تقييمات المستخدمين',
              onTap: () {},
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSettingsSection(
          title: 'التقارير',
          items: [
            _buildSettingsItem(
              icon: Icons.analytics,
              title: 'تقارير الاستخدام',
              subtitle: 'إحصائيات مفصلة عن النظام',
              onTap: () {},
            ),
            _buildSettingsItem(
              icon: Icons.download,
              title: 'تصدير البيانات',
              subtitle: 'تصدير بيانات النظام',
              onTap: () {},
            ),
          ],
        ),
      ],
    );
  }

  /// قسم إعدادات
  Widget _buildSettingsSection({
    required String title,
    required List<Widget> items,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ...items,
        ],
      ),
    );
  }

  /// عنصر إعدادات
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AssetsColors.dufaultGreencolor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AssetsColors.dufaultGreencolor,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
