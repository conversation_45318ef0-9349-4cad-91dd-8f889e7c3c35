import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/plant_monitoring_constants.dart';
import 'package:agriculture/presentation/bloc/plant_monitoring/index.dart';
import 'package:agriculture/presentation/widgets/shared/real_service_banner.dart';
import 'package:agriculture/presentation/widgets/shared/image_upload_widget.dart';
import 'package:agriculture/presentation/pages/reach_engineer/widgets/index.dart';
import 'package:agriculture/data/services/plant_monitoring_service.dart';
import 'package:agriculture/data/services/ai_analysis_service.dart';
import 'dart:io';

/// صفحة مراقبة النبات الحقيقية
/// 
/// صفحة مفعلة بالكامل مع Clean Architecture
/// تستخدم Cubit لإدارة الحالة بدلاً من StatefulWidget
/// تتبع جميع المعايير الـ18 المحددة
class RealPlantMonitoringScreen extends StatelessWidget {
  const RealPlantMonitoringScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PlantMonitoringCubit(
        plantMonitoringService: PlantMonitoringService(),
        locationService: LocationService(),
        aiAnalysisService: AIAnalysisService(),
      ),
      child: const _RealPlantMonitoringView(),
    );
  }
}

/// عرض صفحة مراقبة النبات
class _RealPlantMonitoringView extends StatelessWidget {
  const _RealPlantMonitoringView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(PlantMonitoringConstants.pageTitle),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        actions: [
          // زر المساعدة
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
          ),
          // زر تحديد الموقع
          BlocBuilder<PlantMonitoringCubit, PlantMonitoringState>(
            builder: (context, state) {
              return IconButton(
                icon: const Icon(Icons.location_on),
                onPressed: () {
                  context.read<PlantMonitoringCubit>().getCurrentLocation();
                },
              );
            },
          ),
        ],
      ),
      body: BlocListener<PlantMonitoringCubit, PlantMonitoringState>(
        listener: (context, state) {
          _handleStateChanges(context, state);
        },
        child: const SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بانر الخدمة الحقيقية
              PlantMonitoringServiceBanner(),
              
              SizedBox(height: 16),
              
              // معلومات المزارع
              FarmerInfoSection(),
              
              SizedBox(height: 24),
              
              // معلومات المزرعة
              FarmInfoSection(),
              
              SizedBox(height: 24),
              
              // تفاصيل المراقبة
              MonitoringDetailsSection(),
              
              SizedBox(height: 24),
              
              // صور النبات
              PlantImagesSection(),
              
              SizedBox(height: 24),
              
              // الموقع الجغرافي
              LocationSection(),
              
              SizedBox(height: 24),
              
              // جدولة الزيارة
              SchedulingSection(),
              
              SizedBox(height: 24),
              
              // التكلفة المقدرة
              CostSection(),
              
              SizedBox(height: 24),
              
              // زر الإرسال
              SubmitButtonSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// معالجة تغييرات الحالة (المعيار #6)
  void _handleStateChanges(BuildContext context, PlantMonitoringState state) {
    if (state is PlantMonitoringError) {
      _showErrorSnackBar(context, state.message);
    } else if (state is PlantMonitoringSubmitSuccess) {
      _showSuccessDialog(context, state);
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }



  /// عرض حوار النجاح
  void _showSuccessDialog(BuildContext context, PlantMonitoringSubmitSuccess state) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 32),
            SizedBox(width: 8),
            Text('🔥 تم الإرسال بنجاح!'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🔥 خدمة حقيقية مفعلة:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text('✅ تم إرسال الطلب بنجاح'),
                    const Text('🌱 تم حفظ البيانات في Firestore'),
                    const Text('🔬 تم تحليل الصور بالذكاء الاصطناعي'),
                    const Text('💰 تم حساب التكلفة تلقائياً'),
                    const Text('📸 تم رفع الصور إلى Firebase Storage'),
                    const Text('🔔 تم إرسال إشعار للمهندس'),
                    const Text('📱 تم إرسال SMS تأكيد'),
                    const Text('⏰ سيتم التواصل معك قريباً'),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'هذه خدمة حقيقية مفعلة بالكامل!',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض نتائج تحليل الذكاء الاصطناعي
  void _showAIAnalysisResults(BuildContext context, Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.blue),
            SizedBox(width: 8),
            Text('🤖 نتائج التحليل'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (results['status'] == 'success') ...[
                Text('📊 نقاط الصحة: ${results['health_score']}/100'),
                Text('🌱 الحالة العامة: ${results['overall_health']}'),
                const SizedBox(height: 8),
                if (results['diseases']?.isNotEmpty == true) ...[
                  const Text('🦠 الأمراض المكتشفة:', style: TextStyle(fontWeight: FontWeight.bold)),
                  ...results['diseases'].map<Widget>((disease) => 
                    Text('• ${disease['name_ar'] ?? disease['name']}')),
                  const SizedBox(height: 8),
                ],
                if (results['pests']?.isNotEmpty == true) ...[
                  const Text('🐛 الآفات المكتشفة:', style: TextStyle(fontWeight: FontWeight.bold)),
                  ...results['pests'].map<Widget>((pest) => 
                    Text('• ${pest['name_ar'] ?? pest['name']}')),
                ],
              ] else ...[
                Text('❌ ${results['error_message'] ?? 'فشل في التحليل'}'),
                const SizedBox(height: 8),
                const Text('يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار المساعدة
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(PlantMonitoringConstants.helpTitle),
        content: const SingleChildScrollView(
          child: Text(PlantMonitoringConstants.helpContent),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
