import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/plant_monitoring_constants.dart';
import 'package:agriculture/presentation/bloc/plant_monitoring/index.dart';
import 'package:agriculture/domain/entities/plant_monitoring_request.dart';

/// قسم الموقع الجغرافي
/// 
/// يحتوي على واجهة تحديد الموقع الجغرافي بـ GPS
/// يتبع مبادئ Clean Architecture ويستخدم Cubit لإدارة الحالة
class LocationSection extends StatelessWidget {
  const LocationSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlantMonitoringCubit, PlantMonitoringState>(
      builder: (context, state) {
        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان القسم
                _buildSectionTitle(),
                
                const SizedBox(height: 16),
                
                // معلومات الموقع الحالي
                _buildCurrentLocationInfo(context, state),
                
                const SizedBox(height: 12),
                
                // نص المساعدة
                _buildHelpText(),
                
                // عرض حالة تحديد الموقع
                if (state is PlantMonitoringLoading && 
                    state.message.contains('موقع'))
                  _buildLocationLoadingIndicator(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle() {
    return Row(
      children: [
        Icon(
          Icons.location_on,
          color: Colors.green[600],
          size: 24,
        ),
        const SizedBox(width: 8),
        const Text(
          PlantMonitoringConstants.locationTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الموقع الحالي
  Widget _buildCurrentLocationInfo(BuildContext context, PlantMonitoringState state) {
    GpsLocation? currentLocation;
    if (state is PlantMonitoringFormUpdated) {
      currentLocation = state.currentLocation;
    } else if (state is PlantMonitoringLocationUpdated) {
      currentLocation = state.location;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: currentLocation != null ? Colors.green[50] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: currentLocation != null ? Colors.green[200]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                currentLocation != null ? Icons.gps_fixed : Icons.gps_off,
                color: currentLocation != null ? Colors.green[600] : Colors.grey[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentLocation != null ? '📍 الموقع محدد' : '📍 الموقع غير محدد',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: currentLocation != null ? Colors.green[700] : Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (currentLocation != null) ...[
                      Text(
                        'خط العرض: ${currentLocation.latitude.toStringAsFixed(6)}',
                        style: TextStyle(
                          color: Colors.green[600],
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        'خط الطول: ${currentLocation.longitude.toStringAsFixed(6)}',
                        style: TextStyle(
                          color: Colors.green[600],
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        'دقة التحديد: ${currentLocation.accuracy.toStringAsFixed(1)} متر',
                        style: TextStyle(
                          color: Colors.green[600],
                          fontSize: 12,
                        ),
                      ),
                    ] else ...[
                      Text(
                        'اضغط على زر التحديث لتحديد الموقع',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // زر تحديث الموقع
              IconButton(
                onPressed: () {
                  context.read<PlantMonitoringCubit>().getCurrentLocation();
                },
                icon: Icon(
                  Icons.refresh,
                  color: Colors.green[600],
                ),
                tooltip: PlantMonitoringConstants.refreshLocationText,
              ),
            ],
          ),
          
          // معلومات إضافية عن الموقع
          if (currentLocation != null) ...[
            const SizedBox(height: 12),
            _buildLocationAccuracyIndicator(currentLocation),
          ],
        ],
      ),
    );
  }

  /// بناء مؤشر دقة الموقع
  Widget _buildLocationAccuracyIndicator(GpsLocation location) {
    Color accuracyColor;
    String accuracyText;
    IconData accuracyIcon;

    if (location.accuracy <= 10) {
      accuracyColor = Colors.green;
      accuracyText = 'دقة عالية جداً';
      accuracyIcon = Icons.gps_fixed;
    } else if (location.accuracy <= 50) {
      accuracyColor = Colors.lightGreen;
      accuracyText = 'دقة عالية';
      accuracyIcon = Icons.gps_fixed;
    } else if (location.accuracy <= 100) {
      accuracyColor = Colors.orange;
      accuracyText = 'دقة متوسطة';
      accuracyIcon = Icons.gps_not_fixed;
    } else {
      accuracyColor = Colors.red;
      accuracyText = 'دقة منخفضة';
      accuracyIcon = Icons.gps_off;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: accuracyColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: accuracyColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(accuracyIcon, color: accuracyColor, size: 16),
          const SizedBox(width: 4),
          Text(
            accuracyText,
            style: TextStyle(
              color: accuracyColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء نص المساعدة
  Widget _buildHelpText() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.blue[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'سيتم استخدام الموقع الجغرافي لتحديد أقرب مهندس زراعي وحساب تكلفة النقل',
              style: TextStyle(
                color: Colors.blue[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر تحميل الموقع
  Widget _buildLocationLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'جاري تحديد الموقع الجغرافي...',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
