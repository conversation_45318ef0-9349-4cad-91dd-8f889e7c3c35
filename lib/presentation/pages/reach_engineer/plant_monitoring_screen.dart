import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
// تم حذف استيراد advisor_state غير المستخدم
import 'package:agriculture/data/datasources/local/shared_prefs.dart';

class PlantMonitoringScreen extends StatefulWidget {
  const PlantMonitoringScreen({super.key});

  @override
  State<PlantMonitoringScreen> createState() => _PlantMonitoringScreenState();
}

class _PlantMonitoringScreenState extends State<PlantMonitoringScreen> {
  final ImagePicker _picker = ImagePicker();
  final List<File> _selectedImages = [];
  final TextEditingController _plantTypeController = TextEditingController();
  final TextEditingController _symptomsController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();

  String _selectedPlantCategory = 'خضروات';
  String _selectedUrgency = 'عادي';

  final List<String> _plantCategories = [
    'خضروات',
    'فواكه',
    'نباتات زينة',
    'أشجار',
    'نباتات عطرية',
    'محاصيل حقلية',
  ];

  final List<String> _urgencyLevels = [
    'عادي',
    'متوسط',
    'عاجل',
    'طارئ',
  ];

  @override
  void dispose() {
    _plantTypeController.dispose();
    _symptomsController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: const Text('مراقبة النبات'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات الخدمة
            _buildServiceInfo(),
            const SizedBox(height: 20),

            // نموذج مراقبة النبات
            _buildMonitoringForm(),

            const SizedBox(height: 24),

            // زر الإرسال
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الخدمة
  Widget _buildServiceInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AssetsColors.dufaultGreencolor.withOpacity(0.1),
              AssetsColors.dufaultGreencolor.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.eco,
              size: 48,
              color: AssetsColors.dufaultGreencolor,
            ),
            const SizedBox(height: 12),
            Text(
              'مراقبة وتشخيص النباتات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'ارفع صور نباتاتك واحصل على تشخيص دقيق للمشاكل والأمراض مع نصائح العلاج من خبرائنا',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نموذج مراقبة النبات
  Widget _buildMonitoringForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل النبات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
            ),
            const SizedBox(height: 20),

            // صور النبات
            _buildImageSection(),
            const SizedBox(height: 20),

            // فئة النبات
            _buildPlantCategoryDropdown(),
            const SizedBox(height: 16),

            // نوع النبات
            _buildTextField(
              controller: _plantTypeController,
              label: 'نوع النبات',
              icon: Icons.local_florist,
              hint: 'مثال: طماطم، خيار، ورد...',
            ),
            const SizedBox(height: 16),

            // الأعراض المرصودة
            _buildTextField(
              controller: _symptomsController,
              label: 'الأعراض المرصودة',
              icon: Icons.warning,
              hint: 'اكتب الأعراض التي تلاحظها على النبات...',
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // الموقع/المنطقة
            _buildTextField(
              controller: _locationController,
              label: 'الموقع أو المنطقة',
              icon: Icons.location_on,
              hint: 'مثال: غزة، مزرعة منزلية...',
            ),
            const SizedBox(height: 16),

            // مستوى الأولوية
            _buildUrgencyDropdown(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الصور
  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.camera_alt, color: AssetsColors.dufaultGreencolor),
            const SizedBox(width: 8),
            const Text(
              'صور النبات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _showImageSourceDialog,
              icon: Icon(Icons.add, color: AssetsColors.dufaultGreencolor),
              label: Text(
                'إضافة صورة',
                style: TextStyle(color: AssetsColors.dufaultGreencolor),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        if (_selectedImages.isEmpty)
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_photo_alternate, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط لإضافة صور النبات',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          )
        else
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length + 1,
              itemBuilder: (context, index) {
                if (index == _selectedImages.length) {
                  return GestureDetector(
                    onTap: _showImageSourceDialog,
                    child: Container(
                      width: 120,
                      margin: const EdgeInsets.only(left: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add, size: 32, color: Colors.grey[400]),
                          const SizedBox(height: 4),
                          Text(
                            'إضافة',
                            style: TextStyle(color: Colors.grey[600], fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(left: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImages[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  /// بناء حقل نص
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AssetsColors.dufaultGreencolor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AssetsColors.dufaultGreencolor),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة فئة النبات
  Widget _buildPlantCategoryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'فئة النبات',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedPlantCategory,
          decoration: InputDecoration(
            prefixIcon: Icon(Icons.category, color: AssetsColors.dufaultGreencolor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AssetsColors.dufaultGreencolor),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: _plantCategories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPlantCategory = value!;
            });
          },
        ),
      ],
    );
  }

  /// بناء قائمة مستوى الأولوية
  Widget _buildUrgencyDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مستوى الأولوية',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedUrgency,
          decoration: InputDecoration(
            prefixIcon: Icon(Icons.priority_high, color: AssetsColors.dufaultGreencolor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AssetsColors.dufaultGreencolor),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: _urgencyLevels.map((urgency) {
            Color urgencyColor;
            switch (urgency) {
              case 'عادي':
                urgencyColor = Colors.green;
                break;
              case 'متوسط':
                urgencyColor = Colors.orange;
                break;
              case 'عاجل':
                urgencyColor = Colors.red;
                break;
              case 'طارئ':
                urgencyColor = Colors.red[900]!;
                break;
              default:
                urgencyColor = Colors.green;
            }

            return DropdownMenuItem(
              value: urgency,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: urgencyColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(urgency),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedUrgency = value!;
            });
          },
        ),
      ],
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return ElevatedButton(
      onPressed: _canSubmit() ? _submitMonitoringRequest : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: AssetsColors.dufaultGreencolor,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
      ),
      child: const Text(
        'إرسال طلب المراقبة',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// التحقق من إمكانية الإرسال
  bool _canSubmit() {
    return _selectedImages.isNotEmpty &&
        _plantTypeController.text.isNotEmpty &&
        _symptomsController.text.isNotEmpty;
  }

  /// عرض حوار اختيار مصدر الصورة
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'اختر مصدر الصورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.dufaultGreencolor,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.camera);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AssetsColors.dufaultGreencolor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AssetsColors.dufaultGreencolor.withOpacity(0.3)),
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.camera_alt, size: 48, color: AssetsColors.dufaultGreencolor),
                            const SizedBox(height: 8),
                            Text(
                              'الكاميرا',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.dufaultGreencolor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.gallery);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AssetsColors.dufaultGreencolor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AssetsColors.dufaultGreencolor.withOpacity(0.3)),
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.photo_library, size: 48, color: AssetsColors.dufaultGreencolor),
                            const SizedBox(height: 8),
                            Text(
                              'المعرض',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.dufaultGreencolor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// اختيار صورة
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الصورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إرسال طلب مراقبة النبات (إرسال فعلي لقاعدة البيانات)
  Future<void> _submitMonitoringRequest() async {
    if (!_isFormValid()) {
      _showErrorDialog('يرجى إكمال جميع البيانات المطلوبة');
      return;
    }

    // عرض مؤشر التحميل
    _showLoadingDialog();

    try {
      // جلب بيانات المستخدم
      final userId = SharedPrefs.getString('uid') ?? 'guest_user';
      final userName = SharedPrefs.getString('name') ?? 'مزارع';

      print('🌱 === بدء إرسال طلب مراقبة النبات ===');
      print('👤 المستخدم: $userName ($userId)');
      print('🌿 نوع النبات: ${_plantTypeController.text}');
      print('📍 فئة النبات: $_selectedPlantCategory');
      print('📍 الموقع: ${_locationController.text}');
      print('⚠️ مستوى الأولوية: $_selectedUrgency');
      print('📝 الأعراض: ${_symptomsController.text}');
      print('📸 عدد الصور: ${_selectedImages.length}');

      // إنشاء وصف شامل للمراقبة
      final monitoringDescription = '''
نوع النبات: ${_plantTypeController.text}
فئة النبات: $_selectedPlantCategory
الموقع: ${_locationController.text}
مستوى الأولوية: $_selectedUrgency

الأعراض والملاحظات:
${_symptomsController.text}
      '''.trim();

      // إرسال كاستشارة مع نوع خاص للمراقبة
      await context.read<AdvisorCubit>().createConsultation(
        userId: userId,
        userName: userName,
        advisorId: 'general_advisor', // ✅ إصلاح المعرف
        cropType: '$_selectedPlantCategory - ${_plantTypeController.text}',
        problemDescription: monitoringDescription,
        area: '1000', // مساحة افتراضية
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
      );

    } catch (e) {
      Navigator.pop(context); // إغلاق مؤشر التحميل
      _showErrorDialog('حدث خطأ أثناء إرسال طلب المراقبة: $e');
    }
  }

  /// تحقق من صحة النموذج
  bool _isFormValid() {
    return _plantTypeController.text.trim().isNotEmpty &&
        _locationController.text.trim().isNotEmpty &&
        _symptomsController.text.trim().length >= 20;
  }

  /// عرض مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مؤشر هرمي للتحميل
              Container(
                width: 60,
                height: 60,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // الهرم الخارجي
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AssetsColors.dufaultGreencolor.withOpacity(0.3),
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    // الهرم الداخلي (متحرك)
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: AssetsColors.dufaultGreencolor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'جاري إرسال طلب المراقبة...',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AssetsColors.dufaultGreencolor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'يرجى الانتظار...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessDialog(String monitoringId) {
    Navigator.pop(context); // إغلاق مؤشر التحميل

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: AssetsColors.dufaultGreencolor,
                  size: 48,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'تم إرسال طلب المراقبة بنجاح! ✓',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.dufaultGreencolor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'سيتم مراجعة طلبك من قبل المختصين وإرسال التوصيات قريباً',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // زر عرض الطلبات
            TextButton(
              child: const Text('عرض طلباتي'),
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار
                Navigator.pushReplacementNamed(context, '/myRequests');
              },
            ),
            // زر موافق
            TextButton(
              child: const Text('موافق'),
              onPressed: () {
                Navigator.pop(context); // إغلاق الحوار

                // إعادة تعيين النموذج
                setState(() {
                  _plantTypeController.clear();
                  _symptomsController.clear();
                  _locationController.clear();
                  _selectedPlantCategory = 'خضروات';
                  _selectedUrgency = 'عادي';
                  _selectedImages.clear();
                });
              },
            ),
          ],
        );
      },
    );
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text('خطأ'),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }
}
