import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
// تم حذف الاستيراد غير المستخدم
import 'package:agriculture/presentation/bloc/agricultural_questions/agricultural_questions_cubit.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';

/// صفحة قائمة الأسئلة الزراعية
///
/// تحتوي على:
/// - تبويبات للأسئلة (الأحدث، الأكثر مشاهدة، المميزة)
/// - عرض الاستشارات الحقيقية والبيانات الاحتياطية
/// - إمكانية التنقل إلى لوحة تحكم المرشد
class AgriculturalQuestionsList extends StatefulWidget {
  const AgriculturalQuestionsList({Key? key}) : super(key: key);

  @override
  _AgriculturalQuestionsListState createState() => _AgriculturalQuestionsListState();
}

/// حالة صفحة قائمة الأسئلة الزراعية
class _AgriculturalQuestionsListState extends State<AgriculturalQuestionsList> with SingleTickerProviderStateMixin {
  late AgriculturalQuestionsCubit _questionsCubit;
  late AdvisorCubit _advisorCubit;

  @override
  void initState() {
    super.initState();
    _advisorCubit = context.read<AdvisorCubit>();
    _questionsCubit = AgriculturalQuestionsCubit(
      vsync: this,
      advisorCubit: _advisorCubit,
    )..initialize();
  }

  @override
  void dispose() {
    _questionsCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _questionsCubit,
      child: BlocBuilder<AgriculturalQuestionsCubit, AgriculturalQuestionsState>(
        builder: (context, state) {
          if (state is AgriculturalQuestionsLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (state is AgriculturalQuestionsError) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('أسئلة المرشد الزراعي'),
                backgroundColor: AssetsColors.dufaultGreencolor,
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      state.message,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _questionsCubit.refresh(),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is AgriculturalQuestionsLoaded) {
            return _AgriculturalQuestionsContent(state: state);
          }

          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        },
      ),
    );
  }
}

/// محتوى صفحة الأسئلة الزراعية
class _AgriculturalQuestionsContent extends StatelessWidget {
  final AgriculturalQuestionsLoaded state;

  const _AgriculturalQuestionsContent({required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AgriculturalQuestionsCubit>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('أسئلة المرشد الزراعي'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        actions: [
          // زر لوحة تحكم المرشد
          IconButton(
            onPressed: () {
              // تم حذف لوحة تحكم المرشد
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('لوحة تحكم المرشد غير متاحة حالياً'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            icon: const Icon(Icons.dashboard),
            tooltip: 'لوحة تحكم المرشد',
          ),
          // زر التحديث
          IconButton(
            onPressed: () => cubit.refresh(),
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
          ),
        ],
        bottom: TabBar(
          controller: state.tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'الأحدث'),
            Tab(text: 'الأكثر مشاهدة'),
            Tab(text: 'المميزة'),
          ],
        ),
      ),
      body: state.isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.dufaultGreencolor),
              ),
            )
          : TabBarView(
              controller: state.tabController,
              children: [
                // الأسئلة الأحدث
                _buildConsultationsList(context, state.consultations.isNotEmpty ? state.consultations : []),

                // الأسئلة الأكثر مشاهدة (مرتبة حسب التاريخ)
                _buildConsultationsList(context, state.consultations.isNotEmpty
                    ? (state.consultations..sort((a, b) => DateTime.parse(b.createdAt).compareTo(DateTime.parse(a.createdAt))))
                    : []),

                // الأسئلة المجابة
                _buildConsultationsList(context, state.consultations.where((c) => c.status == ConsultationStatus.answered).toList()),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pop(context);
        },
        backgroundColor: AssetsColors.dufaultGreencolor,
        child: const Icon(Icons.add_comment),
        tooltip: 'إضافة سؤال جديد',
      ),
    );
  }

  /// بناء قائمة الاستشارات
  Widget _buildConsultationsList(BuildContext context, List<ConsultationModel> consultations) {
    // طباعة معلومات debug
    debugPrint('🔍 عدد الاستشارات المحملة: ${consultations.length}');
    for (int i = 0; i < consultations.length; i++) {
      final consultation = consultations[i];
      debugPrint('📋 استشارة $i: ${consultation.userName} - صور: ${consultation.images?.length ?? 0}');
      if (consultation.images != null && consultation.images!.isNotEmpty) {
        for (int j = 0; j < consultation.images!.length; j++) {
          debugPrint('🖼️ صورة $j: ${consultation.images![j]}');
        }
      }
    }

    if (consultations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.help_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد استشارات حالياً',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم عرض الاستشارات هنا عند إرسالها',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),

          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: consultations.length,
      itemBuilder: (context, index) {
        final consultation = consultations[index];
        debugPrint('🎨 بناء بطاقة للاستشارة: ${consultation.userName} - صور: ${consultation.images?.length ?? 0}');
        return _buildConsultationCard(context, consultation);
      },
    );
  }



  /// بناء قائمة الأسئلة الاحتياطية
  Widget _buildQuestionsList(BuildContext context, List<Map<String, dynamic>> questions) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: questions.length,
      itemBuilder: (context, index) {
        final question = questions[index];
        return _buildQuestionCard(context, question);
      },
    );
  }

  /// بناء بطاقة الاستشارة
  Widget _buildConsultationCard(BuildContext context, ConsultationModel consultation) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showConsultationDetails(context, consultation),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // معلومات المستخدم والتاريخ
          ListTile(
            leading: CircleAvatar(
              backgroundColor: AssetsColors.dufaultGreencolor,
              child: Text(
                consultation.userName.isNotEmpty ? consultation.userName[0] : 'م',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            title: Text(consultation.userName, style: const TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Text(_formatDate(consultation.createdAt)),
            trailing: _buildConsultationStatusChip(consultation.status),
          ),

          // محتوى الاستشارة
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نوع المحصول
                Row(
                  children: [
                    Icon(Icons.eco, color: AssetsColors.dufaultGreencolor, size: 16),
                    const SizedBox(width: 8),
                    Text('نوع المحصول: ${consultation.cropType}',
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                  ],
                ),
                const SizedBox(height: 8),

                // وصف المشكلة
                Text(
                  consultation.problemDescription,
                  style: const TextStyle(fontSize: 14, color: Colors.black87),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // معلومات إضافية
                Row(
                  children: [
                    const Icon(Icons.location_on, color: Colors.grey, size: 16),
                    const SizedBox(width: 4),
                    Text('المساحة: ${consultation.area}', style: const TextStyle(color: Colors.grey)),
                  ],
                ),

                // عرض الصور المرفقة إذا كانت موجودة 🔥
                if (consultation.images != null && consultation.images!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.image,
                              size: 18,
                              color: Colors.purple,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'الصور المرفقة (${consultation.images!.length})',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.purple,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.purple,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '${consultation.images!.length}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          height: 100,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: consultation.images!.length,
                            itemBuilder: (context, index) {
                              final imageUrl = consultation.images![index];
                              debugPrint('🖼️ عرض صورة $index: $imageUrl');

                              return Container(
                                width: 100,
                                height: 100,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: GestureDetector(
                                    onTap: () {
                                      debugPrint('👆 نقر على الصورة: $imageUrl');
                                      _showImageFullScreen(context, imageUrl);
                                    },
                                    child: Stack(
                                      children: [
                                        // الصورة مع معالجة التحميل والأخطاء
                                        Container(
                                          width: 100,
                                          height: 100,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(8),
                                            color: Colors.grey[200],
                                          ),
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: Image.network(
                                              imageUrl,
                                              width: 100,
                                              height: 100,
                                              fit: BoxFit.cover,
                                              loadingBuilder: (context, child, loadingProgress) {
                                                if (loadingProgress == null) return child;
                                                return Container(
                                                  width: 100,
                                                  height: 100,
                                                  color: Colors.grey[200],
                                                  child: const Center(
                                                    child: CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                    ),
                                                  ),
                                                );
                                              },
                                              errorBuilder: (context, error, stackTrace) {
                                                debugPrint('❌ خطأ في تحميل الصورة: $imageUrl - $error');
                                                return Container(
                                                  width: 100,
                                                  height: 100,
                                                  color: Colors.grey[300],
                                                  child: const Column(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Icon(Icons.error, color: Colors.red, size: 24),
                                                      Text('خطأ', style: TextStyle(fontSize: 10)),
                                                    ],
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                        // مؤشر للنقر
                                        Positioned(
                                          top: 4,
                                          right: 4,
                                          child: Container(
                                            padding: const EdgeInsets.all(2),
                                            decoration: const BoxDecoration(
                                              color: Colors.black54,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.zoom_in,
                                              color: Colors.white,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // رسالة عدم وجود صور
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.image_not_supported,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'لا توجد صور مرفقة',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 12),

                // الإحصائيات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(context, Icons.access_time, _formatDate(consultation.createdAt), 'تاريخ الإرسال'),
                    if (consultation.advisorResponse != null)
                      _buildStatItem(context, Icons.check_circle, 'تمت الإجابة', 'الحالة'),
                    if (consultation.images != null && consultation.images!.isNotEmpty)
                      _buildStatItem(context, Icons.image, '${consultation.images!.length}', 'صور'),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),
        ],
      ),
      ),
    );
  }

  /// بناء بطاقة السؤال الاحتياطي
  Widget _buildQuestionCard(BuildContext context, Map<String, dynamic> question) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // معلومات المستخدم والتاريخ
          ListTile(
            leading: CircleAvatar(
              backgroundColor: AssetsColors.dufaultGreencolor,
              child: Text(
                question['user'].isNotEmpty ? question['user'][0] : 'م',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            title: Text(question['user'], style: const TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Text(question['date']),
            trailing: _buildStatusChip(question['status']),
          ),

          // صورة المشكلة
          Container(
            height: 180,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              image: DecorationImage(
                image: AssetImage('assets/images/placeholder.jpg'),
                fit: BoxFit.cover,
                onError: (_, __) {},
              ),
            ),
            child: const Center(
              child: Icon(Icons.image, size: 50, color: Colors.grey),
            ),
          ),

          // تفاصيل المشكلة
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نوع المحصول والمساحة
                Row(
                  children: [
                    Chip(
                      label: Text(question['crop']),
                      backgroundColor: Colors.green[100],
                      labelStyle: const TextStyle(color: Colors.green),
                    ),
                    const SizedBox(width: 8),
                    Text('المساحة: ${question['area']} م²'),
                  ],
                ),

                const SizedBox(height: 8),

                // وصف المشكلة
                Text(
                  question['problem'],
                  style: const TextStyle(fontSize: 16),
                ),

                const SizedBox(height: 12),

                // الإحصائيات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(context, Icons.remove_red_eye, '${question['views']}', 'مشاهدة'),
                    _buildStatItem(context, Icons.question_answer, '${question['answers']}', 'إجابة'),
                    _buildRatingBar(context, question['rating']),
                  ],
                ),
              ],
            ),
          ),

          // أزرار التفاعل
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.visibility, color: Colors.blue),
                  label: const Text('عرض التفاصيل', style: TextStyle(color: Colors.blue)),
                ),
                TextButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.thumb_up_alt_outlined, color: Colors.green),
                  label: const Text('مفيد', style: TextStyle(color: Colors.green)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return 'منذ ${difference.inDays} يوم';
      } else if (difference.inHours > 0) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inMinutes > 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else {
        return 'الآن';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// بناء شارة حالة الاستشارة
  Widget _buildConsultationStatusChip(ConsultationStatus status) {
    Color color;
    IconData icon;
    String text;

    switch (status) {
      case ConsultationStatus.pending:
        color = Colors.orange;
        icon = Icons.hourglass_empty;
        text = 'في الانتظار';
        break;
      case ConsultationStatus.inProgress:
        color = Colors.blue;
        icon = Icons.sync;
        text = 'جاري المعالجة';
        break;
      case ConsultationStatus.answered:
        color = Colors.green;
        icon = Icons.check_circle;
        text = 'تمت الإجابة';
        break;
      case ConsultationStatus.closed:
        color = Colors.grey;
        icon = Icons.lock;
        text = 'مغلقة';
        break;
      case ConsultationStatus.cancelled:
        color = Colors.red;
        icon = Icons.cancel;
        text = 'ملغاة';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    IconData icon;

    if (status == 'تمت الإجابة') {
      color = Colors.green;
      icon = Icons.check_circle;
    } else {
      color = Colors.orange;
      icon = Icons.hourglass_empty;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(status, style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(BuildContext context, IconData icon, String value, String label) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
      ],
    );
  }

  /// بناء شريط التقييم
  Widget _buildRatingBar(BuildContext context, double rating) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.star, size: 16, color: rating > 0 ? Colors.amber : Colors.grey),
            Icon(Icons.star, size: 16, color: rating >= 1 ? Colors.amber : Colors.grey),
            Icon(Icons.star, size: 16, color: rating >= 2 ? Colors.amber : Colors.grey),
            Icon(Icons.star, size: 16, color: rating >= 3 ? Colors.amber : Colors.grey),
            Icon(Icons.star, size: 16, color: rating >= 4 ? Colors.amber : Colors.grey),
          ],
        ),
        Text(rating > 0 ? rating.toString() : 'لا تقييم',
             style: const TextStyle(color: Colors.grey, fontSize: 12)),
      ],
    );
  }

  /// عرض تفاصيل الاستشارة الكاملة
  void _showConsultationDetails(BuildContext context, ConsultationModel consultation) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              // صورة المزارع
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AssetsColors.dufaultGreencolor,
                      AssetsColors.dufaultGreencolor.withValues(alpha: 0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    consultation.userName.isNotEmpty
                        ? consultation.userName[0].toUpperCase()
                        : 'م',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الاستشارة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.dufaultGreencolor,
                      ),
                    ),
                    Text(
                      consultation.userName,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات أساسية
                  _buildDetailRow(Icons.agriculture, 'نوع المحصول', consultation.cropType),
                  const SizedBox(height: 8),
                  _buildDetailRow(Icons.square_foot, 'المساحة', consultation.area),
                  const SizedBox(height: 8),
                  _buildDetailRow(Icons.access_time, 'تاريخ الطلب', _formatDate(consultation.createdAt)),
                  const SizedBox(height: 16),

                  // وصف المشكلة
                  const Text(
                    'وصف المشكلة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      consultation.problemDescription,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),

                  // الصور المرفقة
                  if (consultation.images != null && consultation.images!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(Icons.image, color: Colors.purple),
                        const SizedBox(width: 8),
                        Text(
                          'الصور المرفقة (${consultation.images!.length})',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                        childAspectRatio: 1,
                      ),
                      itemCount: consultation.images!.length,
                      itemBuilder: (context, index) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: GestureDetector(
                            onTap: () => _showImageFullScreen(context, consultation.images![index]),
                            child: CachedNetImage(
                              imageUrl: consultation.images![index],
                              fit: BoxFit.cover,
                              borderRadius: 8,
                              enableFullScreen: false,
                              heroTag: 'detail_image_${consultation.id}_$index',
                            ),
                          ),
                        );
                      },
                    ),
                  ],

                  // رد المرشد إذا كان موجود
                  if (consultation.advisorResponse != null) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.reply, color: AssetsColors.dufaultGreencolor),
                        const SizedBox(width: 8),
                        const Text(
                          'رد المرشد:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        consultation.advisorResponse!,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              child: const Text('رد على الاستشارة'),
              onPressed: () {
                Navigator.pop(context);
                // يمكن إضافة منطق الرد هنا
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة الرد على الاستشارة قريباً'),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
            ),
            ElevatedButton(
              child: const Text('إغلاق'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: AssetsColors.dufaultGreencolor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// عرض الصورة بملء الشاشة
  void _showImageFullScreen(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: Stack(
            children: [
              // الصورة بملء الشاشة
              Center(
                child: InteractiveViewer(
                  panEnabled: true,
                  boundaryMargin: const EdgeInsets.all(20),
                  minScale: 0.5,
                  maxScale: 4.0,
                  child: CachedNetImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.contain,
                    width: double.infinity,
                    height: double.infinity,
                    borderRadius: 0,
                    enableFullScreen: false,
                    heroTag: 'fullscreen_$imageUrl',
                  ),
                ),
              ),
              // زر الإغلاق
              Positioned(
                top: 40,
                right: 20,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ),
              // معلومات الصورة
              Positioned(
                bottom: 40,
                left: 20,
                right: 20,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'اضغط مرتين للتكبير • اسحب للتحريك • قرص للتكبير/التصغير',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
