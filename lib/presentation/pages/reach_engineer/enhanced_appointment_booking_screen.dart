import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/appointments/appointments_cubit.dart';
import 'package:agriculture/domain/entities/appointment.dart';
import 'package:agriculture/core/shared/services/user/user_data_service.dart';
import 'package:agriculture/core/shared/services/ui/dialog_service.dart';
import 'package:agriculture/core/constants/unified_submission_constants.dart';

/// صفحة حجز موعد محسنة مع فحص التوفر
/// 
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #15: التركيز على مجلد widgets منفصل
/// وفق المعيار #12: تعليقات عربية شاملة
class EnhancedAppointmentBookingScreen extends StatelessWidget {
  /// بيانات المرشد
  final Map<String, dynamic>? advisor;

  /// منشئ صفحة حجز الموعد المحسنة
  /// 
  /// المعلمات:
  /// - [advisor]: بيانات المرشد
  const EnhancedAppointmentBookingScreen({
    super.key,
    this.advisor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حجز موعد'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'مساعدة',
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => AppointmentsCubit(
          appointmentsService: context.read(),
          notificationService: context.read(),
        ),
        child: BlocBuilder<AppointmentsCubit, AppointmentsState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المرشد
                  _buildAdvisorInfoCard(),
                  
                  const SizedBox(height: 20),
                  
                  // نموذج حجز الموعد
                  _buildBookingForm(context, state),
                  
                  const SizedBox(height: 20),
                  
                  // الأوقات المتاحة
                  if (state is AppointmentsLoaded)
                    _buildAvailableSlots(context, state),
                  
                  const SizedBox(height: 100), // مساحة للأزرار العائمة
                ],
              ),
            );
          },
        ),
      ),
      bottomNavigationBar: _buildActionButtons(context),
    );
  }

  /// بناء بطاقة معلومات المرشد
  Widget _buildAdvisorInfoCard() {
    final advisorName = advisor?['name'] ?? 'مرشد زراعي';
    final advisorSpecialty = advisor?['specialty'] ?? 'استشارات عامة';
    final advisorRating = advisor?['rating'] ?? 4.5;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundImage: advisor?['image'] != null
                  ? NetworkImage(advisor!['image'])
                  : null,
              child: advisor?['image'] == null
                  ? const Icon(Icons.person, size: 30)
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    advisorName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    advisorSpecialty,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        advisorRating.toString(),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'متاح',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نموذج حجز الموعد
  Widget _buildBookingForm(BuildContext context, AppointmentsState state) {
    final consultationTypeController = TextEditingController();
    final problemDescriptionController = TextEditingController();
    DateTime? selectedDate;
    TimeOfDay? selectedTime;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الموعد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // نوع الاستشارة
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'نوع الاستشارة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.category),
              ),
              items: ConsultationType.values.map((type) {
                return DropdownMenuItem(
                  value: type.value,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (value) {
                consultationTypeController.text = value ?? '';
              },
            ),
            
            const SizedBox(height: 16),
            
            // اختيار التاريخ
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'تاريخ الموعد',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.calendar_today),
                ),
                child: Text(
                  selectedDate != null
                      ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                      : 'اختر التاريخ',
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // وصف المشكلة
            TextFormField(
              controller: problemDescriptionController,
              maxLines: 4,
              decoration: InputDecoration(
                labelText: 'وصف المشكلة',
                hintText: 'اكتب وصفاً تفصيلياً للمشكلة التي تواجهها...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.description),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // خيارات إضافية
            Row(
              children: [
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('موعد عن بُعد'),
                    value: false, // TODO: ربط بالحالة
                    onChanged: (value) {
                      // TODO: تنفيذ التغيير
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('موعد عاجل'),
                    value: false, // TODO: ربط بالحالة
                    onChanged: (value) {
                      // TODO: تنفيذ التغيير
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الأوقات المتاحة
  Widget _buildAvailableSlots(BuildContext context, AppointmentsLoaded state) {
    final availableSlots = state.availableSlots[state.selectedDate.toIso8601String().split('T')[0]] ?? [];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأوقات المتاحة - ${state.selectedDate.day}/${state.selectedDate.month}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            if (availableSlots.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info, color: Colors.orange),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'لا توجد أوقات متاحة في هذا التاريخ، يرجى اختيار تاريخ آخر',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  ],
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: availableSlots.map((slot) {
                  return ChoiceChip(
                    label: Text('${slot.hour.toString().padLeft(2, '0')}:${slot.minute.toString().padLeft(2, '0')}'),
                    selected: false, // TODO: ربط بالحالة
                    onSelected: (selected) {
                      if (selected) {
                        // TODO: تحديد الوقت المختار
                      }
                    },
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: () => _bookAppointment(context),
              icon: const Icon(Icons.event),
              label: const Text('حجز الموعد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _checkAvailability(context),
              icon: const Icon(Icons.search),
              label: const Text('فحص التوفر'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      // TODO: تحديث التاريخ المختار وتحميل الأوقات المتاحة
      final advisorId = advisor?['id'] ?? UnifiedSubmissionConstants.defaultAdvisorId;
      context.read<AppointmentsCubit>().loadAdvisorAppointments(
        advisorId: advisorId,
        date: picked,
      );
    }
  }

  /// عرض حوار المساعدة
  void _showHelpDialog(BuildContext context) {
    DialogService.showInfoDialog(
      context,
      title: 'مساعدة في حجز الموعد',
      message: 'نصائح لحجز موعد ناجح:\n'
          '• اختر التاريخ والوقت المناسب\n'
          '• اكتب وصفاً واضحاً للمشكلة\n'
          '• تأكد من توفر رقم هاتفك\n'
          '• ستصلك رسالة تأكيد عند الموافقة',
    );
  }

  /// فحص توفر الأوقات
  void _checkAvailability(BuildContext context) {
    // TODO: تنفيذ فحص التوفر
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('يرجى اختيار التاريخ أولاً')),
    );
  }

  /// حجز الموعد
  void _bookAppointment(BuildContext context) {
    // TODO: تنفيذ حجز الموعد
    final userData = UserDataService.getCurrentUserData();
    
    // التحقق من البيانات المطلوبة
    if (userData.userId == UnifiedSubmissionConstants.anonymousUserId) {
      DialogService.showErrorSnackBar(
        context,
        'يرجى تسجيل الدخول أولاً لحجز موعد',
      );
      return;
    }

    // TODO: جمع البيانات من النموذج وإرسال الطلب
    context.read<AppointmentsCubit>().bookAppointment(
      advisorId: advisor?['id'] ?? UnifiedSubmissionConstants.defaultAdvisorId,
      date: DateTime.now().add(const Duration(days: 1)), // TODO: من النموذج
      time: const TimeOfDay(hour: 9, minute: 0), // TODO: من النموذج
      consultationType: 'general', // TODO: من النموذج
      problemDescription: 'وصف المشكلة', // TODO: من النموذج
    );
  }
}
