import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/pages/my_requests/my_requests_screen.dart';
import 'package:agriculture/core/shared/services/user/user_data_service.dart';

/// شاشة نموذج الاستشارة الزراعية
class ConsultationFormScreen extends StatefulWidget {
  final String advisorId;
  final String advisorName;

  const ConsultationFormScreen({
    super.key,
    required this.advisorId,
    required this.advisorName,
  });

  @override
  State<ConsultationFormScreen> createState() => _ConsultationFormScreenState();
}

class _ConsultationFormScreenState extends State<ConsultationFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cropTypeController = TextEditingController();
  final _problemController = TextEditingController();
  final _areaController = TextEditingController();

  final List<File> _selectedImages = [];
  final ImagePicker _imagePicker = ImagePicker();

  bool _isSubmitting = false;
  bool _isFormValid = false;

  // رسائل الخطأ
  String? _cropTypeError;
  String? _problemError;
  String? _areaError;

  @override
  void initState() {
    super.initState();
    // إضافة listeners للحقول
    _cropTypeController.addListener(_validateForm);
    _problemController.addListener(_validateForm);
    _areaController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _cropTypeController.dispose();
    _problemController.dispose();
    _areaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: const Text(
          'استشارة زراعية',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        centerTitle: true,
      ),
      body: BlocListener<AdvisorCubit, AdvisorState>(
        listener: (context, state) {
          if (state is ConsultationCreated) {
            setState(() {
              _isSubmitting = false;
            });

            // إغلاق حوار المعالجة
            Navigator.of(context, rootNavigator: true).pop();

            // عرض رسالة نجاح محسنة
            _showSuccessDialog();

            // تنظيف النموذج
            _clearForm();

          } else if (state is AdvisorError) {
            setState(() {
              _isSubmitting = false;
            });

            // إغلاق حوار المعالجة
            Navigator.of(context, rootNavigator: true).pop();

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(child: Text(state.message)),
                  ],
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAdvisorInfo(),
                const SizedBox(height: 24),
                _buildFormFields(),
                const SizedBox(height: 24),
                _buildImageSection(),
                const SizedBox(height: 32),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات المرشد
  Widget _buildAdvisorInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AssetsColors.dufaultGreencolor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.person,
              color: AssetsColors.dufaultGreencolor,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'استشارة مع',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.advisorName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقول النموذج
  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('معلومات المحصول'),
        const SizedBox(height: 16),

        // نوع المحصول
        _buildTextField(
          controller: _cropTypeController,
          label: 'نوع المحصول',
          hint: 'مثال: طماطم، خيار، فلفل...',
          icon: Icons.eco,
          errorText: _cropTypeError,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال نوع المحصول';
            }
            if (value.trim().length < 2) {
              return 'يجب أن يكون نوع المحصول حرفين على الأقل';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // المساحة المزروعة
        _buildTextField(
          controller: _areaController,
          label: 'المساحة المزروعة',
          hint: 'مثال: 100 متر مربع، 1 دونم...',
          icon: Icons.square_foot,
          errorText: _areaError,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال مساحة الزراعة';
            }
            return null;
          },
        ),

        const SizedBox(height: 24),

        _buildSectionTitle('وصف المشكلة'),
        const SizedBox(height: 16),

        // وصف المشكلة
        _buildTextField(
          controller: _problemController,
          label: 'وصف المشكلة بالتفصيل',
          hint: 'اشرح المشكلة التي تواجهها مع المحصول...',
          icon: Icons.description,
          maxLines: 5,
          errorText: _problemError,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى وصف المشكلة أو الاستفسار';
            }
            if (value.trim().length < 10) {
              return 'يجب أن يكون الوصف 10 أحرف على الأقل';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AssetsColors.dufaultGreencolor,
      ),
    );
  }

  /// بناء حقل النص
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? errorText,
    String? Function(String?)? validator,
  }) {
    final hasError = errorText != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(
              icon,
              color: hasError ? Colors.red : AssetsColors.dufaultGreencolor
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey[300]!,
                width: hasError ? 2 : 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey[300]!,
                width: hasError ? 2 : 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : AssetsColors.dufaultGreencolor,
                width: 2
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: hasError ? Colors.red.withOpacity(0.05) : Colors.white,
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
        if (hasError) ...[
          const SizedBox(height: 6),
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  errorText,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء قسم الصور
  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('صور المشكلة (اختياري)'),
        const SizedBox(height: 8),
        Text(
          'يمكنك إضافة صور توضح المشكلة لمساعدة المرشد في التشخيص',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),

        // عرض الصور المحددة
        if (_selectedImages.isNotEmpty) ...[
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(_selectedImages[index]),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],

        // زر إضافة صور
        if (_selectedImages.length < 5)
          GestureDetector(
            onTap: _pickImages,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AssetsColors.dufaultGreencolor.withOpacity(0.3),
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.add_photo_alternate,
                    color: AssetsColors.dufaultGreencolor,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إضافة صور (${_selectedImages.length}/5)',
                    style: TextStyle(
                      color: AssetsColors.dufaultGreencolor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    final isEnabled = _isFormValid && !_isSubmitting;

    return Column(
      children: [
        // رسالة تنبيه إذا لم يكن النموذج صالحاً
        if (!_isFormValid && (_cropTypeController.text.isNotEmpty ||
                              _problemController.text.isNotEmpty ||
                              _areaController.text.isNotEmpty)) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'يرجى تعبئة جميع الحقول المطلوبة بشكل صحيح',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isEnabled ? _submitConsultation : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isEnabled
                  ? AssetsColors.dufaultGreencolor
                  : Colors.grey[400],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: isEnabled ? 2 : 0,
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        isEnabled ? Icons.send : Icons.lock,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isEnabled ? 'إرسال الاستشارة' : 'يرجى تعبئة الحقول',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1080,
        maxHeight: 1920,
        imageQuality: 80,
      );

      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الصور: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إرسال الاستشارة
  Future<void> _submitConsultation() async {
    // التحقق من صحة النموذج أولاً
    _validateForm();

    if (!_isFormValid) {
      // عرض رسالة خطأ مفصلة
      String errorMessage = 'يرجى تعبئة الحقول التالية بشكل صحيح:';
      List<String> missingFields = [];

      if (_cropTypeError != null) missingFields.add('نوع المحصول');
      if (_problemError != null) missingFields.add('وصف المشكلة');
      if (_areaError != null) missingFields.add('مساحة الزراعة');

      if (missingFields.isNotEmpty) {
        errorMessage += '\n• ${missingFields.join('\n• ')}';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
      return; // التوقف هنا ولا نكمل الإرسال
    }

    // جميع الحقول صحيحة - بدء عملية الإرسال
    setState(() {
      _isSubmitting = true;
    });

    try {
      // عرض رسالة تحضيرية مع مؤشر التحميل
      _showProcessingDialog();
      print('=== تم عرض حوار المعالجة ===');

      // تأخير بسيط لإظهار عملية المعالجة
      await Future.delayed(const Duration(milliseconds: 1500));

      // الحصول على معلومات المستخدم الحالي من SharedPrefs
      final userData = UserDataService.getCurrentUserData();
      final String currentUserId = userData.userId;
      final String currentUserName = userData.userName;

      // طباعة بيانات الإرسال للتشخيص
      print('=== بدء إرسال الاستشارة ===');
      print('معرف المرشد: ${widget.advisorId}');
      print('نوع المحصول: ${_cropTypeController.text.trim()}');
      print('وصف المشكلة: ${_problemController.text.trim()}');
      print('المساحة: ${_areaController.text.trim()}');

      // إرسال الاستشارة - BlocListener سيتولى معالجة النتيجة
      context.read<AdvisorCubit>().createConsultation(
        userId: currentUserId,
        userName: currentUserName,
        advisorId: widget.advisorId,
        cropType: _cropTypeController.text.trim(),
        problemDescription: _problemController.text.trim(),
        area: _areaController.text.trim(),
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
      );

      debugPrint('✅ تم استدعاء createConsultation - BlocListener سيتولى المعالجة');

    } catch (error) {
      // إغلاق حوار المعالجة في حالة الخطأ
      Navigator.of(context).pop();

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ حدث خطأ أثناء إرسال الاستشارة: $error'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      // إنهاء حالة الإرسال
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// عرض حوار المعالجة
  void _showProcessingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 16),
            const Text(
              'جاري معالجة الاستشارة...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يتم رفع الصور وحفظ البيانات',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار النجاح
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'تم إرسال الاستشارة بنجاح!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم الرد عليها خلال 24-48 ساعة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك متابعة حالة الاستشارة في قسم "طلباتي"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق الحوار
                      Navigator.of(context).pop(); // العودة للشاشة السابقة
                    },
                    child: const Text('حسناً'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق الحوار
                      Navigator.of(context).pop(); // العودة للشاشة السابقة
                      _navigateToMyRequests();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'عرض طلباتي',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// التنقل إلى شاشة طلباتي
  void _navigateToMyRequests() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MyRequestsScreen(),
      ),
    );
  }

  void _validateForm() {
    setState(() {
      if (_cropTypeController.text.trim().isEmpty) {
        _cropTypeError = 'يرجى إدخال نوع المحصول';
      } else if (_cropTypeController.text.trim().length < 2) {
        _cropTypeError = 'يجب أن يكون نوع المحصول حرفين على الأقل';
      } else {
        _cropTypeError = null;
      }

      if (_problemController.text.trim().isEmpty) {
        _problemError = 'يرجى وصف المشكلة أو الاستفسار';
      } else if (_problemController.text.trim().length < 10) {
        _problemError = 'يجب أن يكون الوصف 10 أحرف على الأقل';
      } else {
        _problemError = null;
      }

      if (_areaController.text.trim().isEmpty) {
        _areaError = 'يرجى إدخال مساحة الزراعة';
      } else {
        _areaError = null;
      }

      _isFormValid = _cropTypeError == null &&
                     _problemError == null &&
                     _areaError == null &&
                     _cropTypeController.text.trim().isNotEmpty &&
                     _problemController.text.trim().isNotEmpty &&
                     _areaController.text.trim().isNotEmpty;
    });
  }

  /// تنظيف النموذج بعد الإرسال الناجح
  void _clearForm() {
    setState(() {
      _cropTypeController.clear();
      _problemController.clear();
      _areaController.clear();
      _selectedImages.clear();
      _cropTypeError = null;
      _problemError = null;
      _areaError = null;
      _isFormValid = false;
    });
  }
}