import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/pages/my_requests/my_requests_screen.dart';
import 'package:agriculture/core/shared/services/unified_submission_service.dart';

/// شاشة طلب مراقبة النبات
class PlantMonitoringRequestScreen extends StatefulWidget {
  final String advisorId;
  final String advisorName;

  const PlantMonitoringRequestScreen({
    Key? key,
    required this.advisorId,
    required this.advisorName,
  }) : super(key: key);

  @override
  State<PlantMonitoringRequestScreen> createState() => _PlantMonitoringRequestScreenState();
}

class _PlantMonitoringRequestScreenState extends State<PlantMonitoringRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _plantTypeController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();

  final List<File> _selectedImages = [];
  final ImagePicker _imagePicker = ImagePicker();

  String _monitoringType = 'weekly'; // weekly, monthly, seasonal
  bool _isSubmitting = false;
  bool _isFormValid = false;

  // رسائل الخطأ
  String? _plantTypeError;
  String? _locationError;
  String? _notesError;

  @override
  void initState() {
    super.initState();
    // إضافة listeners للحقول
    _plantTypeController.addListener(_validateForm);
    _locationController.addListener(_validateForm);
    _notesController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _plantTypeController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: const Text(
          'طلب مراقبة النبات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        centerTitle: true,
      ),
      body: BlocListener<AdvisorCubit, AdvisorState>(
        listener: (context, state) {
          if (state is ConsultationCreated) {
            setState(() {
              _isSubmitting = false;
            });

            // إغلاق حوار المعالجة
            Navigator.of(context, rootNavigator: true).pop();

            // عرض رسالة نجاح محسنة
            _showSuccessDialog();

          } else if (state is AdvisorError) {
            setState(() {
              _isSubmitting = false;
            });

            // إغلاق حوار المعالجة
            Navigator.of(context, rootNavigator: true).pop();

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(child: Text(state.message)),
                  ],
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAdvisorInfo(),
                const SizedBox(height: 24),
                _buildFormFields(),
                const SizedBox(height: 24),
                _buildMonitoringTypeSection(),
                const SizedBox(height: 24),
                _buildImageSection(),
                const SizedBox(height: 32),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات المرشد
  Widget _buildAdvisorInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.visibility,
              color: Colors.blue,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'طلب مراقبة من',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.advisorName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقول النموذج
  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('معلومات النبات'),
        const SizedBox(height: 16),

        // نوع النبات
        _buildTextField(
          controller: _plantTypeController,
          label: 'نوع النبات/المحصول',
          hint: 'مثال: طماطم، خيار، ورود...',
          icon: Icons.local_florist,
          errorText: _plantTypeError,
        ),

        const SizedBox(height: 16),

        // الموقع
        _buildTextField(
          controller: _locationController,
          label: 'موقع النبات',
          hint: 'مثال: حديقة المنزل، مزرعة، بيت محمي...',
          icon: Icons.location_on,
          errorText: _locationError,
        ),

        const SizedBox(height: 24),

        _buildSectionTitle('ملاحظات إضافية'),
        const SizedBox(height: 16),

        // ملاحظات
        _buildTextField(
          controller: _notesController,
          label: 'ملاحظات أو طلبات خاصة',
          hint: 'أي معلومات إضافية تريد من المرشد مراقبتها...',
          icon: Icons.note,
          maxLines: 4,
          errorText: _notesError,
        ),
      ],
    );
  }

  /// بناء قسم نوع المراقبة
  Widget _buildMonitoringTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('نوع المراقبة'),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMonitoringOption(
                value: 'weekly',
                title: 'مراقبة أسبوعية',
                subtitle: 'تقرير كل أسبوع عن حالة النبات',
                icon: Icons.calendar_view_week,
              ),
              const Divider(),
              _buildMonitoringOption(
                value: 'monthly',
                title: 'مراقبة شهرية',
                subtitle: 'تقرير شهري مفصل عن نمو النبات',
                icon: Icons.calendar_month,
              ),
              const Divider(),
              _buildMonitoringOption(
                value: 'seasonal',
                title: 'مراقبة موسمية',
                subtitle: 'متابعة كاملة خلال الموسم الزراعي',
                icon: Icons.calendar_today,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خيار نوع المراقبة
  Widget _buildMonitoringOption({
    required String value,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return RadioListTile<String>(
      value: value,
      groupValue: _monitoringType,
      onChanged: (String? newValue) {
        setState(() {
          _monitoringType = newValue!;
        });
      },
      title: Row(
        children: [
          Icon(
            icon,
            color: _monitoringType == value
                ? AssetsColors.dufaultGreencolor
                : Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _monitoringType == value
                  ? AssetsColors.dufaultGreencolor
                  : Colors.black,
            ),
          ),
        ],
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      activeColor: AssetsColors.dufaultGreencolor,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AssetsColors.dufaultGreencolor,
      ),
    );
  }

  /// بناء حقل النص
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? errorText,
  }) {
    final hasError = errorText != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(
              icon,
              color: hasError ? Colors.red : AssetsColors.dufaultGreencolor
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey[300]!,
                width: hasError ? 2 : 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey[300]!,
                width: hasError ? 2 : 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: hasError ? Colors.red : AssetsColors.dufaultGreencolor,
                width: 2
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: hasError ? Colors.red.withValues(alpha: 0.05) : Colors.white,
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
        if (hasError) ...[
          const SizedBox(height: 6),
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  errorText,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء قسم الصور
  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('صور النبات الحالية'),
        const SizedBox(height: 8),
        Text(
          'أضف صور حديثة للنبات لمساعدة المرشد في المراقبة',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),

        // عرض الصور المحددة
        if (_selectedImages.isNotEmpty) ...[
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(_selectedImages[index]),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],

        // زر إضافة صور
        if (_selectedImages.length < 5)
          GestureDetector(
            onTap: _pickImages,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.3),
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.add_a_photo,
                    color: Colors.blue,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إضافة صور النبات (${_selectedImages.length}/5)',
                    style: const TextStyle(
                      color: Colors.blue,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    final isEnabled = _isFormValid && !_isSubmitting;

    return Column(
      children: [
        // رسالة تنبيه إذا لم يكن النموذج صالحاً
        if (!_isFormValid && (_plantTypeController.text.isNotEmpty ||
                              _locationController.text.isNotEmpty ||
                              _notesController.text.isNotEmpty)) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'يرجى تعبئة جميع الحقول المطلوبة بشكل صحيح',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isEnabled ? _submitMonitoringRequest : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isEnabled
                  ? Colors.blue
                  : Colors.grey[400],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: isEnabled ? 2 : 0,
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        isEnabled ? Icons.send : Icons.lock,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isEnabled ? 'إرسال طلب المراقبة' : 'يرجى تعبئة الحقول',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1080,
        maxHeight: 1920,
        imageQuality: 80,
      );

      if (images.isNotEmpty) {
        setState(() {
          for (final image in images) {
            if (_selectedImages.length < 5) {
              _selectedImages.add(File(image.path));
            }
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الصور: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إرسال طلب المراقبة
  Future<void> _submitMonitoringRequest() async {
    // التحقق من صحة النموذج مرة أخيرة
    _validateForm();

    if (!_isFormValid) {
      // عرض رسالة خطأ مفصلة
      String errorMessage = 'يرجى تعبئة الحقول التالية:';
      List<String> missingFields = [];

      if (_plantTypeError != null) missingFields.add('نوع النبات');
      if (_locationError != null) missingFields.add('موقع النبات');
      if (_notesError != null) missingFields.add('الملاحظات');

      if (missingFields.isNotEmpty) {
        errorMessage += '\n• ${missingFields.join('\n• ')}';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
      return; // التوقف هنا ولا نكمل الإرسال
    }

    // التأكد مرة أخيرة من أن الحقول ليست فارغة
    if (_plantTypeController.text.trim().isEmpty ||
        _locationController.text.trim().isEmpty ||
        _notesController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ خطأ: يرجى تعبئة جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return; // التوقف هنا
    }

    // بدء عملية الإرسال
    setState(() {
      _isSubmitting = true;
    });

    try {
      // عرض رسالة تحضيرية
      _showProcessingDialog();

      // تأخير بسيط لإظهار عملية المعالجة
      await Future.delayed(const Duration(milliseconds: 1800));

      // ملاحظة: الخدمة الموحدة تحصل على بيانات المستخدم تلقائياً

      // استخدام الخدمة الموحدة لإرسال طلب المراقبة
      final success = await UnifiedSubmissionService.submitPlantMonitoring(
        context: context,
        plantType: _plantTypeController.text.trim(),
        location: _locationController.text.trim(),
        monitoringType: _getMonitoringTypeText(),
        notes: _notesController.text.trim(),
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
      );

      // إذا نجح الإرسال، عرض رسالة إضافية (اختياري)
      if (success) {
        // الخدمة الموحدة تعرض رسالة النجاح تلقائياً
        debugPrint('✅ تم إرسال طلب مراقبة النبات بنجاح');

        // العودة للصفحة السابقة بعد النجاح
        if (mounted) {
          Navigator.of(context).pop();
        }
      }

    } catch (error) {
      // عرض رسالة خطأ بدون محاولة إغلاق حوار غير موجود

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ حدث خطأ أثناء إرسال طلب المراقبة: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      // إنهاء حالة الإرسال
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// عرض حوار المعالجة
  void _showProcessingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 16),
            const Text(
              'جاري معالجة طلب المراقبة...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يتم رفع صور النبات وحفظ البيانات',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار النجاح
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.visibility,
                color: Colors.blue,
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'تم إرسال طلب المراقبة بنجاح!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيبدأ المرشد بمراقبة نباتك قريباً',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'ستتلقى تقارير دورية حسب نوع المراقبة',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق الحوار
                      Navigator.of(context).pop(); // العودة للشاشة السابقة
                    },
                    child: const Text('حسناً'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق الحوار
                      Navigator.of(context).pop(); // العودة للشاشة السابقة
                      _navigateToMyRequests();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'عرض طلباتي',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على نص نوع المراقبة
  String _getMonitoringTypeText() {
    switch (_monitoringType) {
      case 'weekly':
        return 'مراقبة أسبوعية';
      case 'monthly':
        return 'مراقبة شهرية';
      case 'seasonal':
        return 'مراقبة موسمية';
      default:
        return 'مراقبة أسبوعية';
    }
  }

  /// التنقل إلى شاشة طلباتي
  void _navigateToMyRequests() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MyRequestsScreen(),
      ),
    );
  }

  /// التحقق من صحة النموذج
  void _validateForm() {
    setState(() {
      // تحقق من نوع النبات
      if (_plantTypeController.text.trim().isEmpty) {
        _plantTypeError = 'يرجى إدخال نوع النبات';
      } else if (_plantTypeController.text.trim().length < 2) {
        _plantTypeError = 'يجب أن يكون نوع النبات حرفين على الأقل';
      } else {
        _plantTypeError = null;
      }

      // تحقق من الموقع
      if (_locationController.text.trim().isEmpty) {
        _locationError = 'يرجى إدخال موقع الزراعة';
      } else {
        _locationError = null;
      }

      // تحقق من الملاحظات
      if (_notesController.text.trim().isEmpty) {
        _notesError = 'يرجى إضافة ملاحظات أو تفاصيل إضافية';
      } else if (_notesController.text.trim().length < 10) {
        _notesError = 'يجب أن تكون الملاحظات 10 أحرف على الأقل';
      } else {
        _notesError = null;
      }

      // تحديد صحة النموذج
      _isFormValid = _plantTypeError == null &&
                     _locationError == null &&
                     _notesError == null &&
                     _plantTypeController.text.trim().isNotEmpty &&
                     _locationController.text.trim().isNotEmpty &&
                     _notesController.text.trim().isNotEmpty;
    });
  }
}
