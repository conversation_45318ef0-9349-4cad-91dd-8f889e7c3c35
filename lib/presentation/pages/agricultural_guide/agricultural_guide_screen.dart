import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/pages/agricultural_guide/consultation_form_screen.dart';
import 'package:agriculture/presentation/pages/agricultural_guide/plant_monitoring_request_screen.dart';

/// شاشة المرشد الزراعي - الخطوات العملية اليومية
class AgriculturalGuideScreen extends StatefulWidget {
  const AgriculturalGuideScreen({super.key});

  @override
  State<AgriculturalGuideScreen> createState() => _AgriculturalGuideScreenState();
}

class _AgriculturalGuideScreenState extends State<AgriculturalGuideScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  // قائمة المراحل الزراعية الأساسية
  final List<Map<String, dynamic>> _farmingStages = [
    {
      'id': 'site_selection',
      'title': 'اختيار الموقع',
      'icon': Icons.location_on,
      'color': Colors.blue,
      'description': 'اختيار الموقع المناسب للزراعة',
      'steps': [
        'فحص نوع التربة ومدى خصوبتها',
        'التأكد من توفر مصدر مياه قريب',
        'فحص التعرض لأشعة الشمس (6-8 ساعات يومياً)',
        'التحقق من التصريف الجيد للمياه',
        'قياس درجة حموضة التربة (pH)',
        'فحص المنطقة من الآفات والأمراض',
      ],
      'hasTraining': true,
      'trainingTitle': 'دورة تحليل التربة المتقدمة',
    },
    {
      'id': 'field_preparation',
      'title': 'تجهيز الحقل',
      'icon': Icons.agriculture,
      'color': Colors.brown,
      'description': 'إعداد الأرض للزراعة',
      'steps': [
        'إزالة الأعشاب والحجارة من الأرض',
        'حراثة التربة على عمق 20-30 سم',
        'تسوية سطح التربة',
        'إضافة السماد العضوي',
        'تقسيم الأرض إلى أحواض أو خطوط',
        'إنشاء نظام الري المناسب',
      ],
      'hasTraining': true,
      'trainingTitle': 'تقنيات تحضير التربة الحديثة',
    },
    {
      'id': 'planting',
      'title': 'الزراعة',
      'icon': Icons.eco,
      'color': Colors.green,
      'description': 'زراعة البذور أو الشتلات',
      'steps': [
        'اختيار البذور أو الشتلات عالية الجودة',
        'تحديد المسافات المناسبة بين النباتات',
        'حفر الحفر بالعمق المناسب',
        'وضع البذور أو الشتلات في مكانها',
        'تغطية البذور بطبقة رقيقة من التربة',
        'الري الخفيف بعد الزراعة مباشرة',
      ],
      'hasTraining': true,
      'trainingTitle': 'اختيار وزراعة المحاصيل المناسبة',
    },
    {
      'id': 'irrigation',
      'title': 'الري',
      'icon': Icons.water_drop,
      'color': Colors.cyan,
      'description': 'إدارة المياه والري',
      'steps': [
        'تحديد احتياجات النبات من المياه',
        'اختيار أوقات الري المناسبة (صباحاً أو مساءً)',
        'فحص رطوبة التربة قبل الري',
        'استخدام كمية المياه المناسبة',
        'تجنب الري المفرط أو النقص',
        'صيانة نظام الري بانتظام',
      ],
      'hasTraining': true,
      'trainingTitle': 'أنظمة الري الحديثة والذكية',
    },
    {
      'id': 'fertilization',
      'title': 'التسميد الكيميائي',
      'icon': Icons.science,
      'color': Colors.orange,
      'description': 'إضافة الأسمدة الكيميائية',
      'steps': [
        'تحليل التربة لمعرفة العناصر المطلوبة',
        'اختيار نوع السماد المناسب (NPK)',
        'حساب الكمية المطلوبة حسب المساحة',
        'توزيع السماد بانتظام',
        'خلط السماد مع التربة',
        'الري بعد التسميد لتفعيل العناصر',
      ],
      'hasTraining': true,
      'trainingTitle': 'الاستخدام الآمن للأسمدة الكيميائية',
    },
    {
      'id': 'weed_control',
      'title': 'إزالة الأعشاب الضارة',
      'icon': Icons.grass,
      'color': Colors.red,
      'description': 'مكافحة الأعشاب الضارة',
      'steps': [
        'تحديد أنواع الأعشاب الضارة',
        'اختيار طريقة المكافحة (يدوية أو كيميائية)',
        'إزالة الأعشاب في الوقت المناسب',
        'استخدام أدوات الإزالة المناسبة',
        'التخلص من الأعشاب المقطوعة',
        'المتابعة الدورية لمنع نموها مجدداً',
      ],
      'hasTraining': true,
      'trainingTitle': 'طرق مكافحة الأعشاب المتقدمة',
    },
    {
      'id': 'plant_protection',
      'title': 'حماية النباتات كيميائياً',
      'icon': Icons.shield,
      'color': Colors.purple,
      'description': 'استخدام المبيدات والمواد الكيميائية',
      'steps': [
        'تحديد نوع الآفة أو المرض',
        'اختيار المبيد المناسب',
        'قراءة تعليمات الاستخدام بعناية',
        'ارتداء معدات الحماية الشخصية',
        'رش المبيد في الوقت المناسب',
        'احترام فترة الأمان قبل الحصاد',
      ],
      'hasTraining': true,
      'trainingTitle': 'الاستخدام الآمن للمبيدات الكيميائية',
    },
    {
      'id': 'harvesting',
      'title': 'الحصاد',
      'icon': Icons.agriculture_outlined,
      'color': Colors.amber,
      'description': 'جني المحصول في الوقت المناسب',
      'steps': [
        'تحديد علامات نضج المحصول',
        'اختيار الوقت المناسب للحصاد',
        'استخدام أدوات الحصاد المناسبة',
        'جمع المحصول بعناية لتجنب التلف',
        'فرز المحصول حسب الجودة',
        'تنظيف المحصول من الأتربة والشوائب',
      ],
      'hasTraining': true,
      'trainingTitle': 'تقنيات الحصاد الحديثة',
    },
    {
      'id': 'post_harvest',
      'title': 'ما بعد الحصاد',
      'icon': Icons.inventory,
      'color': Colors.teal,
      'description': 'التعامل مع المحصول بعد الجني',
      'steps': [
        'تجفيف المحصول إذا لزم الأمر',
        'تعبئة المحصول في عبوات مناسبة',
        'تخزين المحصول في مكان مناسب',
        'مراقبة درجة الحرارة والرطوبة',
        'فحص المحصول دورياً',
        'تسويق المحصول في الوقت المناسب',
      ],
      'hasTraining': true,
      'trainingTitle': 'إدارة التخزين والتسويق',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: const Text(
          'المرشد الزراعي',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.list_alt),
              text: 'الخطوات العملية',
            ),
            Tab(
              icon: Icon(Icons.timeline),
              text: 'المراحل المتسلسلة',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStepsListView(),
          _buildTimelineView(),
        ],
      ),
    );
  }

  /// بناء عرض قائمة الخطوات
  Widget _buildStepsListView() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _farmingStages.length,
      itemBuilder: (context, index) {
        final stage = _farmingStages[index];
        return _buildStageCard(stage, index);
      },
    );
  }

  /// بناء عرض المراحل المتسلسلة
  Widget _buildTimelineView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildTimelineHeader(),
          const SizedBox(height: 20),
          ..._farmingStages.asMap().entries.map((entry) {
            final index = entry.key;
            final stage = entry.value;
            return _buildTimelineItem(stage, index);
          }),
        ],
      ),
    );
  }

  /// بناء رأس المراحل المتسلسلة
  Widget _buildTimelineHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor,
            AssetsColors.dufaultGreencolor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.dufaultGreencolor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.timeline,
            color: Colors.white,
            size: 48,
          ),
          const SizedBox(height: 12),
          const Text(
            'دليل المراحل الزراعية المتسلسلة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'اتبع هذه المراحل بالترتيب للحصول على أفضل النتائج',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المرحلة
  Widget _buildStageCard(Map<String, dynamic> stage, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: stage['color'].withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            stage['icon'],
            color: stage['color'],
            size: 24,
          ),
        ),
        title: Text(
          stage['title'],
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          stage['description'],
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الخطوات العملية
                const Text(
                  'الخطوات العملية:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                ...stage['steps'].asMap().entries.map<Widget>((entry) {
                  final stepIndex = entry.key;
                  final step = entry.value;
                  return _buildStepItem(step, stepIndex + 1);
                }).toList(),

                const SizedBox(height: 16),

                // زر التدريب المتقدم
                if (stage['hasTraining'])
                  _buildTrainingButton(stage),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الخطوة
  Widget _buildStepItem(String step, int stepNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AssetsColors.dufaultGreencolor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                stepNumber.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              step,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر التدريب المتقدم
  Widget _buildTrainingButton(Map<String, dynamic> stage) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue,
            Colors.blue.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          _navigateToTraining(stage);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.school,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                stage['trainingTitle'],
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر المراحل المتسلسلة
  Widget _buildTimelineItem(Map<String, dynamic> stage, int index) {
    final isLast = index == _farmingStages.length - 1;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // خط المراحل المتسلسلة
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: stage['color'],
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: stage['color'].withOpacity(0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  (index + 1).toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 60,
                color: Colors.grey[300],
                margin: const EdgeInsets.symmetric(vertical: 8),
              ),
          ],
        ),
        const SizedBox(width: 16),

        // محتوى المرحلة
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: stage['color'].withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      stage['icon'],
                      color: stage['color'],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        stage['title'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: stage['color'],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  stage['description'],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'عدد الخطوات: ${stage['steps'].length}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// التنقل إلى صفحة التدريب
  void _navigateToTraining(Map<String, dynamic> stage) {
    _showTrainingOptions(stage);
  }

  /// عرض خيارات التدريب والاستشارة
  void _showTrainingOptions(Map<String, dynamic> stage) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // العنوان
            Text(
              stage['trainingTitle'],
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'اختر نوع الخدمة التي تريدها',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // خيارات الخدمة
            _buildServiceOption(
              icon: Icons.school,
              title: 'تدريب متقدم',
              subtitle: 'دروس وفيديوهات تعليمية',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _showComingSoon('التدريب المتقدم');
              },
            ),
            const SizedBox(height: 12),
            _buildServiceOption(
              icon: Icons.chat,
              title: 'استشارة فورية',
              subtitle: 'اطرح سؤالاً على مرشد زراعي',
              color: AssetsColors.dufaultGreencolor,
              onTap: () {
                Navigator.pop(context);
                _navigateToConsultation();
              },
            ),
            const SizedBox(height: 12),
            _buildServiceOption(
              icon: Icons.visibility,
              title: 'طلب مراقبة',
              subtitle: 'مراقبة دورية للنبات',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _navigateToMonitoring();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء خيار خدمة
  Widget _buildServiceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// التنقل إلى استشارة
  void _navigateToConsultation() {
    // مرشد افتراضي للتجربة
    const String advisorId = 'advisor_001';
    const String advisorName = 'د. أحمد محمد';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConsultationFormScreen(
          advisorId: advisorId,
          advisorName: advisorName,
        ),
      ),
    );
  }

  /// التنقل إلى مراقبة النبات
  void _navigateToMonitoring() {
    // مرشد افتراضي للتجربة
    const String advisorId = 'advisor_001';
    const String advisorName = 'د. أحمد محمد';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PlantMonitoringRequestScreen(
          advisorId: advisorId,
          advisorName: advisorName,
        ),
      ),
    );
  }

  /// عرض رسالة قريباً
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('قريباً: $feature'),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
