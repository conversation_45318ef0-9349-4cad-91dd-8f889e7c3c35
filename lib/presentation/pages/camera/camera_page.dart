import 'dart:io';
import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/widgets/shared/appbar.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

/// صفحة الكاميرا
///
/// تعرض واجهة الكاميرا للمستخدم وتتيح التقاط الصور وعرضها
class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  final ImagePicker _picker = ImagePicker();
  File? _image;
  bool _processingImage = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// التحقق من أذونات الكاميرا
  Future<void> _checkPermissions() async {
    final cameraStatus = await Permission.camera.status;
    if (!cameraStatus.isGranted) {
      await Permission.camera.request();
    }

    final storageStatus = await Permission.storage.status;
    if (!storageStatus.isGranted) {
      await Permission.storage.request();
    }

    // للأجهزة التي تعمل بنظام Android 13 وما فوق
    if (Platform.isAndroid) {
      final photosStatus = await Permission.photos.status;
      if (!photosStatus.isGranted) {
        await Permission.photos.request();
      }
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    try {
      setState(() {
        _processingImage = true;
        _errorMessage = null;
      });

      final XFile? photo = await _picker.pickImage(source: ImageSource.camera);
      if (photo != null) {
        setState(() {
          _image = File(photo.path);
          _processingImage = false;
        });
      } else {
        setState(() {
          _processingImage = false;
        });
      }
    } catch (e) {
      setState(() {
        _processingImage = false;
        _errorMessage = 'حدث خطأ أثناء التقاط الصورة: $e';
      });
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    try {
      setState(() {
        _processingImage = true;
        _errorMessage = null;
      });

      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _image = File(image.path);
          _processingImage = false;
        });
      } else {
        setState(() {
          _processingImage = false;
        });
      }
    } catch (e) {
      setState(() {
        _processingImage = false;
        _errorMessage = 'حدث خطأ أثناء اختيار الصورة: $e';
      });
    }
  }

  /// حذف الصورة الحالية
  void _deleteImage() {
    setState(() {
      _image = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: defaultAppBar(
        color: AssetsColors.dufaultGreencolor,
        context: context,
        titel: 'الكاميرا',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عرض الصورة إذا تم التقاطها
            if (_image != null)
              Expanded(
                flex: 3,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    _image!,
                    fit: BoxFit.cover,
                  ),
                ),
              )
            else
              Expanded(
                flex: 3,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: _processingImage
                        ? const CircularProgressIndicator()
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.camera_alt,
                                size: 80,
                                color: AssetsColors.dufaultGreencolor,
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                'لم يتم التقاط صورة بعد',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (_errorMessage != null) ...[
                                const SizedBox(height: 16),
                                Text(
                                  _errorMessage!,
                                  style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ],
                          ),
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار التحكم
            Expanded(
              flex: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton(
                    icon: Icons.photo_library,
                    label: 'المعرض',
                    onPressed: _pickImage,
                    color: Colors.blue,
                  ),
                  _buildActionButton(
                    icon: Icons.camera_alt,
                    label: 'التقاط صورة',
                    onPressed: _takePhoto,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                  if (_image != null)
                    _buildActionButton(
                      icon: Icons.delete,
                      label: 'حذف',
                      onPressed: _deleteImage,
                      color: Colors.red,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
            backgroundColor: color,
            foregroundColor: Colors.white,
          ),
          child: Icon(icon, size: 28),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }
}
