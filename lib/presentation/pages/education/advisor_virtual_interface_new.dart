import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/bloc/advisor_virtual_interface/index.dart';

/// واجهة الاستشاري الزراعي الافتراضية المحسنة
///
/// هذه الواجهة مخصصة للمرشد الزراعي نفسه لعرض جميع الطلبات المرسلة من المزارعين
/// تشمل: الاستشارة الفورية، حجز المواعيد، مراقبة النبات
/// محولة إلى StatelessWidget مع Cubit لضمان استقرار البيانات مثل واجهة أسئلة المرشد
class AdvisorVirtualInterfaceNew extends StatelessWidget {
  const AdvisorVirtualInterfaceNew({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AdvisorVirtualInterfaceCubit>(
          create: (context) {
            final cubit = AdvisorVirtualInterfaceCubit();
            // ربط AdvisorCubit الموجود
            cubit.setAdvisorCubit(context.read<AdvisorCubit>());
            cubit.initialize();
            return cubit;
          },
        ),
      ],
      child: const _AdvisorVirtualInterfaceView(),
    );
  }
}

/// عرض واجهة الاستشاري الزراعي المحسن
class _AdvisorVirtualInterfaceView extends StatelessWidget {
  const _AdvisorVirtualInterfaceView();

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AdvisorVirtualInterfaceCubit, AdvisorVirtualInterfaceState>(
      listener: (context, state) {
        // معالجة الأحداث والإشعارات مثل واجهة أسئلة المرشد
        if (state is AdvisorVirtualInterfaceNewConsultation && state.hasNewConsultations) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث الاستشارات - ${state.newConsultationsCount} استشارة جديدة'),
              backgroundColor: AssetsColors.dufaultGreencolor,
              duration: const Duration(seconds: 2),
            ),
          );
          
          // إعادة تعيين الإشعارات بعد عرضها
          context.read<AdvisorVirtualInterfaceCubit>().clearNotifications();
        }
        
        if (state is AdvisorVirtualInterfaceError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.grey.shade50,
          appBar: _buildAppBar(context, state),
          body: _buildBody(context, state),
          bottomNavigationBar: _buildBottomNavigationBar(context, state),
          floatingActionButton: _buildRefreshButton(context, state),
        );
      },
    );
  }

  /// بناء شريط التطبيق المحسن
  PreferredSizeWidget _buildAppBar(BuildContext context, AdvisorVirtualInterfaceState state) {
    return AppBar(
      title: const Text('واجهة المرشد الزراعي'),
      backgroundColor: AssetsColors.dufaultGreencolor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // عرض عدد الاستشارات الجديدة
        if (state is AdvisorVirtualInterfaceLoaded && state.hasNewConsultations)
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.notifications),
                  onPressed: () {
                    // الانتقال إلى تبويب الاستشارات
                    context.read<AdvisorVirtualInterfaceCubit>().changeTab(1);
                  },
                ),
                if (state.newConsultationsCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '${state.newConsultationsCount}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء الجسم الرئيسي للواجهة
  Widget _buildBody(BuildContext context, AdvisorVirtualInterfaceState state) {
    if (state is AdvisorVirtualInterfaceLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (state is AdvisorVirtualInterfaceError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<AdvisorVirtualInterfaceCubit>().refresh();
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }
    
    // عرض المحتوى حسب التبويب المحدد
    return IndexedStack(
      index: state.currentIndex,
      children: [
        _buildDashboardTab(context),
        _buildConsultationsTab(context),
        _buildAppointmentsTab(context),
        _buildPlantMonitoringTab(context),
        _buildReportsTab(context),
      ],
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar(BuildContext context, AdvisorVirtualInterfaceState state) {
    return BottomNavigationBar(
      currentIndex: state.currentIndex,
      onTap: (index) {
        context.read<AdvisorVirtualInterfaceCubit>().changeTab(index);
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AssetsColors.dufaultGreencolor,
      unselectedItemColor: Colors.grey,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'لوحة التحكم',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble),
          label: 'الاستشارات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today),
          label: 'المواعيد',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.eco),
          label: 'مراقبة النبات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      ],
    );
  }

  /// بناء زر التحديث
  Widget _buildRefreshButton(BuildContext context, AdvisorVirtualInterfaceState state) {
    final isLoading = state is AdvisorVirtualInterfaceLoading;
    
    return FloatingActionButton(
      onPressed: isLoading ? null : () {
        context.read<AdvisorVirtualInterfaceCubit>().refresh();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الاستشارات'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      },
      backgroundColor: isLoading ? Colors.grey : AssetsColors.dufaultGreencolor,
      tooltip: 'تحديث الاستشارات',
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
    );
  }

  /// بناء تبويب لوحة التحكم
  Widget _buildDashboardTab(BuildContext context) {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        return const Center(
          child: Text(
            'لوحة التحكم - قيد التطوير',
            style: TextStyle(fontSize: 18),
          ),
        );
      },
    );
  }

  /// بناء تبويب الاستشارات
  Widget _buildConsultationsTab(BuildContext context) {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        if (state is ConsultationsLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (state is ConsultationsLoaded) {
          if (state.consultations.isEmpty) {
            return const Center(
              child: Text(
                'لا توجد استشارات حالياً',
                style: TextStyle(fontSize: 18),
              ),
            );
          }
          
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.consultations.length,
            itemBuilder: (context, index) {
              final consultation = state.consultations[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  title: Text(consultation.userName),
                  subtitle: Text(consultation.problemDescription),
                  trailing: Text(consultation.status.name),
                ),
              );
            },
          );
        }
        
        return const Center(
          child: Text('حدث خطأ في تحميل الاستشارات'),
        );
      },
    );
  }

  /// بناء تبويب المواعيد
  Widget _buildAppointmentsTab(BuildContext context) {
    return const Center(
      child: Text(
        'المواعيد - قيد التطوير',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// بناء تبويب مراقبة النبات
  Widget _buildPlantMonitoringTab(BuildContext context) {
    return const Center(
      child: Text(
        'مراقبة النبات - قيد التطوير',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// بناء تبويب التقارير
  Widget _buildReportsTab(BuildContext context) {
    return const Center(
      child: Text(
        'التقارير - قيد التطوير',
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}
