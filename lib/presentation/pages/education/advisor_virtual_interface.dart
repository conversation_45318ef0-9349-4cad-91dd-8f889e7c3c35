import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/shared/index.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/widgets/shared/advisor/index.dart';
import 'widgets/advisor_header.dart';
import 'widgets/advisor_consultation_card.dart' as local_widgets;
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/presentation/widgets/shared/appbar.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/data/datasources/local/shared_prefs.dart';

/// واجهة الاستشاري الزراعي الافتراضية
///
/// هذه الواجهة مخصصة للمرشد الزراعي نفسه لعرض جميع الطلبات المرسلة من المزارعين
/// تشمل: الاستشارة الفورية، حجز المواعيد، مراقبة النبات
class AdvisorVirtualInterface extends StatefulWidget {
  const AdvisorVirtualInterface({super.key});

  @override
  State<AdvisorVirtualInterface> createState() =>
      _AdvisorVirtualInterfaceState();
}

class _AdvisorVirtualInterfaceState extends State<AdvisorVirtualInterface> {
  int _currentIndex = 0;
  String _currentAdvisorId = 'general_advisor';
  bool _isLoading = true;

  // Stream للاستشارات الجديدة
  StreamSubscription? _consultationsStream;

  // الخدمات الموحدة
  final FarmerAdvisorBridge _bridge = FarmerAdvisorBridge();
  final UnifiedNotificationService _notifications =
      UnifiedNotificationService();
  final UnifiedAnalyticsService _analytics = UnifiedAnalyticsService();
  final UnifiedConsultationService _consultationService =
      UnifiedConsultationService();

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadAdvisorData();
    _setupAutoRefresh();
    _setupRealTimeConsultations(); // ✅ مراقبة الاستشارات الجديدة
  }

  @override
  void dispose() {
    // تنظيف Stream للاستشارات
    _consultationsStream?.cancel();

    // إزالة المستمعين
    _bridge.removeConsultationListener(_onNewConsultation);
    _consultationService.removeConsultationListener(_onConsultationsUpdated);

    // تنظيف الخدمات
    _bridge.dispose();
    _notifications.dispose();
    _analytics.dispose();
    _consultationService.dispose();

    super.dispose();
  }

  /// إعداد التحديث التلقائي كل 30 ثانية
  void _setupAutoRefresh() {
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        debugPrint('🔄 تحديث تلقائي - تحديث البيانات...');
        _loadAllRequests();
      } else {
        timer.cancel();
      }
    });
  }

  /// إعداد مراقبة الاستشارات الجديدة في الوقت الفعلي
  void _setupRealTimeConsultations() {
    _consultationsStream = FirebaseFirestore.instance
        .collection('consultations')
        .where('advisorId', isEqualTo: _currentAdvisorId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen((snapshot) {
      if (mounted && snapshot.docChanges.isNotEmpty) {
        // التحقق من وجود استشارات جديدة
        final newConsultations = snapshot.docChanges
            .where((change) => change.type == DocumentChangeType.added)
            .toList();

        if (newConsultations.isNotEmpty) {
          debugPrint('🔔 تم اكتشاف ${newConsultations.length} استشارة جديدة');

          // تحديث البيانات فوراً
          _loadAllRequests();

          // عرض إشعار للاستشارات الجديدة
          if (_currentIndex == 1) { // إذا كنا في صفحة الاستشارات
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('📩 استشارة جديدة وصلت للتو!'),
                backgroundColor: AssetsColors.dufaultGreencolor,
                duration: const Duration(seconds: 3),
                action: SnackBarAction(
                  label: 'عرض',
                  textColor: Colors.white,
                  onPressed: () {
                    // التمرير إلى أعلى القائمة لرؤية الاستشارة الجديدة
                  },
                ),
              ),
            );
          }
        }
      }
    });
  }

  /// تحميل بيانات المرشد والطلبات
  Future<void> _loadAdvisorData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // الحصول على معرف المستخدم الحالي
      final uid = SharedPrefs.getString('uid');

      // إذا لم يكن هناك معرف مستخدم، استخدم المرشد العام
      // وإلا استخدم معرف المستخدم الحالي كمرشد
      final advisorId = uid ?? 'general_advisor';

      setState(() {
        _currentAdvisorId = advisorId;
      });

      debugPrint('🔍 تحميل بيانات المرشد: $_currentAdvisorId');

      // تحميل جميع أنواع الطلبات
      await _loadAllRequests();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل بيانات المرشد',
        error: e,
        tag: 'AdvisorVirtualInterface',
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل جميع الطلبات - محسن مثل واجهة أسئلة المرشد
  Future<void> _loadAllRequests() async {
    try {
      debugPrint('🔄 تحميل الطلبات للمرشد: $_currentAdvisorId');

      // استخدام نفس طريقة واجهة أسئلة المرشد - تحميل جميع الاستشارات
      debugPrint('📋 تحميل جميع الاستشارات (مثل واجهة أسئلة المرشد)');
      await context.read<AdvisorCubit>().getAllConsultations();

      debugPrint('✅ تم تحميل الطلبات بنجاح');

      // ملاحظة: المواعيد ومراقبة النبات ستكون متاحة لاحقاً
      // await context.read<AdvisorCubit>().getAdvisorAppointments(advisorId: _currentAdvisorId);
      // await context.read<AdvisorCubit>().getAdvisorPlantMonitoring(advisorId: _currentAdvisorId);
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الطلبات: $e');

      // إظهار حالة الخطأ - سيتم إدارتها تلقائياً بواسطة Cubit
      LoggerService.error(
        'فشل في تحميل الاستشارات',
        error: e,
        tag: 'AdvisorVirtualInterface',
      );
    }
  }

  /// تهيئة الخدمات الموحدة
  Future<void> _initializeServices() async {
    try {
      await _bridge.initialize();
      await _notifications.initialize();
      await _analytics.initialize();

      // تسجيل زيارة الصفحة
      await _analytics.trackScreenView('advisor_virtual_interface');

      // إضافة مستمعين للتحديثات
      _bridge.addConsultationListener(_onNewConsultation);
      _consultationService.addConsultationListener(_onConsultationsUpdated);

      debugPrint('✅ تم تهيئة جميع الخدمات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات: $e');
    }
  }

  /// معالجة استشارة جديدة
  void _onNewConsultation(ConsultationModel consultation) {
    // تحديث الواجهة عند وصول استشارة جديدة
    if (mounted) {
      setState(() {
        // يمكن إضافة منطق إضافي هنا
      });

      // عرض إشعار في التطبيق
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('استشارة جديدة: ${consultation.cropType}'),
          backgroundColor: UnifiedColors.success,
          action: SnackBarAction(
            label: 'عرض',
            onPressed: () => _onPageChanged(1),
          ),
        ),
      );
    }
  }

  /// معالجة تحديث قائمة الاستشارات
  void _onConsultationsUpdated(List<ConsultationModel> consultations) {
    if (mounted) {
      setState(() {
        // تحديث الواجهة
      });
    }
  }

  /// تغيير الصفحة الحالية
  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // تسجيل تغيير الصفحة في التحليلات
    final pageNames = [
      'dashboard',
      'consultations',
      'appointments',
      'plant_monitoring',
      'reports',
    ];
    if (index < pageNames.length) {
      _analytics.trackScreenView('advisor_${pageNames[index]}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: defaultAppBar(
        color: AssetsColors.dufaultGreencolor,
        context: context,
        titel: 'الاستشاري الزراعي',
      ),
      body: BlocConsumer<AdvisorCubit, AdvisorState>(
        listener: (context, state) {
          if (state is ConsultationResponseSuccess) {
            _showSuccessMessage(state.message);
          } else if (state is AdvisorError) {
            _showErrorMessage(state.message);
          } else if (state is ConsultationCreated) {
            _showSuccessMessage('تم إنشاء الاستشارة بنجاح');
            // ✅ تحديث فوري للاستشارات عند إنشاء استشارة جديدة
            _loadAllRequests();
          } else if (state is AppointmentBooked) {
            _showSuccessMessage('تم حجز الموعد بنجاح');
          } else if (state is ConsultationsLoaded) {
            // ✅ معالجة تحميل الاستشارات الجديدة
            debugPrint('🔄 تم تحميل ${state.consultations.length} استشارة جديدة');

            // إذا كان هناك استشارات جديدة، عرض إشعار
            if (state.consultations.isNotEmpty && _currentIndex == 1) {
              // عرض إشعار للاستشارات الجديدة فقط إذا كنا في صفحة الاستشارات
              final newConsultations = state.consultations.where((c) =>
                c.status == ConsultationStatus.pending
              ).length;

              if (newConsultations > 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تحديث الاستشارات - $newConsultations استشارة جديدة'),
                    backgroundColor: AssetsColors.dufaultGreencolor,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            }
          }
        },
        builder: (context, state) {
          if (_isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return _buildCurrentPage(state);
        },
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildRefreshButton(),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _currentIndex,
      onTap: _onPageChanged,
      selectedItemColor: AssetsColors.dufaultGreencolor,
      unselectedItemColor: Colors.grey,
      backgroundColor: Colors.white,
      elevation: 8,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'لوحة التحكم',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble),
          label: 'الاستشارات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today),
          label: 'المواعيد',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.eco), label: 'مراقبة النبات'),
        BottomNavigationBarItem(icon: Icon(Icons.analytics), label: 'التقارير'),
      ],
    );
  }

  /// بناء الصفحة الحالية
  Widget _buildCurrentPage(AdvisorState state) {
    switch (_currentIndex) {
      case 0:
        return _buildDashboardPage(state);
      case 1:
        return _buildConsultationsTab(state);
      case 2:
        return _buildAppointmentsTab(state);
      case 3:
        return _buildPlantMonitoringTab(state);
      case 4:
        return _buildReportsTab(state);
      default:
        return _buildDashboardPage(state);
    }
  }

  /// بناء صفحة لوحة التحكم - محسن
  Widget _buildDashboardPage(AdvisorState state) {
    return RefreshIndicator(
      onRefresh: _loadAllRequests,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المرشد المحسنة
            AdvisorHeader(
              advisorId: _currentAdvisorId,
              advisorName: 'المرشد الزراعي العام',
              advisorSpecialty: 'استشارات زراعية شاملة',
            ),

            const SizedBox(height: 20),

            // بطاقة الترحيب مع معلومات حقيقية
            _buildWelcomeCard(state),

            const SizedBox(height: 20),

            // إحصائيات سريعة من البيانات الحقيقية
            _buildQuickStats(state),

            const SizedBox(height: 20),

            // النشاط الأخير من الاستشارات الحقيقية
            _buildRecentActivity(state),

            const SizedBox(height: 20),

            // روابط سريعة للأقسام
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الترحيب
  Widget _buildWelcomeCard(AdvisorState state) {
    final hour = DateTime.now().hour;
    String greeting;
    if (hour < 12) {
      greeting = 'صباح الخير';
    } else if (hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor,
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      greeting,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'مرحباً بك في لوحة تحكم المرشد الزراعي',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _onPageChanged(1),
                  icon: const Icon(Icons.chat_bubble, size: 18),
                  label: const Text('عرض الاستشارات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AssetsColors.dufaultGreencolor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _onPageChanged(2),
                  icon: const Icon(Icons.calendar_today, size: 18),
                  label: const Text('عرض المواعيد'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white, width: 1.5),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(AdvisorState state) {
    int totalConsultations = 0;
    int pendingConsultations = 0;
    int todayAppointments = 0;
    int plantsMonitored = 0;

    if (state is ConsultationsLoaded) {
      totalConsultations = state.consultations.length;
      pendingConsultations =
          state.consultations
              .where((c) => c.status == ConsultationStatus.pending)
              .length;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات اليوم',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),

        AdvisorStatsRow(
          stats: [
            AdvisorStatData(
              label: 'استشارات جديدة',
              count: '$pendingConsultations',
              color: Colors.blue,
              icon: Icons.chat_bubble_outline,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'مواعيد اليوم',
              count: '$todayAppointments',
              color: Colors.orange,
              icon: Icons.calendar_today,
              onTap: () => _onPageChanged(2),
            ),
          ],
          spacing: 12,
        ),
        const SizedBox(height: 12),

        AdvisorStatsRow(
          stats: [
            AdvisorStatData(
              label: 'إجمالي الاستشارات',
              count: '$totalConsultations',
              color: Colors.green,
              icon: Icons.chat,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'النباتات المراقبة',
              count: '$plantsMonitored',
              color: Colors.teal,
              icon: Icons.eco,
              onTap: () => _onPageChanged(3),
            ),
          ],
          spacing: 12,
        ),
      ],
    );
  }

  /// بناء النشاط الأخير
  Widget _buildRecentActivity(AdvisorState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النشاط الأخير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
            ),
            TextButton(
              onPressed: () => _onPageChanged(1),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        if (state is ConsultationsLoaded && state.consultations.isNotEmpty) ...[
          ...state.consultations
              .take(3)
              .map(
                (consultation) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: local_widgets.AdvisorConsultationCard(
                    consultation: consultation,
                    isAdvisor: true,
                    onTap: () => _viewConsultationDetails(consultation),
                    onReply:
                        consultation.status == ConsultationStatus.pending
                            ? () => _respondToConsultation(consultation)
                            : null,
                    onViewDetails: () => _viewConsultationDetails(consultation),
                    onUserImageTap: () => _showFarmerProfile(consultation),
                  ),
                ),
              ),
        ] else
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(Icons.eco, size: 64, color: Colors.grey[300]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد أنشطة حديثة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر الأنشطة الحديثة هنا',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء تبويب الاستشارات الواردة - محسن للمرشد
  Widget _buildConsultationsTab(AdvisorState state) {
    return RefreshIndicator(
      onRefresh: _loadAllRequests,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // إحصائيات سريعة للاستشارات
            _buildConsultationsStats(state),

            // قائمة الاستشارات
            if (state is ConsultationsLoaded) ...[
              if (state.consultations.isEmpty)
                _buildEmptyState(
                  icon: Icons.chat_bubble_outline,
                  title: 'لا توجد استشارات',
                  subtitle: 'لم يتم استلام أي طلبات استشارة حتى الآن\nاسحب للأسفل للتحديث أو اضغط على زر التحديث',
                )
              else ...[
                // عرض عدد الاستشارات المحملة
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'تم تحميل ${state.consultations.length} استشارة',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
                _buildConsultationsList(state.consultations),
              ]
            ] else if (state is ConsultationsLoading)
              Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل الاستشارات...',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              )
            else if (state is AdvisorError)
              _buildErrorState(state.message)
            else
              _buildEmptyState(
                icon: Icons.chat_bubble_outline,
                title: 'ابدأ بتحميل الاستشارات',
                subtitle: 'اضغط على زر التحديث لتحميل الاستشارات',
              ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات الاستشارات
  Widget _buildConsultationsStats(AdvisorState state) {
    int totalCount = 0;
    int pendingCount = 0;
    int answeredCount = 0;
    int urgentCount = 0;

    if (state is ConsultationsLoaded) {
      final consultations = state.consultations;
      totalCount = consultations.length;
      pendingCount =
          consultations
              .where((c) => c.status == ConsultationStatus.pending)
              .length;
      answeredCount =
          consultations
              .where((c) => c.status == ConsultationStatus.answered)
              .length;

      // تحديد الاستشارات العاجلة (أقل من ساعة)
      urgentCount =
          consultations.where((c) {
            final createdDate = DateTime.tryParse(c.createdAt);
            if (createdDate != null) {
              final difference = DateTime.now().difference(createdDate);
              return difference.inHours < 1 &&
                  c.status == ConsultationStatus.pending;
            }
            return false;
          }).length;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: AssetsColors.dufaultGreencolor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'إحصائيات الاستشارات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.dufaultGreencolor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'إجمالي',
                count: '$totalCount',
                color: Colors.blue,
                icon: Icons.chat_bubble_outline,
              ),
              AdvisorStatData(
                label: 'جديدة',
                count: '$pendingCount',
                color: Colors.orange,
                icon: Icons.new_releases,
              ),
              AdvisorStatData(
                label: 'مجابة',
                count: '$answeredCount',
                color: Colors.green,
                icon: Icons.check_circle,
              ),
              AdvisorStatData(
                label: 'عاجلة',
                count: '$urgentCount',
                color: Colors.red,
                icon: Icons.priority_high,
              ),
            ],
            spacing: 8,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الاستشارات باستخدام المكونات المشتركة - محسن مثل واجهة أسئلة المرشد
  Widget _buildConsultationsList(List<ConsultationModel> consultations) {
    // طباعة معلومات debug مثل واجهة أسئلة المرشد
    debugPrint('🔍 عدد الاستشارات المحملة في واجهة الاستشاري: ${consultations.length}');
    for (int i = 0; i < consultations.length && i < 3; i++) {
      final consultation = consultations[i];
      debugPrint('📋 استشارة $i: ${consultation.userName} - صور: ${consultation.images?.length ?? 0}');
      if (consultation.images != null && consultation.images!.isNotEmpty) {
        for (int j = 0; j < consultation.images!.length && j < 2; j++) {
          debugPrint('🖼️ صورة $j: ${consultation.images![j]}');
        }
      }
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: consultations.length,
      itemBuilder: (context, index) {
        final consultation = consultations[index];
        debugPrint('🎨 بناء بطاقة للاستشارة في واجهة الاستشاري: ${consultation.userName} - صور: ${consultation.images?.length ?? 0}');

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: local_widgets.AdvisorConsultationCard(
            consultation: consultation,
            isAdvisor: true,
            onTap: () => _viewConsultationDetails(consultation),
            onReply:
                consultation.status == ConsultationStatus.pending
                    ? () => _respondToConsultation(consultation)
                    : null,
            onViewDetails: () => _viewConsultationDetails(consultation),
            onUserImageTap: () => _showFarmerProfile(consultation),
          ),
        );
      },
    );
  }

  /// بناء تبويب المواعيد الواردة - محسن للمرشد
  Widget _buildAppointmentsTab(AdvisorState state) {
    return RefreshIndicator(
      onRefresh: _loadAllRequests,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // إحصائيات المواعيد
            _buildAppointmentsStats(state),

            // قائمة المواعيد مع تحسينات
            if (state is AppointmentsLoaded) ...[
              if (state.appointments.isEmpty)
                _buildEmptyState(
                  icon: Icons.calendar_today_outlined,
                  title: 'لا توجد مواعيد محجوزة',
                  subtitle: 'سيتم عرض المواعيد المحجوزة من المزارعين هنا\nاسحب للأسفل للتحديث',
                )
              else ...[
                // عرض عدد المواعيد
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'تم تحميل ${state.appointments.length} موعد',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
                _buildAppointmentsList(state.appointments),
              ]
            ] else if (state is ConsultationsLoading)
              const Padding(
                padding: EdgeInsets.all(32.0),
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل المواعيد...'),
                  ],
                ),
              )
            else
              _buildEmptyState(
                icon: Icons.calendar_today_outlined,
                title: 'المواعيد',
                subtitle: 'اضغط على زر التحديث لتحميل المواعيد المحجوزة',
              ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات المواعيد
  Widget _buildAppointmentsStats(AdvisorState state) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calendar_today, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              Text(
                'جدولة المواعيد',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'اليوم',
                count: '0',
                color: Colors.green,
                icon: Icons.today,
              ),
              AdvisorStatData(
                label: 'هذا الأسبوع',
                count: '0',
                color: Colors.orange,
                icon: Icons.date_range,
              ),
              AdvisorStatData(
                label: 'مؤكدة',
                count: '0',
                color: Colors.blue,
                icon: Icons.check_circle,
              ),
              AdvisorStatData(
                label: 'في الانتظار',
                count: '0',
                color: Colors.grey,
                icon: Icons.hourglass_empty,
              ),
            ],
            spacing: 8,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList(List<dynamic> appointments) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: appointments.length,
      itemBuilder: (context, index) {
        final appointment = appointments[index];
        return _buildEnhancedAppointmentCard(appointment);
      },
    );
  }

  /// بناء تبويب طلبات مراقبة النبات - محسن للمرشد
  Widget _buildPlantMonitoringTab(AdvisorState state) {
    return RefreshIndicator(
      onRefresh: _loadAllRequests,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // إحصائيات مراقبة النبات
            _buildPlantMonitoringStats(state),

            // قائمة طلبات المراقبة مع تحسينات
            if (state is PlantMonitoringLoaded) ...[
              if (state.plantMonitorings.isEmpty)
                _buildEmptyState(
                  icon: Icons.eco_outlined,
                  title: 'لا توجد طلبات مراقبة نبات',
                  subtitle: 'سيتم عرض طلبات مراقبة النبات من المزارعين هنا\nاسحب للأسفل للتحديث',
                )
              else ...[
                // عرض عدد طلبات المراقبة
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'تم تحميل ${state.plantMonitorings.length} طلب مراقبة',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
                _buildPlantMonitoringList(state.plantMonitorings),
              ]
            ] else if (state is ConsultationsLoading)
              const Padding(
                padding: EdgeInsets.all(32.0),
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل طلبات مراقبة النبات...'),
                  ],
                ),
              )
            else
              _buildEmptyState(
                icon: Icons.eco_outlined,
                title: 'مراقبة النبات',
                subtitle: 'اضغط على زر التحديث لتحميل طلبات مراقبة النبات\n(ستكون متاحة قريباً)',
              ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات مراقبة النبات
  Widget _buildPlantMonitoringStats(AdvisorState state) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.eco, color: Colors.green.shade600, size: 24),
              const SizedBox(width: 8),
              Text(
                'مراقبة النباتات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'صحية',
                count: '0',
                color: Colors.green,
                icon: Icons.eco,
              ),
              AdvisorStatData(
                label: 'تحتاج عناية',
                count: '0',
                color: Colors.orange,
                icon: Icons.warning,
              ),
              AdvisorStatData(
                label: 'مريضة',
                count: '0',
                color: Colors.red,
                icon: Icons.error,
              ),
              AdvisorStatData(
                label: 'إجمالي',
                count: '0',
                color: Colors.blue,
                icon: Icons.nature,
              ),
            ],
            spacing: 8,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة طلبات مراقبة النبات
  Widget _buildPlantMonitoringList(List<dynamic> monitoringRequests) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: monitoringRequests.length,
      itemBuilder: (context, index) {
        final request = monitoringRequests[index];
        return _buildEnhancedMonitoringCard(request);
      },
    );
  }

  /// بناء صفحة التقارير - محسن
  Widget _buildReportsTab(AdvisorState state) {
    return RefreshIndicator(
      onRefresh: _loadAllRequests,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس التقارير المحسن
            _buildReportsHeader(),
            const SizedBox(height: 20),

            // إحصائيات الأداء مع بيانات حقيقية
            _buildPerformanceStats(state),
            const SizedBox(height: 20),

            // أكثر المشاكل شيوعاً من البيانات الحقيقية
            _buildCommonProblems(),
            const SizedBox(height: 20),

            // تحليلات متقدمة محسنة
            _buildAdvancedAnalytics(state),
            const SizedBox(height: 20),

            // تصدير التقرير مع خيارات متقدمة
            _buildExportSection(),

            // ملاحظة حول التقارير
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue.shade600),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'التقارير تُحدث تلقائياً بناءً على الاستشارات والمواعيد الحقيقية',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس التقارير
  Widget _buildReportsHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade50, Colors.purple.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.purple.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.purple.shade600, size: 24),
              const SizedBox(width: 8),
              Text(
                'تقارير الأداء',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'تحليل شامل لأداء الاستشارات والخدمات المقدمة',
            style: TextStyle(fontSize: 14, color: Colors.purple.shade600),
          ),
        ],
      ),
    );
  }

  /// بناء إحصائيات الأداء
  Widget _buildPerformanceStats(AdvisorState state) {
    int totalConsultations = 0;
    int answeredConsultations = 0;
    double responseRate = 0.0;

    if (state is ConsultationsLoaded) {
      totalConsultations = state.consultations.length;
      answeredConsultations =
          state.consultations
              .where((c) => c.status == ConsultationStatus.answered)
              .length;
      responseRate =
          totalConsultations > 0
              ? (answeredConsultations / totalConsultations) * 100
              : 0.0;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أداء هذا الشهر',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 16),

          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'الاستشارات',
                count: '$totalConsultations',
                color: Colors.blue,
                icon: Icons.chat_bubble,
              ),
              AdvisorStatData(
                label: 'معدل الرد',
                count: '${responseRate.toStringAsFixed(1)}%',
                color: Colors.green,
                icon: Icons.trending_up,
              ),
            ],
            spacing: 12,
          ),
          const SizedBox(height: 12),

          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'التقييم',
                count: '⭐⭐⭐⭐½',
                color: Colors.orange,
                icon: Icons.star,
              ),
              AdvisorStatData(
                label: 'وقت الرد',
                count: '3.2س',
                color: Colors.purple,
                icon: Icons.access_time,
              ),
            ],
            spacing: 12,
          ),
        ],
      ),
    );
  }

  /// بناء أكثر المشاكل شيوعاً
  Widget _buildCommonProblems() {
    final problems = [
      {'name': 'آفات الطماطم', 'percentage': 25.0, 'color': Colors.red},
      {'name': 'مشاكل الري', 'percentage': 20.0, 'color': Colors.blue},
      {'name': 'أمراض القمح', 'percentage': 15.0, 'color': Colors.orange},
      {'name': 'نقص التسميد', 'percentage': 10.0, 'color': Colors.green},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أكثر المشاكل شيوعاً',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 16),

          ...problems.map(
            (problem) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      problem['name'] as String,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (problem['percentage'] as double) / 100,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              problem['color'] as Color,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${problem['percentage']}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: problem['color'] as Color,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التحليلات المتقدمة
  Widget _buildAdvancedAnalytics(AdvisorState state) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تحليلات متقدمة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Icon(Icons.trending_up, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              const Text(
                'تحسن في معدل الاستجابة بنسبة 15% هذا الشهر',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Icon(Icons.schedule, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              const Text(
                'متوسط وقت الاستجابة: 3.2 ساعة',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Icon(Icons.star, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              const Text(
                'تقييم المزارعين: 4.8/5.0',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم التصدير
  Widget _buildExportSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تصدير التقرير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 16),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('سيتم تصدير التقرير قريباً'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              icon: const Icon(Icons.file_download),
              label: const Text('تصدير التقرير PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // تقليل الحجم للحد الأدنى
          children: [
            Icon(
              icon,
              size: 60, // تقليل حجم الأيقونة
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 12), // تقليل المسافة
            Flexible(
              // استخدام Flexible
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 18, // تقليل حجم الخط
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
            const SizedBox(height: 6), // تقليل المسافة
            Flexible(
              // استخدام Flexible
              child: Text(
                subtitle,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14, // تقليل حجم الخط
                  color: Colors.grey.shade500,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
              ),
            ),
            const SizedBox(height: 16), // تقليل المسافة
            ElevatedButton.icon(
              onPressed: _loadAllRequests,
              icon: const Icon(Icons.refresh, size: 18), // تقليل حجم الأيقونة
              label: const Text('تحديث البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ), // تقليل الحشو
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تم حذف التعريفات القديمة - استبدلت بالمحسنة







  /// بناء شريحة معلومات محسنة (غير مستخدمة حالياً)
  // Widget _buildInfoChip(
  //   IconData icon,
  //   String label,
  //   String value,
  //   Color color,
  // ) {
  //   return Container(
  //     padding: const EdgeInsets.all(8),
  //     decoration: BoxDecoration(
  //       color: color.withValues(alpha: 0.1),
  //       borderRadius: BorderRadius.circular(8),
  //       border: Border.all(color: color.withValues(alpha: 0.2)),
  //     ),
  //     child: Row(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Icon(icon, size: 14, color: color),
  //         const SizedBox(width: 4),
  //         Expanded(
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 label,
  //                 style: TextStyle(
  //                   fontSize: 10,
  //                   color: color,
  //                   fontWeight: FontWeight.bold,
  //                 ),
  //               ),
  //               Text(
  //                 value,
  //                 style: TextStyle(
  //                   fontSize: 11,
  //                   color: color.withValues(alpha: 0.8),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  /// بناء شريحة الحالة المحسنة
  Widget _buildEnhancedStatusChip(dynamic status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case ConsultationStatus.pending:
        color = Colors.orange;
        text = 'جديدة';
        icon = Icons.new_releases;
        break;
      case ConsultationStatus.inProgress:
        color = Colors.blue;
        text = 'قيد المعالجة';
        icon = Icons.hourglass_empty;
        break;
      case ConsultationStatus.answered:
        color = Colors.green;
        text = 'تمت الإجابة';
        icon = Icons.check_circle;
        break;
      case ConsultationStatus.closed:
        color = Colors.grey;
        text = 'مغلقة';
        icon = Icons.lock;
        break;
      case ConsultationStatus.cancelled:
        color = Colors.red;
        text = 'ملغاة';
        icon = Icons.cancel;
        break;
      default:
        color = Colors.grey;
        text = 'غير محدد';
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة موعد محسنة
  Widget _buildEnhancedAppointmentCard(dynamic appointment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.calendar_today,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'موعد مع مزارع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'مؤكد',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text('التاريخ: 2024/01/15 - الوقت: 10:00 ص'),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة مراقبة محسنة
  Widget _buildEnhancedMonitoringCard(dynamic monitoring) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.eco, color: Colors.green, size: 20),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'طلب مراقبة نبات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text('نوع النبات: طماطم - الموقع: البيت المحمي الأول'),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// الرد على الاستشارة - محسن
  void _respondToConsultation(dynamic consultation) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildResponseSheet(consultation),
    );
  }

  /// عرض تفاصيل الاستشارة - محسن
  void _viewConsultationDetails(dynamic consultation) {
    showDialog(
      context: context,
      builder: (context) => _buildConsultationDetailsDialog(consultation),
    );
  }

  /// بناء ورقة الرد المحسنة
  Widget _buildResponseSheet(dynamic consultation) {
    final responseController = TextEditingController();
    final recommendationsController = TextEditingController();

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'الرد على الاستشارة',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          const Divider(),

          // النموذج
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(20),
              children: [
                // معلومات الاستشارة
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المزارع: ${consultation.userName ?? "غير معروف"}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'نوع المحصول: ${consultation.cropType ?? "غير محدد"}',
                      ),
                      const SizedBox(height: 4),
                      Text('المساحة: ${consultation.area ?? "غير محدد"}'),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // نص الرد
                TextFormField(
                  controller: responseController,
                  maxLines: 5,
                  decoration: const InputDecoration(
                    labelText: 'نص الرد *',
                    hintText: 'اكتب ردك على الاستشارة هنا...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.reply),
                  ),
                ),

                const SizedBox(height: 16),

                // التوصيات
                TextFormField(
                  controller: recommendationsController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'التوصيات (اختياري)',
                    hintText: 'أي توصيات إضافية...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.lightbulb),
                  ),
                ),

                const SizedBox(height: 24),

                // مساحة إضافية لتجنب إعاقة الكيبورد
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom + 20),
              ],
            ),
          ),

          // زر الإرسال ثابت في الأسفل - لا يعيقه الكيبورد
          Container(
            padding: EdgeInsets.only(
              left: 20,
              right: 20,
              bottom: MediaQuery.of(context).viewInsets.bottom + 20,
              top: 10,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: () {
                  if (responseController.text.trim().isNotEmpty) {
                    // إرسال الرد
                    context.read<AdvisorCubit>().respondToConsultation(
                      consultationId: consultation.id ?? '',
                      advisorId: _currentAdvisorId,
                      response: responseController.text.trim(),
                      recommendations:
                          recommendationsController.text.trim(),
                    );
                    Navigator.of(context).pop();
                  }
                },
                icon: const Icon(Icons.send),
                label: const Text('إرسال الرد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حوار تفاصيل الاستشارة
  Widget _buildConsultationDetailsDialog(dynamic consultation) {
    return AlertDialog(
      title: const Text('تفاصيل الاستشارة'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('المزارع:', consultation.userName ?? 'غير معروف'),
            _buildDetailRow(
              'نوع المحصول:',
              consultation.cropType ?? 'غير محدد',
            ),
            _buildDetailRow('المساحة:', consultation.area ?? 'غير محدد'),
            const SizedBox(height: 12),
            const Text(
              'وصف المشكلة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(consultation.problemDescription ?? 'لا يوجد وصف'),

            if (consultation.response != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'الرد:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              Text(consultation.response!),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    if (dateString.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays == 0) {
        return 'اليوم ${_formatTime(date)}';
      } else if (difference.inDays == 1) {
        return 'أمس ${_formatTime(date)}';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} أيام';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String message) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAllRequests,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }



  /// عرض ملف المزارع الشخصي
  void _showFarmerProfile(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              CircleAvatar(
                backgroundColor: AssetsColors.dufaultGreencolor,
                child: Text(
                  (consultation.userName?.isNotEmpty ?? false)
                      ? consultation.userName![0].toUpperCase()
                      : 'م',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'ملف ${consultation.userName ?? "المزارع"}',
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileRow('الاسم', consultation.userName ?? 'غير محدد'),
              _buildProfileRow('نوع المحصول', consultation.cropType ?? 'غير محدد'),
              _buildProfileRow('المساحة', consultation.area ?? 'غير محددة'),
              _buildProfileRow('تاريخ الاستشارة', _formatDateString(consultation.createdAt)),
              _buildProfileRow('حالة الاستشارة', _getStatusText(consultation.status)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _respondToConsultation(consultation);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
              ),
              child: const Text('رد على الاستشارة'),
            ),
          ],
        );
      },
    );
  }

  /// بناء صف في ملف المزارع
  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ من String
  String _formatDateString(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays == 0) {
        return 'اليوم ${_formatTime(date)}';
      } else if (difference.inDays == 1) {
        return 'أمس';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} أيام';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'جديدة';
      case ConsultationStatus.inProgress:
        return 'قيد المعالجة';
      case ConsultationStatus.answered:
        return 'تمت الإجابة';
      case ConsultationStatus.closed:
        return 'مغلقة';
      case ConsultationStatus.cancelled:
        return 'ملغاة';
    }
  }

  /// بناء زر التحديث العائم
  Widget _buildRefreshButton() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        final isLoading = state is ConsultationsLoading;

        return FloatingActionButton(
          onPressed: isLoading ? null : () async {
            debugPrint('🔄 تحديث يدوي للاستشارات');

            // حفظ السياق قبل العملية غير المتزامنة
            final scaffoldMessenger = ScaffoldMessenger.of(context);

            await _loadAllRequests();

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث الاستشارات'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          backgroundColor: isLoading
              ? Colors.grey
              : AssetsColors.dufaultGreencolor,
          tooltip: 'تحديث الاستشارات',
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(
                  Icons.refresh,
                  color: Colors.white,
                ),
        );
      },
    );
  }

  /// بناء الروابط السريعة للأقسام
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, color: AssetsColors.dufaultGreencolor),
              const SizedBox(width: 8),
              Text(
                'إجراءات سريعة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.chat_bubble_outline,
                  label: 'الاستشارات',
                  onTap: () => setState(() => _currentIndex = 1),
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.calendar_today,
                  label: 'المواعيد',
                  onTap: () => setState(() => _currentIndex = 2),
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.eco,
                  label: 'مراقبة النبات',
                  onTap: () => setState(() => _currentIndex = 3),
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.analytics,
                  label: 'التقارير',
                  onTap: () => setState(() => _currentIndex = 4),
                  color: Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

