import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/shared/services/unified_submission_service.dart';

import '../../../../core/constants/advisor_constants.dart';
import '../../../bloc/advisor/advisor_cubit.dart';
import '../../../bloc/advisor/advisor_state.dart';
import '../../../widgets/shared/advisor/consultation_card.dart';
import '../../../widgets/shared/advisor/empty_state_widget.dart';
import '../../../widgets/shared/advisor/loading_widget.dart';
import '../../../widgets/shared/advisor/error_widget.dart';

/// محتوى تبويب الاستشارات في صفحة التدريب
/// 
/// يعرض قائمة الاستشارات مع إمكانية إنشاء استشارة جديدة
/// ومتابعة حالة الاستشارات الموجودة
class ConsultationTabContent extends StatefulWidget {
  /// معرف المستخدم
  final String userId;
  
  /// اسم المستخدم
  final String userName;
  
  /// صورة المستخدم (اختياري)
  final String? userImage;
  
  /// منشئ محتوى تبويب الاستشارات
  const ConsultationTabContent({
    super.key,
    required this.userId,
    required this.userName,
    this.userImage,
  });

  @override
  State<ConsultationTabContent> createState() => _ConsultationTabContentState();
}

class _ConsultationTabContentState extends State<ConsultationTabContent> {
  // تم حذف _refreshIndicator - غير مستخدم

  @override
  void initState() {
    super.initState();
    _loadUserConsultations();
  }

  /// تحميل استشارات المستخدم
  void _loadUserConsultations() {
    context.read<AdvisorCubit>().getUserConsultations(userId: widget.userId);
  }

  /// تحديث الاستشارات
  static Future<void> _refreshConsultations() async {
    // سيتم تنفيذ التحديث من خلال الكيوبت
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط الإجراءات العلوي
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  AdvisorConstants.consultationsTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _showCreateConsultationDialog,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('جديدة'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: const Size(0, 32),
                ),
              ),
            ],
          ),
        ),

        // محتوى التبويب
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              _loadUserConsultations();
              await _refreshConsultations();
            },
            child: BlocBuilder<AdvisorCubit, AdvisorState>(
              builder: (context, state) {
                if (state is ConsultationsLoading) {
                  return AdvisorLoadingStates.consultations();
                }

                if (state is ConsultationsLoaded) {
                  return _buildConsultationsList(state.consultations);
                }

                if (state is AdvisorError) {
                  return AdvisorErrorStates.consultationsError(
                    onRetry: _loadUserConsultations,
                    details: state.details,
                  );
                }

                // الحالة الأولية - تحميل الاستشارات
                return AdvisorLoadingStates.consultations();
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة الاستشارات
  Widget _buildConsultationsList(List<dynamic> consultations) {
    if (consultations.isEmpty) {
      return AdvisorEmptyStates.consultations(
        onAddConsultation: _showCreateConsultationDialog,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: consultations.length,
      itemBuilder: (context, index) {
        final consultation = consultations[index];
        
        return AdvisorConsultationCard(
          consultation: consultation,
          onTap: () => _showConsultationDetails(consultation),
          showUserImage: false, // لا نعرض صورة المستخدم في استشاراته الخاصة
          showStatus: true,
        );
      },
    );
  }

  /// عرض حوار إنشاء استشارة جديدة
  void _showCreateConsultationDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CreateConsultationSheet(
        userId: widget.userId,
        userName: widget.userName,
        userImage: widget.userImage,
        onConsultationCreated: _loadUserConsultations,
      ),
    );
  }

  /// عرض تفاصيل الاستشارة
  void _showConsultationDetails(dynamic consultation) {
    showDialog(
      context: context,
      builder: (context) => _ConsultationDetailsDialog(
        consultation: consultation,
      ),
    );
  }
}

/// ورقة إنشاء استشارة جديدة
class _CreateConsultationSheet extends StatefulWidget {
  final String userId;
  final String userName;
  final String? userImage;
  final VoidCallback onConsultationCreated;

  const _CreateConsultationSheet({
    required this.userId,
    required this.userName,
    this.userImage,
    required this.onConsultationCreated,
  });

  @override
  State<_CreateConsultationSheet> createState() => _CreateConsultationSheetState();
}

class _CreateConsultationSheetState extends State<_CreateConsultationSheet> {
  final _formKey = GlobalKey<FormState>();
  final _cropController = TextEditingController();
  final _problemController = TextEditingController();
  final _areaController = TextEditingController();
  
  String? _selectedAdvisorId;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _cropController.dispose();
    _problemController.dispose();
    _areaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'إنشاء استشارة جديدة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // النموذج
          Expanded(
            child: BlocListener<AdvisorCubit, AdvisorState>(
              listener: (context, state) {
                if (state is ConsultationCreated) {
                  Navigator.of(context).pop();
                  widget.onConsultationCreated();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(AdvisorConstants.successConsultationCreated),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is AdvisorError) {
                  setState(() {
                    _isSubmitting = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    // نوع المحصول
                    TextFormField(
                      controller: _cropController,
                      decoration: const InputDecoration(
                        labelText: 'نوع المحصول',
                        hintText: 'مثال: طماطم، خيار، قمح...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.eco),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال نوع المحصول';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // وصف المشكلة
                    TextFormField(
                      controller: _problemController,
                      maxLines: 4,
                      decoration: const InputDecoration(
                        labelText: 'وصف المشكلة',
                        hintText: 'اشرح المشكلة التي تواجهها بالتفصيل...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى وصف المشكلة';
                        }
                        if (value.trim().length < 10) {
                          return 'يرجى كتابة وصف أكثر تفصيلاً';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // المساحة
                    TextFormField(
                      controller: _areaController,
                      decoration: const InputDecoration(
                        labelText: 'المساحة المزروعة',
                        hintText: 'مثال: 5 دونم، 2 هكتار...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.crop_landscape),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال المساحة المزروعة';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // زر الإرسال
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _submitConsultation,
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text(
                                'إرسال الاستشارة',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إرسال الاستشارة - موحد
  Future<void> _submitConsultation() async {
    // استخدام الخدمة الموحدة
    final success = await UnifiedSubmissionService.submitConsultation(
      context: context,
      cropType: _cropController.text.trim(),
      problemDescription: _problemController.text.trim(),
      area: _areaController.text.trim(),
      advisorId: _selectedAdvisorId ?? 'general_advisor',
    );

    if (success) {
      // إعادة تعيين النموذج
      _formKey.currentState?.reset();
      _cropController.clear();
      _problemController.clear();
      _areaController.clear();
      setState(() {
        _selectedAdvisorId = null;
        _isSubmitting = false;
      });

      // استدعاء callback إذا كان متوفراً
      if (widget.onConsultationCreated != null) {
        widget.onConsultationCreated!();
      }
    }
  }
}

/// حوار تفاصيل الاستشارة
class _ConsultationDetailsDialog extends StatelessWidget {
  final dynamic consultation;

  const _ConsultationDetailsDialog({
    required this.consultation,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تفاصيل الاستشارة'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('نوع المحصول:', consultation.cropType),
            _buildDetailRow('المساحة:', consultation.area),
            const SizedBox(height: 12),
            const Text(
              'وصف المشكلة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(consultation.problemDescription),
            
            if (consultation.response != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'رد المرشد:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              Text(consultation.response!),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
