import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/advisor_constants.dart';
import '../../../../core/shared/services/unified_submission_service.dart';
import '../../../bloc/advisor/advisor_cubit.dart';
import '../../../bloc/advisor/advisor_state.dart';
import '../../../widgets/shared/advisor/empty_state_widget.dart';

/// محتوى تبويب المواعيد في صفحة التدريب
/// 
/// يعرض قائمة المواعيد المحجوزة مع إمكانية حجز موعد جديد
/// ومتابعة حالة المواعيد الموجودة
class AppointmentTabContent extends StatefulWidget {
  /// معرف المستخدم
  final String userId;
  
  /// اسم المستخدم
  final String userName;
  
  /// هاتف المستخدم
  final String userPhone;
  
  /// منشئ محتوى تبويب المواعيد
  const AppointmentTabContent({
    super.key,
    required this.userId,
    required this.userName,
    required this.userPhone,
  });

  @override
  State<AppointmentTabContent> createState() => _AppointmentTabContentState();
}

class _AppointmentTabContentState extends State<AppointmentTabContent> {
  /// قائمة المواعيد المحملة
  List<dynamic> _appointments = [];

  @override
  void initState() {
    super.initState();
    _loadUserAppointments();
  }

  /// تحميل مواعيد المستخدم
  void _loadUserAppointments() {
    // TODO: تنفيذ تحميل المواعيد من الكيوبت
    // context.read<AdvisorCubit>().getUserAppointments(userId: widget.userId);
    
    // مؤقتاً - قائمة فارغة
    setState(() {
      _appointments = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط الإجراءات العلوي
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  AdvisorConstants.appointmentsTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _showBookAppointmentDialog,
                icon: const Icon(Icons.calendar_today, size: 16),
                label: const Text('حجز موعد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: const Size(0, 32),
                ),
              ),
            ],
          ),
        ),

        // محتوى التبويب
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              _loadUserAppointments();
              await Future.delayed(const Duration(milliseconds: 500));
            },
            child: _buildAppointmentsList(),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList() {
    if (_appointments.isEmpty) {
      return AdvisorEmptyStates.appointments(
        onAddAppointment: _showBookAppointmentDialog,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _appointments.length,
      itemBuilder: (context, index) {
        final appointment = _appointments[index];
        
        return _AppointmentCard(
          appointment: appointment,
          onTap: () => _showAppointmentDetails(appointment),
        );
      },
    );
  }

  /// عرض حوار حجز موعد جديد
  void _showBookAppointmentDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _BookAppointmentSheet(
        userId: widget.userId,
        userName: widget.userName,
        userPhone: widget.userPhone,
        onAppointmentBooked: _loadUserAppointments,
      ),
    );
  }

  /// عرض تفاصيل الموعد
  void _showAppointmentDetails(dynamic appointment) {
    showDialog(
      context: context,
      builder: (context) => _AppointmentDetailsDialog(
        appointment: appointment,
      ),
    );
  }
}

/// بطاقة الموعد
class _AppointmentCard extends StatelessWidget {
  final dynamic appointment;
  final VoidCallback? onTap;

  const _AppointmentCard({
    required this.appointment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.calendar_today,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'موعد مع المرشد',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'نوع الاستشارة: عام',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip('مؤكد'),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // تفاصيل الموعد
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'التاريخ: 2024/01/15 - الوقت: 10:00 ص',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'مؤكد':
        color = Colors.green;
        break;
      case 'معلق':
        color = Colors.orange;
        break;
      case 'ملغي':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        status,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }
}

/// ورقة حجز موعد جديد
class _BookAppointmentSheet extends StatefulWidget {
  final String userId;
  final String userName;
  final String userPhone;
  final VoidCallback onAppointmentBooked;

  const _BookAppointmentSheet({
    required this.userId,
    required this.userName,
    required this.userPhone,
    required this.onAppointmentBooked,
  });

  @override
  State<_BookAppointmentSheet> createState() => _BookAppointmentSheetState();
}

class _BookAppointmentSheetState extends State<_BookAppointmentSheet> {
  final _formKey = GlobalKey<FormState>();
  final _problemController = TextEditingController();
  
  String _selectedConsultationType = 'استشارة عامة';
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  bool _isSubmitting = false;

  final List<String> _consultationTypes = [
    'استشارة عامة',
    'أمراض النبات',
    'مكافحة الآفات',
    'التسميد والتغذية',
    'أنظمة الري',
    'الزراعة العضوية',
  ];

  @override
  void dispose() {
    _problemController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'حجز موعد جديد',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // النموذج
          Expanded(
            child: BlocListener<AdvisorCubit, AdvisorState>(
              listener: (context, state) {
                if (state is AppointmentBooked) {
                  Navigator.of(context).pop();
                  widget.onAppointmentBooked();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(AdvisorConstants.successAppointmentBooked),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is AdvisorError) {
                  setState(() {
                    _isSubmitting = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    // نوع الاستشارة
                    DropdownButtonFormField<String>(
                      value: _selectedConsultationType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الاستشارة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      items: _consultationTypes.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedConsultationType = value!;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // التاريخ
                    InkWell(
                      onTap: _selectDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الموعد',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _selectedDate != null
                              ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                              : 'اختر التاريخ',
                          style: TextStyle(
                            color: _selectedDate != null ? Colors.black : Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // الوقت
                    InkWell(
                      onTap: _selectTime,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'وقت الموعد',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.access_time),
                        ),
                        child: Text(
                          _selectedTime != null
                              ? _selectedTime!.format(context)
                              : 'اختر الوقت',
                          style: TextStyle(
                            color: _selectedTime != null ? Colors.black : Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // وصف المشكلة
                    TextFormField(
                      controller: _problemController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'وصف المشكلة (اختياري)',
                        hintText: 'اشرح المشكلة التي تريد مناقشتها...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // زر الحجز
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _submitAppointment,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text(
                                'حجز الموعد',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// اختيار الوقت
  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 10, minute: 0),
    );
    if (picked != null) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  /// إرسال طلب الموعد - موحد
  Future<void> _submitAppointment() async {
    // تنسيق التاريخ والوقت
    final dateString = _selectedDate != null
        ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
        : '';
    final timeString = _selectedTime?.format(context) ?? '';

    // استخدام الخدمة الموحدة
    final success = await UnifiedSubmissionService.bookAppointment(
      context: context,
      consultationType: _selectedConsultationType,
      appointmentDate: dateString,
      appointmentTime: timeString,
      problemDescription: _problemController.text.trim(),
    );

    if (success) {
      // إعادة تعيين النموذج
      setState(() {
        _selectedDate = null;
        _selectedTime = null;
        _selectedConsultationType = 'استشارة عامة';
        _isSubmitting = false;
      });
      _problemController.clear();

      // استدعاء callback إذا كان متوفراً
      widget.onAppointmentBooked?.call();
    } else {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}

/// حوار تفاصيل الموعد
class _AppointmentDetailsDialog extends StatelessWidget {
  final dynamic appointment;

  const _AppointmentDetailsDialog({
    required this.appointment,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تفاصيل الموعد'),
      content: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('تفاصيل الموعد ستظهر هنا'),
          // TODO: إضافة تفاصيل الموعد الفعلية
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}
