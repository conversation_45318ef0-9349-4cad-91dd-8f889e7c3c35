import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/advisor_constants.dart';
import '../../../bloc/advisor/advisor_cubit.dart';
import '../../../bloc/advisor/advisor_state.dart';
import '../../../widgets/shared/advisor/stat_card.dart';

/// رأس صفحة الاستشاري الافتراضي
/// 
/// يعرض الإحصائيات السريعة والمعلومات الأساسية للمرشد
class AdvisorHeader extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// اسم المرشد
  final String advisorName;
  
  /// تخصص المرشد
  final String advisorSpecialty;
  
  /// صورة المرشد (اختياري)
  final String? advisorImage;
  
  /// منشئ رأس الاستشاري
  const AdvisorHeader({
    super.key,
    required this.advisorId,
    required this.advisorName,
    required this.advisorSpecialty,
    this.advisorImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // معلومات المرشد
          _buildAdvisorInfo(),
          
          const SizedBox(height: 24),
          
          // الإحصائيات السريعة
          _buildQuickStats(),
        ],
      ),
    );
  }
  
  /// بناء معلومات المرشد
  Widget _buildAdvisorInfo() {
    return Row(
      children: [
        // صورة المرشد
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
          ),
          child: CircleAvatar(
            radius: 27,
            backgroundImage: advisorImage != null
                ? NetworkImage(advisorImage!)
                : null,
            child: advisorImage == null
                ? const Icon(
                    Icons.person,
                    size: 30,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // معلومات المرشد
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                advisorName,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                advisorSpecialty,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.verified,
                          size: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'مرشد معتمد',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.white.withValues(alpha: 0.9),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // زر الإعدادات
        IconButton(
          onPressed: () {
            // TODO: فتح إعدادات المرشد
          },
          icon: const Icon(
            Icons.settings,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
  
  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        if (state is AdvisorStatsLoaded) {
          return _buildStatsRow(state.stats);
        }
        
        // تحميل الإحصائيات إذا لم تكن محملة
        context.read<AdvisorCubit>().getAdvisorStats(advisorId);
        
        return _buildLoadingStats();
      },
    );
  }
  
  /// بناء صف الإحصائيات
  Widget _buildStatsRow(Map<String, dynamic> stats) {
    final statsList = [
      AdvisorStatData(
        label: AdvisorConstants.statNew,
        count: '${stats['totalConsultations'] ?? 0}',
        color: Colors.white,
        icon: Icons.chat_bubble_outline,
      ),
      AdvisorStatData(
        label: 'مجاب عليها',
        count: '${stats['answeredConsultations'] ?? 0}',
        color: Colors.white,
        icon: Icons.check_circle_outline,
      ),
      AdvisorStatData(
        label: 'المواعيد',
        count: '${stats['totalAppointments'] ?? 0}',
        color: Colors.white,
        icon: Icons.calendar_today,
      ),
      AdvisorStatData(
        label: 'التقييم',
        count: '${(stats['averageRating'] ?? 0.0).toStringAsFixed(1)}',
        color: Colors.white,
        icon: Icons.star,
      ),
    ];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AdvisorConstants.quickStatsTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 12),
          AdvisorStatsRow(
            stats: statsList,
            spacing: 8,
          ),
        ],
      ),
    );
  }
  
  /// بناء حالة تحميل الإحصائيات
  Widget _buildLoadingStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AdvisorConstants.quickStatsTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: List.generate(4, (index) {
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                    left: index > 0 ? 4 : 0,
                    right: index < 3 ? 4 : 0,
                  ),
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white.withValues(alpha: 0.7),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
