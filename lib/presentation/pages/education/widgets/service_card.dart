import 'package:flutter/material.dart';

/// بطاقة خدمة محسنة لصفحة التدريب
/// 
/// تستخدم لعرض الخدمات المختلفة في تبويب التدريب
/// مع تصميم موحد ومتجاوب
class EducationServiceCard extends StatelessWidget {
  /// عنوان الخدمة
  final String title;
  
  /// أيقونة الخدمة
  final IconData icon;
  
  /// لون الخدمة
  final Color color;
  
  /// وصف الخدمة (اختياري)
  final String? description;
  
  /// دالة النقر على البطاقة
  final VoidCallback onTap;
  
  /// إظهار شارة جديد
  final bool showNewBadge;
  
  /// نص الشارة المخصص
  final String? customBadgeText;
  
  /// لون الشارة
  final Color? badgeColor;
  
  /// حجم البطاقة
  final Size? cardSize;
  
  /// منشئ بطاقة الخدمة
  const EducationServiceCard({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
    this.description,
    this.showNewBadge = false,
    this.customBadgeText,
    this.badgeColor,
    this.cardSize,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // البطاقة الرئيسية
        Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              width: cardSize?.width,
              height: cardSize?.height,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withValues(alpha: 0.1),
                    color.withValues(alpha: 0.05),
                  ],
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // الأيقونة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      icon,
                      size: 32,
                      color: color,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // العنوان
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  // الوصف إذا كان متوفراً
                  if (description != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      description!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        
        // الشارة إذا كانت مطلوبة
        if (showNewBadge || customBadgeText != null)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: badgeColor ?? Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                customBadgeText ?? 'جديد',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// شبكة بطاقات الخدمات
/// 
/// تستخدم لعرض مجموعة من بطاقات الخدمات في شبكة منظمة
class EducationServicesGrid extends StatelessWidget {
  /// قائمة بيانات الخدمات
  final List<EducationServiceData> services;
  
  /// عدد الأعمدة في الشبكة
  final int crossAxisCount;
  
  /// المسافة بين البطاقات
  final double spacing;
  
  /// نسبة العرض إلى الارتفاع للبطاقات
  final double childAspectRatio;
  
  /// منشئ شبكة الخدمات
  const EducationServicesGrid({
    super.key,
    required this.services,
    this.crossAxisCount = 2,
    this.spacing = 16,
    this.childAspectRatio = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: services.length,
      itemBuilder: (context, index) {
        final service = services[index];
        return EducationServiceCard(
          title: service.title,
          icon: service.icon,
          color: service.color,
          description: service.description,
          onTap: service.onTap,
          showNewBadge: service.showNewBadge,
          customBadgeText: service.customBadgeText,
          badgeColor: service.badgeColor,
        );
      },
    );
  }
}

/// نموذج بيانات الخدمة
class EducationServiceData {
  /// عنوان الخدمة
  final String title;
  
  /// أيقونة الخدمة
  final IconData icon;
  
  /// لون الخدمة
  final Color color;
  
  /// وصف الخدمة
  final String? description;
  
  /// دالة النقر
  final VoidCallback onTap;
  
  /// إظهار شارة جديد
  final bool showNewBadge;
  
  /// نص الشارة المخصص
  final String? customBadgeText;
  
  /// لون الشارة
  final Color? badgeColor;
  
  /// منشئ بيانات الخدمة
  const EducationServiceData({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
    this.description,
    this.showNewBadge = false,
    this.customBadgeText,
    this.badgeColor,
  });
}
