import 'package:flutter/material.dart';

import '../../../../core/constants/advisor_constants.dart';
import '../../../../core/shared/services/unified_submission_service.dart';
import '../../../widgets/shared/advisor/empty_state_widget.dart';

/// محتوى تبويب مراقبة النبات في صفحة الاستشاري الزراعي
/// 
/// يعرض قائمة النباتات المراقبة كمحتوى تبويب مدمج
/// ضمن نظام التبويبات الموحد للاستشاري الزراعي
class PlantMonitoringTabContent extends StatefulWidget {
  /// معرف المستخدم
  final String userId;
  
  /// منشئ محتوى تبويب مراقبة النبات
  const PlantMonitoringTabContent({
    super.key,
    required this.userId,
  });

  @override
  State<PlantMonitoringTabContent> createState() => _PlantMonitoringTabContentState();
}

class _PlantMonitoringTabContentState extends State<PlantMonitoringTabContent> {
  /// قائمة النباتات المراقبة
  List<dynamic> _plantMonitorings = [];

  @override
  void initState() {
    super.initState();
    _loadPlantMonitorings();
  }

  /// تحميل مراقبة النباتات
  void _loadPlantMonitorings() {
    // TODO: تنفيذ تحميل مراقبة النباتات من الكيوبت
    setState(() {
      _plantMonitorings = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط الإجراءات العلوي
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  AdvisorConstants.plantMonitoringTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _showAddMonitoringDialog,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('إضافة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: const Size(0, 32),
                ),
              ),
            ],
          ),
        ),
        
        // محتوى التبويب
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              _loadPlantMonitorings();
              await Future.delayed(const Duration(milliseconds: 500));
            },
            child: _buildPlantMonitoringsList(),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة مراقبة النباتات
  Widget _buildPlantMonitoringsList() {
    if (_plantMonitorings.isEmpty) {
      return AdvisorEmptyStates.plantMonitoring(
        onAddMonitoring: _showAddMonitoringDialog,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _plantMonitorings.length,
      itemBuilder: (context, index) {
        final monitoring = _plantMonitorings[index];
        
        return _PlantMonitoringCard(
          monitoring: monitoring,
          onTap: () => _showMonitoringDetails(monitoring),
        );
      },
    );
  }

  /// عرض حوار إضافة مراقبة جديدة
  void _showAddMonitoringDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AddMonitoringSheet(
        userId: widget.userId,
        onMonitoringAdded: _loadPlantMonitorings,
      ),
    );
  }

  /// عرض تفاصيل المراقبة
  void _showMonitoringDetails(dynamic monitoring) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل المراقبة'),
        content: const Text('تفاصيل المراقبة ستظهر هنا عند التنفيذ الكامل'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

/// بطاقة مراقبة النبات المدمجة
class _PlantMonitoringCard extends StatelessWidget {
  final dynamic monitoring;
  final VoidCallback? onTap;

  const _PlantMonitoringCard({
    required this.monitoring,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.eco,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'طماطم - البيت المحمي الأول',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'نوع المراقبة: مراقبة النمو',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip('نشط'),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // شريط التقدم
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحلة النمو: الإزهار (70%)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: 0.7,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        status,
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: Colors.green,
        ),
      ),
    );
  }
}

/// ورقة إضافة مراقبة جديدة (مبسطة للتبويب)
class _AddMonitoringSheet extends StatefulWidget {
  final String userId;
  final VoidCallback onMonitoringAdded;

  const _AddMonitoringSheet({
    required this.userId,
    required this.onMonitoringAdded,
  });

  @override
  State<_AddMonitoringSheet> createState() => _AddMonitoringSheetState();
}

class _AddMonitoringSheetState extends State<_AddMonitoringSheet> {
  final _formKey = GlobalKey<FormState>();
  final _plantNameController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedMonitoringType = AdvisorConstants.monitoringTypeLabels[
      AdvisorConstants.monitoringTypeGrowth]!;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _plantNameController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'إضافة مراقبة نبات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // النموذج المبسط
          Expanded(
            child: Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(20),
                children: [
                  // اسم النبات
                  TextFormField(
                    controller: _plantNameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم النبات',
                      hintText: 'مثال: طماطم، خيار...',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.eco),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم النبات';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // نوع المراقبة
                  DropdownButtonFormField<String>(
                    value: _selectedMonitoringType,
                    decoration: const InputDecoration(
                      labelText: 'نوع المراقبة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.monitor_heart),
                    ),
                    items: AdvisorConstants.monitoringTypeLabels.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedMonitoringType = value!;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // الموقع
                  TextFormField(
                    controller: _locationController,
                    decoration: const InputDecoration(
                      labelText: 'الموقع',
                      hintText: 'مثال: البيت المحمي الأول...',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.location_on),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال موقع النبات';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // الملاحظات
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات إضافية (اختياري)',
                      hintText: 'أي ملاحظات أو تفاصيل إضافية...',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.note),
                    ),
                    maxLines: 3,
                    maxLength: 200,
                  ),

                  const SizedBox(height: 24),

                  // زر الإضافة
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isSubmitting ? null : _submitMonitoring,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                      child: _isSubmitting
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text(
                              'إضافة المراقبة',
                              style: TextStyle(fontSize: 16),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إرسال طلب المراقبة - موحد
  Future<void> _submitMonitoring() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      try {
        // استخدام الخدمة الموحدة
        final success = await UnifiedSubmissionService.submitPlantMonitoring(
          context: context,
          plantType: _plantNameController.text.trim(),
          location: _locationController.text.trim(),
          monitoringType: 'مراقبة عامة',
          notes: _notesController.text.trim(),
        );

        if (success && mounted) {
          Navigator.of(context).pop();
          widget.onMonitoringAdded();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }
}
