
import 'package:flutter/material.dart';
import 'news_tiem.dart';
class NewsSection extends StatelessWidget {
  final List<NewsItem> newsItems;

  const NewsSection({super.key, required this.newsItems});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الأخبار الزراعية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            TextButton(
              onPressed: () => _showAllNews(context),
              child: Text(
                'عرض الكل',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Column(
              children: newsItems.take(3).map((item) => item).toList(),
            ),
          ),
        ),
      ],
    );
  }

  void _showAllNews(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (ctx) => Container(
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: [
            const Text(
              'جميع الأخبار',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            ...newsItems.map((item) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: item,
            )),
          ],
        ),
      ),
    );
  }
}