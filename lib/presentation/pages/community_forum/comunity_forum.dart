import 'package:agriculture/core/constants/app_constants.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/headings.dart';
import 'package:agriculture/core/shared/services/user/user_data_service.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_cubit.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_state.dart';
import 'package:agriculture/presentation/bloc/community_forum/community_forum_cubit.dart';
import 'package:agriculture/presentation/pages/community_forum/comments_page.dart';
import 'package:agriculture/presentation/pages/reach_engineer/agricultural_forum_screen_new.dart';
import 'package:agriculture/presentation/pages/reach_engineer/agricultural_questions_list.dart';
import 'package:agriculture/presentation/pages/reach_engineer/advisor_details_screen.dart';
import 'package:agriculture/presentation/pages/reach_engineer/appointment_booking_screen.dart';
import 'package:agriculture/presentation/pages/reach_engineer/plant_monitoring_screen.dart';
import 'package:agriculture/presentation/pages/agricultural_guide/agricultural_guide_screen.dart';
// تم حذف استيرادات advisor غير المستخدمة
import 'package:agriculture/data/models/agricultural_advisor/advisor_model.dart';
import 'package:agriculture/presentation/widgets/community_forum/card_post_user.dart';
import 'package:agriculture/presentation/widgets/community_forum/new_post_screen.dart';
import 'package:agriculture/presentation/widgets/community_forum/top_add_post.dart';
import 'package:agriculture/presentation/widgets/shared/circular_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// قائمة أفضل المرشدين (بيانات وهمية)
/// سيتم نقلها إلى خدمة منفصلة لاحقاً
final List<Map<String, dynamic>> _bestAdvisors = [
  {
    'name': 'د. أحمد محمد',
    'specialty': 'متخصص في زراعة الخضروات',
    'rating': 5,
    'reviews': 127,
    'phone': '+967 77 123 4567',
    'email': '<EMAIL>',
    'location': 'صنعاء، اليمن',
    'clients': 250,
    'experience': 12,
    'successRate': 98,
    'specialties': [
      'زراعة الخضروات',
      'أمراض النبات',
      'الري الحديث',
      'التسميد العضوي',
    ],
  },
  {
    'name': 'د. فاطمة علي',
    'specialty': 'متخصصة في أمراض النبات',
    'rating': 5,
    'reviews': 89,
    'phone': '+967 71 987 6543',
    'email': '<EMAIL>',
    'location': 'عدن، اليمن',
    'clients': 180,
    'experience': 8,
    'successRate': 96,
    'specialties': [
      'أمراض النبات',
      'الآفات الزراعية',
      'المكافحة الحيوية',
      'تشخيص الأمراض',
    ],
  },
  {
    'name': 'م. عبدالله سالم',
    'specialty': 'متخصص في زراعة الفواكه',
    'rating': 4,
    'reviews': 156,
    'phone': '+967 78 456 7890',
    'email': '<EMAIL>',
    'location': 'تعز، اليمن',
    'clients': 320,
    'experience': 15,
    'successRate': 94,
    'specialties': [
      'زراعة الفواكه',
      'أشجار النخيل',
      'الحمضيات',
      'الزراعة المحمية',
    ],
  },
  {
    'name': 'د. مريم حسن',
    'specialty': 'متخصصة في الزراعة العضوية',
    'rating': 5,
    'reviews': 203,
    'phone': '+967 73 321 0987',
    'email': '<EMAIL>',
    'location': 'إب، اليمن',
    'clients': 290,
    'experience': 10,
    'successRate': 97,
    'specialties': [
      'الزراعة العضوية',
      'الزراعة المستدامة',
      'الأسمدة الطبيعية',
      'الزراعة البيئية',
    ],
  },
];

/// صفحة المنتدى المجتمعي
///
/// تحتوي على تبويبين رئيسيين:
/// 1. المنتدى المجتمعي - لعرض المنشورات والتفاعل معها
/// 2. المرشد الزراعي - للحصول على استشارات زراعية
class ComunityForum extends StatefulWidget {
  const ComunityForum({super.key});

  @override
  State<ComunityForum> createState() => _ComunityForumState();
}

/// حالة صفحة المنتدى المجتمعي
class _ComunityForumState extends State<ComunityForum>
    with TickerProviderStateMixin {
  late CommunityForumCubit _cubit;

  @override
  void initState() {
    super.initState();
    // الحصول على معرف المستخدم الحقيقي
    final userData = UserDataService.getCurrentUserData();
    _cubit = CommunityForumCubit(
      uid: userData.userId,
      vsync: this,
    )..initialize();
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _cubit,
      child: BlocBuilder<CommunityForumCubit, CommunityForumState>(
        builder: (context, state) {
          if (state is CommunityForumLoading) {
            return const Scaffold(body: Center(child: CircularProgress()));
          }

          if (state is CommunityForumError) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _cubit.initialize();
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is CommunityForumLoaded) {
            return _CommunityForumContent(state: state);
          }

          return const Scaffold(body: Center(child: CircularProgress()));
        },
      ),
    );
  }
}

/// محتوى صفحة المنتدى المجتمعي
class _CommunityForumContent extends StatelessWidget {
  final CommunityForumLoaded state;

  const _CommunityForumContent({required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CommunityForumCubit>();

    /// الحصول على معرف المستخدم الحالي
    final uid = cubit.uid;

    return Scaffold(
      appBar: _buildAppBar(context, cubit),
      body: _buildTabBarView(context, cubit, uid),
    );
  }

  /// بناء شريط التطبيق
  AppBar _buildAppBar(BuildContext context, CommunityForumCubit cubit) {
    return AppBar(
      backgroundColor: AssetsColors.dufaultGreencolor,
      title:
          state.isSearchMode
              ? _buildSearchField(context, cubit)
              : Text(Headings.comunityForum),
      actions: _buildAppBarActions(context, cubit),
      bottom: _buildTabBar(context, cubit),
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField(BuildContext context, CommunityForumCubit cubit) {
    return TextField(
      controller: cubit.searchController,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: 'ابحث عن منشورات...',
        hintStyle: const TextStyle(color: Colors.white70),
        border: InputBorder.none,
        suffixIcon: IconButton(
          icon: const Icon(Icons.clear, color: Colors.white),
          onPressed: () {
            cubit.searchController.clear();
            context.read<PostsCubit>().clearSearch();
          },
        ),
      ),
      onSubmitted: (_) => _performSearch(context, cubit),
      textInputAction: TextInputAction.search,
      autofocus: true,
    );
  }

  /// بناء أزرار شريط التطبيق
  List<Widget> _buildAppBarActions(
    BuildContext context,
    CommunityForumCubit cubit,
  ) {
    return [
      // زر البحث (يظهر فقط في تبويب المنتدى المجتمعي)
      if (state.mainTabIndex == 0)
        IconButton(
          icon: Icon(state.isSearchMode ? Icons.search_off : Icons.search),
          onPressed: () => cubit.toggleSearchMode(),
        ),
    ];
  }

  /// إجراء البحث
  void _performSearch(BuildContext context, CommunityForumCubit cubit) {
    final query = cubit.searchController.text.trim();
    if (query.isNotEmpty) {
      final postsCubit = context.read<PostsCubit>();
      postsCubit.searchPosts(query);
    }
  }

  /// بناء شريط التبويب
  PreferredSizeWidget _buildTabBar(
    BuildContext context,
    CommunityForumCubit cubit,
  ) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(
        112,
      ), // زيادة الارتفاع لاستيعاب شريطي التبويب
      child: Column(
        children: [
          // شريط التبويب الرئيسي
          TabBar(
            controller: cubit.mainTabController,
            indicatorColor: Colors.white,
            indicatorWeight: 3,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 14,
            ),
            tabs: const [
              Tab(icon: Icon(Icons.groups, size: 24), text: 'المنتدى المجتمعي'),
              Tab(
                icon: Icon(Icons.support_agent, size: 24),
                text: 'المرشد الزراعي',
              ),
            ],
          ),
          // شريط التبويب الفرعي (يظهر فقط في تبويب المنتدى المجتمعي)
          if (state.mainTabIndex == 0 && !state.isSearchMode)
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 56,
              child: TabBar(
                controller: cubit.forumTabController,
                indicatorColor: Colors.white,
                indicatorWeight: 2,
                indicatorSize: TabBarIndicatorSize.label,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.normal,
                  fontSize: 11,
                ),
                labelPadding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                tabs: const [
                  Tab(icon: Icon(Icons.access_time, size: 18), text: 'الأحدث'),
                  Tab(
                    icon: Icon(Icons.favorite, size: 18),
                    text: 'الأكثر إعجابًا',
                  ),
                  Tab(
                    icon: Icon(Icons.comment, size: 18),
                    text: 'الأكثر تعليقًا',
                  ),
                  Tab(
                    icon: Icon(Icons.visibility, size: 18),
                    text: 'الأكثر مشاهدة',
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عرض التبويبات
  Widget _buildTabBarView(
    BuildContext context,
    CommunityForumCubit cubit,
    String uid,
  ) {
    return TabBarView(
      controller: cubit.mainTabController,
      children: [
        // تبويب المنتدى المجتمعي
        _buildCommunityForumTab(context, cubit, uid),
        // تبويب المرشد الزراعي
        _buildAgriculturalGuideTab(context, cubit),
      ],
    );
  }

  /// بناء تبويب المنتدى المجتمعي
  Widget _buildCommunityForumTab(
    BuildContext context,
    CommunityForumCubit cubit,
    String uid,
  ) {
    return NestedScrollView(
      controller: cubit.scrollController,
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [];
      },
      body: BlocConsumer<PostsCubit, PostsState>(
        listener: (context, state) {
          // عرض رسائل الخطأ
          if (state is PostsError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }

          // عرض رسالة نجاح إنشاء منشور
          if (state is PostCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppConstants.successPostCreation)),
            );
          }

          // عرض رسالة خطأ إنشاء منشور
          if (state is PostCreationError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        builder: (context, state) {
          print('الحالة الحالية: ${state.runtimeType}');

          if (state is PostsLoading) {
            print('جاري تحميل المنشورات...');
            return const Center(child: CircularProgress());
          }

          if (state is PostsLoaded) {
            print(
              'تم تحميل المنشورات بنجاح. عدد المنشورات: ${state.posts.length}',
            );
            return RefreshIndicator(
              onRefresh: () async {
                // إعادة تحميل المنشورات عند سحب الشاشة للأسفل
                final postsCubit = context.read<PostsCubit>();
                await postsCubit.getPosts(refresh: true);
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // مكون إضافة منشور جديد (لا يظهر في وضع البحث)
                      if (!state.isSearchMode)
                        TopAddPost(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => BlocProvider.value(
                                      value: context.read<PostsCubit>(),
                                      child: const NewPostScreen(),
                                    ),
                              ),
                            );
                          },
                        ),
                      const SizedBox(height: 8),

                      // عرض معلومات عن نوع الترتيب الحالي أو نتائج البحث
                      state.isSearchMode
                          ? _buildSearchInfoChip(
                            state.searchQuery,
                            state.posts.length,
                          )
                          : _buildSortInfoChip(state.sortType),
                      const SizedBox(height: 8),

                      // عرض المنشورات
                      if (state.posts.isEmpty)
                        Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Center(
                            child: Text(
                              AppConstants.infoNoPosts,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                        )
                      else
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount:
                              state.posts.length +
                              (state.hasReachedMax ? 0 : 1),
                          itemBuilder: (context, index) {
                            // إذا وصلنا إلى نهاية القائمة ولم نصل إلى الحد الأقصى، نحمل المزيد
                            if (index == state.posts.length &&
                                !state.hasReachedMax) {
                              context.read<PostsCubit>().getPosts();
                              return const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Center(child: CircularProgress()),
                              );
                            }

                            // عرض المنشور
                            if (index < state.posts.length) {
                              final post = state.posts[index];
                              return CardPostUser(
                                post: post,
                                userId: uid,
                                onLike:
                                    () => context.read<PostsCubit>().likePost(
                                      post.id,
                                      uid,
                                    ),
                                onUnlike:
                                    () => context.read<PostsCubit>().unlikePost(
                                      post.id,
                                      uid,
                                    ),
                                isLiked: context
                                    .read<PostsCubit>()
                                    .isPostLikedByUser(post.id, uid),
                                onComment: () {
                                  // فتح صفحة التعليقات
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder:
                                          (context) => CommentsPage(post: post),
                                    ),
                                  );
                                },
                                onView: () {
                                  // زيادة عدد المشاهدات (مرة واحدة لكل منشور في الجلسة الحالية)
                                  context.read<PostsCubit>().incrementPostViews(
                                    post.id,
                                  );
                                },
                              );
                            }

                            return null;
                          },
                        ),
                    ],
                  ),
                ),
              ),
            );
          }

          // حالة الخطأ
          if (state is PostsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<PostsCubit>().getPosts(refresh: true);
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          // الحالة الافتراضية
          print('الحالة الافتراضية: ${state.runtimeType}');

          // إذا كانت الحالة هي PostsInitial، نقوم بتحميل المنشورات
          if (state is PostsInitial) {
            // استدعاء getPosts مرة واحدة فقط
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.read<PostsCubit>().getPosts(refresh: true);
            });

            // عرض شريط الانتظار مؤقتًا حتى يتم تحميل المنشورات
            return const Center(child: CircularProgress());
          }

          // إذا كانت الحالة هي CommentsLoaded ولكن ليست PostsLoaded
          // نقوم بتحميل المنشورات وعرض شريط الانتظار مؤقتًا
          if (state is CommentsLoaded && !(state is PostsLoaded)) {
            // استدعاء getPosts مرة واحدة فقط
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.read<PostsCubit>().getPosts(refresh: true);
            });

            // عرض شريط الانتظار مؤقتًا حتى يتم تحميل المنشورات
            return const Center(child: CircularProgress());
          }

          // إذا كانت الحالة هي CommentsLoaded وأيضًا PostsLoaded
          // نعرض المنشورات من حالة PostsLoaded
          if (state is CommentsLoaded && state is PostsLoaded) {
            // الحالة هي PostsLoaded وCommentsLoaded معًا
            // نعرض المنشورات من حالة PostsLoaded
            final postsState = state as PostsLoaded;
            return RefreshIndicator(
              onRefresh: () async {
                // إعادة تحميل المنشورات عند سحب الشاشة للأسفل
                final postsCubit = context.read<PostsCubit>();
                await postsCubit.getPosts(refresh: true);
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // مكون إضافة منشور جديد (لا يظهر في وضع البحث)
                      if (!postsState.isSearchMode)
                        TopAddPost(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => BlocProvider.value(
                                      value: context.read<PostsCubit>(),
                                      child: const NewPostScreen(),
                                    ),
                              ),
                            );
                          },
                        ),

                      // عرض رسالة إذا لم يتم العثور على منشورات
                      if (postsState.posts.isEmpty)
                        const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(child: Text('لا توجد منشورات لعرضها')),
                        ),

                      // عرض قائمة المنشورات
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: postsState.posts.length,
                        itemBuilder: (context, index) {
                          // عرض المنشور
                          final post = postsState.posts[index];
                          return CardPostUser(
                            post: post,
                            userId: uid,
                            onLike:
                                () => context.read<PostsCubit>().likePost(
                                  post.id,
                                  uid,
                                ),
                            onUnlike:
                                () => context.read<PostsCubit>().unlikePost(
                                  post.id,
                                  uid,
                                ),
                            isLiked: context
                                .read<PostsCubit>()
                                .isPostLikedByUser(post.id, uid),
                            onComment: () {
                              // فتح صفحة التعليقات
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => CommentsPage(post: post),
                                ),
                              );
                            },
                            onView: () {
                              // زيادة عدد المشاهدات (مرة واحدة لكل منشور في الجلسة الحالية)
                              context.read<PostsCubit>().incrementPostViews(
                                post.id,
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          // لأي حالة أخرى غير متوقعة
          return const Center(child: CircularProgress());
        },
      ),
    );
  }

  /// بناء تبويب المرشد الزراعي
  Widget _buildAgriculturalGuideTab(
    BuildContext context,
    CommunityForumCubit cubit,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // شريط البحث
          _buildSearchBar(context),
          const SizedBox(height: 20),

          // الخدمات السريعة
          _buildQuickActions(context),
          const SizedBox(height: 24),

          // قسم أفضل المرشدين
          _buildBestAdvisorsSection(context, cubit),
          const SizedBox(height: 24),

          // معلومات التواصل
          _buildContactInfoCard(context),
          const SizedBox(height: 24),

          // نصائح وإرشادات
          _buildTipsCard(context),
        ],
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'اكتب مشكلتك...',
          hintStyle: TextStyle(color: Colors.grey[600]),
          prefixIcon: Icon(Icons.search, color: AssetsColors.dufaultGreencolor),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onSubmitted: (value) {
          // يمكن إضافة منطق البحث هنا
          if (value.isNotEmpty) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text('البحث عن: $value')));
          }
        },
      ),
    );
  }

  /// بناء الخدمات السريعة
  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الخدمات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: '💬',
                title: 'استشارة فورية',
                subtitle: 'دردشة مباشرة',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AgriculturalForumScreenNew(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: '📅',
                title: 'حجز موعد',
                subtitle: 'تقويم متاح',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AppointmentBookingScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: '🌿',
                title: 'مراقبة النبات',
                subtitle: 'تشخيص المشاكل',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PlantMonitoringScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: '📋',
                title: 'الدليل العملي',
                subtitle: 'خطوات الزراعة',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AgriculturalGuideScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة خدمة سريعة
  Widget _buildQuickActionCard({
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isFullWidth = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child:
            isFullWidth
                ? Row(
                  children: [
                    Text(icon, style: const TextStyle(fontSize: 24)),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                )
                : Column(
                  children: [
                    Text(icon, style: const TextStyle(fontSize: 32)),
                    const SizedBox(height: 8),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
      ),
    );
  }

  /// بناء قسم أفضل المرشدين
  Widget _buildBestAdvisorsSection(
    BuildContext context,
    CommunityForumCubit cubit,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أفضل المرشدين',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),

        // عرض قائمة المرشدين الثابتة
        Container(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _bestAdvisors.length,
            itemBuilder: (context, index) {
              final advisor = _bestAdvisors[index];
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 8),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          advisor['name'],
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          advisor['specialty'],
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        // زر عرض جميع الأسئلة
        Center(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AgriculturalQuestionsList(),
                ),
              );
            },
            icon: Icon(Icons.list_alt, color: AssetsColors.dufaultGreencolor),
            label: Text(
              'عرض جميع الأسئلة المطروحة',
              style: TextStyle(
                color: AssetsColors.dufaultGreencolor,
                fontSize: 14,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: AssetsColors.dufaultGreencolor,
                width: 1.5,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة المرشد من نموذج AdvisorModel
  Widget _buildAdvisorCardFromModel(
    BuildContext context,
    AdvisorModel advisor,
  ) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // صورة المرشد (قابلة للنقر)
            GestureDetector(
              onTap: () => _navigateToAdvisorDetails(context, advisor),
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AssetsColors.dufaultGreencolor.withValues(
                      alpha: 0.3,
                    ),
                    width: 2,
                  ),
                ),
                child: CircleAvatar(
                  radius: 26,
                  backgroundColor: AssetsColors.dufaultGreencolor,
                  backgroundImage:
                      advisor.image != null
                          ? NetworkImage(advisor.image!)
                          : null,
                  child:
                      advisor.image == null
                          ? Text(
                            advisor.name.isNotEmpty ? advisor.name[0] : '?',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                          : null,
                ),
              ),
            ),
            const SizedBox(width: 10),
            // معلومات المرشد
            Expanded(
              child: GestureDetector(
                onTap: () => _navigateToAdvisorDetails(context, advisor),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      advisor.name,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      advisor.specialty,
                      style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 3),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < advisor.rating.round()
                                ? Icons.star
                                : Icons.star_border,
                            size: 14,
                            color: Colors.amber,
                          );
                        }),
                        const SizedBox(width: 3),
                        Flexible(
                          child: Text(
                            '(${advisor.reviewsCount})',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    // إضافة نص يشير إلى إمكانية النقر
                    Text(
                      'اضغط لعرض التفاصيل',
                      style: TextStyle(
                        fontSize: 9,
                        color: AssetsColors.dufaultGreencolor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 8),
            // زر الاستشارة
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 70,
                  height: 28,
                  child: ElevatedButton(
                    onPressed:
                        advisor.isAvailable
                            ? () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) =>
                                          const AgriculturalForumScreenNew(),
                                ),
                              );
                            }
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          advisor.isAvailable
                              ? AssetsColors.dufaultGreencolor
                              : Colors.grey,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 4,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: FittedBox(
                      child: Text(
                        advisor.isAvailable ? 'استشر الآن' : 'غير متاح',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// التنقل إلى صفحة تفاصيل المرشد
  void _navigateToAdvisorDetails(BuildContext context, AdvisorModel advisor) {
    final advisorMap = {
      'id': advisor.id,
      'name': advisor.name,
      'specialty': advisor.specialty,
      'image': advisor.image,
      'phone': advisor.phone,
      'email': advisor.email,
      'location': advisor.location,
      'rating': advisor.rating,
      'reviews': advisor.reviewsCount,
      'clients': advisor.clientsCount,
      'experience': advisor.experienceYears,
      'successRate': advisor.successRate,
      'specialties': advisor.specialties,
      'workingHours': advisor.workingHours,
      'isAvailable': advisor.isAvailable,
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AdvisorDetailsScreen(advisor: advisorMap),
      ),
    );
  }

  /// بناء بطاقة المرشد (للبيانات الوهمية)
  Widget _buildAdvisorCard(BuildContext context, Map<String, dynamic> advisor) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المرشد (قابلة للنقر)
          GestureDetector(
            onTap: () {
              // فتح صفحة تفاصيل المرشد عند النقر على الصورة
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AdvisorDetailsScreen(advisor: advisor),
                ),
              );
            },
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: CircleAvatar(
                radius: 30,
                backgroundColor: AssetsColors.dufaultGreencolor,
                child: Text(
                  advisor['name'][0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // معلومات المرشد
          Expanded(
            child: GestureDetector(
              onTap: () {
                // فتح صفحة تفاصيل المرشد عند النقر على المعلومات
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => AdvisorDetailsScreen(advisor: advisor),
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    advisor['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    advisor['specialty'],
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      ...List.generate(5, (index) {
                        return Icon(
                          index < advisor['rating']
                              ? Icons.star
                              : Icons.star_border,
                          size: 16,
                          color: Colors.amber,
                        );
                      }),
                      const SizedBox(width: 4),
                      Text(
                        '(${advisor['reviews']})',
                        style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // إضافة نص يشير إلى إمكانية النقر
                  Text(
                    'اضغط لعرض التفاصيل',
                    style: TextStyle(
                      fontSize: 10,
                      color: AssetsColors.dufaultGreencolor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // زر الاستشارة
          ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AgriculturalForumScreenNew(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AssetsColors.dufaultGreencolor,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              minimumSize: const Size(60, 32),
            ),
            child: const Text(
              'استشر الآن',
              style: TextStyle(color: Colors.white, fontSize: 11),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات التواصل العامة
  Widget _buildContactInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.support_agent,
                  color: AssetsColors.dufaultGreencolor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'مركز خدمة العملاء',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildContactItem(Icons.phone, 'الخط الساخن', '+967 ************'),
            _buildContactItem(
              Icons.email,
              'البريد الإلكتروني',
              '<EMAIL>',
            ),
            _buildContactItem(Icons.access_time, 'أوقات العمل', 'متاح 24/7'),
            _buildContactItem(Icons.location_on, 'العنوان', 'صنعاء، اليمن'),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة النصائح
  Widget _buildTipsCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'نصائح مهمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildTipItem('أضف تفاصيل كافية عند طرح السؤال'),
            _buildTipItem('أرفق صوراً واضحة للمشكلة'),
            _buildTipItem('حدد نوع المحصول ومساحة الزراعة'),
            _buildTipItem('اذكر الظروف البيئية والمناخية'),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر معلومات التواصل
  Widget _buildContactItem(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AssetsColors.dufaultGreencolor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(color: Colors.grey[600], fontSize: 13),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر نصيحة
  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.orange[600],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(tip, style: const TextStyle(fontSize: 14, height: 1.4)),
          ),
        ],
      ),
    );
  }

  /// بناء شريحة معلومات الترتيب
  Widget _buildSortInfoChip(PostSortType sortType) {
    String sortText;
    IconData sortIcon;
    Color chipColor;

    switch (sortType) {
      case PostSortType.newest:
        sortText = 'ترتيب حسب الأحدث';
        sortIcon = Icons.access_time;
        chipColor = Colors.blue.shade700;
        break;
      case PostSortType.mostLiked:
        sortText = 'ترتيب حسب الأكثر إعجابًا';
        sortIcon = Icons.favorite;
        chipColor = Colors.red.shade700;
        break;
      case PostSortType.mostCommented:
        sortText = 'ترتيب حسب الأكثر تعليقًا';
        sortIcon = Icons.comment;
        chipColor = Colors.green.shade700;
        break;
      case PostSortType.mostViewed:
        sortText = 'ترتيب حسب الأكثر مشاهدة';
        sortIcon = Icons.visibility;
        chipColor = Colors.purple.shade700;
        break;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: chipColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(sortIcon, size: 20, color: chipColor),
          const SizedBox(width: 10),
          Text(
            sortText,
            style: TextStyle(
              color: chipColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريحة معلومات البحث
  Widget _buildSearchInfoChip(String query, int resultsCount) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.search, size: 18, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'نتائج البحث عن: "$query"',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'تم العثور على $resultsCount منشور',
            style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
          ),
          const SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              // سيتم تنفيذ هذا عبر الـ Cubit
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.close, size: 14, color: Colors.blue.shade700),
                const SizedBox(width: 4),
                Text(
                  'إلغاء البحث',
                  style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
