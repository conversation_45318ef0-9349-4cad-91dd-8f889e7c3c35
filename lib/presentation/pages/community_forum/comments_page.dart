import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/constants_url.dart';
import '../../../core/utils/extensions/date_time_extensions.dart';
import '../../../data/models/community_forum/comment_model.dart';
import '../../../data/models/community_forum/post_model.dart';
import '../../bloc/auth/core/auth_cubit.dart';
import '../../bloc/community_forum/posts_cubit.dart';
import '../../bloc/community_forum/posts_state.dart';
import '../../widgets/shared/cachd_net_image.dart';
import '../../widgets/shared/circular_progress.dart';
import '../../widgets/shared/dialogs/confirmation_dialog.dart';

/// صفحة التعليقات
///
/// تستخدم هذه الصفحة لعرض جميع التعليقات على منشور وإضافة تعليقات جديدة.
class CommentsPage extends StatefulWidget {
  /// المنشور الذي يتم عرض تعليقاته
  final PostModel post;

  /// منشئ صفحة التعليقات
  const CommentsPage({
    super.key,
    required this.post,
  });

  @override
  State<CommentsPage> createState() => _CommentsPageState();
}

class _CommentsPageState extends State<CommentsPage> {
  /// تحكم في حقل إدخال التعليق
  final TextEditingController _commentController = TextEditingController();

  /// تحكم في التمرير
  final ScrollController _scrollController = ScrollController();

  /// مؤشر ما إذا كان يتم إرسال تعليق
  bool _isSubmitting = false;
  bool _isFirstLoad = true;

  @override
  void initState() {
    super.initState();

    // إضافة مستمع للتمرير لتحميل المزيد من التعليقات عند الوصول إلى نهاية القائمة
    _scrollController.addListener(_onScroll);

    // تحميل التعليقات مرة واحدة فقط عند تهيئة الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // إنشاء كيوبت جديد خاص بصفحة التعليقات
      final postsCubit = context.read<PostsCubit>();

      // تحميل التعليقات مباشرة
      print('جاري تحميل التعليقات من initState للمنشور: ${widget.post.id}');
      postsCubit.getComments(widget.post.id, refresh: true);
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// دالة تستدعى عند التمرير
  void _onScroll() {
    // إذا وصلنا إلى نهاية القائمة، نحمل المزيد من التعليقات
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<PostsCubit>().state;
      if (state is CommentsLoaded &&
          state.postId == widget.post.id &&
          !state.hasReachedMax) {
        context.read<PostsCubit>().getComments(widget.post.id);
      }
    }
  }

  /// إرسال تعليق جديد
  Future<void> _submitComment() async {
    final text = _commentController.text.trim();
    if (text.isEmpty) return;

    // مسح حقل الإدخال فورًا
    _commentController.clear();

    // الحصول على معلومات المستخدم الحالي
    final userId = uid;
    final authCubit = context.read<AuthCubit>();
    final userName = authCubit.currentUser?.name ?? 'مستخدم';
    final userImage = authCubit.currentUser?.image ?? '';

    // إضافة التعليق
    await context.read<PostsCubit>().addComment(
          postId: widget.post.id,
          userId: userId,
          userName: userName,
          userImage: userImage,
          text: text,
        );
  }

  /// حذف تعليق
  Future<void> _deleteComment(String commentId) async {
    // استخدام حوار التأكيد المشترك
    await ConfirmationDialog.show(
      context: context,
      title: 'حذف التعليق',
      content: 'هل أنت متأكد من حذف هذا التعليق؟',
      confirmButtonText: 'حذف',
      cancelButtonText: 'إلغاء',
      confirmButtonColor: Colors.red,
      confirmButtonIcon: Icons.delete,
      onConfirm: () async {
        await context.read<PostsCubit>().deleteComment(widget.post.id, commentId, uid);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التعليقات'),
      ),
      body: Column(
        children: [
          // عرض معلومات المنشور
          _buildPostHeader(),

          // عرض التعليقات
          Expanded(
            child: BlocBuilder<PostsCubit, PostsState>(
              builder: (context, state) {
                // إذا كانت التعليقات قيد التحميل، نعرض مؤشر تحميل
                if (state is CommentsLoading &&
                    state.postId == widget.post.id) {
                  // نعرض مؤشر تحميل مع رسالة
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgress(),
                        SizedBox(height: 16),
                        Text('جاري تحميل التعليقات...'),
                      ],
                    ),
                  );
                }

                // إذا كانت التعليقات قيد الإضافة أو الحذف، نعرض رسالة انتظار
                if ((state is CommentAdding &&
                        (state).postId == widget.post.id) ||
                    (state is CommentDeleting &&
                        (state).postId == widget.post.id) ||
                    (state is CommentAdded &&
                        (state).postId == widget.post.id) ||
                    (state is CommentDeleted &&
                        (state).postId == widget.post.id)) {
                  // نعرض رسالة انتظار حتى تكتمل العملية
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgress(),
                        SizedBox(height: 16),
                        Text('جاري معالجة التعليق...'),
                      ],
                    ),
                  );
                }

                // إذا حدث خطأ في تحميل التعليقات، نعرض رسالة الخطأ
                if (state is CommentsError && state.postId == widget.post.id) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'خطأ في تحميل التعليقات',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.red,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () =>
                              context.read<PostsCubit>().getComments(
                                    widget.post.id,
                                    refresh: true,
                                  ),
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                // إذا تم تحميل التعليقات بنجاح، نعرضها
                if (state is CommentsLoaded && state.postId == widget.post.id) {
                  print(
                      'تم تحميل التعليقات بنجاح: ${state.comments.length} تعليق');
                  final comments = state.comments;

                  // إذا لم يكن هناك تعليقات، نعرض رسالة
                  if (comments.isEmpty) {
                    return const Center(
                      child: Text('لا توجد تعليقات حتى الآن'),
                    );
                  }

                  // عرض قائمة التعليقات
                  return RefreshIndicator(
                    onRefresh: () async {
                      await context.read<PostsCubit>().getComments(
                            widget.post.id,
                            refresh: true,
                          );
                    },
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount:
                          comments.length + (state.hasReachedMax ? 0 : 1),
                      itemBuilder: (context, index) {
                        // إذا وصلنا إلى نهاية القائمة ولم نصل إلى الحد الأقصى، نعرض مؤشر تحميل
                        if (index == comments.length) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 16),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        // عرض التعليق
                        final comment = comments[index];
                        return _buildCommentItem(comment);
                      },
                    ),
                  );
                }

                // حالة افتراضية - إذا لم يتم التعرف على الحالة
                print('حالة غير معروفة: ${state.runtimeType}');

                // إذا كانت الحالة هي PostsLoading أو PostsLoaded، نعرض التعليقات المحملة سابقًا إن وجدت
                if (state is PostsLoading || state is PostsLoaded) {
                  // نتحقق من وجود تعليقات محملة سابقًا
                  final comments = context
                      .read<PostsCubit>()
                      .getCommentsForPost(widget.post.id);
                  if (comments != null && comments.isNotEmpty) {
                    print(
                        'عرض التعليقات المحملة سابقًا: ${comments.length} تعليق');
                    return RefreshIndicator(
                      onRefresh: () async {
                        await context.read<PostsCubit>().getComments(
                              widget.post.id,
                              refresh: true,
                            );
                      },
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: comments.length,
                        itemBuilder: (context, index) {
                          final comment = comments[index];
                          return _buildCommentItem(comment);
                        },
                      ),
                    );
                  }

                  // إذا لم تكن هناك تعليقات محملة سابقًا، نعرض رسالة انتظار
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgress(),
                        SizedBox(height: 16),
                        Text('جاري تحميل المنشورات...'),
                      ],
                    ),
                  );
                }

                // محاولة تحميل التعليقات مرة أخرى بعد تأخير قصير
                Future.delayed(const Duration(seconds: 1), () {
                  if (mounted) {
                    context
                        .read<PostsCubit>()
                        .getComments(widget.post.id, refresh: true);
                  }
                });

                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgress(),
                      SizedBox(height: 16),
                      Text('جاري تحميل التعليقات...'),
                    ],
                  ),
                );
              },
            ),
          ),

          // حقل إدخال التعليق
          _buildCommentInput(),
        ],
      ),
    );
  }

  /// بناء رأس المنشور
  Widget _buildPostHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم
          ClipOval(
            child: CachedNetImage(
              imageUrl: widget.post.userImage,
              width: 40,
              height: 40,
              fit: BoxFit.cover,
              lazyLoad: true,
              memCacheWidth: 80,
              memCacheHeight: 80,
            ),
          ),
          const SizedBox(width: 12),

          // معلومات المنشور
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم المستخدم والتاريخ
                Row(
                  children: [
                    Text(
                      widget.post.userName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateTime.parse(widget.post.createdAt).dateTime,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),

                // نص المنشور
                if (widget.post.text != null && widget.post.text!.isNotEmpty)
                  Text(
                    widget.post.text!,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),

                // الوسوم (هاشتاج)
                if (widget.post.hashtags.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: widget.post.hashtags
                          .map((tag) => _buildHashtagChip(tag))
                          .toList(),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر تعليق بتصميم جديد كرسالة محادثة
  Widget _buildCommentItem(CommentModel comment) {
    // تحويل التاريخ إلى كائن DateTime
    final DateTime commentDate = DateTime.parse(comment.createdAt);

    // التحقق مما إذا كان المستخدم الحالي هو صاحب التعليق فقط
    final bool canDelete = comment.userId == uid;

    // تحديد ما إذا كان التعليق من المستخدم الحالي
    final bool isCurrentUser = comment.userId == uid;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        // تغيير اتجاه الصف بناءً على ما إذا كان التعليق من المستخدم الحالي
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isCurrentUser) ...[
            // إذا لم يكن التعليق من المستخدم الحالي، نضع الصورة في اليسار
            // صورة المستخدم بحجم أصغر
            ClipOval(
              child: CachedNetImage(
                imageUrl: comment.userImage,
                width: 25, // حجم أصغر
                height: 25, // حجم أصغر
                fit: BoxFit.cover,
                lazyLoad: true,
                memCacheWidth: 60,
                memCacheHeight: 60,
              ),
            ),
            const SizedBox(width: 8),
          ],

          // محتوى التعليق كرسالة محادثة
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isCurrentUser
                    ? AssetsColors.primary.withOpacity(0.1)
                    : Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: isCurrentUser
                      ? const Radius.circular(16)
                      : const Radius.circular(4),
                  bottomRight: isCurrentUser
                      ? const Radius.circular(4)
                      : const Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: isCurrentUser
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  // اسم المستخدم
                  Text(
                    comment.userName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: isCurrentUser
                              ? AssetsColors.primary
                              : Colors.black87,
                        ),
                  ),
                  const SizedBox(height: 4),

                  // نص التعليق
                  Text(
                    comment.text,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 3),

                  // تاريخ التعليق في الأسفل
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        commentDate.dateTime,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                              fontSize: 10,
                            ),
                      ),

                      // زر حذف التعليق (إذا كان المستخدم هو صاحب التعليق أو صاحب المنشور)
                      if (canDelete)
                        IconButton(
                          onPressed: () => _deleteComment(comment.id),
                          icon: const Icon(
                            Icons.delete,
                            size: 14,
                            color: Colors.red,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          if (isCurrentUser) ...[
            // إذا كان التعليق من المستخدم الحالي، نضع الصورة في اليمين
            const SizedBox(width: 8),
            // صورة المستخدم بحجم أصغر
            ClipOval(
              child: CachedNetImage(
                imageUrl: comment.userImage,
                width: 25, // حجم أصغر
                height: 25, // حجم أصغر
                fit: BoxFit.cover,
                lazyLoad: true,
                memCacheWidth: 60,
                memCacheHeight: 60,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء شريحة الوسم (هاشتاج)
  Widget _buildHashtagChip(String tag) {
    return InkWell(
      onTap: () {
        // البحث عن المنشورات بنفس الوسم
        context.read<PostsCubit>().searchPosts('#$tag');
        Navigator.pop(context); // العودة إلى صفحة المنتدى
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: AssetsColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
              color: AssetsColors.primary.withOpacity(0.3), width: 1),
        ),
        child: Text(
          '#$tag',
          style: TextStyle(
              color: AssetsColors.primary,
              fontSize: 13,
              fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  /// بناء حقل إدخال التعليق
  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المستخدم
          ClipOval(
            child: CachedNetImage(
              imageUrl: context.read<AuthCubit>().currentUser?.image ?? '',
              width: 40,
              height: 40,
              fit: BoxFit.cover,
              lazyLoad: true,
              memCacheWidth: 80,
              memCacheHeight: 80,
            ),
          ),
          const SizedBox(width: 12),

          // حقل إدخال التعليق
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                hintText: 'أضف تعليقًا...',
                border: InputBorder.none,
              ),
              maxLines: null,
              textInputAction: TextInputAction.newline,
            ),
          ),

          // زر إرسال التعليق
          IconButton(
            onPressed: _isSubmitting ? null : _submitComment,
            icon: _isSubmitting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.send),
            color: AssetsColors.primary,
          ),
        ],
      ),
    );
  }
}
