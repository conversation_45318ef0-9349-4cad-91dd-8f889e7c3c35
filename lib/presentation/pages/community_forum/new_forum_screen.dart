import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/headings.dart';
import 'package:agriculture/presentation/pages/community_forum/comunity_forum.dart';
import 'package:agriculture/presentation/pages/reach_engineer/reach_engineer.dart';

class ForumScreen extends StatefulWidget {
  const ForumScreen({Key? key}) : super(key: key);

  @override
  State<ForumScreen> createState() => _ForumScreenState();
}

class _ForumScreenState extends State<ForumScreen> {
  // حالة التبويب الحالي (المنتدى المجتمعي أو المرشد الزراعي)
  // جعل المنتدى المجتمعي مختارًا دائمًا
  bool _isForumTab = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: Text(Headings.comunityForum),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // شريط التنقل الداخلي للمنتدى
          Container(
            color: Colors.teal.shade50,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTabButton(
                  context,
                  "المنتدى المجتمعي",
                  Icons.groups,
                  _isForumTab,
                  () {
                    // الانتقال إلى الواجهة القديمة للمنتدى
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ComunityForum(),
                      ),
                    );
                  },
                ),
                _buildTabButton(
                  context,
                  "المرشد الزراعي",
                  Icons.support_agent,
                  false, // دائمًا غير مختار
                  () {
                    // التنقل إلى صفحة المرشد الزراعي مباشرة
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ReachEngineer(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // محتوى المنتدى
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: 10,
              itemBuilder: (context, index) {
                return _buildForumPost(context, index);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // إضافة منشور جديد
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("سيتم إضافة منشور جديد"),
              duration: Duration(seconds: 2),
            ),
          );
        },
        backgroundColor: AssetsColors.dufaultGreencolor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabButton(
    BuildContext context,
    String title,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.teal : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.teal,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.teal,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForumPost(BuildContext context, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.teal.shade200,
                  child: const Icon(Icons.person, color: Colors.white),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "أحمد محمد",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      "منذ ${index + 1} ساعات",
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              index % 2 == 0
                  ? "كيف يمكنني التعامل مع آفة الذبابة البيضاء التي تهاجم محصول الطماطم؟"
                  : "ما هي أفضل طرق ري محصول القمح في المناطق الجافة؟",
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                Chip(
                  label: Text(index % 2 == 0 ? "طماطم" : "قمح"),
                  backgroundColor: Colors.green.shade100,
                ),
                Chip(
                  label: Text(index % 2 == 0 ? "آفات" : "ري"),
                  backgroundColor: Colors.red.shade100,
                ),
                Chip(
                  label: Text(index % 2 == 0 ? "مكافحة" : "زراعة"),
                  backgroundColor: Colors.blue.shade100,
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(Icons.thumb_up_outlined, "إعجاب", () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("تم تسجيل الإعجاب"),
                      duration: Duration(seconds: 1),
                    ),
                  );
                }),
                _buildActionButton(Icons.comment_outlined, "تعليق", () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("فتح التعليقات"),
                      duration: Duration(seconds: 1),
                    ),
                  );
                }),
                _buildActionButton(Icons.share_outlined, "مشاركة", () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("مشاركة المنشور"),
                      duration: Duration(seconds: 1),
                    ),
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    IconData icon,
    String label,
    VoidCallback onPressed,
  ) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.grey),
      label: Text(
        label,
        style: const TextStyle(color: Colors.grey),
      ),
    );
  }
}
