import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/widgets/shared/floating_action_button/index.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import '../education/widgets/advisor_header.dart';
import '../education/widgets/advisor_consultation_card.dart' as local_widgets;

/// واجهة الاستشاري الزراعي الافتراضية المحسنة
/// 
/// هذه الواجهة مخصصة للمرشد الزراعي نفسه لعرض جميع الطلبات المرسلة من المزارعين
/// تشمل: الاستشارة الفورية، حجز المواعيد، مراقبة النبات
/// 
/// الميزات المحسنة:
/// ✅ نظام Cache ذكي للبيانات
/// ✅ التحديث التلقائي كل 30 ثانية
/// ✅ معالجة الأخطاء المتقدمة
/// ✅ واجهة مستخدم محسنة
/// ✅ البيانات الحقيقية من Firebase
class AdvisorMainScreen extends StatefulWidget {
  const AdvisorMainScreen({super.key});

  @override
  State<AdvisorMainScreen> createState() => _AdvisorMainScreenState();
}

class _AdvisorMainScreenState extends State<AdvisorMainScreen> {
  int _currentIndex = 0;
  String _currentAdvisorId = 'general_advisor';
  bool _isLoading = true;

  // نظام cache ذكي لتجنب التحديث غير الضروري
  DateTime? _lastDataUpdate;
  List<ConsultationModel>? _cachedConsultations;
  static const Duration _cacheValidDuration = Duration(minutes: 2);

  @override
  void initState() {
    super.initState();
    _loadAdvisorData();
    _setupNotificationListener();
  }

  /// إعداد مستمع الإشعارات للاستشارات الجديدة
  void _setupNotificationListener() {
    // تحديث البيانات كل 30 ثانية للتحقق من الاستشارات الجديدة
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadAllRequests();
      } else {
        timer.cancel();
      }
    });
  }

  /// تحميل بيانات المرشد والطلبات
  Future<void> _loadAdvisorData() async {
    try {
      // استخدام معرف المرشد العام الثابت لتطابق مع الاستشارات المرسلة
      debugPrint('🔍 تحديد معرف المرشد...');

      setState(() {
        _currentAdvisorId = 'general_advisor'; // ✅ توحيد مع الإرسال
      });

      debugPrint('✅ تم تحديد معرف المرشد: $_currentAdvisorId');

      // تحميل جميع أنواع الطلبات
      await _loadAllRequests();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المرشد: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// فحص صحة Cache
  bool _isCacheValid() {
    if (_lastDataUpdate == null) return false;
    return DateTime.now().difference(_lastDataUpdate!) < _cacheValidDuration;
  }

  /// تحميل جميع الطلبات مع نظام cache ذكي ومحسن
  Future<void> _loadAllRequests({bool forceRefresh = false}) async {
    try {
      // إذا كان Cache صالح ولا نريد تحديث قسري، استخدم البيانات المحفوظة
      if (!forceRefresh && _isCacheValid() && _cachedConsultations != null) {
        debugPrint(
          '📦 استخدام البيانات المحفوظة - آخر تحديث: $_lastDataUpdate',
        );
        return;
      }

      debugPrint('🔍 بدء تحميل الاستشارات للمرشد: $_currentAdvisorId');

      // إظهار مؤشر التحميل فقط للتحديث الأول
      if (_lastDataUpdate == null) {
        setState(() {
          _isLoading = true;
        });
      }

      // تحميل الاستشارات للمرشد الحقيقي
      await context.read<AdvisorCubit>().getAdvisorConsultations(
        advisorId: _currentAdvisorId,
      );

      // تحديث وقت آخر تحديث
      _lastDataUpdate = DateTime.now();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      debugPrint('✅ تم تحميل الاستشارات للمرشد: $_currentAdvisorId');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الطلبات: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // إذا فشل التحميل وكان لدينا بيانات محفوظة، استخدمها
      if (_cachedConsultations != null) {
        debugPrint('📦 استخدام البيانات المحفوظة بسبب فشل التحميل');
        // عرض رسالة تنبيه للمستخدم
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم عرض البيانات المحفوظة - تحقق من الاتصال'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                onPressed: () => _loadAllRequests(forceRefresh: true),
              ),
            ),
          );
        }
      }
    }
  }

  /// تغيير الصفحة الحالية
  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: () => _showAdvisorProfile(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.white, Colors.white.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: Center(
                  child: Text(
                    'م',
                    style: TextStyle(
                      color: AssetsColors.dufaultGreencolor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'المرشد الزراعي',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'متصل الآن',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
        backgroundColor: AssetsColors.dufaultGreencolor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // أيقونة الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showAdvisorSettings(),
            tooltip: 'إعدادات المرشد',
          ),
          // أيقونة الإشعارات
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () => _showNotifications(),
                tooltip: 'الإشعارات',
              ),
              // نقطة حمراء للإشعارات الجديدة
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<AdvisorCubit, AdvisorState>(
        listener: (context, state) {
          debugPrint('🔄 تغيير حالة المرشد: ${state.runtimeType}');

          if (state is ConsultationResponseSuccess) {
            _showSuccessMessage(state.message);
          } else if (state is ConsultationsLoaded) {
            debugPrint('✅ تم تحميل ${state.consultations.length} استشارة');

            // حفظ البيانات في Cache
            _cachedConsultations = List.from(state.consultations);
            _lastDataUpdate = DateTime.now();

            // إخفاء مؤشر التحميل
            if (_isLoading) {
              setState(() {
                _isLoading = false;
              });
            }

            debugPrint('📦 تم حفظ البيانات في Cache');
          } else if (state is AdvisorError) {
            debugPrint('❌ خطأ في المرشد: ${state.message}');
            _showErrorMessage(state.message);

            // إخفاء مؤشر التحميل
            if (_isLoading) {
              setState(() {
                _isLoading = false;
              });
            }
          } else if (state is ConsultationCreated) {
            _showSuccessMessage('تم إنشاء الاستشارة بنجاح');
          } else if (state is AppointmentBooked) {
            _showSuccessMessage('تم حجز الموعد بنجاح');
          }
        },
        builder: (context, state) {
          // إظهار مؤشر التحميل فقط إذا لم تكن هناك بيانات محفوظة
          if (_isLoading && _cachedConsultations == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.dufaultGreencolor),
                  ),
                  const SizedBox(height: 16),
                  const Text('جاري تحميل الاستشارات...'),
                ],
              ),
            );
          }

          return _buildCurrentPage(state);
        },
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      // ✅ إضافة الزر العائم الذكي مع الاحتفاظ بالزر الأصلي
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // الزر العائم الذكي الجديد
          const SmartFloatingActionButton(),
          const SizedBox(height: 16),
          // الزر العائم الأصلي للتحديث
          FloatingActionButton(
            onPressed: () => _loadAllRequests(forceRefresh: true),
            backgroundColor: AssetsColors.dufaultGreencolor,
            tooltip: 'تحديث البيانات',
            child: const Icon(Icons.refresh),
          ),
        ],
      ),
    );
  }

  /// بناء الصفحة الحالية
  Widget _buildCurrentPage(AdvisorState state) {
    return IndexedStack(
      index: _currentIndex,
      children: [
        _buildDashboardPage(state),
        _buildConsultationsPage(state),
        _buildAppointmentsPage(state),
        _buildPlantMonitoringPage(state),
        _buildReportsPage(state),
      ],
    );
  }

  /// بناء صفحة لوحة التحكم
  Widget _buildDashboardPage(AdvisorState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AdvisorHeader(
            advisorId: _currentAdvisorId,
            advisorName: 'المرشد العام',
            advisorSpecialty: 'لديك طلبات جديدة تحتاج للمراجعة',
          ),
          const SizedBox(height: 20),
          _buildQuickStats(state),
          const SizedBox(height: 20),
          _buildRecentActivity(state),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(AdvisorState state) {
    int consultationsCount = 0;
    if (state is ConsultationsLoaded) {
      consultationsCount = state.consultations.length;
    } else if (_cachedConsultations != null) {
      consultationsCount = _cachedConsultations!.length;
    }

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'الاستشارات اليوم',
            '$consultationsCount',
            Icons.chat_bubble,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'المواعيد المحجوزة',
            '3',
            Icons.calendar_today,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء النشاط الأخير
  Widget _buildRecentActivity(AdvisorState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'النشاط الأخير',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildActivityItem(
                'استشارة جديدة من أحمد محمد',
                'منذ 5 دقائق',
                Icons.chat_bubble,
                Colors.green,
              ),
              const Divider(),
              _buildActivityItem(
                'تم حجز موعد مع سارة أحمد',
                'منذ 15 دقيقة',
                Icons.calendar_today,
                Colors.blue,
              ),
              const Divider(),
              _buildActivityItem(
                'تحديث حالة النبات - الطماطم',
                'منذ 30 دقيقة',
                Icons.eco,
                Colors.orange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء عنصر النشاط
  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صفحة الاستشارات
  Widget _buildConsultationsPage(AdvisorState state) {
    List<ConsultationModel> consultations = [];

    // استخدام البيانات من الحالة أو من Cache
    if (state is ConsultationsLoaded) {
      consultations = state.consultations;
    } else if (_cachedConsultations != null) {
      consultations = _cachedConsultations!;
    }

    if (consultations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد استشارات حالياً\nسيتم التحديث تلقائياً كل 30 ثانية',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadAllRequests(forceRefresh: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
              ),
              child: const Text('تحديث الآن'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadAllRequests(forceRefresh: true),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: consultations.length,
        itemBuilder: (context, index) {
          final consultation = consultations[index];
          return local_widgets.AdvisorConsultationCard(
            consultation: consultation,
            isAdvisor: true,
            onTap: () => _viewConsultationDetails(consultation),
            onReply: () => _replyToConsultation(consultation),
            onUserImageTap: () => _showFarmerProfile(consultation),
          );
        },
      ),
    );
  }

  /// بناء صفحة المواعيد
  Widget _buildAppointmentsPage(AdvisorState state) {
    return const Center(
      child: Text(
        'صفحة المواعيد\n(سيتم تطويرها لاحقاً)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  /// بناء صفحة مراقبة النبات
  Widget _buildPlantMonitoringPage(AdvisorState state) {
    return const Center(
      child: Text(
        'صفحة مراقبة النبات\n(سيتم تطويرها لاحقاً)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  /// بناء صفحة التقارير
  Widget _buildReportsPage(AdvisorState state) {
    return const Center(
      child: Text(
        'صفحة التقارير\n(سيتم تطويرها لاحقاً)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _currentIndex,
      onTap: _onPageChanged,
      selectedItemColor: AssetsColors.dufaultGreencolor,
      unselectedItemColor: Colors.grey,
      backgroundColor: Colors.white,
      elevation: 8,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'لوحة التحكم',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble),
          label: 'الاستشارات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today),
          label: 'المواعيد',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.eco),
          label: 'مراقبة النبات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      ],
    );
  }

  /// عرض تفاصيل الاستشارة
  void _viewConsultationDetails(ConsultationModel consultation) {
    debugPrint('عرض تفاصيل الاستشارة: ${consultation.id}');
    // TODO: تنفيذ عرض تفاصيل الاستشارة
  }

  /// الرد على الاستشارة
  void _replyToConsultation(ConsultationModel consultation) {
    debugPrint('الرد على الاستشارة: ${consultation.id}');
    // TODO: تنفيذ الرد على الاستشارة
  }

  /// عرض ملف المزارع
  void _showFarmerProfile(ConsultationModel consultation) {
    debugPrint('عرض ملف المزارع: ${consultation.userName}');
    // TODO: تنفيذ عرض ملف المزارع
  }

  /// عرض ملف المرشد الشخصي
  void _showAdvisorProfile() {
    debugPrint('عرض ملف المرشد الشخصي');
    // TODO: تنفيذ عرض ملف المرشد
  }

  /// عرض إعدادات المرشد
  void _showAdvisorSettings() {
    debugPrint('عرض إعدادات المرشد');
    // TODO: تنفيذ إعدادات المرشد
  }

  /// عرض الإشعارات
  void _showNotifications() {
    debugPrint('عرض الإشعارات');
    // TODO: تنفيذ عرض الإشعارات
  }
}
