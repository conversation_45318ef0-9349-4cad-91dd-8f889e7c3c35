import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'widgets/index.dart';

/// صفحة تقارير المرشد الزراعي
/// 
/// تعرض تقارير شاملة عن أداء المرشد والإحصائيات
/// تشمل: تقارير الأداء، الإحصائيات، التحليلات
/// 
/// المعايير المطبقة:
/// ✅ Clean Architecture - فصل منطق العمل
/// ✅ Cubit لإدارة الحالة
/// ✅ widgets منفصلة للمكونات
/// ✅ تعليقات عربية شاملة
/// ✅ تجزئة الوظائف لوحدات صغيرة
class AdvisorReportsPage extends StatefulWidget {
  /// معرف المرشد
  final String advisorId;

  const AdvisorReportsPage({
    super.key,
    required this.advisorId,
  });

  @override
  State<AdvisorReportsPage> createState() => _AdvisorReportsPageState();
}

class _AdvisorReportsPageState extends State<AdvisorReportsPage>
    with SingleTickerProviderStateMixin {
  
  /// متحكم التبويبات
  late TabController _tabController;
  
  /// فترة التقرير المحددة
  String _reportPeriod = 'monthly';
  
  /// تاريخ بداية التقرير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  
  /// تاريخ نهاية التقرير
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل التقارير
  /// 
  /// يحمل جميع التقارير والإحصائيات للمرشد
  Future<void> _loadReports() async {
    try {
      // TODO: تنفيذ تحميل التقارير
      // context.read<AdvisorCubit>().getAdvisorReports(
      //   advisorId: widget.advisorId,
      //   startDate: _startDate,
      //   endDate: _endDate,
      // );
      debugPrint('تحميل التقارير للمرشد: ${widget.advisorId}');
    } catch (e) {
      debugPrint('خطأ في تحميل التقارير: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الأداء'),
            Tab(text: 'الإحصائيات'),
            Tab(text: 'التحليلات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangePicker,
            tooltip: 'اختيار الفترة',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportReports,
            tooltip: 'تصدير التقارير',
          ),
        ],
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          return TabBarView(
            controller: _tabController,
            children: [
              // تقارير الأداء
              AdvisorPerformanceReports(
                advisorId: widget.advisorId,
                startDate: _startDate,
                endDate: _endDate,
                onRefresh: _loadReports,
                onViewDetails: _viewPerformanceDetails,
              ),

              // الإحصائيات
              AdvisorStatisticsReports(
                advisorId: widget.advisorId,
                startDate: _startDate,
                endDate: _endDate,
                onRefresh: _loadReports,
                onViewChart: _viewStatisticsChart,
              ),

              // التحليلات
              AdvisorAnalyticsReports(
                advisorId: widget.advisorId,
                startDate: _startDate,
                endDate: _endDate,
                onRefresh: _loadReports,
                onViewInsights: _viewAnalyticsInsights,
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _generateCustomReport,
        backgroundColor: AssetsColors.dufaultGreencolor,
        tooltip: 'تقرير مخصص',
        child: const Icon(Icons.add_chart),
      ),
    );
  }

  /// عرض منتقي نطاق التاريخ
  /// 
  /// يفتح منتقي نطاق التاريخ لاختيار فترة التقرير
  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );
    
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadReports();
    }
  }

  /// تصدير التقارير
  /// 
  /// يصدر التقارير بصيغة PDF أو Excel
  void _exportReports() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقارير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('تصدير PDF'),
              onTap: () {
                Navigator.pop(context);
                _exportToPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('تصدير Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تصدير إلى PDF
  /// 
  /// يصدر التقارير بصيغة PDF
  void _exportToPDF() {
    // TODO: تنفيذ تصدير PDF
    debugPrint('تصدير التقارير إلى PDF');
  }

  /// تصدير إلى Excel
  /// 
  /// يصدر التقارير بصيغة Excel
  void _exportToExcel() {
    // TODO: تنفيذ تصدير Excel
    debugPrint('تصدير التقارير إلى Excel');
  }

  /// إنشاء تقرير مخصص
  /// 
  /// يفتح نموذج إنشاء تقرير مخصص
  void _generateCustomReport() {
    // TODO: تنفيذ إنشاء تقرير مخصص
    debugPrint('إنشاء تقرير مخصص');
  }

  /// عرض تفاصيل الأداء
  /// 
  /// يفتح صفحة تفاصيل الأداء
  void _viewPerformanceDetails(String performanceType) {
    // TODO: تنفيذ عرض تفاصيل الأداء
    debugPrint('عرض تفاصيل الأداء: $performanceType');
  }

  /// عرض مخطط الإحصائيات
  /// 
  /// يفتح صفحة مخطط الإحصائيات
  void _viewStatisticsChart(String chartType) {
    // TODO: تنفيذ عرض مخطط الإحصائيات
    debugPrint('عرض مخطط الإحصائيات: $chartType');
  }

  /// عرض رؤى التحليلات
  /// 
  /// يفتح صفحة رؤى التحليلات
  void _viewAnalyticsInsights(String insightType) {
    // TODO: تنفيذ عرض رؤى التحليلات
    debugPrint('عرض رؤى التحليلات: $insightType');
  }
}
