import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// تقارير أداء المرشد الزراعي
///
/// تعرض تقارير مفصلة عن أداء المرشد
class AdvisorPerformanceReports extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// تاريخ البداية
  final DateTime startDate;
  
  /// تاريخ النهاية
  final DateTime endDate;
  
  /// دالة التحديث
  final VoidCallback? onRefresh;
  
  /// دالة عرض التفاصيل
  final Function(String)? onViewDetails;

  const AdvisorPerformanceReports({
    super.key,
    required this.advisorId,
    required this.startDate,
    required this.endDate,
    this.onRefresh,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقارير الأداء',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // مؤشرات الأداء الرئيسية
            _buildPerformanceMetrics(),
            
            const SizedBox(height: 20),
            
            // تقييم الأداء
            _buildPerformanceRating(),
            
            const SizedBox(height: 20),
            
            // الأهداف والإنجازات
            _buildGoalsAndAchievements(),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشرات الأداء
  Widget _buildPerformanceMetrics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مؤشرات الأداء الرئيسية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'الاستشارات المجابة',
                '25',
                '↗️ +15%',
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'متوسط وقت الرد',
                '2.5 ساعة',
                '↘️ -30%',
                Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'رضا المزارعين',
                '4.8/5',
                '↗️ +0.3',
                Colors.amber,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'معدل الحل',
                '92%',
                '↗️ +5%',
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة مؤشر
  Widget _buildMetricCard(String title, String value, String change, Color color) {
    return GestureDetector(
      onTap: () => onViewDetails?.call(title),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              change,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تقييم الأداء
  Widget _buildPerformanceRating() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تقييم الأداء العام',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ممتاز'),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: 0.9,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AssetsColors.dufaultGreencolor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '90%',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.dufaultGreencolor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء الأهداف والإنجازات
  Widget _buildGoalsAndAchievements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأهداف والإنجازات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildGoalItem('الرد على 30 استشارة', 25, 30, Colors.green),
        const SizedBox(height: 8),
        _buildGoalItem('تحقيق تقييم 4.5+', 4.8, 4.5, Colors.amber),
        const SizedBox(height: 8),
        _buildGoalItem('تقليل وقت الرد إلى 3 ساعات', 2.5, 3.0, Colors.blue),
      ],
    );
  }

  /// بناء عنصر هدف
  Widget _buildGoalItem(String title, double current, double target, Color color) {
    final progress = (current / target).clamp(0.0, 1.0);
    final isAchieved = current >= target;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ),
              Icon(
                isAchieved ? Icons.check_circle : Icons.radio_button_unchecked,
                color: isAchieved ? Colors.green : Colors.grey,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: 4),
          Text(
            '${current.toString()} / ${target.toString()}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
