import 'package:flutter/material.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';

/// قائمة استشارات المرشد الزراعي
///
/// تعرض قائمة الاستشارات مع إمكانيات التفاعل
class AdvisorConsultationsList extends StatelessWidget {
  /// قائمة الاستشارات
  final List<ConsultationModel> consultations;
  
  /// رسالة الحالة الفارغة
  final String emptyMessage;
  
  /// دالة التحديث
  final Future<void> Function() onRefresh;
  
  /// دالة النقر على الاستشارة
  final Function(ConsultationModel) onConsultationTap;
  
  /// دالة الرد على الاستشارة
  final Function(ConsultationModel) onReply;
  
  /// دالة تحديث حالة الاستشارة
  final Function(ConsultationModel, ConsultationStatus) onUpdateStatus;
  
  /// دالة عرض ملف المزارع
  final Function(ConsultationModel) onShowFarmerProfile;

  const AdvisorConsultationsList({
    super.key,
    required this.consultations,
    required this.emptyMessage,
    required this.onRefresh,
    required this.onConsultationTap,
    required this.onReply,
    required this.onUpdateStatus,
    required this.onShowFarmerProfile,
  });

  @override
  Widget build(BuildContext context) {
    if (consultations.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: consultations.length,
        itemBuilder: (context, index) {
          final consultation = consultations[index];
          return _buildConsultationCard(consultation);
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRefresh,
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الاستشارة
  Widget _buildConsultationCard(ConsultationModel consultation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onConsultationTap(consultation),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  GestureDetector(
                    onTap: () => onShowFarmerProfile(consultation),
                    child: CircleAvatar(
                      backgroundColor: Colors.green,
                      child: Text(
                        consultation.userName.isNotEmpty 
                            ? consultation.userName[0].toUpperCase()
                            : 'م',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          consultation.userName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          _formatDate(consultation.createdAt),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(consultation.status),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // محتوى الاستشارة
              Text(
                'نوع المحصول: ${consultation.cropType}',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                consultation.problemDescription,
                style: const TextStyle(fontSize: 14),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // أزرار الإجراءات
              _buildActionButtons(consultation),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(ConsultationStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case ConsultationStatus.pending:
        color = Colors.orange;
        text = 'جديدة';
        break;
      case ConsultationStatus.inProgress:
        color = Colors.blue;
        text = 'قيد المعالجة';
        break;
      case ConsultationStatus.answered:
        color = Colors.green;
        text = 'مجابة';
        break;
      case ConsultationStatus.closed:
        color = Colors.grey;
        text = 'مغلقة';
        break;
      case ConsultationStatus.cancelled:
        color = Colors.red;
        text = 'ملغية';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(ConsultationModel consultation) {
    List<Widget> buttons = [];
    
    if (consultation.status == ConsultationStatus.pending) {
      buttons.addAll([
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => onReply(consultation),
            icon: const Icon(Icons.reply, size: 16),
            label: const Text('رد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => onUpdateStatus(consultation, ConsultationStatus.inProgress),
            icon: const Icon(Icons.hourglass_empty, size: 16),
            label: const Text('قيد المعالجة'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
        ),
      ]);
    } else if (consultation.status == ConsultationStatus.inProgress) {
      buttons.addAll([
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => onReply(consultation),
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('تحديث الرد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => onUpdateStatus(consultation, ConsultationStatus.answered),
            icon: const Icon(Icons.check_circle, size: 16),
            label: const Text('إنهاء'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
        ),
      ]);
    }
    
    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Row(children: buttons);
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);
      
      if (difference.inDays > 0) {
        return 'منذ ${difference.inDays} يوم';
      } else if (difference.inHours > 0) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inMinutes > 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else {
        return 'الآن';
      }
    } catch (e) {
      return dateString;
    }
  }
}
