import 'package:flutter/material.dart';

/// تقارير تحليلات المرشد الزراعي
///
/// تعرض تحليلات متقدمة ورؤى ذكية
class AdvisorAnalyticsReports extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// تاريخ البداية
  final DateTime startDate;
  
  /// تاريخ النهاية
  final DateTime endDate;
  
  /// دالة التحديث
  final VoidCallback? onRefresh;
  
  /// دالة عرض الرؤى
  final Function(String)? onViewInsights;

  const AdvisorAnalyticsReports({
    super.key,
    required this.advisorId,
    required this.startDate,
    required this.endDate,
    this.onRefresh,
    this.onViewInsights,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التحليلات والرؤى',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // الرؤى الذكية
            _buildSmartInsights(),
            
            const SizedBox(height: 20),
            
            // اتجاهات الأداء
            _buildPerformanceTrends(),
            
            const SizedBox(height: 20),
            
            // التوصيات
            _buildRecommendations(),
          ],
        ),
      ),
    );
  }

  /// بناء الرؤى الذكية
  Widget _buildSmartInsights() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الرؤى الذكية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildInsightCard(
          'أداء متميز',
          'تحسن أداؤك بنسبة 25% هذا الشهر مقارنة بالشهر الماضي',
          Icons.trending_up,
          Colors.green,
        ),
        const SizedBox(height: 8),
        _buildInsightCard(
          'وقت الذروة',
          'معظم الاستشارات تأتي بين الساعة 9-11 صباحاً',
          Icons.access_time,
          Colors.blue,
        ),
        const SizedBox(height: 8),
        _buildInsightCard(
          'المحصول الأكثر طلباً',
          'الطماطم هي المحصول الأكثر طلباً للاستشارة (35%)',
          Icons.eco,
          Colors.red,
        ),
      ],
    );
  }

  /// بناء بطاقة رؤية
  Widget _buildInsightCard(String title, String description, IconData icon, Color color) {
    return GestureDetector(
      onTap: () => onViewInsights?.call(title),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء اتجاهات الأداء
  Widget _buildPerformanceTrends() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اتجاهات الأداء',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              _buildTrendItem('عدد الاستشارات', '↗️ +15%', Colors.green),
              const Divider(),
              _buildTrendItem('سرعة الرد', '↗️ +30%', Colors.green),
              const Divider(),
              _buildTrendItem('رضا المزارعين', '↗️ +5%', Colors.green),
              const Divider(),
              _buildTrendItem('معدل الحل', '→ ثابت', Colors.grey),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء عنصر اتجاه
  Widget _buildTrendItem(String metric, String trend, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            metric,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            trend,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التوصيات
  Widget _buildRecommendations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التوصيات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildRecommendationCard(
          'زيادة التفاعل',
          'حاول الرد على الاستشارات خلال ساعة واحدة لتحسين رضا المزارعين',
          Icons.speed,
          Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildRecommendationCard(
          'تخصص في الطماطم',
          'نظراً لكثرة الاستشارات حول الطماطم، فكر في التخصص أكثر في هذا المجال',
          Icons.local_florist,
          Colors.red,
        ),
        const SizedBox(height: 8),
        _buildRecommendationCard(
          'ساعات الذروة',
          'كن متاحاً أكثر في الفترة الصباحية (9-11 ص) لتلبية الطلب',
          Icons.schedule,
          Colors.blue,
        ),
      ],
    );
  }

  /// بناء بطاقة توصية
  Widget _buildRecommendationCard(String title, String description, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
