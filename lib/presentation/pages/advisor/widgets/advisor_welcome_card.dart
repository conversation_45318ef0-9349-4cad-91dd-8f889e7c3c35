import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// بطاقة الترحيب للمرشد الزراعي
///
/// تعرض رسالة ترحيب وإحصائيات سريعة
class AdvisorWelcomeCard extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// دالة عرض الاستشارات
  final VoidCallback? onViewConsultations;
  
  /// دالة عرض المواعيد
  final VoidCallback? onViewAppointments;

  const AdvisorWelcomeCard({
    super.key,
    required this.advisorId,
    this.onViewConsultations,
    this.onViewAppointments,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor,
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مرحباً بك، المرشد الزراعي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'لديك استشارات جديدة في انتظارك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: onViewConsultations,
                  icon: const Icon(Icons.chat_bubble_outline, size: 18),
                  label: const Text('الاستشارات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AssetsColors.dufaultGreencolor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onViewAppointments,
                  icon: const Icon(Icons.calendar_today, size: 18),
                  label: const Text('المواعيد'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
