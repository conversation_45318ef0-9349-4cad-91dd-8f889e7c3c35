import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// شريط التطبيق للمرشد الزراعي
///
/// يحتوي على ملف المرشد الشخصي والإعدادات والإشعارات
class AdvisorAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// دالة النقر على الملف الشخصي
  final VoidCallback? onProfileTap;
  
  /// دالة النقر على الإعدادات
  final VoidCallback? onSettingsTap;
  
  /// دالة النقر على الإشعارات
  final VoidCallback? onNotificationsTap;

  const AdvisorAppBar({
    super.key,
    required this.advisorId,
    this.onProfileTap,
    this.onSettingsTap,
    this.onNotificationsTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: GestureDetector(
        onTap: onProfileTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // صورة المرشد
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.white, Colors.white.withValues(alpha: 0.8)],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Center(
                child: Text(
                  'م',
                  style: TextStyle(
                    color: AssetsColors.dufaultGreencolor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            
            // معلومات المرشد
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'المرشد الزراعي',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                Text(
                  'متصل الآن',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
      backgroundColor: AssetsColors.dufaultGreencolor,
      elevation: 0,
      centerTitle: true,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        // أيقونة الإعدادات
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: onSettingsTap,
          tooltip: 'إعدادات المرشد',
        ),
        
        // أيقونة الإشعارات مع نقطة حمراء
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: onNotificationsTap,
              tooltip: 'الإشعارات',
            ),
            // نقطة حمراء للإشعارات الجديدة
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
