import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// الزر العائم للمرشد الزراعي
///
/// يوفر إجراءات سريعة للمرشد
class AdvisorFloatingActionButton extends StatelessWidget {
  /// دالة التحديث
  final VoidCallback? onRefresh;

  const AdvisorFloatingActionButton({
    super.key,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onRefresh,
      backgroundColor: AssetsColors.dufaultGreencolor,
      tooltip: 'تحديث البيانات',
      child: const Icon(
        Icons.refresh,
        color: Colors.white,
      ),
    );
  }
}
