import 'package:flutter/material.dart';

/// تقارير إحصائيات المرشد الزراعي
///
/// تعرض إحصائيات مفصلة ومخططات
class AdvisorStatisticsReports extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;
  
  /// تاريخ البداية
  final DateTime startDate;
  
  /// تاريخ النهاية
  final DateTime endDate;
  
  /// دالة التحديث
  final VoidCallback? onRefresh;
  
  /// دالة عرض المخطط
  final Function(String)? onViewChart;

  const AdvisorStatisticsReports({
    super.key,
    required this.advisorId,
    required this.startDate,
    required this.endDate,
    this.onRefresh,
    this.onViewChart,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإحصائيات التفصيلية',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // إحصائيات الاستشارات
            _buildConsultationStats(),
            
            const SizedBox(height: 20),
            
            // إحصائيات المحاصيل
            _buildCropStats(),
            
            const SizedBox(height: 20),
            
            // إحصائيات الوقت
            _buildTimeStats(),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات الاستشارات
  Widget _buildConsultationStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات الاستشارات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              _buildStatRow('إجمالي الاستشارات', '45', Colors.blue),
              const Divider(),
              _buildStatRow('الاستشارات الجديدة', '8', Colors.orange),
              const Divider(),
              _buildStatRow('قيد المعالجة', '12', Colors.purple),
              const Divider(),
              _buildStatRow('المجابة', '25', Colors.green),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات المحاصيل
  Widget _buildCropStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المحاصيل',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              _buildStatRow('الطماطم', '15', Colors.red),
              const Divider(),
              _buildStatRow('الخيار', '10', Colors.green),
              const Divider(),
              _buildStatRow('الفلفل', '8', Colors.orange),
              const Divider(),
              _buildStatRow('الباذنجان', '5', Colors.purple),
              const Divider(),
              _buildStatRow('أخرى', '7', Colors.grey),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات الوقت
  Widget _buildTimeStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات الوقت',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.amber.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              _buildStatRow('متوسط وقت الرد', '2.5 ساعة', Colors.amber),
              const Divider(),
              _buildStatRow('أسرع رد', '15 دقيقة', Colors.green),
              const Divider(),
              _buildStatRow('أبطأ رد', '8 ساعات', Colors.red),
              const Divider(),
              _buildStatRow('ساعات العمل اليومية', '6 ساعات', Colors.blue),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
