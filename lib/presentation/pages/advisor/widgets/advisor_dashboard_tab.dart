import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// تبويب لوحة التحكم للمرشد الزراعي
///
/// يعرض نظرة عامة على أداء المرشد والإحصائيات
class AdvisorDashboardTab extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;

  const AdvisorDashboardTab({
    super.key,
    required this.advisorId,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // TODO: تنفيذ تحديث البيانات
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الترحيب
            _buildWelcomeCard(),
            
            const SizedBox(height: 20),
            
            // الإحصائيات السريعة
            _buildQuickStats(),
            
            const SizedBox(height: 20),
            
            // النشاط الأخير
            _buildRecentActivity(),
            
            const SizedBox(height: 20),
            
            // الإجراءات السريعة
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الترحيب
  Widget _buildWelcomeCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor,
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك، المرشد الزراعي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'لديك استشارات جديدة في انتظارك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildStatCard('الاستشارات الجديدة', '5', Icons.new_releases, Colors.orange)),
            const SizedBox(width: 12),
            Expanded(child: _buildStatCard('المواعيد اليوم', '3', Icons.today, Colors.blue)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildStatCard('الاستشارات المجابة', '25', Icons.check_circle, Colors.green)),
            const SizedBox(width: 12),
            Expanded(child: _buildStatCard('التقييم', '4.8', Icons.star, Colors.amber)),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء النشاط الأخير
  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'النشاط الأخير',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        // TODO: إضافة قائمة النشاط الأخير
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Text('لا يوجد نشاط حديث'),
          ),
        ),
      ],
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'عرض الاستشارات',
                Icons.chat_bubble_outline,
                AssetsColors.dufaultGreencolor,
                () {
                  // TODO: الانتقال إلى الاستشارات
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'المواعيد',
                Icons.calendar_today,
                Colors.blue,
                () {
                  // TODO: الانتقال إلى المواعيد
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
