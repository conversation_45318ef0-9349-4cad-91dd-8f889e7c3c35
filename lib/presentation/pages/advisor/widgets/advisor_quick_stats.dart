import 'package:flutter/material.dart';

import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';

/// الإحصائيات السريعة للمرشد الزراعي
///
/// تعرض إحصائيات سريعة عن الاستشارات والأداء
class AdvisorQuickStats extends StatelessWidget {
  /// حالة المرشد
  final AdvisorState state;
  
  /// دالة النقر على الإحصائية
  final Function(String)? onStatTap;

  const AdvisorQuickStats({
    super.key,
    required this.state,
    this.onStatTap,
  });

  @override
  Widget build(BuildContext context) {
    // حساب الإحصائيات من البيانات الحقيقية
    int totalConsultations = 0;
    int pendingCount = 0;
    int answeredCount = 0;
    double averageRating = 4.8;

    if (state is ConsultationsLoaded) {
      final consultations = (state as ConsultationsLoaded).consultations;
      totalConsultations = consultations.length;
      pendingCount = consultations
          .where((c) => c.status == ConsultationStatus.pending)
          .length;
      answeredCount = consultations
          .where((c) => c.status == ConsultationStatus.answered)
          .length;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الاستشارات الجديدة',
                pendingCount.toString(),
                Icons.new_releases,
                Colors.orange,
                () => onStatTap?.call('pending'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'المواعيد اليوم',
                '3', // TODO: حساب من البيانات الحقيقية
                Icons.today,
                Colors.blue,
                () => onStatTap?.call('appointments'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الاستشارات المجابة',
                answeredCount.toString(),
                Icons.check_circle,
                Colors.green,
                () => onStatTap?.call('answered'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'التقييم',
                averageRating.toString(),
                Icons.star,
                Colors.amber,
                () => onStatTap?.call('rating'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
