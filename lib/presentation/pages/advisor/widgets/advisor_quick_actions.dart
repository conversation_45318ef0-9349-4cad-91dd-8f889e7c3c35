import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// الإجراءات السريعة للمرشد الزراعي
///
/// تعرض أزرار للإجراءات السريعة
class AdvisorQuickActions extends StatelessWidget {
  /// دالة إنشاء استشارة
  final VoidCallback? onCreateConsultation;
  
  /// دالة جدولة موعد
  final VoidCallback? onScheduleAppointment;
  
  /// دالة عرض التقارير
  final VoidCallback? onViewReports;
  
  /// دالة إدارة الملف الشخصي
  final VoidCallback? onManageProfile;

  const AdvisorQuickActions({
    super.key,
    this.onCreateConsultation,
    this.onScheduleAppointment,
    this.onViewReports,
    this.onManageProfile,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'عرض الاستشارات',
                Icons.chat_bubble_outline,
                AssetsColors.dufaultGreencolor,
                onCreateConsultation,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'المواعيد',
                Icons.calendar_today,
                Colors.blue,
                onScheduleAppointment,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'التقارير',
                Icons.analytics,
                Colors.purple,
                onViewReports,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'الملف الشخصي',
                Icons.person,
                Colors.orange,
                onManageProfile,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
