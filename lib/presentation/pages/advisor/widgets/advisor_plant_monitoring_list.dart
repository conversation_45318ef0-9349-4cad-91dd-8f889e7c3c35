import 'package:flutter/material.dart';

/// قائمة مراقبة النبات للمرشد الزراعي
///
/// تعرض قائمة طلبات مراقبة النبات مع إمكانيات التفاعل
class AdvisorPlantMonitoringList extends StatelessWidget {
  /// قائمة طلبات مراقبة النبات
  final List<dynamic> requests;
  
  /// رسالة الحالة الفارغة
  final String emptyMessage;
  
  /// دالة التحديث
  final Future<void> Function() onRefresh;
  
  /// دالة النقر على الطلب
  final Function(dynamic) onRequestTap;
  
  /// دالة تحليل الطلب
  final Function(dynamic) onAnalyze;
  
  /// دالة تقديم التوصيات
  final Function(dynamic) onProvideRecommendations;
  
  /// دالة تحديث حالة الطلب
  final Function(dynamic, String) onUpdateStatus;

  const AdvisorPlantMonitoringList({
    super.key,
    required this.requests,
    required this.emptyMessage,
    required this.onRefresh,
    required this.onRequestTap,
    required this.onAnalyze,
    required this.onProvideRecommendations,
    required this.onUpdateStatus,
  });

  @override
  Widget build(BuildContext context) {
    if (requests.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: requests.length,
        itemBuilder: (context, index) {
          final request = requests[index];
          return _buildRequestCard(request);
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.eco,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRefresh,
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الطلب
  Widget _buildRequestCard(dynamic request) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onRequestTap(request),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.green,
                    child: const Icon(
                      Icons.eco,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'طلب مراقبة نبات', // TODO: استخدام البيانات الحقيقية
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'اليوم 2:30 م', // TODO: استخدام البيانات الحقيقية
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                    ),
                    child: const Text(
                      'جديد',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // تفاصيل الطلب
              const Text(
                'نوع النبات: طماطم', // TODO: استخدام البيانات الحقيقية
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                'المشكلة: اصفرار في الأوراق وبقع بنية', // TODO: استخدام البيانات الحقيقية
                style: TextStyle(fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => onAnalyze(request),
                      icon: const Icon(Icons.analytics, size: 16),
                      label: const Text('تحليل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => onProvideRecommendations(request),
                      icon: const Icon(Icons.lightbulb, size: 16),
                      label: const Text('توصيات'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
