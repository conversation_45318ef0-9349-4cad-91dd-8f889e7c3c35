import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/assets_colors.dart';

/// شريط التنقل السفلي للمرشد الزراعي
///
/// يحتوي على 5 صفحات رئيسية للمرشد
class AdvisorBottomNavBar extends StatelessWidget {
  /// الفهرس الحالي
  final int currentIndex;
  
  /// دالة تغيير الصفحة
  final Function(int) onPageChanged;

  const AdvisorBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onPageChanged,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AssetsColors.dufaultGreencolor,
      unselectedItemColor: Colors.grey,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'لوحة التحكم',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: 'الاستشارات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today),
          label: 'المواعيد',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.eco),
          label: 'مراقبة النبات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      ],
    );
  }
}
