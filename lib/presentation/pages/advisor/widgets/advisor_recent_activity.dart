import 'package:flutter/material.dart';

import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';

/// النشاط الأخير للمرشد الزراعي
///
/// يعرض آخر الأنشطة والاستشارات
class AdvisorRecentActivity extends StatelessWidget {
  /// حالة المرشد
  final AdvisorState state;
  
  /// دالة النقر على الاستشارة
  final Function(String)? onConsultationTap;
  
  /// دالة عرض الكل
  final VoidCallback? onViewAll;

  const AdvisorRecentActivity({
    super.key,
    required this.state,
    this.onConsultationTap,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    List<ConsultationModel> recentConsultations = [];

    if (state is ConsultationsLoaded) {
      // أخذ آخر 3 استشارات
      recentConsultations = (state as ConsultationsLoaded).consultations.take(3).toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'النشاط الأخير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (recentConsultations.isNotEmpty)
              TextButton(
                onPressed: onViewAll,
                child: const Text('عرض الكل'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        
        if (recentConsultations.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text('لا يوجد نشاط حديث'),
            ),
          )
        else
          ...recentConsultations.map((consultation) => 
            _buildActivityItem(consultation)
          ),
      ],
    );
  }

  /// بناء عنصر النشاط
  Widget _buildActivityItem(ConsultationModel consultation) {
    Color statusColor;
    IconData statusIcon;
    
    switch (consultation.status) {
      case ConsultationStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.new_releases;
        break;
      case ConsultationStatus.inProgress:
        statusColor = Colors.blue;
        statusIcon = Icons.hourglass_empty;
        break;
      case ConsultationStatus.answered:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case ConsultationStatus.closed:
        statusColor = Colors.grey;
        statusIcon = Icons.lock;
        break;
      case ConsultationStatus.cancelled:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: InkWell(
        onTap: () => onConsultationTap?.call(consultation.id),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: statusColor.withValues(alpha: 0.1),
              child: Icon(statusIcon, color: statusColor, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'استشارة من ${consultation.userName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    consultation.cropType,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(consultation.status),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'جديدة';
      case ConsultationStatus.inProgress:
        return 'قيد المعالجة';
      case ConsultationStatus.answered:
        return 'مجابة';
      case ConsultationStatus.closed:
        return 'مغلقة';
      case ConsultationStatus.cancelled:
        return 'ملغية';
    }
  }
}
