import 'package:flutter/material.dart';

/// قائمة مواعيد المرشد الزراعي
///
/// تعرض قائمة المواعيد مع إمكانيات التفاعل
class AdvisorAppointmentsList extends StatelessWidget {
  /// قائمة المواعيد
  final List<dynamic> appointments;
  
  /// رسالة الحالة الفارغة
  final String emptyMessage;
  
  /// دالة التحديث
  final Future<void> Function() onRefresh;
  
  /// دالة النقر على الموعد
  final Function(dynamic) onAppointmentTap;
  
  /// دالة تحديث حالة الموعد
  final Function(dynamic, String) onUpdateStatus;
  
  /// دالة إعادة جدولة الموعد
  final Function(dynamic) onReschedule;
  
  /// دالة إلغاء الموعد
  final Function(dynamic) onCancel;

  const AdvisorAppointmentsList({
    super.key,
    required this.appointments,
    required this.emptyMessage,
    required this.onRefresh,
    required this.onAppointmentTap,
    required this.onUpdateStatus,
    required this.onReschedule,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    if (appointments.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: appointments.length,
        itemBuilder: (context, index) {
          final appointment = appointments[index];
          return _buildAppointmentCard(appointment);
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calendar_today,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRefresh,
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الموعد
  Widget _buildAppointmentCard(dynamic appointment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onAppointmentTap(appointment),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'موعد استشارة', // TODO: استخدام البيانات الحقيقية
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'اليوم 10:00 ص', // TODO: استخدام البيانات الحقيقية
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                    ),
                    child: const Text(
                      'مؤكد',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // تفاصيل الموعد
              const Text(
                'استشارة زراعية حول محصول الطماطم', // TODO: استخدام البيانات الحقيقية
                style: TextStyle(fontSize: 14),
              ),
              
              const SizedBox(height: 12),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => onReschedule(appointment),
                      icon: const Icon(Icons.schedule, size: 16),
                      label: const Text('إعادة جدولة'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => onCancel(appointment),
                      icon: const Icon(Icons.cancel, size: 16),
                      label: const Text('إلغاء'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
