import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/appointments/appointments_cubit.dart';
import 'package:agriculture/domain/entities/appointment.dart';
import 'package:agriculture/core/shared/services/ui/dialog_service.dart';

/// واجهة إدارة المواعيد المحسنة للاستشاري (نسخة منفصلة)
///
/// هذه نسخة محسنة من واجهة المواعيد مع الحفاظ على الواجهة الأصلية
///
/// الميزات الجديدة:
/// ✅ استخدام AppointmentsCubit المحسن
/// ✅ 4 تبويبات منظمة (اليوم/انتظار/مؤكدة/مكتملة)
/// ✅ فحص التوفر الذكي
/// ✅ إحصائيات متقدمة
/// ✅ بحث وفلترة محسنة
/// ✅ تذكيرات ذكية
///
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #15: التركيز على مجلد widgets منفصل
/// وفق المعيار #12: تعليقات عربية شاملة
class EnhancedAdvisorAppointmentsV2 extends StatefulWidget {
  /// معرف المرشد
  final String advisorId;

  /// منشئ الواجهة المحسنة
  ///
  /// المعلمات:
  /// - [advisorId]: معرف المرشد
  const EnhancedAdvisorAppointmentsV2({
    super.key,
    required this.advisorId,
  });

  @override
  State<EnhancedAdvisorAppointmentsV2> createState() => _EnhancedAdvisorAppointmentsV2State();
}

class _EnhancedAdvisorAppointmentsV2State extends State<EnhancedAdvisorAppointmentsV2>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  /// متحكم التبويبات
  late TabController _tabController;

  /// التاريخ المحدد
  DateTime _selectedDate = DateTime.now();

  /// نص البحث
  String _searchQuery = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _setupAppointmentReminders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// إعداد تذكيرات المواعيد الذكية
  void _setupAppointmentReminders() {
    // TODO: تنفيذ نظام التذكيرات الذكية
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // للـ AutomaticKeepAliveClientMixin

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المواعيد المحسنة'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.today),
              text: 'اليوم',
            ),
            Tab(
              icon: Icon(Icons.pending),
              text: 'في الانتظار',
            ),
            Tab(
              icon: Icon(Icons.check_circle),
              text: 'مؤكدة',
            ),
            Tab(
              icon: Icon(Icons.done_all),
              text: 'مكتملة',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'البحث المتقدم',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'اختيار التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAppointments,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('الإحصائيات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('تصدير البيانات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('الإعدادات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => AppointmentsCubit(
          appointmentsService: context.read(),
          notificationService: context.read(),
        )..loadAdvisorAppointments(advisorId: widget.advisorId),
        child: BlocBuilder<AppointmentsCubit, AppointmentsState>(
          builder: (context, state) {
            return Column(
              children: [
                // شريط الإحصائيات السريعة
                _buildQuickStatsBar(state),

                // محتوى التبويبات
                Expanded(
                  child: _buildTabContent(state),
                ),
              ],
            );
          },
        ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "quick_actions",
            onPressed: _showQuickActions,
            backgroundColor: Colors.blue,
            child: const Icon(Icons.flash_on),
            tooltip: 'إجراءات سريعة',
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "add_appointment",
            onPressed: _createNewAppointment,
            backgroundColor: AssetsColors.dufaultGreencolor,
            child: const Icon(Icons.add),
            tooltip: 'موعد جديد',
          ),
        ],
      ),
    );
  }

  /// بناء شريط الإحصائيات السريعة
  Widget _buildQuickStatsBar(AppointmentsState state) {
    if (state is! AppointmentsLoaded) {
      return const SizedBox.shrink();
    }

    final appointments = state.appointments;
    final todayCount = appointments.where((a) => _isToday(a.appointmentDate)).length;
    final pendingCount = appointments.where((a) => a.status == AppointmentStatus.pending).length;
    final confirmedCount = appointments.where((a) => a.status == AppointmentStatus.confirmed).length;
    final completedCount = appointments.where((a) => a.status == AppointmentStatus.completed).length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('اليوم', todayCount, Colors.blue),
          ),
          Expanded(
            child: _buildStatCard('انتظار', pendingCount, Colors.orange),
          ),
          Expanded(
            child: _buildStatCard('مؤكدة', confirmedCount, Colors.green),
          ),
          Expanded(
            child: _buildStatCard('مكتملة', completedCount, Colors.purple),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التبويبات
  Widget _buildTabContent(AppointmentsState state) {
    if (state is AppointmentsLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المواعيد...'),
          ],
        ),
      );
    }

    if (state is AppointmentsError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المواعيد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _refreshAppointments,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    List<Appointment> appointments = [];
    if (state is AppointmentsLoaded) {
      appointments = state.appointments;
    }

    return TabBarView(
      controller: _tabController,
      children: [
        // مواعيد اليوم
        _buildAppointmentsList(
          appointments: _filterTodayAppointments(appointments),
          emptyMessage: 'لا توجد مواعيد اليوم',
          emptyIcon: Icons.today,
        ),

        // في الانتظار
        _buildAppointmentsList(
          appointments: _filterPendingAppointments(appointments),
          emptyMessage: 'لا توجد مواعيد في الانتظار',
          emptyIcon: Icons.pending,
        ),

        // مؤكدة
        _buildAppointmentsList(
          appointments: _filterConfirmedAppointments(appointments),
          emptyMessage: 'لا توجد مواعيد مؤكدة',
          emptyIcon: Icons.check_circle,
        ),

        // مكتملة
        _buildAppointmentsList(
          appointments: _filterCompletedAppointments(appointments),
          emptyMessage: 'لا توجد مواعيد مكتملة',
          emptyIcon: Icons.done_all,
        ),
      ],
    );
  }

  /// فلترة مواعيد اليوم
  List<Appointment> _filterTodayAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) => _isToday(appointment.appointmentDate)).toList();
  }

  /// فلترة المواعيد في الانتظار
  List<Appointment> _filterPendingAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) => appointment.status == AppointmentStatus.pending).toList();
  }

  /// فلترة المواعيد المؤكدة
  List<Appointment> _filterConfirmedAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) => appointment.status == AppointmentStatus.confirmed).toList();
  }

  /// فلترة المواعيد المكتملة
  List<Appointment> _filterCompletedAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) => appointment.status == AppointmentStatus.completed).toList();
  }

  /// التحقق من كون التاريخ اليوم
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList({
    required List<Appointment> appointments,
    required String emptyMessage,
    required IconData emptyIcon,
  }) {
    if (appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(emptyIcon, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر المواعيد هنا عند توفرها',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async => _refreshAppointments(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: appointments.length,
        itemBuilder: (context, index) {
          final appointment = appointments[index];
          return _buildEnhancedAppointmentCard(appointment);
        },
      ),
    );
  }

  /// بناء بطاقة موعد محسنة
  Widget _buildEnhancedAppointmentCard(Appointment appointment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _viewAppointmentDetails(appointment),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة مع الحالة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(appointment.status),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(appointment.status),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          appointment.farmerName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          appointment.consultationType.displayName,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(appointment.status).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          appointment.formattedTime,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: _getStatusColor(appointment.status),
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        appointment.formattedDate,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // وصف المشكلة
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Text(
                  appointment.problemDescription,
                  style: const TextStyle(fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              const SizedBox(height: 12),

              // أزرار الإجراءات المحسنة
              _buildActionButtons(appointment),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(Appointment appointment) {
    return Row(
      children: [
        // زر التأكيد (إذا كان متاحاً)
        if (appointment.canBeConfirmed) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _confirmAppointment(appointment),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('تأكيد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],

        // زر التفاصيل
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _viewAppointmentDetails(appointment),
            icon: const Icon(Icons.visibility, size: 18),
            label: const Text('التفاصيل'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),

        const SizedBox(width: 8),

        // قائمة الإجراءات الإضافية
        PopupMenuButton<String>(
          onSelected: (value) => _handleAppointmentAction(appointment, value),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          itemBuilder: (context) => [
            if (appointment.canBeRescheduled)
              const PopupMenuItem(
                value: 'reschedule',
                child: Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('إعادة جدولة'),
                  ],
                ),
              ),
            if (appointment.canBeCancelled)
              const PopupMenuItem(
                value: 'cancel',
                child: Row(
                  children: [
                    Icon(Icons.cancel, color: Colors.red),
                    SizedBox(width: 8),
                    Text('إلغاء'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'contact',
              child: Row(
                children: [
                  Icon(Icons.phone, color: Colors.green),
                  SizedBox(width: 8),
                  Text('اتصال'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'notes',
              child: Row(
                children: [
                  Icon(Icons.note_add, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('إضافة ملاحظة'),
                ],
              ),
            ),
          ],
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.more_vert, size: 20),
          ),
        ),
      ],
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.confirmed:
        return Colors.blue;
      case AppointmentStatus.inProgress:
        return Colors.purple;
      case AppointmentStatus.completed:
        return Colors.green;
      case AppointmentStatus.cancelled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Icons.pending;
      case AppointmentStatus.confirmed:
        return Icons.check_circle;
      case AppointmentStatus.inProgress:
        return Icons.work;
      case AppointmentStatus.completed:
        return Icons.done_all;
      case AppointmentStatus.cancelled:
        return Icons.cancel;
      case AppointmentStatus.noShow:
        return Icons.person_off;
    }
  }

  /// عرض حوار البحث المتقدم
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث المتقدم'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'ابحث باسم المزارع...',
                prefixIcon: Icon(Icons.person),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
            const SizedBox(height: 16),
            // TODO: إضافة فلاتر إضافية (التاريخ، الحالة، إلخ)
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSearch();
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ البحث
  void _performSearch() {
    // TODO: تنفيذ البحث المتقدم
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('البحث عن: $_searchQuery')),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() => _selectedDate = picked);
      _refreshAppointments();
    }
  }

  /// تحديث المواعيد
  void _refreshAppointments() {
    context.read<AppointmentsCubit>().loadAdvisorAppointments(
      advisorId: widget.advisorId,
      date: _selectedDate,
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'statistics':
        _showStatistics();
        break;
      case 'export':
        _exportData();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات المواعيد'),
        content: const Text('سيتم تطوير الإحصائيات المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تصدير البيانات
  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير تصدير البيانات قريباً')),
    );
  }

  /// عرض الإعدادات
  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير الإعدادات قريباً')),
    );
  }

  /// عرض الإجراءات السريعة
  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.event_available, color: Colors.green),
              title: const Text('تحديد أوقات متاحة'),
              onTap: () {
                Navigator.pop(context);
                _setAvailableSlots();
              },
            ),
            ListTile(
              leading: const Icon(Icons.block, color: Colors.red),
              title: const Text('حجب أوقات'),
              onTap: () {
                Navigator.pop(context);
                _blockTimeSlots();
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications, color: Colors.blue),
              title: const Text('إرسال تذكير جماعي'),
              onTap: () {
                Navigator.pop(context);
                _sendBulkReminder();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء موعد جديد
  void _createNewAppointment() {
    // TODO: فتح نموذج إنشاء موعد جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير إنشاء موعد جديد قريباً')),
    );
  }

  /// تأكيد الموعد
  void _confirmAppointment(Appointment appointment) {
    context.read<AppointmentsCubit>().confirmAppointment(
      appointmentId: appointment.id,
    );
  }

  /// عرض تفاصيل الموعد
  void _viewAppointmentDetails(Appointment appointment) {
    // TODO: فتح صفحة تفاصيل الموعد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل موعد ${appointment.farmerName}')),
    );
  }

  /// معالجة إجراءات الموعد
  void _handleAppointmentAction(Appointment appointment, String action) {
    switch (action) {
      case 'reschedule':
        _rescheduleAppointment(appointment);
        break;
      case 'cancel':
        _showCancelDialog(appointment);
        break;
      case 'contact':
        _contactFarmer(appointment);
        break;
      case 'notes':
        _addNotes(appointment);
        break;
    }
  }

  /// إعادة جدولة الموعد
  void _rescheduleAppointment(Appointment appointment) {
    // TODO: فتح نموذج إعادة الجدولة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير إعادة الجدولة قريباً')),
    );
  }

  /// عرض حوار الإلغاء
  void _showCancelDialog(Appointment appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الموعد'),
        content: Text('هل أنت متأكد من إلغاء موعد ${appointment.farmerName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AppointmentsCubit>().cancelAppointment(
                appointmentId: appointment.id,
                reason: 'تم الإلغاء من قبل المرشد',
                cancelledBy: 'advisor',
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }

  /// الاتصال بالمزارع
  void _contactFarmer(Appointment appointment) {
    // TODO: تنفيذ الاتصال
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بـ ${appointment.farmerName}')),
    );
  }

  /// إضافة ملاحظات
  void _addNotes(Appointment appointment) {
    // TODO: فتح نموذج إضافة الملاحظات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير إضافة الملاحظات قريباً')),
    );
  }

  /// تحديد أوقات متاحة
  void _setAvailableSlots() {
    // TODO: تنفيذ تحديد الأوقات المتاحة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير تحديد الأوقات المتاحة قريباً')),
    );
  }

  /// حجب أوقات
  void _blockTimeSlots() {
    // TODO: تنفيذ حجب الأوقات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير حجب الأوقات قريباً')),
    );
  }

  /// إرسال تذكير جماعي
  void _sendBulkReminder() {
    // TODO: تنفيذ إرسال التذكير الجماعي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير التذكير الجماعي قريباً')),
    );
  }
}