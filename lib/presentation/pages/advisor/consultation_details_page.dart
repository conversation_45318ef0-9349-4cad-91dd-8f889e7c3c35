import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
// import 'package:agriculture/presentation/widgets/shared/loading_widget.dart';
// import 'package:agriculture/presentation/widgets/shared/error_widget.dart';

/// صفحة تفاصيل الاستشارة للاستشاري
/// 
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #15: التركيز على مجلد widgets منفصل
/// وفق المعيار #12: تعليقات عربية شاملة
class ConsultationDetailsPage extends StatelessWidget {
  /// نموذج الاستشارة
  final ConsultationModel consultation;

  /// منشئ صفحة تفاصيل الاستشارة
  /// 
  /// المعلمات:
  /// - [consultation]: نموذج الاستشارة
  const ConsultationDetailsPage({
    super.key,
    required this.consultation,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('استشارة ${consultation.userName}'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareConsultation(context),
            tooltip: 'مشاركة',
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'mark_urgent',
                child: Row(
                  children: [
                    Icon(Icons.priority_high, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تحديد كعاجل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'assign_specialist',
                child: Row(
                  children: [
                    Icon(Icons.person_add, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعيين متخصص'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'archive',
                child: Row(
                  children: [
                    Icon(Icons.archive, color: Colors.grey),
                    SizedBox(width: 8),
                    Text('أرشفة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المزارع
                _buildFarmerInfoCard(),
                
                const SizedBox(height: 16),
                
                // تفاصيل الاستشارة
                _buildConsultationDetailsCard(),
                
                const SizedBox(height: 16),
                
                // الصور المرفقة
                // if (consultation.imageUrls.isNotEmpty)
                //   _buildImagesSection(),
                
                const SizedBox(height: 16),
                
                // حالة الاستشارة
                _buildStatusCard(),
                
                const SizedBox(height: 16),
                
                // الردود السابقة
                _buildRepliesSection(context, state),
                
                const SizedBox(height: 100), // مساحة للأزرار العائمة
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: _buildActionButtons(context),
    );
  }

  /// بناء بطاقة معلومات المزارع
  Widget _buildFarmerInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: consultation.userImage != null
                      ? NetworkImage(consultation.userImage!)
                      : null,
                  child: consultation.userImage == null
                      ? const Icon(Icons.person, size: 30)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        consultation.userName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'معرف المزارع: ${consultation.userId}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            _formatDateTime(consultation.createdAt.toString()),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(consultation.status.toString()),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(consultation.status.toString()),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة تفاصيل الاستشارة
  Widget _buildConsultationDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الاستشارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('نوع المحصول', consultation.cropType),
            _buildDetailRow('المساحة', '${consultation.area} متر مربع'),
            _buildDetailRow('معرف الاستشارة', consultation.id),
            const SizedBox(height: 12),
            const Text(
              'وصف المشكلة:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                consultation.problemDescription,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الصور
  // Widget _buildImagesSection() {
  //   return Card(
  //     elevation: 2,
  //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //     child: Padding(
  //       padding: const EdgeInsets.all(16),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Text(
  //             'الصور المرفقة (${consultation.imageUrls.length})',
  //             style: const TextStyle(
  //               fontSize: 18,
  //               fontWeight: FontWeight.bold,
  //             ),
  //           ),
  //           const SizedBox(height: 12),
  //           SizedBox(
  //             height: 120,
  //             child: ListView.builder(
  //               scrollDirection: Axis.horizontal,
  //               itemCount: consultation.imageUrls.length,
  //               itemBuilder: (context, index) {
  //                 return Container(
  //                   margin: const EdgeInsets.only(right: 8),
  //                   child: ClipRRect(
  //                     borderRadius: BorderRadius.circular(8),
  //                     child: Image.network(
  //                       consultation.imageUrls[index],
  //                       width: 120,
  //                       height: 120,
  //                       fit: BoxFit.cover,
  //                       errorBuilder: (context, error, stackTrace) {
  //                         return Container(
  //                           width: 120,
  //                           height: 120,
  //                           color: Colors.grey[300],
  //                           child: const Icon(Icons.error),
  //                         );
  //                       },
  //                     ),
  //                   ),
  //                 );
  //               },
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  /// بناء بطاقة الحالة
  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة الاستشارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  _getStatusIcon(consultation.status.toString()),
                  color: _getStatusColor(consultation.status.toString()),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _getStatusText(consultation.status.toString()),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getStatusColor(consultation.status.toString()),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _getStatusDescription(consultation.status.toString()),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الردود
  Widget _buildRepliesSection(BuildContext context, AdvisorState state) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الردود والمتابعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            // TODO: عرض الردود السابقة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'لم يتم الرد على هذه الاستشارة بعد',
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _replyToConsultation(context),
              icon: const Icon(Icons.reply),
              label: const Text('الرد على الاستشارة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          OutlinedButton.icon(
            onPressed: () => _updateStatus(context),
            icon: const Icon(Icons.update),
            label: const Text('تحديث الحالة'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        return 'اليوم ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays == 1) {
        return 'أمس ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else {
        return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return dateTimeString;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'answered':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'in_progress':
        return Icons.work;
      case 'answered':
        return Icons.check_circle;
      case 'closed':
        return Icons.lock;
      default:
        return Icons.help;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'in_progress':
        return 'قيد المعالجة';
      case 'answered':
        return 'تم الرد';
      case 'closed':
        return 'مغلقة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على وصف الحالة
  String _getStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return 'الاستشارة في انتظار المراجعة من قبل المرشد الزراعي';
      case 'in_progress':
        return 'يتم حالياً مراجعة الاستشارة وإعداد الرد';
      case 'answered':
        return 'تم الرد على الاستشارة وإرسال التوصيات للمزارع';
      case 'closed':
        return 'تم إغلاق الاستشارة وانتهاء المتابعة';
      default:
        return 'حالة غير معروفة';
    }
  }

  /// مشاركة الاستشارة
  void _shareConsultation(BuildContext context) {
    // TODO: تنفيذ مشاركة الاستشارة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير المشاركة قريباً')),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'mark_urgent':
        // TODO: تحديد كعاجل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديد الاستشارة كعاجلة')),
        );
        break;
      case 'assign_specialist':
        // TODO: تعيين متخصص
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('سيتم تطوير تعيين المتخصص قريباً')),
        );
        break;
      case 'archive':
        // TODO: أرشفة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم أرشفة الاستشارة')),
        );
        break;
    }
  }

  /// الرد على الاستشارة
  void _replyToConsultation(BuildContext context) {
    // TODO: فتح صفحة الرد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير نموذج الرد قريباً')),
    );
  }

  /// تحديث حالة الاستشارة
  void _updateStatus(BuildContext context) {
    // TODO: فتح حوار تحديث الحالة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير تحديث الحالة قريباً')),
    );
  }
}
