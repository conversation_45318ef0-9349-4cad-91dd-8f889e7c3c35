import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
// import 'package:agriculture/core/shared/services/user/user_data_service.dart';
import 'package:agriculture/core/shared/services/ui/dialog_service.dart';

/// صفحة الرد على الاستشارة للاستشاري
/// 
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #15: التركيز على مجلد widgets منفصل
/// وفق المعيار #12: تعليقات عربية شاملة
class ConsultationReplyPage extends StatelessWidget {
  /// نموذج الاستشارة
  final ConsultationModel consultation;

  /// منشئ صفحة الرد على الاستشارة
  /// 
  /// المعلمات:
  /// - [consultation]: نموذج الاستشارة
  const ConsultationReplyPage({
    super.key,
    required this.consultation,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الرد على الاستشارة'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'مساعدة',
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => AdvisorCubit(
          advisorRepository: context.read(),
        ),
        child: BlocBuilder<AdvisorCubit, AdvisorState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الاستشارة
                  _buildConsultationSummary(),
                  
                  const SizedBox(height: 20),
                  
                  // نموذج الرد
                  _buildReplyForm(context, state),
                  
                  const SizedBox(height: 20),
                  
                  // قوالب الردود الجاهزة
                  _buildReplyTemplates(context),
                  
                  const SizedBox(height: 20),
                  
                  // أدوات مساعدة
                  _buildHelpfulTools(context),
                  
                  const SizedBox(height: 100), // مساحة للأزرار العائمة
                ],
              ),
            );
          },
        ),
      ),
      bottomNavigationBar: _buildActionButtons(context),
    );
  }

  /// بناء ملخص الاستشارة
  Widget _buildConsultationSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الاستشارة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildSummaryRow('المزارع', consultation.userName),
            _buildSummaryRow('نوع المحصول', consultation.cropType),
            _buildSummaryRow('المساحة', '${consultation.area} متر مربع'),
            _buildSummaryRow('تاريخ الطلب', _formatDate(consultation.createdAt.toString())),
            const SizedBox(height: 8),
            const Text(
              'وصف المشكلة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(consultation.problemDescription),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نموذج الرد
  Widget _buildReplyForm(BuildContext context, AdvisorState state) {
    final replyController = TextEditingController();
    final recommendationsController = TextEditingController();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرد على الاستشارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // حقل الرد الرئيسي
            TextFormField(
              controller: replyController,
              maxLines: 6,
              decoration: InputDecoration(
                labelText: 'الرد التفصيلي',
                hintText: 'اكتب ردك التفصيلي على استشارة المزارع...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.chat_bubble_outline),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // حقل التوصيات
            TextFormField(
              controller: recommendationsController,
              maxLines: 4,
              decoration: InputDecoration(
                labelText: 'التوصيات والحلول',
                hintText: 'اكتب التوصيات والحلول المقترحة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.lightbulb_outline),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // خيارات إضافية
            Row(
              children: [
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('طلب صور إضافية'),
                    value: false, // TODO: ربط بالحالة
                    onChanged: (value) {
                      // TODO: تنفيذ التغيير
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('تحديد موعد متابعة'),
                    value: false, // TODO: ربط بالحالة
                    onChanged: (value) {
                      // TODO: تنفيذ التغيير
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قوالب الردود الجاهزة
  Widget _buildReplyTemplates(BuildContext context) {
    final templates = [
      {
        'title': 'مشكلة آفات',
        'content': 'بناءً على الوصف والصور، يبدو أن النبات يعاني من إصابة بالآفات. أنصح بـ...',
      },
      {
        'title': 'نقص التغذية',
        'content': 'الأعراض تشير إلى نقص في العناصر الغذائية. يُنصح بإجراء تحليل للتربة و...',
      },
      {
        'title': 'مشاكل الري',
        'content': 'المشكلة تبدو مرتبطة بنظام الري. أنصح بمراجعة جدول الري و...',
      },
      {
        'title': 'أمراض فطرية',
        'content': 'الأعراض تشير إلى إصابة فطرية. يُنصح باستخدام مبيد فطري مناسب...',
      },
    ];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قوالب الردود الجاهزة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...templates.map((template) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: OutlinedButton(
                onPressed: () => _useTemplate(context, template['content']!),
                style: OutlinedButton.styleFrom(
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.all(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      template['title']!,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      template['content']!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// بناء أدوات مساعدة
  Widget _buildHelpfulTools(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أدوات مساعدة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openCropGuide(context),
                    icon: const Icon(Icons.book),
                    label: const Text('دليل المحاصيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openPestDatabase(context),
                    icon: const Icon(Icons.bug_report),
                    label: const Text('قاعدة الآفات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openWeatherInfo(context),
                    icon: const Icon(Icons.cloud),
                    label: const Text('معلومات الطقس'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _consultSpecialist(context),
                    icon: const Icon(Icons.people),
                    label: const Text('استشارة متخصص'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: () => _sendReply(context),
              icon: const Icon(Icons.send),
              label: const Text('إرسال الرد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _saveDraft(context),
              icon: const Icon(Icons.save),
              label: const Text('حفظ مسودة'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف الملخص
  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  /// عرض حوار المساعدة
  void _showHelpDialog(BuildContext context) {
    DialogService.showInfoDialog(
      context,
      title: 'مساعدة في الرد',
      message: 'نصائح لكتابة رد فعال:\n'
          '• اقرأ الاستشارة بعناية\n'
          '• استخدم لغة واضحة ومفهومة\n'
          '• قدم حلول عملية قابلة للتطبيق\n'
          '• اطلب معلومات إضافية إذا لزم الأمر',
    );
  }

  /// استخدام قالب رد
  void _useTemplate(BuildContext context, String template) {
    // TODO: تنفيذ نسخ القالب إلى حقل الرد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم نسخ القالب')),
    );
  }

  /// فتح دليل المحاصيل
  void _openCropGuide(BuildContext context) {
    // TODO: تنفيذ فتح دليل المحاصيل
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير دليل المحاصيل قريباً')),
    );
  }

  /// فتح قاعدة بيانات الآفات
  void _openPestDatabase(BuildContext context) {
    // TODO: تنفيذ فتح قاعدة الآفات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير قاعدة الآفات قريباً')),
    );
  }

  /// فتح معلومات الطقس
  void _openWeatherInfo(BuildContext context) {
    // TODO: تنفيذ فتح معلومات الطقس
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير معلومات الطقس قريباً')),
    );
  }

  /// استشارة متخصص
  void _consultSpecialist(BuildContext context) {
    // TODO: تنفيذ استشارة متخصص
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير استشارة المتخصص قريباً')),
    );
  }

  /// إرسال الرد
  void _sendReply(BuildContext context) {
    // TODO: تنفيذ إرسال الرد
    DialogService.showSuccessDialog(
      context,
      title: 'تم إرسال الرد بنجاح!',
      message: 'تم إرسال ردك على الاستشارة وسيصل للمزارع قريباً',
    );
  }

  /// حفظ مسودة
  void _saveDraft(BuildContext context) {
    // TODO: تنفيذ حفظ المسودة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ المسودة')),
    );
  }
}
