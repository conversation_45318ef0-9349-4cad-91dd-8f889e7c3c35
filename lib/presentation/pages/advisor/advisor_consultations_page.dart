import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'widgets/index.dart';

/// صفحة استشارات المرشد الزراعي
/// 
/// تعرض جميع الاستشارات المرسلة للمرشد مقسمة حسب الحالة
/// تشمل: الجديدة، قيد المعالجة، المجابة، المغلقة
/// 
/// المعايير المطبقة:
/// ✅ Clean Architecture - فصل منطق العمل
/// ✅ Cubit لإدارة الحالة
/// ✅ widgets منفصلة للمكونات
/// ✅ تعليقات عربية شاملة
/// ✅ تجزئة الوظائف لوحدات صغيرة
class AdvisorConsultationsPage extends StatefulWidget {
  /// معرف المرشد
  final String advisorId;

  const AdvisorConsultationsPage({
    super.key,
    required this.advisorId,
  });

  @override
  State<AdvisorConsultationsPage> createState() => _AdvisorConsultationsPageState();
}

class _AdvisorConsultationsPageState extends State<AdvisorConsultationsPage>
    with SingleTickerProviderStateMixin {
  
  /// متحكم التبويبات
  late TabController _tabController;
  
  /// فلتر البحث الحالي
  String _searchFilter = '';
  
  /// فلتر نوع المحصول
  String _cropTypeFilter = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadConsultations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الاستشارات
  /// 
  /// يحمل جميع الاستشارات للمرشد
  Future<void> _loadConsultations() async {
    try {
      context.read<AdvisorCubit>().getAdvisorConsultations(
        advisorId: widget.advisorId,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الاستشارات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الاستشارات'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الجديدة'),
            Tab(text: 'قيد المعالجة'),
            Tab(text: 'المجابة'),
            Tab(text: 'المغلقة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'البحث في الاستشارات',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة الاستشارات',
          ),
        ],
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          if (state is ConsultationsLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is AdvisorError) {
            return AdvisorErrorWidget(
              message: state.message,
              onRetry: _loadConsultations,
            );
          }

          List<ConsultationModel> consultations = [];
          if (state is ConsultationsLoaded) {
            consultations = _filterConsultations(state.consultations);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // الاستشارات الجديدة
              AdvisorConsultationsList(
                consultations: consultations
                    .where((c) => c.status == ConsultationStatus.pending)
                    .toList(),
                emptyMessage: 'لا توجد استشارات جديدة',
                onRefresh: _loadConsultations,
                onConsultationTap: _viewConsultationDetails,
                onReply: _replyToConsultation,
                onUpdateStatus: _updateConsultationStatus,
                onShowFarmerProfile: _showFarmerProfile,
              ),

              // الاستشارات قيد المعالجة
              AdvisorConsultationsList(
                consultations: consultations
                    .where((c) => c.status == ConsultationStatus.inProgress)
                    .toList(),
                emptyMessage: 'لا توجد استشارات قيد المعالجة',
                onRefresh: _loadConsultations,
                onConsultationTap: _viewConsultationDetails,
                onReply: _replyToConsultation,
                onUpdateStatus: _updateConsultationStatus,
                onShowFarmerProfile: _showFarmerProfile,
              ),

              // الاستشارات المجابة
              AdvisorConsultationsList(
                consultations: consultations
                    .where((c) => c.status == ConsultationStatus.answered)
                    .toList(),
                emptyMessage: 'لا توجد استشارات مجابة',
                onRefresh: _loadConsultations,
                onConsultationTap: _viewConsultationDetails,
                onReply: _replyToConsultation,
                onUpdateStatus: _updateConsultationStatus,
                onShowFarmerProfile: _showFarmerProfile,
              ),

              // الاستشارات المغلقة
              AdvisorConsultationsList(
                consultations: consultations
                    .where((c) => c.status == ConsultationStatus.closed)
                    .toList(),
                emptyMessage: 'لا توجد استشارات مغلقة',
                onRefresh: _loadConsultations,
                onConsultationTap: _viewConsultationDetails,
                onReply: _replyToConsultation,
                onUpdateStatus: _updateConsultationStatus,
                onShowFarmerProfile: _showFarmerProfile,
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadConsultations,
        backgroundColor: AssetsColors.dufaultGreencolor,
        tooltip: 'تحديث الاستشارات',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  /// فلترة الاستشارات
  /// 
  /// يطبق فلاتر البحث ونوع المحصول
  List<ConsultationModel> _filterConsultations(List<ConsultationModel> consultations) {
    List<ConsultationModel> filtered = consultations;

    // فلتر البحث
    if (_searchFilter.isNotEmpty) {
      filtered = filtered.where((c) =>
          c.userName.toLowerCase().contains(_searchFilter.toLowerCase()) ||
          c.problemDescription.toLowerCase().contains(_searchFilter.toLowerCase())).toList();
    }

    // فلتر نوع المحصول
    if (_cropTypeFilter.isNotEmpty) {
      filtered = filtered.where((c) => c.cropType == _cropTypeFilter).toList();
    }

    return filtered;
  }

  /// عرض حوار البحث
  /// 
  /// يفتح حوار البحث في الاستشارات
  void _showSearchDialog() {
    // TODO: تنفيذ حوار البحث
    debugPrint('عرض حوار البحث');
  }

  /// عرض حوار الفلترة
  /// 
  /// يفتح حوار فلترة الاستشارات
  void _showFilterDialog() {
    // TODO: تنفيذ حوار الفلترة
    debugPrint('عرض حوار الفلترة');
  }

  /// عرض تفاصيل الاستشارة
  /// 
  /// يفتح صفحة تفاصيل الاستشارة
  void _viewConsultationDetails(ConsultationModel consultation) {
    // TODO: تنفيذ عرض تفاصيل الاستشارة
    debugPrint('عرض تفاصيل الاستشارة: ${consultation.id}');
  }

  /// الرد على الاستشارة
  /// 
  /// يفتح نموذج الرد على الاستشارة
  void _replyToConsultation(ConsultationModel consultation) {
    // TODO: تنفيذ الرد على الاستشارة
    debugPrint('الرد على الاستشارة: ${consultation.id}');
  }

  /// تحديث حالة الاستشارة
  /// 
  /// يغير حالة الاستشارة
  void _updateConsultationStatus(ConsultationModel consultation, ConsultationStatus newStatus) {
    context.read<AdvisorCubit>().updateConsultationStatus(
      consultationId: consultation.id,
      status: newStatus,
      advisorId: widget.advisorId,
    );
  }

  /// عرض ملف المزارع
  /// 
  /// يعرض معلومات المزارع
  void _showFarmerProfile(ConsultationModel consultation) {
    // TODO: تنفيذ عرض ملف المزارع
    debugPrint('عرض ملف المزارع: ${consultation.userName}');
  }
}
