import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/appointments/appointments_cubit.dart';
import 'package:agriculture/domain/entities/appointment.dart';
import 'package:agriculture/core/shared/services/ui/dialog_service.dart';

/// صفحة إدارة المواعيد المحسنة للاستشاري
/// 
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #15: التركيز على مجلد widgets منفصل
/// وفق المعيار #12: تعليقات عربية شاملة
class EnhancedAdvisorAppointmentsPage extends StatelessWidget {
  /// معرف المرشد
  final String advisorId;

  /// منشئ صفحة إدارة المواعيد المحسنة
  /// 
  /// المعلمات:
  /// - [advisorId]: معرف المرشد
  const EnhancedAdvisorAppointmentsPage({
    super.key,
    required this.advisorId,
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة المواعيد'),
          backgroundColor: AssetsColors.dufaultGreencolor,
          foregroundColor: Colors.white,
          bottom: const TabBar(
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              Tab(text: 'اليوم'),
              Tab(text: 'في الانتظار'),
              Tab(text: 'مؤكدة'),
              Tab(text: 'مكتملة'),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.calendar_today),
              onPressed: () => _selectDate(context),
              tooltip: 'اختيار التاريخ',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _refreshAppointments(context),
              tooltip: 'تحديث',
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(context, value),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'statistics',
                  child: Row(
                    children: [
                      Icon(Icons.analytics),
                      SizedBox(width: 8),
                      Text('الإحصائيات'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'export',
                  child: Row(
                    children: [
                      Icon(Icons.download),
                      SizedBox(width: 8),
                      Text('تصدير البيانات'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: BlocProvider(
          create: (context) => AppointmentsCubit(
            appointmentsService: context.read(),
            notificationService: context.read(),
          )..loadAdvisorAppointments(advisorId: advisorId),
          child: BlocBuilder<AppointmentsCubit, AppointmentsState>(
            builder: (context, state) {
              return TabBarView(
                children: [
                  // مواعيد اليوم
                  _buildAppointmentsList(
                    context,
                    state,
                    filter: (appointment) => _isToday(appointment.appointmentDate),
                    emptyMessage: 'لا توجد مواعيد اليوم',
                    emptyIcon: Icons.today,
                  ),
                  
                  // في الانتظار
                  _buildAppointmentsList(
                    context,
                    state,
                    filter: (appointment) => appointment.status == AppointmentStatus.pending,
                    emptyMessage: 'لا توجد مواعيد في الانتظار',
                    emptyIcon: Icons.pending,
                  ),
                  
                  // مؤكدة
                  _buildAppointmentsList(
                    context,
                    state,
                    filter: (appointment) => appointment.status == AppointmentStatus.confirmed,
                    emptyMessage: 'لا توجد مواعيد مؤكدة',
                    emptyIcon: Icons.check_circle,
                  ),
                  
                  // مكتملة
                  _buildAppointmentsList(
                    context,
                    state,
                    filter: (appointment) => appointment.status == AppointmentStatus.completed,
                    emptyMessage: 'لا توجد مواعيد مكتملة',
                    emptyIcon: Icons.done_all,
                  ),
                ],
              );
            },
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showQuickActions(context),
          backgroundColor: AssetsColors.dufaultGreencolor,
          tooltip: 'إجراءات سريعة',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  /// بناء قائمة المواعيد
  /// 
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [state]: حالة الـ Cubit
  /// - [filter]: دالة الفلترة (اختياري)
  /// - [emptyMessage]: رسالة القائمة الفارغة
  /// - [emptyIcon]: أيقونة القائمة الفارغة
  Widget _buildAppointmentsList(
    BuildContext context,
    AppointmentsState state, {
    bool Function(Appointment)? filter,
    required String emptyMessage,
    required IconData emptyIcon,
  }) {
    if (state is AppointmentsLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المواعيد...'),
          ],
        ),
      );
    }

    if (state is AppointmentsError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المواعيد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _refreshAppointments(context),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state is AppointmentsLoaded) {
      List<Appointment> appointments = state.appointments;

      // تطبيق الفلتر إذا تم توفيره
      if (filter != null) {
        appointments = appointments.where(filter).toList();
      }

      if (appointments.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                emptyIcon,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                emptyMessage,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'ستظهر المواعيد هنا عند توفرها',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => _refreshAppointments(context),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: appointments.length,
          itemBuilder: (context, index) {
            final appointment = appointments[index];
            return _buildAppointmentCard(context, appointment);
          },
        ),
      );
    }

    return const Center(child: Text('حالة غير معروفة'));
  }

  /// بناء بطاقة الموعد
  /// 
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [appointment]: الموعد
  Widget _buildAppointmentCard(BuildContext context, Appointment appointment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(appointment.status),
                  child: Icon(
                    _getStatusIcon(appointment.status),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment.farmerName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        appointment.consultationType.displayName,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      appointment.formattedTime,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      appointment.formattedDate,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // وصف المشكلة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                appointment.problemDescription,
                style: const TextStyle(fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // أزرار الإجراءات
            Row(
              children: [
                if (appointment.canBeConfirmed)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _confirmAppointment(context, appointment),
                      icon: const Icon(Icons.check, size: 18),
                      label: const Text('تأكيد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                
                if (appointment.canBeConfirmed) const SizedBox(width: 8),
                
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewAppointmentDetails(context, appointment),
                    icon: const Icon(Icons.visibility, size: 18),
                    label: const Text('التفاصيل'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAppointmentAction(context, appointment, value),
                  itemBuilder: (context) => [
                    if (appointment.canBeRescheduled)
                      const PopupMenuItem(
                        value: 'reschedule',
                        child: Row(
                          children: [
                            Icon(Icons.schedule),
                            SizedBox(width: 8),
                            Text('إعادة جدولة'),
                          ],
                        ),
                      ),
                    if (appointment.canBeCancelled)
                      const PopupMenuItem(
                        value: 'cancel',
                        child: Row(
                          children: [
                            Icon(Icons.cancel, color: Colors.red),
                            SizedBox(width: 8),
                            Text('إلغاء'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'contact',
                      child: Row(
                        children: [
                          Icon(Icons.phone),
                          SizedBox(width: 8),
                          Text('اتصال'),
                        ],
                      ),
                    ),
                  ],
                  child: const Icon(Icons.more_vert),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// التحقق من كون التاريخ اليوم
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.confirmed:
        return Colors.blue;
      case AppointmentStatus.inProgress:
        return Colors.purple;
      case AppointmentStatus.completed:
        return Colors.green;
      case AppointmentStatus.cancelled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Icons.pending;
      case AppointmentStatus.confirmed:
        return Icons.check_circle;
      case AppointmentStatus.inProgress:
        return Icons.work;
      case AppointmentStatus.completed:
        return Icons.done_all;
      case AppointmentStatus.cancelled:
        return Icons.cancel;
      case AppointmentStatus.noShow:
        return Icons.person_off;
    }
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      // تحديث المواعيد للتاريخ المختار
      context.read<AppointmentsCubit>().loadAdvisorAppointments(
        advisorId: advisorId,
        date: picked,
      );
    }
  }

  /// تحديث المواعيد
  Future<void> _refreshAppointments(BuildContext context) async {
    context.read<AppointmentsCubit>().loadAdvisorAppointments(advisorId: advisorId);
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'statistics':
        // TODO: عرض الإحصائيات
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('سيتم تطوير الإحصائيات قريباً')),
        );
        break;
      case 'export':
        // TODO: تصدير البيانات
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('سيتم تطوير التصدير قريباً')),
        );
        break;
    }
  }

  /// عرض الإجراءات السريعة
  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.event_available),
              title: const Text('تحديد أوقات متاحة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: تنفيذ تحديد الأوقات المتاحة
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('حجب أوقات'),
              onTap: () {
                Navigator.pop(context);
                // TODO: تنفيذ حجب الأوقات
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('إرسال تذكير'),
              onTap: () {
                Navigator.pop(context);
                // TODO: تنفيذ إرسال التذكير
              },
            ),
          ],
        ),
      ),
    );
  }

  /// تأكيد الموعد
  void _confirmAppointment(BuildContext context, Appointment appointment) {
    context.read<AppointmentsCubit>().confirmAppointment(
      appointmentId: appointment.id,
    );
  }

  /// عرض تفاصيل الموعد
  void _viewAppointmentDetails(BuildContext context, Appointment appointment) {
    // TODO: فتح صفحة تفاصيل الموعد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل موعد ${appointment.farmerName}')),
    );
  }

  /// معالجة إجراءات الموعد
  void _handleAppointmentAction(BuildContext context, Appointment appointment, String action) {
    switch (action) {
      case 'reschedule':
        // TODO: إعادة جدولة الموعد
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('سيتم تطوير إعادة الجدولة قريباً')),
        );
        break;
      case 'cancel':
        // TODO: إلغاء الموعد
        _showCancelDialog(context, appointment);
        break;
      case 'contact':
        // TODO: الاتصال بالمزارع
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('الاتصال بـ ${appointment.farmerName}')),
        );
        break;
    }
  }

  /// عرض حوار الإلغاء
  void _showCancelDialog(BuildContext context, Appointment appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الموعد'),
        content: Text('هل أنت متأكد من إلغاء موعد ${appointment.farmerName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AppointmentsCubit>().cancelAppointment(
                appointmentId: appointment.id,
                reason: 'تم الإلغاء من قبل المرشد',
                cancelledBy: 'advisor',
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }
}
