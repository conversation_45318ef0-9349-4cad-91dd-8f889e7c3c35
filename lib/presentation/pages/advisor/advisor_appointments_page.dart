import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/appointments/appointments_cubit.dart' as appointments_cubit;
import 'package:agriculture/domain/entities/appointment.dart';

/// صفحة مواعيد المرشد الزراعي
///
/// تعرض جميع المواعيد المجدولة للمرشد
/// تشمل: المواعيد اليوم، المواعيد القادمة، المواعيد المكتملة
///
/// المعايير المطبقة:
/// ✅ Clean Architecture - فصل منطق العمل
/// ✅ Cubit لإدارة الحالة
/// ✅ widgets منفصلة للمكونات
/// ✅ تعليقات عربية شاملة
/// ✅ تجزئة الوظائف لوحدات صغيرة
class AdvisorAppointmentsPage extends StatefulWidget {
  /// معرف المرشد
  final String advisorId;

  const AdvisorAppointmentsPage({
    super.key,
    required this.advisorId,
  });

  @override
  State<AdvisorAppointmentsPage> createState() => _AdvisorAppointmentsPageState();
}

class _AdvisorAppointmentsPageState extends State<AdvisorAppointmentsPage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  /// متحكم التبويبات
  late TabController _tabController;

  /// التاريخ المحدد للعرض
  DateTime _selectedDate = DateTime.now();

  /// نص البحث
  String _searchQuery = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadAppointments();
    _setupAppointmentReminders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل المواعيد من الخدمة
  void _loadAppointments() {
    context.read<appointments_cubit.AppointmentsCubit>().loadAdvisorAppointments(
      advisorId: widget.advisorId,
      date: _selectedDate,
    );
  }

  /// إعداد تذكيرات المواعيد
  void _setupAppointmentReminders() {
    context.read<appointments_cubit.AppointmentsCubit>().stream.listen((state) {
      if (state is appointments_cubit.AppointmentsLoaded) {
        final upcomingAppointments = state.appointments
            .where((a) => a.status == AppointmentStatus.confirmed)
            .take(3)
            .toList();

        for (final appointment in upcomingAppointments) {
          _setReminder(appointment);
        }
      }
    });
  }

  /// تعيين تذكير للموعد
  void _setReminder(Appointment appointment) {
    // TODO: تنفيذ نظام الإشعارات للتذكير
    debugPrint('تم تعيين تذكير للموعد: ${appointment.id}');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // للـ AutomaticKeepAliveClientMixin
    return Scaffold(
      appBar: AppBar(
        title: const Text('المواعيد'),
        backgroundColor: AssetsColors.dufaultGreencolor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'اليوم'),
            Tab(text: 'في الانتظار'),
            Tab(text: 'مؤكدة'),
            Tab(text: 'مكتملة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'البحث',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _showDatePicker,
            tooltip: 'اختيار التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAppointments,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('الإحصائيات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('تصدير البيانات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => appointments_cubit.AppointmentsCubit(
          appointmentsService: context.read(),
          notificationService: context.read(),
        )..loadAdvisorAppointments(advisorId: widget.advisorId),
        child: BlocBuilder<appointments_cubit.AppointmentsCubit, appointments_cubit.AppointmentsState>(
          builder: (context, state) {
            if (state is appointments_cubit.AppointmentsLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل المواعيد...'),
                  ],
                ),
              );
            }

            if (state is appointments_cubit.AppointmentsError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text(
                      'خطأ في تحميل المواعيد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _refreshAppointments,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            List<Appointment> appointments = [];
            if (state is appointments_cubit.AppointmentsLoaded) {
              appointments = state.appointments;
            }

            // تصنيف المواعيد
            final todayAppointments = _filterTodayAppointments(appointments);
            final pendingAppointments = _filterPendingAppointments(appointments);
            final confirmedAppointments = _filterConfirmedAppointments(appointments);
            final completedAppointments = _filterCompletedAppointments(appointments);

            return TabBarView(
              controller: _tabController,
              children: [
                // مواعيد اليوم
                _buildAppointmentsList(
                  appointments: todayAppointments,
                  emptyMessage: 'لا توجد مواعيد اليوم',
                  emptyIcon: Icons.today,
                ),

                // في الانتظار
                _buildAppointmentsList(
                  appointments: pendingAppointments,
                  emptyMessage: 'لا توجد مواعيد في الانتظار',
                  emptyIcon: Icons.pending,
                ),

                // مؤكدة
                _buildAppointmentsList(
                  appointments: confirmedAppointments,
                  emptyMessage: 'لا توجد مواعيد مؤكدة',
                  emptyIcon: Icons.check_circle,
                ),

                // مكتملة
                _buildAppointmentsList(
                  appointments: completedAppointments,
                  emptyMessage: 'لا توجد مواعيد مكتملة',
                  emptyIcon: Icons.done_all,
                ),
              ],
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewAppointment,
        backgroundColor: AssetsColors.dufaultGreencolor,
        tooltip: 'موعد جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// فلترة مواعيد اليوم
  List<Appointment> _filterTodayAppointments(List<Appointment> appointments) {
    final today = DateTime.now();
    return appointments.where((appointment) {
      return appointment.appointmentDate.year == today.year &&
          appointment.appointmentDate.month == today.month &&
          appointment.appointmentDate.day == today.day;
    }).toList();
  }

  /// فلترة المواعيد في الانتظار
  List<Appointment> _filterPendingAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) {
      return appointment.status == AppointmentStatus.pending;
    }).toList();
  }

  /// فلترة المواعيد المؤكدة
  List<Appointment> _filterConfirmedAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) {
      return appointment.status == AppointmentStatus.confirmed;
    }).toList();
  }

  /// فلترة المواعيد المكتملة
  List<Appointment> _filterCompletedAppointments(List<Appointment> appointments) {
    return appointments.where((appointment) {
      return appointment.status == AppointmentStatus.completed;
    }).toList();
  }

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList({
    required List<Appointment> appointments,
    required String emptyMessage,
    required IconData emptyIcon,
  }) {
    if (appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              emptyIcon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر المواعيد هنا عند توفرها',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async => _refreshAppointments(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: appointments.length,
        itemBuilder: (context, index) {
          final appointment = appointments[index];
          return _buildAppointmentCard(appointment);
        },
      ),
    );
  }

  /// بناء بطاقة الموعد
  Widget _buildAppointmentCard(Appointment appointment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(appointment.status),
                  child: Icon(
                    _getStatusIcon(appointment.status),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment.farmerName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        appointment.consultationType.displayName,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      appointment.formattedTime,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      appointment.formattedDate,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // وصف المشكلة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                appointment.problemDescription,
                style: const TextStyle(fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: 12),

            // أزرار الإجراءات
            Row(
              children: [
                if (appointment.canBeConfirmed)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _confirmAppointment(appointment),
                      icon: const Icon(Icons.check, size: 18),
                      label: const Text('تأكيد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),

                if (appointment.canBeConfirmed) const SizedBox(width: 8),

                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewAppointmentDetails(appointment),
                    icon: const Icon(Icons.visibility, size: 18),
                    label: const Text('التفاصيل'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                PopupMenuButton<String>(
                  onSelected: (value) => _handleAppointmentAction(appointment, value),
                  itemBuilder: (context) => [
                    if (appointment.canBeRescheduled)
                      const PopupMenuItem(
                        value: 'reschedule',
                        child: Row(
                          children: [
                            Icon(Icons.schedule),
                            SizedBox(width: 8),
                            Text('إعادة جدولة'),
                          ],
                        ),
                      ),
                    if (appointment.canBeCancelled)
                      const PopupMenuItem(
                        value: 'cancel',
                        child: Row(
                          children: [
                            Icon(Icons.cancel, color: Colors.red),
                            SizedBox(width: 8),
                            Text('إلغاء'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'contact',
                      child: Row(
                        children: [
                          Icon(Icons.phone),
                          SizedBox(width: 8),
                          Text('اتصال'),
                        ],
                      ),
                    ),
                  ],
                  child: const Icon(Icons.more_vert),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.confirmed:
        return Colors.blue;
      case AppointmentStatus.inProgress:
        return Colors.purple;
      case AppointmentStatus.completed:
        return Colors.green;
      case AppointmentStatus.cancelled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Icons.pending;
      case AppointmentStatus.confirmed:
        return Icons.check_circle;
      case AppointmentStatus.inProgress:
        return Icons.work;
      case AppointmentStatus.completed:
        return Icons.done_all;
      case AppointmentStatus.cancelled:
        return Icons.cancel;
      case AppointmentStatus.noShow:
        return Icons.person_off;
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في المواعيد'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'ابحث باسم المزارع أو نوع الاستشارة...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSearch();
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ البحث
  void _performSearch() {
    if (_searchQuery.isNotEmpty) {
      // TODO: تنفيذ البحث في المواعيد
      debugPrint('البحث عن: $_searchQuery');
    }
  }

  /// تحديث المواعيد
  void _refreshAppointments() {
    context.read<appointments_cubit.AppointmentsCubit>().loadAdvisorAppointments(
      advisorId: widget.advisorId,
      date: _selectedDate,
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'statistics':
        _showStatistics();
        break;
      case 'export':
        _exportData();
        break;
    }
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    // TODO: عرض إحصائيات المواعيد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير الإحصائيات قريباً')),
    );
  }

  /// تصدير البيانات
  void _exportData() {
    // TODO: تصدير بيانات المواعيد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير التصدير قريباً')),
    );
  }

  /// تأكيد الموعد
  void _confirmAppointment(Appointment appointment) {
    context.read<appointments_cubit.AppointmentsCubit>().confirmAppointment(
      appointmentId: appointment.id,
    );
  }

  /// معالجة إجراءات الموعد
  void _handleAppointmentAction(Appointment appointment, String action) {
    switch (action) {
      case 'reschedule':
        _rescheduleAppointment(appointment);
        break;
      case 'cancel':
        _showCancelDialog(appointment);
        break;
      case 'contact':
        _contactFarmer(appointment);
        break;
    }
  }

  /// عرض حوار الإلغاء
  void _showCancelDialog(Appointment appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الموعد'),
        content: Text('هل أنت متأكد من إلغاء موعد ${appointment.farmerName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<appointments_cubit.AppointmentsCubit>().cancelAppointment(
                appointmentId: appointment.id,
                reason: 'تم الإلغاء من قبل المرشد',
                cancelledBy: 'advisor',
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }

  /// الاتصال بالمزارع
  void _contactFarmer(Appointment appointment) {
    // TODO: تنفيذ الاتصال بالمزارع
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بـ ${appointment.farmerName}')),
    );
  }

  /// عرض منتقي التاريخ
  ///
  /// يفتح منتقي التاريخ لاختيار تاريخ معين
  void _showDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadAppointments();
    }
  }

  /// إنشاء موعد جديد
  ///
  /// يفتح نموذج إنشاء موعد جديد
  void _createNewAppointment() {
    // TODO: تنفيذ إنشاء موعد جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير إنشاء موعد جديد قريباً')),
    );
  }

  /// عرض تفاصيل الموعد
  ///
  /// يفتح صفحة تفاصيل الموعد
  void _viewAppointmentDetails(dynamic appointment) {
    // TODO: تنفيذ عرض تفاصيل الموعد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تفاصيل الموعد')),
    );
  }

  // تم حذف _updateAppointmentStatus لأنها غير مستخدمة

  /// إعادة جدولة الموعد
  ///
  /// يفتح نموذج إعادة جدولة الموعد
  void _rescheduleAppointment(dynamic appointment) {
    // TODO: تنفيذ إعادة جدولة الموعد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير إعادة الجدولة قريباً')),
    );
  }

  // تم حذف _cancelAppointment لأنها غير مستخدمة (يوجد بديل محسن)
}
