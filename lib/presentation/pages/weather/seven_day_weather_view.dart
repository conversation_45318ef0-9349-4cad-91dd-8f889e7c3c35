import 'package:agriculture/imports.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// صفحة توقعات الطقس لـ 7 أيام
///
/// تعرض توقعات الطقس التفصيلية لمدة أسبوع كامل
/// مع معلومات زراعية مفيدة للمزارعين
class SevenDayWeatherView extends StatelessWidget {
  /// إنشاء صفحة توقعات الطقس لـ 7 أيام
  const SevenDayWeatherView({super.key});

  @override
  Widget build(BuildContext context) {
    // تحميل بيانات الطقس عند بناء الصفحة إذا لم تكن محملة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final weatherCubit = context.read<WeatherCubit>();
      if (weatherCubit.state is WeatherInitial) {
        LoggerService.info(
          'بدء تحميل بيانات الطقس من صفحة توقعات الأسبوع',
          tag: 'SevenDayWeatherView',
        );
        try {
          weatherCubit.loadAllWeatherData();
        } catch (e) {
          LoggerService.error(
            'خطأ في تحميل بيانات الطقس',
            error: e,
            tag: 'SevenDayWeatherView',
          );
        }
      }
    });

    return Scaffold(
      backgroundColor: AssetsColors.wether2,
      body: SafeArea(
        child: WeatherStateHandler(
          backgroundColor: AssetsColors.wether2,
          loadedBuilder: (context, state) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 10),
                    
                    // شريط التنقل العلوي
                    _buildTopNavigationBar(context),
                    const SizedBox(height: 10),
                    
                    // بطاقة معلومات الطقس الرئيسية
                    WeatherInfoCard(
                      weather: state.currentWeather,
                      weeklyForecast: state.weeklyForecast,
                      showDate: false, // لا نعرض التاريخ لأن لدينا عنوان مخصص
                    ),
                    const SizedBox(height: 20),
                    
                    // توقعات الطقس الأسبوعية
                    const EnhancedWeeklyForecastView(),
                    const SizedBox(height: 20),
                    
                    // معلومات الطقس الزراعية
                    WeatherAgriculturalInfo(
                      weather: state.currentWeather,
                      backgroundColor: AssetsColors.primary,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// بناء شريط التنقل العلوي
  Widget _buildTopNavigationBar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // زر الرجوع
        IconAppBar(
          backgroundColor: AssetsColors.primary,
          colorIcon: AssetsColors.kWhite,
          onTap: () {
            Navigator.of(context).pop();
          },
          icon: Icons.arrow_back_ios_new,
        ),
        
        // عنوان الصفحة
        Text(
          'توقعات 7 أيام',
          style: TextStyles.of(context).bodyLarge(
            fontSize: 18,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kWhite,
          ),
        ),
        
        // زر التحديث
        IconAppBar(
          backgroundColor: AssetsColors.primary,
          colorIcon: AssetsColors.kWhite,
          onTap: () {
            // تحديث بيانات الطقس
            context.read<WeatherCubit>().refreshWeatherData();
          },
          icon: Icons.refresh,
        ),
      ],
    );
  }
}
