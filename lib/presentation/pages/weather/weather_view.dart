import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jiffy/jiffy.dart';

import '../../../core/utils/get_weather_icons.dart';
import '../../../data/models/weather/weather_model.dart';
import '../../../imports.dart';

class WeatherView extends StatefulWidget {
  const WeatherView({super.key});

  @override
  State<WeatherView> createState() => _WeatherViewState();
}

class _WeatherViewState extends State<WeatherView> {
  @override
  void initState() {
    super.initState();
    // تحميل بيانات الطقس عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoggerService.info(
        'بدء تحميل بيانات الطقس من صفحة الطقس الرئيسية',
        tag: 'WeatherView',
      );
      try {
        context.read<WeatherCubit>().loadAllWeatherData();
      } catch (e) {
        LoggerService.error(
          'خطأ في تحميل بيانات الطقس',
          error: e,
          tag: 'WeatherView',
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AssetsColors.primary,
      body: SafeArea(
        child: BlocConsumer<WeatherCubit, WeatherState>(
          listener: (BuildContext context, WeatherState state) {
            if (state is WeatherError) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(state.message)));
            } else if (state is WeatherInitial || state is WeatherLoading) {
              Center(child: CustomLoadingAnimation());
            }
          },
          builder: (context, state) {
            if (state is WeatherInitial || state is WeatherLoading) {
              return const Center(child: CustomLoadingAnimation());
            }
            if (state is WeatherError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed:
                          () =>
                              context.read<WeatherCubit>().refreshWeatherData(),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }
            // if (state is GetWeatherLoading) {
            //   return CircularProgress();
            // }
            if (state is WeatherLoaded) {
              WeatherModel weather = state.currentWeather;
              return Padding(
                padding: const EdgeInsets.only(left: 18.0, right: 18),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,

                    // mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconAppBar(
                            backgroundColor: AssetsColors.wether2,
                            colorIcon: AssetsColors.kWhite,
                            onTap: () {},
                            icon: Icons.grid_view_rounded,
                          ),
                          Text(
                            Jiffy.now().format(pattern: 'dd MMMM yyyy'),
                            style: TextStyles.of(context).bodyLarge(
                              fontSize: 17,
                              fontFamily: AssetsFonts.cairo,
                              fontWeight: FontWeight.bold,
                              color: AssetsColors.kWhite,
                            ),
                          ),
                          IconAppBar(
                            backgroundColor: AssetsColors.wether2,
                            colorIcon: AssetsColors.kWhite,
                            onTap: () {
                              // استخدام دالة refreshWeatherData بدلاً من loadAllWeatherData
                              // لضمان جلب بيانات محدثة من الخادم دائمًا
                              context.read<WeatherCubit>().refreshWeatherData();
                            },
                            icon: Icons.refresh,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Container(
                        decoration: BoxDecoration(
                          color: AssetsColors.wether2,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              spacing: 15,
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: AssetsColors.kWhite,
                                  size: 30,
                                ),
                                Text(
                                  weather.name != null
                                      ? weather.name.toString()
                                      : 'غير معروف',
                                  //cubit.weather.name,
                                  style: TextStyles.of(context).bodyLarge(
                                    fontSize: 18,
                                    fontFamily: AssetsFonts.cairo,
                                    // fontWeight: FontWeight.bold,
                                    color: AssetsColors.kWhite,
                                  ),
                                ),
                              ],
                            ),
                            StackHomeWeather(
                              numberWeather:
                                  weather.main?.temp != null
                                      ? weather.main!.temp!.round().toString()
                                      : '0',
                              color: AssetsColors.kWhite,
                              imageAssets:'assets/icons/${weather.weather![0].icon!.replaceAll('n', 'd')}.png',


                            ),
                            Text(
                              weather.weather != null &&
                                      weather.weather!.isNotEmpty &&
                                      weather.weather![0].description != null
                                  ? weather.weather![0].description.toString()
                                  : 'سماء صافية',
                              style: TextStyles.of(context).bodyLarge(
                                fontSize: 16,
                                fontFamily: AssetsFonts.messiri,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.kWhite,
                              ),
                            ),
                            SizedBox(height: 20),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 10.0),
                              child: RowWeather(
                                temperature:
                                    weather.main?.temp != null
                                        ? weather.main!.temp!.round().toString()
                                        : '0',
                                humidity:
                                    weather.main?.humidity != null
                                        ? weather.main!.humidity.toString()
                                        : '0',
                                windSpeed:
                                    weather.wind?.speed != null
                                        ? weather.wind!.speed!.toString()
                                        : '0',
                                color: AssetsColors.wether2,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'اليوم',
                            style: TextStyles.of(context).bodyLarge(
                              fontSize: 17,
                              fontFamily: AssetsFonts.messiri,
                              fontWeight: FontWeight.bold,
                              color: AssetsColors.kWhite,
                            ),
                          ),
                          InkWell(
                            splashColor: Colors.red,
                            splashFactory: InkSplash.splashFactory,
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                RouteConstants.dayWeather,
                              );
                            },
                            child: Text(
                              'توقعات الطقس لـ 7 ايام',
                              style: TextStyles.of(context).bodyLarge(
                                fontSize: 17,
                                fontWeight: FontWeight.bold,
                                fontFamily: AssetsFonts.messiri,
                                color: AssetsColors.kWhite,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      EnhancedHourlyForecastView(),
                      SizedBox(height: 20),
                      // معلومات الطقس الزراعية
                      WeatherAgriculturalInfo(weather: weather),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              );
            }
            return const Center(child: Text('حدث خطأ غير متوقع'));
          },
        ),
      ),
    );
  }
}


