import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../imports.dart';

/// صفحة التحقق من رمز OTP
///
/// تعرض نموذج إدخال رمز التحقق المرسل إلى رقم الهاتف
class OtpVerificationPage extends StatelessWidget {
  final String phoneNumber;
  final String verificationId;

  const OtpVerificationPage({
    super.key,
    required this.phoneNumber,
    required this.verificationId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<PhoneAuthCubit>(context),
      child: _OtpVerificationPageContent(
        phoneNumber: phoneNumber,
        verificationId: verificationId,
      ),
    );
  }
}

class _OtpVerificationPageContent extends StatelessWidget {
  final String phoneNumber;
  final String verificationId;

  const _OtpVerificationPageContent({
    required this.phoneNumber,
    required this.verificationId,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PhoneAuthCubit>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'التحقق من رقم الهاتف',
          style: TextStyle(
            color: Colors.black,
            fontFamily: AssetsFonts.messiri,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocConsumer<PhoneAuthCubit, PhoneAuthState>(
        listener: (context, state) {
          if (state is PhoneAuthFailure) {
            _showErrorSnackBar(context, state.error);
          } else if (state is PhoneAuthVerified) {
            _handleVerificationSuccess(context, state);
          } else if (state is PhoneAuthResendSuccess) {
            _showSuccessSnackBar(context, 'تم إرسال رمز التحقق بنجاح');
          }
        },
        builder: (context, state) {
          return Form(
            key: cubit.otpFormKey,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // صورة رمز التحقق
                    Image.asset(
                      AssetsImages.otp,
                      height: 150,
                      width: 150,
                    ),
                    const SizedBox(height: 30),

                    // عنوان الصفحة
                    Text(
                      'التحقق من رقم الهاتف',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        fontFamily: AssetsFonts.messiri,
                      ),
                    ),
                    const SizedBox(height: 10),

                    // نص توضيحي
                    Text(
                      'تم إرسال رمز التحقق إلى الرقم $phoneNumber',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        fontFamily: AssetsFonts.messiri,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),

                    // نموذج إدخال رمز التحقق
                    OtpInputForm(phoneNumber: phoneNumber),
                    const SizedBox(height: 30),

                    // زر التحقق من الرمز
                    PhoneAuthButton(
                      text: 'تحقق من الرمز',
                      isLoading: state is PhoneAuthLoading,
                      onPressed: () {
                        // إخفاء لوحة المفاتيح
                        FocusScope.of(context).unfocus();

                        // التحقق من رمز OTP
                        cubit.verifyOTP(phoneNumber);
                      },
                    ),
                    const SizedBox(height: 20),

                    // زر إعادة إرسال الرمز
                    ResendOtpButton(
                      onResend: () {
                        cubit.resendVerificationCode(phoneNumber);
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// معالجة نجاح التحقق
  void _handleVerificationSuccess(
      BuildContext context, PhoneAuthVerified state) {
    if (state.hasCompletedProfile) {
      // إذا كان المستخدم قد أكمل ملفه الشخصي، توجيهه مباشرة إلى الصفحة الرئيسية
      Navigator.pushNamedAndRemoveUntil(
        context,
        RouteConstants.homeScreen,
        (route) => false,
      );
    } else {
      // إذا لم يكن المستخدم قد أكمل ملفه الشخصي، توجيهه إلى صفحة إكمال البروفايل
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => RegisterProfilePage(
            phoneNumber: state.phoneNumber,
          ),
        ),
      );
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
