import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../../core/constants/assets_images.dart';

import '../../../bloc/auth/phone/phone_auth_cubit.dart';
import '../../../widgets/auth/phone/phone_auth_button.dart';
import '../../../widgets/auth/phone/phone_input_form.dart';
import 'otp_verification_page.dart';

/// صفحة إدخال رقم الهاتف
///
/// تعرض نموذج إدخال رقم الهاتف للمصادقة
class PhoneInputPage extends StatelessWidget {
  const PhoneInputPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PhoneAuthCubit(),
      child: const _PhoneInputPageContent(),
    );
  }
}

class _PhoneInputPageContent extends StatelessWidget {
  const _PhoneInputPageContent();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PhoneAuthCubit>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'تسجيل الدخول برقم الهاتف',
          style: TextStyle(
            color: Colors.black,
            fontFamily: AssetsFonts.messiri,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocConsumer<PhoneAuthCubit, PhoneAuthState>(
        listener: (context, state) {
          if (state is PhoneAuthFailure) {
            _showErrorSnackBar(context, state.error);
          } else if (state is PhoneAuthCodeSent) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OtpVerificationPage(
                  phoneNumber: state.phoneNumber,
                  verificationId: state.verificationId,
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          return Form(
            key: cubit.phoneFormKey,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // صورة الهاتف
                    Image.asset(
                      AssetsImages.otp,
                      height: 150,
                      width: 150,
                    ),
                    const SizedBox(height: 30),

                    // عنوان الصفحة
                    Text(
                      'أدخل رقم هاتفك',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        fontFamily: AssetsFonts.messiri,
                      ),
                    ),
                    const SizedBox(height: 10),

                    // نص توضيحي
                    Text(
                      'سنرسل لك رمز تحقق لتأكيد رقم هاتفك',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        fontFamily: AssetsFonts.messiri,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),

                    // نموذج إدخال رقم الهاتف
                    const PhoneInputForm(),
                    const SizedBox(height: 30),

                    // زر إرسال رمز التحقق
                    PhoneAuthButton(
                      text: 'إرسال رمز التحقق',
                      isLoading: state is PhoneAuthLoading,
                      onPressed: () {
                        // إخفاء لوحة المفاتيح
                        FocusScope.of(context).unfocus();

                        // إرسال رمز التحقق
                        cubit.sendVerificationCode();
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }
}
