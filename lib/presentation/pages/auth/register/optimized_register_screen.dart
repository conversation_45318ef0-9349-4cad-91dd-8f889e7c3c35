import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:animate_do/animate_do.dart';

import '../../../../core/utils/validators/form_validators.dart';
import '../../../../imports.dart';
import '../../../widgets/auth/login/fixed_size_text_field.dart';
import '../../../widgets/auth/shared/index.dart';
import '../../../widgets/auth/shared/password_confirmation_field.dart';
import '../../../widgets/shared/error_message.dart';
import '../../../widgets/shared/progress_indicator_overlay.dart';

/// صفحة التسجيل المحسنة
///
/// تستخدم هذه الصفحة المكونات المحسنة مثل OptimizedButton و OptimizedTextField
/// لتحسين تجربة المستخدم وتوحيد مظهر التطبيق.
class OptimizedRegisterScreen extends StatefulWidget {
  const OptimizedRegisterScreen({super.key});

  @override
  State<OptimizedRegisterScreen> createState() =>
      _OptimizedRegisterScreenState();
}

class _OptimizedRegisterScreenState extends State<OptimizedRegisterScreen> {
  /// متحكم حقل البريد الإلكتروني
  final _emailController = TextEditingController();

  /// متحكم حقل كلمة المرور
  final _passwordController = TextEditingController();

  /// متحكم حقل تأكيد كلمة المرور
  final _confirmPasswordController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  /// حالة عرض رسائل الخطأ
  bool _showErrorMessages = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Form(
        key: _formKey,
        autovalidateMode: _showErrorMessages
            ? AutovalidateMode.always
            : AutovalidateMode.disabled,
        child: SingleChildScrollView(
          child: BlocConsumer<RegisterCubit, RegisterState>(
            listener: (context, state) {
              if (state is RegisterSuccess) {
                _handleRegisterSuccess(context, state);
              } else if (state is RegisterFailure) {
                _showErrorMessage(context, state.error);
                // تفعيل عرض رسائل الخطأ عند حدوث خطأ
                setState(() {
                  _showErrorMessages = true;
                });
              }
            },
            builder: (context, state) {
              return ProgressIndicatorOverlay(
                isVisible: state is RegisterLoading,
                message: AppConstants.infoProcessing,
                child: Column(
                  children: <Widget>[
                    _buildHeader(),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: Dimensions.paddingL),
                      child: Column(
                        children: <Widget>[
                          _buildRegisterForm(state),
                          SizedBox(height: Dimensions.spacingL),
                          _buildLoginLink(context),
                          SizedBox(height: Dimensions.spacingL),
                          _buildDivider(),
                          SizedBox(height: Dimensions.spacingL),
                          _buildSocialRegisterOptions(context),
                          SizedBox(height: Dimensions.spacingXXL),
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// بناء رأس الصفحة مع الصور والعنوان
  Widget _buildHeader() {
    return AuthHeader(title: "إنشاء حساب جديد");
  }

  /// بناء نموذج التسجيل (البريد الإلكتروني وكلمة المرور وتأكيد كلمة المرور وزر التسجيل)
  Widget _buildRegisterForm(state) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1800),
      child: Column(
        children: <Widget>[
          // حقل البريد الإلكتروني
          FixedSizeTextField(
            controller: _emailController,
            labelText: 'البريد الإلكتروني',
            hintText: 'أدخل بريدك الإلكتروني',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email,
            validator: FormValidators.validateEmail,
            height: 56.0,
          ),

          SizedBox(height: Dimensions.spacingM),

          // حقل كلمة المرور
          FixedSizeTextField(
            controller: _passwordController,
            labelText: 'كلمة المرور',
            hintText: 'أدخل كلمة المرور',
            isPassword: true,
            prefixIcon: Icons.lock,
            validator: FormValidators.validatePassword,
            height: 56.0,
          ),

          SizedBox(height: Dimensions.spacingM),

          // حقل تأكيد كلمة المرور
          PasswordConfirmationField(
            controller: _confirmPasswordController,
            passwordController: _passwordController,
            labelText: 'تأكيد كلمة المرور',
            hintText: 'أدخل كلمة المرور مرة أخرى',
          ),

          SizedBox(height: Dimensions.spacingL),

          // عرض رسالة خطأ إذا كانت موجودة
          if (state is RegisterFailure) ...[
            ErrorMessage(
              message: state.error,
              padding: const EdgeInsets.only(bottom: 16.0),
            ),
          ],

          // زر التسجيل
          OptimizedButton(
            text: 'إنشاء حساب',
            type: OptimizedButtonType.primary,
            size: OptimizedButtonSize.large,
            isFullWidth: true,
            isLoading: state is RegisterLoading,
            onPressed: _handleRegisterButtonPressed,
          ),
        ],
      ),
    );
  }

  ///  بناء رابط تسجيل الدخول
  Widget _buildLoginLink(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1900),
      child: RichTextLink.registerTextArabic(
          link: 'تسجيل الدخول',
          mainTxt: 'لديك حساب بالفعل؟',
          onTap: () {
            Navigator.pushReplacementNamed(context, RouteConstants.login);
          }),
    );
  }

  /// بناء الفاصل
  Widget _buildDivider() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1900),
      child: const TextWithDivider(),
    );
  }

  /// بناء خيارات التسجيل الاجتماعية
  Widget _buildSocialRegisterOptions(BuildContext context) {
    return SocialLoginButtons(
      titleText: 'أو سجل باستخدام',
      onGoogleTap: () => _handleGoogleRegister(context),
      onFacebookTap: () => _handleFacebookRegister(context),
      onPhoneTap: () => _handlePhoneRegister(context),
    );
  }

  /// معالجة النقر على زر التسجيل
  void _handleRegisterButtonPressed() {
    // تفعيل عرض رسائل الخطأ
    setState(() {
      _showErrorMessages = true;
    });

    if (_formKey.currentState!.validate()) {
      try {
        // نقل قيم الحقول إلى RegisterCubit
        final registerCubit = context.read<RegisterCubit>();
        registerCubit.emailController.text = _emailController.text;
        registerCubit.passwordController.text = _passwordController.text;

        // استدعاء دالة التسجيل
        registerCubit.registerWithEmailAndPassword();

        LoggerService.debug(
          'تم استدعاء registerWithEmailAndPassword بنجاح',
          tag: 'OptimizedRegisterScreen',
        );
      } catch (e) {
        LoggerService.error(
          'خطأ في استدعاء registerWithEmailAndPassword',
          error: e,
          tag: 'OptimizedRegisterScreen',
        );

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      // عرض رسالة للمستخدم بأن هناك أخطاء في النموذج
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppConstants.errorValidation),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// معالجة التسجيل باستخدام Google
  void _handleGoogleRegister(BuildContext context) {
    LoggerService.debug(
      'بدء عملية التسجيل باستخدام Google',
      tag: 'OptimizedRegisterScreen',
    );
    context.read<RegisterCubit>().registerWithGoogle();
  }

  /// معالجة التسجيل باستخدام Facebook
  void _handleFacebookRegister(BuildContext context) {
    context.read<RegisterCubit>().registerWithFacebook();
  }

  /// معالجة التسجيل باستخدام رقم الهاتف
  void _handlePhoneRegister(BuildContext context) {
    // الانتقال إلى صفحة إدخال رقم الهاتف
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PhoneInputPage(),
      ),
    );
  }

  /// معالجة نجاح التسجيل
  void _handleRegisterSuccess(BuildContext context, RegisterSuccess state) {
    // عرض رسالة نجاح للمستخدم
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء الحساب بنجاح!'),
        backgroundColor: Colors.green,
      ),
    );

    // حفظ المتغيرات المطلوبة قبل العملية غير المتزامنة
    final userId = state.user.id;
    final userPhone = state.user.phone.isNotEmpty ? state.user.phone : null;
    final authRepo = context.read<RegisterCubit>().authRepository;

    LoggerService.debug(
      'معالجة نجاح التسجيل: معرف المستخدم = $userId، رقم الهاتف = $userPhone',
      tag: 'OptimizedRegisterScreen',
    );

    // استخدام Future.microtask للتأكد من أن التحقق يحدث بعد بناء الواجهة
    Future.microtask(() async {
      try {
        LoggerService.debug(
          'التحقق من اكتمال ملف المستخدم...',
          tag: 'OptimizedRegisterScreen',
        );

        // التحقق مما إذا كان المستخدم قد أكمل ملفه الشخصي
        final hasCompletedProfile =
            await authRepo.hasUserCompletedProfile(userId);

        LoggerService.debug(
          'نتيجة التحقق من اكتمال ملف المستخدم: $hasCompletedProfile',
          tag: 'OptimizedRegisterScreen',
        );

        // تحديث المتغير العام uid
        uid = userId;

        LoggerService.debug(
          'تم تحديث المتغير العام uid = "$uid"',
          tag: 'OptimizedRegisterScreen',
        );

        // التحقق من أن الـ State لا يزال مرتبطًا بالشجرة
        if (context.mounted) {
          // إذا كان المستخدم قد أكمل ملفه الشخصي، نوجهه إلى الصفحة الرئيسية
          if (hasCompletedProfile) {
            LoggerService.debug(
              'المستخدم أكمل ملفه الشخصي، توجيهه إلى الصفحة الرئيسية',
              tag: 'OptimizedRegisterScreen',
            );

            // تأكد من تعيين متغيرات الجلسة
            SharedPrefs.setBool('isAuth', true);
            SharedPrefs.setString('uid', userId);

            // توجيه المستخدم إلى الصفحة الرئيسية
            AppRouter.navigateToHome(context);
          } else {
            // إذا لم يكمل المستخدم ملفه الشخصي، نوجهه إلى صفحة إكمال البروفايل
            LoggerService.debug(
              'المستخدم لم يكمل ملفه الشخصي، توجيهه إلى صفحة إكمال البروفايل',
              tag: 'OptimizedRegisterScreen',
            );

            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                builder: (context) => RegisterProfilePage(
                  // تمرير رقم الهاتف فقط إذا كان موجودًا
                  phoneNumber: userPhone,
                ),
              ),
              (route) => false,
            );
          }
        }
      } catch (e) {
        LoggerService.error(
          'خطأ في التحقق من اكتمال ملف المستخدم',
          error: e,
          tag: 'OptimizedRegisterScreen',
        );

        if (context.mounted) {
          _showErrorMessage(context, 'حدث خطأ أثناء التحقق من ملف المستخدم');

          // على الرغم من الخطأ، نوجه المستخدم إلى صفحة إكمال البروفايل
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => RegisterProfilePage(
                // تمرير رقم الهاتف فقط إذا كان موجودًا
                phoneNumber: userPhone,
              ),
            ),
            (route) => false,
          );
        }
      }
    });
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
