import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../data/repositories/auth_repository.dart';
import '../../../bloc/auth/core/auth_cubit.dart';
import '../../../bloc/auth/login/login_cubit.dart';
import 'optimized_login_screen.dart';

/// صفحة تسجيل الدخول
///
/// هذه الصفحة هي نقطة الدخول لواجهة تسجيل الدخول.
/// تستخدم الصفحة المحسنة OptimizedLoginScreen لعرض واجهة تسجيل الدخول.
class LoginPage extends StatelessWidget {
  /// منشئ صفحة تسجيل الدخول
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(
        RepositoryProvider.of<AuthRepository>(context),
        context.read<AuthCubit>(),
      ),
      child: const OptimizedLoginScreen(),
    );
  }
}
