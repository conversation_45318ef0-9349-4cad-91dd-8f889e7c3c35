import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/assets_fonts.dart';
import '../../../../core/constants/constants_url.dart';
import '../../../../core/utils/logging/logger_service.dart';
import '../../../../routing/app_router.dart';
import '../../../bloc/auth/core/auth_cubit.dart';
import '../../../bloc/auth/register_profile/register_profile_cubit.dart';
import '../../../widgets/auth/register_profile/phone_display.dart';
import '../../../widgets/auth/register_profile/profile_form_fields.dart';
import '../../../widgets/auth/register_profile/profile_image_picker.dart';
import '../../../widgets/auth/register_profile/profile_submit_button.dart';

/// صفحة إكمال ملف المستخدم
///
/// تعرض نموذج إكمال بيانات المستخدم بعد التسجيل
class RegisterProfilePage extends StatelessWidget {
  final String? phoneNumber; // جعل رقم الهاتف اختياريًا

  const RegisterProfilePage({
    super.key,
    this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RegisterProfileCubit(context.read<AuthCubit>()),
      child: _RegisterProfileContent(phoneNumber: phoneNumber),
    );
  }
}

class _RegisterProfileContent extends StatelessWidget {
  final String? phoneNumber;

  const _RegisterProfileContent({
    this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<RegisterProfileCubit>();

    return BlocListener<RegisterProfileCubit, RegisterProfileState>(
      listener: (context, state) {
        if (state is RegisterProfileFailure) {
          _showErrorSnackBar(context, state.error);
        } else if (state is RegisterProfileSuccess) {
          _showSuccessSnackBar(context, 'تم تسجيل بياناتك بنجاح!');

          // تسجيل نجاح عملية التسجيل
          LoggerService.debug(
            'تم إكمال التسجيل بنجاح، جاري التوجيه إلى الصفحة الرئيسية. uid = "$uid"',
            tag: 'RegisterProfilePage',
          );

          // تأخير التنقل قليلاً للسماح بإكمال العمليات الأخرى
          Future.delayed(Duration(milliseconds: 500), () {
            if (context.mounted) {
              // التنقل إلى الصفحة الرئيسية بعد نجاح التسجيل
              LoggerService.debug(
                'بدء التنقل إلى الصفحة الرئيسية',
                tag: 'RegisterProfilePage',
              );

              AppRouter.navigateToHome(context);
            }
          });
        } else if (state is RegisterProfileGovernorateChanged) {
          if (state.cities.isEmpty) {
            _showWarningSnackBar(
                context, 'لا توجد مناطق مسجلة لهذه المدينة حالياً');
          } else {
            _showSuccessSnackBar(
              context,
              'تم العثور على ${state.cities.length} منطقة في مدينة ${state.governorate}',
            );
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'إكمال ملف المستخدم',
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.tealAccent.shade700,
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.tealAccent.shade700,
            statusBarIconBrightness: Brightness.light,
          ),
          elevation: 0,
          automaticallyImplyLeading: false,
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.tealAccent.shade700,
                Colors.tealAccent.shade700.withAlpha(128),
              ],
            ),
          ),
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 400),
                child: Form(
                  key: cubit.formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // صورة المستخدم
                      const ProfileImagePicker(),
                      const SizedBox(height: 16),

                      // رقم الهاتف (إذا كان متوفرًا)
                      if (phoneNumber != null && phoneNumber!.isNotEmpty) ...[
                        PhoneDisplay(phoneNumber: phoneNumber!),
                        const SizedBox(height: 24),
                      ],

                      // حقول النموذج
                      const ProfileFormFields(),
                      const SizedBox(height: 24),

                      // زر إكمال التسجيل
                      ProfileSubmitButton(phoneNumber: phoneNumber),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض رسالة تحذير
  void _showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontFamily: AssetsFonts.messiri),
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
