# واجهة الاستشاري الزراعي المحسنة

## نظرة عامة

تم تطوير واجهة محسنة للاستشاري الزراعي تحتوي على شريط تنقل سفلي مع 5 واجهات رئيسية:

## الواجهات المتاحة

### 1. 🏠 لوحة التحكم الرئيسية
- **الموقع**: الواجهة الأولى من اليمين
- **الوصف**: لوحة تحكم شاملة تعرض:
  - بطاقة ترحيب مخصصة
  - إحصائيات سريعة (الاستشارات الجديدة، المواعيد اليوم)
  - إجراءات سريعة (إضافة نصيحة، تحديث الحالة)
  - النشاط الأخير

### 2. 💬 الاستشارة الفورية
- **الوصف**: إدارة الاستشارات العاجلة
- **المميزات**:
  - عرض الاستشارات الجديدة
  - تمييز الاستشارات العاجلة
  - إمكانية الرد الفوري أو الجدولة
  - معلومات المزارع والمشكلة

### 3. 📅 حجز المواعيد
- **الوصف**: إدارة مواعيد الاستشارات
- **التبويبات**:
  - اليوم
  - هذا الأسبوع
  - الشهر القادم
- **المميزات**:
  - عرض تفاصيل الموعد
  - حالة الموعد (مؤكد/في الانتظار)
  - بدء الاستشارة أو إعادة الجدولة

### 4. 👁️ مراقبة النبات
- **الوصف**: مراقبة حالة النباتات
- **المميزات**:
  - عرض حالة النباتات (صحية/تحتاج مراجعة/مشكلة)
  - آخر تحديث لكل نبات
  - تصنيف بالألوان حسب الحالة
  - إمكانية عرض التفاصيل

### 5. 📊 لوحة البيانات
- **الوصف**: تحليلات وإحصائيات شاملة
- **المعلومات المعروضة**:
  - إجمالي الاستشارات
  - معدل الرضا
  - المزارعون النشطون
  - مقارنات شهرية

## كيفية الاستخدام

### 1. الاستيراد
```dart
import 'package:agriculture/presentation/pages/advisor_dashboard/enhanced_advisor_dashboard.dart';
```

### 2. الاستخدام المباشر
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const EnhancedAdvisorDashboard(),
  ),
);
```

### 3. الاستخدام مع BLoC
```dart
import 'package:agriculture/presentation/pages/advisor_dashboard/advisor_main_screen.dart';

Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const AdvisorMainScreen(),
  ),
);
```

## المميزات التقنية

### 🎨 التصميم
- تصميم Material Design حديث
- ألوان متناسقة مع هوية التطبيق
- رسوم متحركة سلسة للتنقل
- واجهة مستخدم سهلة الاستخدام

### 📱 الاستجابة
- متوافق مع جميع أحجام الشاشات
- تخطيط مرن يتكيف مع المحتوى
- أيقونات واضحة ومفهومة

### ⚡ الأداء
- تحميل سريع للبيانات
- إدارة فعالة للحالة باستخدام BLoC
- تحديث تلقائي للبيانات

### 🔄 التفاعل
- Pull-to-refresh في جميع القوائم
- تحديث فوري للحالة
- رسائل تأكيد للإجراءات

## الملفات المتضمنة

1. `enhanced_advisor_dashboard.dart` - الواجهة الرئيسية
2. `advisor_main_screen.dart` - إعداد BLoC والتنقل
3. `index.dart` - ملف التصدير
4. `README.md` - هذا الملف

## التخصيص

يمكن تخصيص الواجهة من خلال:
- تعديل الألوان في `AssetsColors`
- إضافة واجهات جديدة في `PageView`
- تخصيص المحتوى في كل صفحة
- إضافة وظائف جديدة

## المتطلبات

- Flutter SDK
- BLoC package
- المودلز والمستودعات المطلوبة

## الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة الكود أو التواصل مع فريق التطوير.
