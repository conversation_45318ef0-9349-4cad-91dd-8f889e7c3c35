import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/widgets/shared/advisor/index.dart';
import 'package:agriculture/data/datasources/local/shared_prefs.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';

/// واجهة الاستشاري الزراعي المحسنة مع شريط التنقل السفلي
class EnhancedAdvisorDashboard extends StatefulWidget {
  const EnhancedAdvisorDashboard({super.key});

  @override
  State<EnhancedAdvisorDashboard> createState() => _EnhancedAdvisorDashboardState();
}

class _EnhancedAdvisorDashboardState extends State<EnhancedAdvisorDashboard> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadAdvisorData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// تحميل بيانات المرشد
  Future<void> _loadAdvisorData() async {
    try {
      final uid = SharedPrefs.getString('uid') ?? '';
      if (uid.isNotEmpty) {
        // تحميل البيانات الأولية
        context.read<AdvisorCubit>().getAdvisorConsultations(advisorId: uid);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المرشد: $e');
    }
  }

  /// تغيير الصفحة
  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لوحة تحكم المرشد الزراعي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              _getPageTitle(_currentIndex),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications, color: Colors.white),
            onPressed: _showNotifications,
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: _showSettings,
          ),
        ],
      ),
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: [
          // 1. لوحة التحكم الرئيسية
          _buildDashboardPage(),
          // 2. الاستشارة الفورية
          _buildInstantConsultationPage(),
          // 3. حجز المواعيد
          _buildAppointmentsPage(),
          // 4. مراقبة النبات
          _buildPlantMonitoringPage(),
          // 5. لوحة البيانات
          _buildDataDashboardPage(),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onPageChanged,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AssetsColors.dufaultGreencolor,
        unselectedItemColor: Colors.grey[600],
        selectedFontSize: 12,
        unselectedFontSize: 10,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'لوحة التحكم',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble),
            label: 'الاستشارات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'المواعيد',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.eco),
            label: 'مراقبة النبات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'التقارير',
          ),
        ],
      ),
    );
  }

  /// الحصول على عنوان الصفحة
  String _getPageTitle(int index) {
    switch (index) {
      case 0:
        return 'لوحة التحكم الرئيسية';
      case 1:
        return 'إدارة الاستشارات';
      case 2:
        return 'جدولة المواعيد';
      case 3:
        return 'مراقبة النباتات';
      case 4:
        return 'التقارير والإحصائيات';
      default:
        return '';
    }
  }

  /// بناء صفحة لوحة التحكم الرئيسية
  Widget _buildDashboardPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 20),
          _buildQuickStatsCards(),
          const SizedBox(height: 20),
          _buildQuickActions(),
          const SizedBox(height: 20),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  /// بناء بطاقة الترحيب المحسنة
  Widget _buildWelcomeCard() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        final hour = DateTime.now().hour;
        String greeting;
        if (hour < 12) {
          greeting = 'صباح الخير';
        } else if (hour < 17) {
          greeting = 'مساء الخير';
        } else {
          greeting = 'مساء الخير';
        }

        // حساب الإحصائيات من الحالة الحالية
        int newConsultations = 0;
        int todayAppointments = 0;

        if (state is ConsultationsLoaded) {
          newConsultations = state.consultations
              .where((c) => c.status == ConsultationStatus.pending)
              .length;
          // يمكن إضافة حساب المواعيد هنا عند توفر البيانات
          todayAppointments = 3; // قيمة مؤقتة
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AssetsColors.dufaultGreencolor,
                AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          greeting,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'مرحباً بك في لوحة تحكم المرشد الزراعي',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // إحصائيات سريعة في بطاقة الترحيب
              Row(
                children: [
                  Expanded(
                    child: _buildWelcomeStatItem(
                      'استشارات جديدة',
                      '$newConsultations',
                      Icons.chat_bubble_outline,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildWelcomeStatItem(
                      'مواعيد اليوم',
                      '$todayAppointments',
                      Icons.calendar_today,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _onPageChanged(1),
                      icon: const Icon(Icons.chat_bubble, size: 18),
                      label: const Text('عرض الاستشارات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AssetsColors.dufaultGreencolor,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _onPageChanged(2),
                      icon: const Icon(Icons.calendar_today, size: 18),
                      label: const Text('عرض المواعيد'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white, width: 1.5),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر إحصائي في بطاقة الترحيب
  Widget _buildWelcomeStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات السريعة المحسنة
  Widget _buildQuickStatsCards() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        // حساب الإحصائيات الفعلية من البيانات
        List<AdvisorStatData> stats = [];

        if (state is ConsultationsLoaded) {
          final consultations = state.consultations;

          // حساب الاستشارات حسب الحالة
          final newCount = consultations.where((c) => c.status == ConsultationStatus.pending).length;
          final inProgressCount = consultations.where((c) => c.status == ConsultationStatus.inProgress).length;
          final completedCount = consultations.where((c) => c.status == ConsultationStatus.answered).length;
          // تحديد الاستشارات العاجلة بناءً على الوقت (أقل من ساعة)
          final urgentCount = consultations.where((c) {
            final createdDate = DateTime.tryParse(c.createdAt);
            if (createdDate != null) {
              final difference = DateTime.now().difference(createdDate);
              return difference.inHours < 1 && c.status == ConsultationStatus.pending;
            }
            return false;
          }).length;

          stats = [
            AdvisorStatData(
              label: 'استشارات جديدة',
              count: '$newCount',
              color: Colors.blue,
              icon: Icons.chat_bubble_outline,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'قيد المعالجة',
              count: '$inProgressCount',
              color: Colors.orange,
              icon: Icons.hourglass_empty,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'مكتملة',
              count: '$completedCount',
              color: Colors.green,
              icon: Icons.check_circle_outline,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'عاجلة',
              count: '$urgentCount',
              color: Colors.red,
              icon: Icons.priority_high,
              onTap: () => _onPageChanged(1),
            ),
          ];
        } else {
          // بيانات افتراضية عند التحميل
          stats = [
            AdvisorStatData(
              label: 'استشارات جديدة',
              count: '0',
              color: Colors.blue,
              icon: Icons.chat_bubble_outline,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'قيد المعالجة',
              count: '0',
              color: Colors.orange,
              icon: Icons.hourglass_empty,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'مكتملة',
              count: '0',
              color: Colors.green,
              icon: Icons.check_circle_outline,
              onTap: () => _onPageChanged(1),
            ),
            AdvisorStatData(
              label: 'عاجلة',
              count: '0',
              color: Colors.red,
              icon: Icons.priority_high,
              onTap: () => _onPageChanged(1),
            ),
          ];
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات اليوم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AssetsColors.dufaultGreencolor,
              ),
            ),
            const SizedBox(height: 12),
            // الصف الأول - استشارات جديدة وقيد المعالجة
            AdvisorStatsRow(
              stats: stats.take(2).toList(),
              spacing: 12,
            ),
            const SizedBox(height: 12),
            // الصف الثاني - مكتملة وعاجلة
            AdvisorStatsRow(
              stats: stats.skip(2).take(2).toList(),
              spacing: 12,
            ),
          ],
        );
      },
    );
  }



  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildActionCard(
              title: 'إضافة نصيحة',
              icon: Icons.lightbulb,
              color: Colors.amber,
              onTap: _addTip,
            ),
            const SizedBox(width: 12),
            _buildActionCard(
              title: 'تحديث الحالة',
              icon: Icons.update,
              color: Colors.green,
              onTap: _updateStatus,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إجراء
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 80, // تحديد ارتفاع ثابت
          padding: const EdgeInsets.all(12), // تقليل الحشو
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: color,
                size: 24, // تقليل حجم الأيقونة
              ),
              const SizedBox(height: 6), // تقليل المسافة
              Flexible(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12, // تقليل حجم الخط
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء النشاط الأخير المحسن
  Widget _buildRecentActivity() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الاستشارات الحديثة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
                TextButton(
                  onPressed: () => _onPageChanged(1),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),

            if (state is ConsultationsLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (state is ConsultationsLoaded) ...[
              if (state.consultations.isEmpty)
                _buildEmptyConsultationsState()
              else
                _buildRecentConsultationsList(state.consultations.take(3).toList()),
            ] else if (state is AdvisorError)
              _buildErrorState(state.message)
            else
              _buildEmptyConsultationsState(),
          ],
        );
      },
    );
  }

  /// بناء حالة عدم وجود استشارات
  Widget _buildEmptyConsultationsState() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.eco,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد استشارات جديدة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الاستشارات الجديدة هنا عندما يرسلها المزارعون',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الاستشارات الحديثة
  Widget _buildRecentConsultationsList(List<ConsultationModel> consultations) {
    return Column(
      children: consultations.map((consultation) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: AdvisorConsultationCard(
            consultation: consultation,
            isAdvisor: true,
            onTap: () => _showConsultationDetails(consultation),
            onReply: () => _replyToConsultation(consultation),
          ),
        );
      }).toList(),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String message) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAdvisorData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }



  /// بناء صفحة الاستشارات المحسنة
  Widget _buildInstantConsultationPage() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: _loadAdvisorData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الصفحة مع الإحصائيات
                _buildConsultationsHeader(state),
                const SizedBox(height: 20),

                // فلاتر الاستشارات
                _buildConsultationFilters(),
                const SizedBox(height: 20),

                // قائمة الاستشارات
                if (state is ConsultationsLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (state is ConsultationsLoaded) ...[
                  if (state.consultations.isEmpty)
                    _buildEmptyConsultationsState()
                  else
                    _buildConsultationsList(state.consultations),
                ] else if (state is AdvisorError)
                  _buildErrorState(state.message)
                else
                  _buildConsultationsWithSampleData(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء رأس صفحة الاستشارات
  Widget _buildConsultationsHeader(AdvisorState state) {
    int totalCount = 0;
    int pendingCount = 0;
    int urgentCount = 0;
    int todayCount = 0;

    if (state is ConsultationsLoaded) {
      final consultations = state.consultations;
      totalCount = consultations.length;
      pendingCount = consultations.where((c) => c.status == ConsultationStatus.pending).length;
      // تحديد الاستشارات العاجلة بناءً على الوقت (أقل من ساعة)
      urgentCount = consultations.where((c) {
        final createdDate = DateTime.tryParse(c.createdAt);
        if (createdDate != null) {
          final difference = DateTime.now().difference(createdDate);
          return difference.inHours < 1 && c.status == ConsultationStatus.pending;
        }
        return false;
      }).length;

      final today = DateTime.now();
      todayCount = consultations.where((c) {
        final createdDate = DateTime.tryParse(c.createdAt);
        return createdDate != null &&
               createdDate.year == today.year &&
               createdDate.month == today.month &&
               createdDate.day == today.day;
      }).length;
    } else {
      // بيانات تجريبية
      totalCount = 45;
      pendingCount = 12;
      urgentCount = 3;
      todayCount = 8;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade100,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.chat_bubble,
                color: Colors.blue.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'إدارة الاستشارات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // إحصائيات سريعة
          AdvisorStatsRow(
            stats: [
              AdvisorStatData(
                label: 'مكتملة',
                count: '$totalCount',
                color: Colors.blue,
                icon: Icons.chat_bubble_outline,
              ),
              AdvisorStatData(
                label: 'عاجل',
                count: '$urgentCount',
                color: Colors.red,
                icon: Icons.priority_high,
              ),
              AdvisorStatData(
                label: 'انتظار',
                count: '$pendingCount',
                color: Colors.orange,
                icon: Icons.hourglass_empty,
              ),
              AdvisorStatData(
                label: 'جديد',
                count: '$todayCount',
                color: Colors.green,
                icon: Icons.today,
              ),
            ],
            spacing: 8,
          ),
        ],
      ),
    );
  }

  /// بناء فلاتر الاستشارات
  Widget _buildConsultationFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildFilterChip('الكل', true),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterChip('جديدة', false),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterChip('عاجلة', false),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterChip('مكتملة', false),
          ),
        ],
      ),
    );
  }

  /// بناء شريحة الفلتر
  Widget _buildFilterChip(String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        // تطبيق الفلتر
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? AssetsColors.dufaultGreencolor : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AssetsColors.dufaultGreencolor : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade600,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// بناء قائمة الاستشارات
  Widget _buildConsultationsList(List<ConsultationModel> consultations) {
    return Column(
      children: consultations.map((consultation) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: AdvisorConsultationCard(
            consultation: consultation,
            isAdvisor: true,
            onTap: () => _showConsultationDetails(consultation),
            onReply: () => _replyToConsultation(consultation),
          ),
        );
      }).toList(),
    );
  }

  /// بناء الاستشارات مع بيانات تجريبية
  Widget _buildConsultationsWithSampleData() {
    return Column(
      children: List.generate(3, (index) => _buildSampleConsultationCard(index)),
    );
  }

  /// بناء بطاقة استشارة تجريبية
  Widget _buildSampleConsultationCard(int index) {
    final consultations = [
      {
        'name': 'أحمد محمد',
        'problem': 'مشكلة في نبات الطماطم - أوراق صفراء',
        'description': 'لاحظت ظهور أوراق صفراء على نباتات الطماطم في الحقل. المساحة حوالي 100 متر مربع.',
        'time': 'منذ 5 دقائق',
        'urgent': true,
      },
      {
        'name': 'فاطمة علي',
        'problem': 'استفسار عن زراعة الخيار',
        'description': 'أريد معرفة أفضل وقت لزراعة الخيار في المنطقة الجنوبية.',
        'time': 'منذ 15 دقيقة',
        'urgent': false,
      },
      {
        'name': 'محمد حسن',
        'problem': 'مشكلة في الري',
        'description': 'نظام الري لا يعمل بشكل صحيح، النباتات تذبل.',
        'time': 'منذ 30 دقيقة',
        'urgent': true,
      },
    ];

    final consultation = consultations[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AssetsColors.dufaultGreencolor,
                  child: Text(
                    (consultation['name'] as String).substring(0, 1),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        consultation['name'] as String,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        consultation['time'] as String,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (consultation['urgent'] as bool)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'عاجل',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              consultation['problem'] as String,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              consultation['description'] as String,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _respondToConsultation(),
                    icon: const Icon(Icons.reply, size: 18),
                    label: const Text('الرد الآن'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.dufaultGreencolor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () => _scheduleConsultation(),
                  icon: const Icon(Icons.schedule, size: 18),
                  label: const Text('جدولة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AssetsColors.dufaultGreencolor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صفحة حجز المواعيد
  Widget _buildAppointmentsPage() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            color: Colors.white,
            child: TabBar(
              labelColor: AssetsColors.dufaultGreencolor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AssetsColors.dufaultGreencolor,
              tabs: const [
                Tab(text: 'اليوم'),
                Tab(text: 'هذا الأسبوع'),
                Tab(text: 'الشهر القادم'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildAppointmentsList('اليوم'),
                _buildAppointmentsList('هذا الأسبوع'),
                _buildAppointmentsList('الشهر القادم'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList(String period) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) {
        return _buildAppointmentCard(index);
      },
    );
  }

  /// بناء بطاقة الموعد
  Widget _buildAppointmentCard(int index) {
    final appointments = [
      {
        'name': 'أحمد محمد علي',
        'type': 'استشارة زراعية - زراعة الخضروات',
        'time': '10:00 ص - 11:00 ص',
        'date': 'اليوم',
        'status': 'مؤكد',
      },
      {
        'name': 'سارة أحمد',
        'type': 'استشارة مراقبة النبات',
        'time': '2:00 م - 3:00 م',
        'date': 'اليوم',
        'status': 'في الانتظار',
      },
      {
        'name': 'محمد حسن',
        'type': 'استشارة أمراض النبات',
        'time': '4:00 م - 5:00 م',
        'date': 'غداً',
        'status': 'مؤكد',
      },
    ];

    final appointment = appointments[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AssetsColors.dufaultGreencolor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.person,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment['name'] as String,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        appointment['type'] as String,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: appointment['status'] == 'مؤكد'
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    appointment['status'] as String,
                    style: TextStyle(
                      color: (appointment['status'] as String) == 'مؤكد' ? Colors.green : Colors.orange,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  appointment['time'] as String,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  appointment['date'] as String,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _startConsultation(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.dufaultGreencolor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('بدء الاستشارة'),
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton(
                  onPressed: () => _rescheduleAppointment(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AssetsColors.dufaultGreencolor,
                  ),
                  child: const Text('إعادة جدولة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صفحة مراقبة النبات المحسنة
  Widget _buildPlantMonitoringPage() {
    return RefreshIndicator(
      onRefresh: _loadAdvisorData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الصفحة
            _buildPlantMonitoringHeader(),
            const SizedBox(height: 20),

            // إحصائيات النباتات
            _buildPlantStatsCards(),
            const SizedBox(height: 20),

            // قائمة النباتات
            _buildPlantsList(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس صفحة مراقبة النبات
  Widget _buildPlantMonitoringHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade50,
            Colors.green.shade100,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.eco,
                color: Colors.green.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'مراقبة النباتات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'متابعة حالة النباتات والمحاصيل',
            style: TextStyle(
              fontSize: 14,
              color: Colors.green.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات إحصائيات النباتات
  Widget _buildPlantStatsCards() {
    return AdvisorStatsRow(
      stats: [
        AdvisorStatData(
          label: 'صحي',
          count: '15',
          color: Colors.green,
          icon: Icons.eco,
        ),
        AdvisorStatData(
          label: 'بحاجة عناية',
          count: '3',
          color: Colors.orange,
          icon: Icons.warning,
        ),
        AdvisorStatData(
          label: 'مريض',
          count: '1',
          color: Colors.red,
          icon: Icons.error,
        ),
        AdvisorStatData(
          label: 'إجمالي',
          count: '19',
          color: Colors.blue,
          icon: Icons.nature,
        ),
      ],
      spacing: 8,
    );
  }

  /// بناء قائمة النباتات
  Widget _buildPlantsList() {
    final plants = [
      {
        'name': 'نبات رقم 1',
        'type': 'طماطم',
        'status': 'صحي',
        'humidity': '75%',
        'temperature': '25°C',
        'lastUpdate': 'منذ ساعتين',
        'color': Colors.green,
        'icon': Icons.eco,
      },
      {
        'name': 'نبات رقم 2',
        'type': 'خيار',
        'status': 'بحاجة عناية',
        'humidity': '40%',
        'temperature': '28°C',
        'lastUpdate': 'منذ 4 ساعات',
        'color': Colors.orange,
        'icon': Icons.warning,
      },
      {
        'name': 'نبات رقم 3',
        'type': 'فلفل',
        'status': 'مريض',
        'humidity': '85%',
        'temperature': '22°C',
        'lastUpdate': 'منذ يوم',
        'color': Colors.red,
        'icon': Icons.error,
      },
      {
        'name': 'نبات رقم 4',
        'type': 'باذنجان',
        'status': 'صحي',
        'humidity': '70%',
        'temperature': '24°C',
        'lastUpdate': 'منذ 3 ساعات',
        'color': Colors.green,
        'icon': Icons.eco,
      },
    ];

    return Column(
      children: plants.map((plant) => _buildEnhancedMonitoringCard(plant)).toList(),
    );
  }

  /// بناء بطاقة مراقبة محسنة
  Widget _buildEnhancedMonitoringCard(Map<String, dynamic> plant) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (plant['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    plant['icon'] as IconData,
                    color: plant['color'] as Color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        plant['name'] as String,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'نوع النبات: ${plant['type']}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (plant['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (plant['color'] as Color).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    plant['status'] as String,
                    style: TextStyle(
                      color: plant['color'] as Color,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات البيئة
            Row(
              children: [
                Expanded(
                  child: _buildEnvironmentInfo(
                    'الرطوبة',
                    plant['humidity'] as String,
                    Icons.water_drop,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildEnvironmentInfo(
                    'الحرارة',
                    plant['temperature'] as String,
                    Icons.thermostat,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // تذييل البطاقة
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'آخر تحديث: ${plant['lastUpdate']}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _viewPlantDetails(),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('عرض التفاصيل'),
                  style: TextButton.styleFrom(
                    foregroundColor: AssetsColors.dufaultGreencolor,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات البيئة
  Widget _buildEnvironmentInfo(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صفحة لوحة البيانات
  Widget _buildDataDashboardPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'لوحة البيانات والتحليلات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AssetsColors.dufaultGreencolor,
            ),
          ),
          const SizedBox(height: 16),
          _buildDataCard(
            title: 'إجمالي الاستشارات',
            value: '127',
            subtitle: '+12% من الشهر الماضي',
            icon: Icons.chat_bubble,
            color: Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildDataCard(
            title: 'معدل الرضا',
            value: '4.8/5',
            subtitle: 'تقييم ممتاز',
            icon: Icons.star,
            color: Colors.amber,
          ),
          const SizedBox(height: 12),
          _buildDataCard(
            title: 'المزارعون النشطون',
            value: '89',
            subtitle: 'هذا الشهر',
            icon: Icons.people,
            color: Colors.green,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة البيانات
  Widget _buildDataCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        height: 100, // تحديد ارتفاع ثابت
        padding: const EdgeInsets.all(16), // تقليل الحشو
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12), // تقليل الحشو
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: color, size: 24), // تقليل حجم الأيقونة
            ),
            const SizedBox(width: 16), // تقليل المسافة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 12, // تقليل حجم الخط
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 2), // تقليل المسافة
                  Flexible(
                    child: Text(
                      value,
                      style: TextStyle(
                        fontSize: 20, // تقليل حجم الخط
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Flexible(
                    child: Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 10, // تقليل حجم الخط
                        color: Colors.grey[500],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ===== وظائف المساعدة =====

  void _showNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض الإشعارات')),
    );
  }

  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح الإعدادات')),
    );
  }

  void _addTip() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة نصيحة جديدة')),
    );
  }

  void _updateStatus() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث الحالة')),
    );
  }

  void _respondToConsultation() {
    _showReplyDialog();
  }

  void _scheduleConsultation() {
    _showScheduleDialog();
  }

  /// عرض حوار الرد على الاستشارة
  void _showReplyDialog() {
    final TextEditingController replyController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الرد على الاستشارة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: replyController,
              decoration: const InputDecoration(
                labelText: 'اكتب ردك هنا',
                border: OutlineInputBorder(),
                hintText: 'قدم نصائح مفيدة للمزارع...',
              ),
              maxLines: 4,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'نصيحة: كن واضحاً ومحدداً في إجابتك',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (replyController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إرسال الرد بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AssetsColors.dufaultGreencolor,
            ),
            child: const Text('إرسال الرد'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار جدولة الاستشارة
  void _showScheduleDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جدولة الاستشارة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('اليوم - 2:00 م'),
              onTap: () {
                Navigator.pop(context);
                _confirmSchedule('اليوم - 2:00 م');
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('غداً - 10:00 ص'),
              onTap: () {
                Navigator.pop(context);
                _confirmSchedule('غداً - 10:00 ص');
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('غداً - 3:00 م'),
              onTap: () {
                Navigator.pop(context);
                _confirmSchedule('غداً - 3:00 م');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تأكيد الجدولة
  void _confirmSchedule(String time) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم جدولة الاستشارة في $time'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _startConsultation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء الاستشارة')),
    );
  }

  void _rescheduleAppointment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعادة جدولة الموعد')),
    );
  }

  void _viewPlantDetails() {
    _showPlantDetailsDialog();
  }

  /// عرض تفاصيل الاستشارة
  void _showConsultationDetails(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الاستشارة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('المزارع', consultation.userName),
              _buildDetailRow('نوع المحصول', consultation.cropType),
              _buildDetailRow('الوصف', consultation.problemDescription),
              if (consultation.area.isNotEmpty)
                _buildDetailRow('المساحة', consultation.area),
              _buildDetailRow('التاريخ', _formatConsultationDate(consultation.createdAt)),
              _buildDetailRow('الحالة', _getStatusText(consultation.status)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _replyToConsultation(consultation);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AssetsColors.dufaultGreencolor,
            ),
            child: const Text('الرد'),
          ),
        ],
      ),
    );
  }

  /// الرد على الاستشارة
  void _replyToConsultation(ConsultationModel consultation) {
    _showReplyDialog();
  }

  /// عرض حوار تفاصيل النبات
  void _showPlantDetailsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل النبات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('النوع', 'طماطم'),
            _buildDetailRow('الحالة', 'صحي'),
            _buildDetailRow('الرطوبة', '75%'),
            _buildDetailRow('الحرارة', '25°C'),
            _buildDetailRow('آخر تحديث', 'منذ ساعتين'),
            _buildDetailRow('الموقع', 'الحقل الشمالي'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تحديث بيانات النبات')),
              );
            },
            child: const Text('تحديث البيانات'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق تاريخ الاستشارة
  String _formatConsultationDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} - ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'في الانتظار';
      case ConsultationStatus.inProgress:
        return 'قيد المعالجة';
      case ConsultationStatus.answered:
        return 'تم الرد';
      case ConsultationStatus.cancelled:
        return 'ملغية';
      case ConsultationStatus.closed:
        return 'مغلقة';
    }
  }
}
