import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';


/// لوحة تحكم المرشد الزراعي
class AdvisorDashboardScreen extends StatefulWidget {
  final String advisorId;
  final String advisorName;

  const AdvisorDashboardScreen({
    super.key,
    required this.advisorId,
    required this.advisorName,
  });

  @override
  State<AdvisorDashboardScreen> createState() => _AdvisorDashboardScreenState();
}

class _AdvisorDashboardScreenState extends State<AdvisorDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadAdvisorData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadAdvisorData() {
    // تحميل استشارات المرشح من قاعدة البيانات
    context.read<AdvisorCubit>().getAdvisorConsultations(
      advisorId: widget.advisorId,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AssetsColors.dufaultGreencolor,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لوحة تحكم المرشد',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              widget.advisorName,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications, color: Colors.white),
            onPressed: _showNotifications,
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: _showSettings,
          ),
        ],
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'الرئيسية',
            ),
            Tab(
              icon: Icon(Icons.chat),
              text: 'الاستشارات',
            ),
            Tab(
              icon: Icon(Icons.calendar_today),
              text: 'المواعيد',
            ),
            Tab(
              icon: Icon(Icons.visibility),
              text: 'المراقبة',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboardTab(),
          _buildConsultationsTab(),
          _buildAppointmentsTab(),
          _buildMonitoringTab(),
        ],
      ),
    );
  }

  /// تبويب الرئيسية
  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 20),
          _buildStatsCards(),
          const SizedBox(height: 20),
          _buildQuickActions(),
          const SizedBox(height: 20),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  /// بطاقة الترحيب
  Widget _buildWelcomeCard() {
    final hour = DateTime.now().hour;
    String greeting;
    if (hour < 12) {
      greeting = 'صباح الخير';
    } else if (hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.dufaultGreencolor,
            AssetsColors.dufaultGreencolor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$greeting، ${widget.advisorName}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'لديك طلبات جديدة تحتاج للمراجعة',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _tabController.animateTo(1),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AssetsColors.dufaultGreencolor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('عرض الاستشارات'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _tabController.animateTo(2),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('عرض المواعيد'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بطاقات الإحصائيات
  Widget _buildStatsCards() {
    return Row(
      children: [
        _buildStatCard(
          title: 'الاستشارات الجديدة',
          value: '12',
          icon: Icons.chat_bubble,
          color: Colors.blue,
        ),
        const SizedBox(width: 12),
        _buildStatCard(
          title: 'المواعيد اليوم',
          value: '3',
          icon: Icons.calendar_today,
          color: Colors.orange,
        ),
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildActionCard(
              title: 'إضافة نصيحة',
              icon: Icons.lightbulb,
              color: Colors.amber,
              onTap: _addTip,
            ),
            const SizedBox(width: 12),
            _buildActionCard(
              title: 'تحديث الحالة',
              icon: Icons.update,
              color: Colors.green,
              onTap: _updateStatus,
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة إجراء
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: color,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// النشاط الأخير
  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.dufaultGreencolor,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildActivityItem(
                title: 'رد على استشارة زراعة الطماطم',
                time: 'منذ ساعتين',
                icon: Icons.chat,
                color: Colors.green,
              ),
              const Divider(),
              _buildActivityItem(
                title: 'تأكيد موعد مع مزارع من صنعاء',
                time: 'منذ 4 ساعات',
                icon: Icons.calendar_today,
                color: Colors.blue,
              ),
              const Divider(),
              _buildActivityItem(
                title: 'إضافة تقرير مراقبة نبات الخيار',
                time: 'أمس',
                icon: Icons.visibility,
                color: Colors.orange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// عنصر نشاط
  Widget _buildActivityItem({
    required String title,
    required String time,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب الاستشارات
  Widget _buildConsultationsTab() {
    return BlocBuilder<AdvisorCubit, AdvisorState>(
      builder: (context, state) {
        if (state is ConsultationsLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل الاستشارات...'),
              ],
            ),
          );
        }

        if (state is AdvisorError) {
          return _buildErrorState(state.message);
        }

        List<ConsultationModel> consultations = [];
        if (state is ConsultationsLoaded) {
          consultations = state.consultations;
        }

        // تصنيف الاستشارات حسب الحالة
        final pendingConsultations = consultations
            .where((c) => c.status == ConsultationStatus.pending)
            .toList();
        final answeredConsultations = consultations
            .where((c) => c.status == ConsultationStatus.answered)
            .toList();

        return RefreshIndicator(
          onRefresh: () async {
            context.read<AdvisorCubit>().getAdvisorConsultations(
              advisorId: widget.advisorId,
            );
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // إحصائيات سريعة
                _buildQuickStats(consultations),
                const SizedBox(height: 20),

                // الاستشارات المعلقة
                if (pendingConsultations.isNotEmpty) ...[
                  _buildSectionHeader('استشارات معلقة', pendingConsultations.length, Colors.orange),
                  const SizedBox(height: 12),
                  ...pendingConsultations.map((consultation) =>
                    _buildConsultationCard(consultation, isPriority: true)),
                  const SizedBox(height: 20),
                ],

                // الاستشارات المجابة
                if (answeredConsultations.isNotEmpty) ...[
                  _buildSectionHeader('استشارات مجابة', answeredConsultations.length, Colors.green),
                  const SizedBox(height: 12),
                  ...answeredConsultations.map((consultation) =>
                    _buildConsultationCard(consultation)),
                ],

                // رسالة إذا لم توجد استشارات
                if (consultations.isEmpty)
                  _buildEmptyState(
                    icon: Icons.chat_bubble_outline,
                    title: 'لا توجد استشارات',
                    subtitle: 'لم تتلق أي استشارات بعد. ستظهر هنا عندما يرسل المزارعون طلبات استشارة.',
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// إحصائيات سريعة للاستشارات
  Widget _buildQuickStats(List<ConsultationModel> consultations) {
    final pending = consultations.where((c) => c.status == ConsultationStatus.pending).length;
    final answered = consultations.where((c) => c.status == ConsultationStatus.answered).length;
    final total = consultations.length;

    return Row(
      children: [
        _buildQuickStatCard(
          title: 'معلقة',
          value: pending.toString(),
          icon: Icons.pending,
          color: Colors.orange,
        ),
        const SizedBox(width: 12),
        _buildQuickStatCard(
          title: 'مجابة',
          value: answered.toString(),
          icon: Icons.check_circle,
          color: Colors.green,
        ),
        const SizedBox(width: 12),
        _buildQuickStatCard(
          title: 'الإجمالي',
          value: total.toString(),
          icon: Icons.chat,
          color: Colors.blue,
        ),
      ],
    );
  }

  /// بطاقة إحصائية سريعة
  Widget _buildQuickStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عنوان قسم
  Widget _buildSectionHeader(String title, int count, Color color) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  /// بطاقة استشارة للمرشد
  Widget _buildConsultationCard(ConsultationModel consultation, {bool isPriority = false}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isPriority ? Border.all(
          color: Colors.orange,
          width: 2,
        ) : null,
        boxShadow: [
          BoxShadow(
            color: isPriority ? Colors.orange.withValues(alpha: 0.2) : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المزارع
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        consultation.userName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'محصول: ${consultation.cropType}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(consultation.status),
              ],
            ),
            const SizedBox(height: 12),

            // وصف المشكلة
            Text(
              consultation.problemDescription,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            // الصور (إن وجدت)
            if (consultation.images != null && consultation.images!.isNotEmpty)
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: consultation.images!.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 60,
                      height: 60,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(consultation.images![index]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ),
            const SizedBox(height: 12),

            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _viewConsultationDetails(consultation),
                    child: const Text('عرض التفاصيل'),
                  ),
                ),
                const SizedBox(width: 8),
                if (consultation.status == ConsultationStatus.pending)
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _replyToConsultation(consultation),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AssetsColors.dufaultGreencolor,
                      ),
                      child: const Text(
                        'الرد',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(ConsultationStatus status) {
    Color color;
    String text;

    switch (status) {
      case ConsultationStatus.pending:
        color = Colors.orange;
        text = 'جديدة';
        break;
      case ConsultationStatus.inProgress:
        color = Colors.blue;
        text = 'جاري المعالجة';
        break;
      case ConsultationStatus.answered:
        color = Colors.green;
        text = 'تم الرد';
        break;
      case ConsultationStatus.closed:
        color = Colors.grey;
        text = 'مغلقة';
        break;
      case ConsultationStatus.cancelled:
        color = Colors.red;
        text = 'ملغاة';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// تبويب المواعيد
  Widget _buildAppointmentsTab() {
    return _buildEmptyState(
      icon: Icons.calendar_today,
      title: 'لا توجد مواعيد',
      subtitle: 'لم يتم حجز أي مواعيد بعد',
    );
  }

  /// تبويب المراقبة
  Widget _buildMonitoringTab() {
    return _buildEmptyState(
      icon: Icons.visibility,
      title: 'لا توجد طلبات مراقبة',
      subtitle: 'لم تتلق أي طلبات مراقبة بعد',
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة خطأ
  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAdvisorData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل الاستشارة
  void _viewConsultationDetails(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('استشارة ${consultation.cropType}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('المزارع: ${consultation.userName}'),
              const SizedBox(height: 8),
              Text('المساحة: ${consultation.area}'),
              const SizedBox(height: 8),
              Text('الوصف: ${consultation.problemDescription}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// الرد على الاستشارة
  void _replyToConsultation(ConsultationModel consultation) {
    final replyController = TextEditingController();
    bool isSubmitting = false;
    String? errorMessage;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('الرد على الاستشارة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عرض تفاصيل الاستشارة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المزارع: ${consultation.userName}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text('المحصول: ${consultation.cropType}'),
                    const SizedBox(height: 4),
                    Text(
                      'المشكلة: ${consultation.problemDescription}',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // حقل الرد
              TextField(
                controller: replyController,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'اكتب ردك المفصل هنا...\n\nيرجى تقديم نصائح واضحة ومفيدة للمزارع',
                  border: const OutlineInputBorder(),
                  errorText: errorMessage,
                ),
                onChanged: (value) {
                  if (errorMessage != null) {
                    setState(() {
                      errorMessage = null;
                    });
                  }
                },
              ),

              // رسالة الخطأ
              if (errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    errorMessage!,
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: isSubmitting ? null : () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: isSubmitting ? null : () async {
                final replyText = replyController.text.trim();

                // التحقق من صحة البيانات
                if (replyText.isEmpty) {
                  setState(() {
                    errorMessage = 'يرجى كتابة رد على الاستشارة';
                  });
                  return;
                }

                if (replyText.length < 10) {
                  setState(() {
                    errorMessage = 'يجب أن يكون الرد 10 أحرف على الأقل';
                  });
                  return;
                }

                if (replyText.length > 1000) {
                  setState(() {
                    errorMessage = 'يجب أن يكون الرد أقل من 1000 حرف';
                  });
                  return;
                }

                // بدء عملية الإرسال
                setState(() {
                  isSubmitting = true;
                  errorMessage = null;
                });

                try {
                  // TODO: ربط بقاعدة البيانات الحقيقية
                  // await context.read<AdvisorCubit>().replyToConsultation(
                  //   consultationId: consultation.id,
                  //   response: replyText,
                  // );

                  // حفظ المرجع قبل العملية غير المتزامنة
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  // محاكاة عملية الإرسال
                  await Future.delayed(const Duration(seconds: 2));

                  navigator.pop();

                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إرسال الرد بنجاح! سيتم إشعار المزارع بالرد.'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 3),
                    ),
                  );

                } catch (e) {
                  setState(() {
                    isSubmitting = false;
                    errorMessage = 'حدث خطأ أثناء إرسال الرد. يرجى المحاولة مرة أخرى.';
                  });
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.dufaultGreencolor,
              ),
              child: isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'إرسال الرد',
                      style: TextStyle(color: Colors.white),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض الإشعارات
  void _showNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('قريباً: نظام الإشعارات'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض الإعدادات
  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('قريباً: إعدادات المرشد'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// إضافة نصيحة
  void _addTip() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('قريباً: إضافة نصائح زراعية'),
        backgroundColor: Colors.amber,
      ),
    );
  }

  /// تحديث الحالة
  void _updateStatus() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('قريباً: تحديث حالة المرشد'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
