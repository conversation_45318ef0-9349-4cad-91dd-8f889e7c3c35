import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/domain/repositories/advisor_repository_interface.dart';
import 'enhanced_advisor_dashboard.dart';

/// الشاشة الرئيسية للمرشد الزراعي مع إعداد الـ BLoC
class AdvisorMainScreen extends StatelessWidget {
  const AdvisorMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AdvisorCubit(
        advisorRepository: context.read<AdvisorRepositoryInterface>(),
      ),
      child: const EnhancedAdvisorDashboard(),
    );
  }
}

/// مثال على كيفية التنقل إلى واجهة المرشد الزراعي
class NavigationExample {
  /// التنقل إلى واجهة المرشد الزراعي
  static void navigateToAdvisorDashboard(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AdvisorMainScreen(),
      ),
    );
  }

  /// التنقل مع استبدال الشاشة الحالية
  static void navigateAndReplaceToAdvisorDashboard(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const AdvisorMainScreen(),
      ),
    );
  }

  /// التنقل وحذف جميع الشاشات السابقة
  static void navigateAndClearToAdvisorDashboard(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const AdvisorMainScreen(),
      ),
      (route) => false,
    );
  }
}
