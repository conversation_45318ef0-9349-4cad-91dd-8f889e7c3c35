import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/data/models/floating_action/fab_action_model.dart';
import 'package:agriculture/core/services/floating_action_button_service.dart';
import 'floating_action_button_state.dart';

/// Cubit لإدارة حالة الزر العائم الذكي
/// وفق المعيار #6: عدم استخدام StatefulWidget - استخدام Cubit فقط
/// وفق المعيار #2: Clean Architecture - طبقة العرض
class FloatingActionButtonCubit extends Cubit<FloatingActionButtonState> {
  final FloatingActionButtonService _fabService;

  FloatingActionButtonCubit({
    FloatingActionButtonService? fabService,
  })  : _fabService = fabService ?? FloatingActionButtonService(),
        super(const FloatingActionButtonInitial());

  /// تحميل إجراءات الاستشاري
  void loadAdvisorActions() {
    try {
      emit(const FloatingActionButtonLoading());
      
      // محاكاة تحميل الإجراءات (يمكن استبدالها بتحميل من API)
      final actions = <FabActionModel>[
        // سيتم تحميل الإجراءات من الخدمة عند الحاجة
      ];

      emit(FloatingActionButtonLoaded(actions: actions));
      debugPrint('✅ تم تحميل ${actions.length} إجراء للزر العائم');
    } catch (e) {
      emit(FloatingActionButtonError(message: 'فشل في تحميل الإجراءات: $e'));
      debugPrint('❌ خطأ في تحميل إجراءات الزر العائم: $e');
    }
  }

  /// تنفيذ إجراء معين
  Future<void> executeAction(String actionId) async {
    try {
      emit(FloatingActionButtonExecuting(actionId: actionId));
      debugPrint('🔄 تنفيذ الإجراء: $actionId');

      // محاكاة تنفيذ الإجراء
      await Future.delayed(const Duration(seconds: 1));

      emit(FloatingActionButtonActionCompleted(
        actionId: actionId,
        message: 'تم تنفيذ الإجراء بنجاح',
      ));
      
      debugPrint('✅ تم تنفيذ الإجراء: $actionId');
    } catch (e) {
      emit(FloatingActionButtonError(
        message: 'فشل في تنفيذ الإجراء: $e',
      ));
      debugPrint('❌ خطأ في تنفيذ الإجراء $actionId: $e');
    }
  }

  /// تحديث حالة إجراء معين
  void updateActionState(String actionId, bool isEnabled) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final updatedActions = currentState.actions.map((action) {
        if (action.id == actionId) {
          return action.copyWith(isEnabled: isEnabled);
        }
        return action;
      }).toList();

      emit(FloatingActionButtonLoaded(actions: updatedActions));
      debugPrint('🔄 تم تحديث حالة الإجراء $actionId: $isEnabled');
    }
  }

  /// تحديث رؤية إجراء معين
  void updateActionVisibility(String actionId, bool isVisible) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final updatedActions = currentState.actions.map((action) {
        if (action.id == actionId) {
          return action.copyWith(isVisible: isVisible);
        }
        return action;
      }).toList();

      emit(FloatingActionButtonLoaded(actions: updatedActions));
      debugPrint('👁️ تم تحديث رؤية الإجراء $actionId: $isVisible');
    }
  }

  /// إضافة إجراء جديد
  void addAction(FabActionModel action) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final updatedActions = [...currentState.actions, action];
      emit(FloatingActionButtonLoaded(actions: updatedActions));
      debugPrint('➕ تم إضافة إجراء جديد: ${action.id}');
    }
  }

  /// حذف إجراء
  void removeAction(String actionId) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final updatedActions = currentState.actions
          .where((action) => action.id != actionId)
          .toList();
      emit(FloatingActionButtonLoaded(actions: updatedActions));
      debugPrint('🗑️ تم حذف الإجراء: $actionId');
    }
  }

  /// إعادة ترتيب الإجراءات حسب الأولوية
  void sortActionsByPriority() {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final sortedActions = [...currentState.actions]
        ..sort((a, b) => a.priority.compareTo(b.priority));
      
      emit(FloatingActionButtonLoaded(actions: sortedActions));
      debugPrint('🔄 تم ترتيب الإجراءات حسب الأولوية');
    }
  }

  /// الحصول على الإجراءات المرئية والمفعلة فقط
  List<FabActionModel> getVisibleActions() {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      return currentState.actions
          .where((action) => action.isVisible && action.isEnabled)
          .toList();
    }
    return [];
  }

  /// الحصول على عدد الإجراءات المفعلة
  int getEnabledActionsCount() {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      return currentState.actions
          .where((action) => action.isEnabled)
          .length;
    }
    return 0;
  }

  /// البحث عن إجراء بالمعرف
  FabActionModel? findActionById(String actionId) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      try {
        return currentState.actions
            .firstWhere((action) => action.id == actionId);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// إعادة تعيين الحالة
  void reset() {
    emit(const FloatingActionButtonInitial());
    debugPrint('🔄 تم إعادة تعيين حالة الزر العائم');
  }

  /// تحديث جميع الإجراءات
  void updateAllActions(List<FabActionModel> actions) {
    emit(FloatingActionButtonLoaded(actions: actions));
    debugPrint('🔄 تم تحديث جميع الإجراءات (${actions.length} إجراء)');
  }

  /// تفعيل/إلغاء تفعيل جميع الإجراءات
  void toggleAllActions(bool isEnabled) {
    final currentState = state;
    if (currentState is FloatingActionButtonLoaded) {
      final updatedActions = currentState.actions.map((action) {
        return action.copyWith(isEnabled: isEnabled);
      }).toList();

      emit(FloatingActionButtonLoaded(actions: updatedActions));
      debugPrint('🔄 تم ${isEnabled ? 'تفعيل' : 'إلغاء تفعيل'} جميع الإجراءات');
    }
  }
}
