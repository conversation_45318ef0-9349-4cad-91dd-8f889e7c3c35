import 'package:equatable/equatable.dart';
import 'package:agriculture/data/models/floating_action/fab_action_model.dart';

/// حالات الزر العائم الذكي
/// وفق المعيار #2: Clean Architecture - طبقة العرض
abstract class FloatingActionButtonState extends Equatable {
  const FloatingActionButtonState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class FloatingActionButtonInitial extends FloatingActionButtonState {
  const FloatingActionButtonInitial();
}

/// حالة التحميل
class FloatingActionButtonLoading extends FloatingActionButtonState {
  const FloatingActionButtonLoading();
}

/// حالة تحميل الإجراءات بنجاح
class FloatingActionButtonLoaded extends FloatingActionButtonState {
  /// قائمة الإجراءات المتاحة
  final List<FabActionModel> actions;

  const FloatingActionButtonLoaded({
    required this.actions,
  });

  @override
  List<Object?> get props => [actions];

  /// الحصول على الإجراءات المرئية والمفعلة
  List<FabActionModel> get visibleActions {
    return actions
        .where((action) => action.isVisible && action.isEnabled)
        .toList()
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  /// عدد الإجراءات المفعلة
  int get enabledActionsCount {
    return actions.where((action) => action.isEnabled).length;
  }

  /// عدد الإجراءات المرئية
  int get visibleActionsCount {
    return actions.where((action) => action.isVisible).length;
  }

  /// البحث عن إجراء بالمعرف
  FabActionModel? findActionById(String actionId) {
    try {
      return actions.firstWhere((action) => action.id == actionId);
    } catch (e) {
      return null;
    }
  }

  /// التحقق من وجود إجراء معين
  bool hasAction(String actionId) {
    return actions.any((action) => action.id == actionId);
  }

  /// إنشاء نسخة محدثة من الحالة
  FloatingActionButtonLoaded copyWith({
    List<FabActionModel>? actions,
  }) {
    return FloatingActionButtonLoaded(
      actions: actions ?? this.actions,
    );
  }
}

/// حالة تنفيذ إجراء
class FloatingActionButtonExecuting extends FloatingActionButtonState {
  /// معرف الإجراء قيد التنفيذ
  final String actionId;

  const FloatingActionButtonExecuting({
    required this.actionId,
  });

  @override
  List<Object?> get props => [actionId];
}

/// حالة اكتمال تنفيذ إجراء
class FloatingActionButtonActionCompleted extends FloatingActionButtonState {
  /// معرف الإجراء المكتمل
  final String actionId;
  
  /// رسالة النجاح
  final String message;
  
  /// بيانات إضافية للنتيجة
  final Map<String, dynamic>? result;

  const FloatingActionButtonActionCompleted({
    required this.actionId,
    required this.message,
    this.result,
  });

  @override
  List<Object?> get props => [actionId, message, result];
}

/// حالة الخطأ
class FloatingActionButtonError extends FloatingActionButtonState {
  /// رسالة الخطأ
  final String message;
  
  /// كود الخطأ (اختياري)
  final String? errorCode;
  
  /// تفاصيل إضافية للخطأ
  final Map<String, dynamic>? details;

  const FloatingActionButtonError({
    required this.message,
    this.errorCode,
    this.details,
  });

  @override
  List<Object?> get props => [message, errorCode, details];
}

/// حالة تحديث إجراء معين
class FloatingActionButtonActionUpdated extends FloatingActionButtonState {
  /// معرف الإجراء المحدث
  final String actionId;
  
  /// نوع التحديث
  final String updateType;
  
  /// القيمة الجديدة
  final dynamic newValue;

  const FloatingActionButtonActionUpdated({
    required this.actionId,
    required this.updateType,
    required this.newValue,
  });

  @override
  List<Object?> get props => [actionId, updateType, newValue];
}

/// حالة إضافة إجراء جديد
class FloatingActionButtonActionAdded extends FloatingActionButtonState {
  /// الإجراء المضاف
  final FabActionModel action;

  const FloatingActionButtonActionAdded({
    required this.action,
  });

  @override
  List<Object?> get props => [action];
}

/// حالة حذف إجراء
class FloatingActionButtonActionRemoved extends FloatingActionButtonState {
  /// معرف الإجراء المحذوف
  final String actionId;

  const FloatingActionButtonActionRemoved({
    required this.actionId,
  });

  @override
  List<Object?> get props => [actionId];
}

/// حالة إعادة ترتيب الإجراءات
class FloatingActionButtonActionsSorted extends FloatingActionButtonState {
  /// نوع الترتيب المطبق
  final String sortType;

  const FloatingActionButtonActionsSorted({
    required this.sortType,
  });

  @override
  List<Object?> get props => [sortType];
}
