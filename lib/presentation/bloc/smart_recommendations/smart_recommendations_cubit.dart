import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import '../../../data/services/smart_recommendations_service.dart';
import '../../../core/services/enhanced_notification_service.dart';

/// حالات كيوبت التوصيات الذكية
abstract class SmartRecommendationsState extends Equatable {
  const SmartRecommendationsState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class SmartRecommendationsInitial extends SmartRecommendationsState {}

/// حالة التحميل
class SmartRecommendationsLoading extends SmartRecommendationsState {}

/// حالة تحميل التوصيات بنجاح
class SmartRecommendationsLoaded extends SmartRecommendationsState {
  final Map<String, dynamic> recommendations;
  final String recommendationType;
  final DateTime generatedAt;

  const SmartRecommendationsLoaded({
    required this.recommendations,
    required this.recommendationType,
    required this.generatedAt,
  });

  @override
  List<Object?> get props => [recommendations, recommendationType, generatedAt];
}

/// حالة تحميل التوصيات اليومية
class DailyRecommendationsLoaded extends SmartRecommendationsState {
  final Map<String, dynamic> dailyRecommendations;
  final DateTime date;

  const DailyRecommendationsLoaded({
    required this.dailyRecommendations,
    required this.date,
  });

  @override
  List<Object?> get props => [dailyRecommendations, date];
}

/// حالة تحميل توصيات الطوارئ
class EmergencyRecommendationsLoaded extends SmartRecommendationsState {
  final Map<String, dynamic> emergencyRecommendations;
  final String emergencyType;
  final String severity;

  const EmergencyRecommendationsLoaded({
    required this.emergencyRecommendations,
    required this.emergencyType,
    required this.severity,
  });

  @override
  List<Object?> get props => [emergencyRecommendations, emergencyType, severity];
}

/// حالة الخطأ
class SmartRecommendationsError extends SmartRecommendationsState {
  final String message;
  final String? errorCode;

  const SmartRecommendationsError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// كيوبت التوصيات الذكية
/// 
/// وفق المعيار #6: عدم استخدام StatefulWidget والاعتماد على Cubit
/// وفق المعيار #2: Clean Architecture
/// وفق المعيار #12: تعليقات عربية شاملة
class SmartRecommendationsCubit extends Cubit<SmartRecommendationsState> {
  /// خدمة التوصيات الذكية
  final SmartRecommendationsService _recommendationsService;

  /// آخر توصيات تم تحميلها
  Map<String, dynamic>? _lastRecommendations;

  /// آخر توصيات يومية
  Map<String, dynamic>? _lastDailyRecommendations;

  /// تاريخ آخر تحديث للتوصيات اليومية
  DateTime? _lastDailyUpdate;

  /// منشئ كيوبت التوصيات الذكية
  /// 
  /// المعلمات:
  /// - [recommendationsService]: خدمة التوصيات الذكية
  SmartRecommendationsCubit({
    required SmartRecommendationsService recommendationsService,
  })  : _recommendationsService = recommendationsService,
        super(SmartRecommendationsInitial());

  /// إنتاج توصيات شاملة
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [imageUrls]: صور النبات (اختياري)
  /// - [location]: الموقع الجغرافي (اختياري)
  /// - [farmSize]: مساحة المزرعة (اختياري)
  Future<void> generateComprehensiveRecommendations({
    required String cropType,
    List<String>? imageUrls,
    String? location,
    double? farmSize,
  }) async {
    try {
      emit(SmartRecommendationsLoading());

      if (kDebugMode) {
        print('🧠 بدء إنتاج التوصيات الشاملة للمحصول: $cropType');
      }

      final recommendations = await _recommendationsService.generateComprehensiveRecommendations(
        cropType: cropType,
        imageUrls: imageUrls,
        location: location,
        farmSize: farmSize,
      );

      _lastRecommendations = recommendations;

      emit(SmartRecommendationsLoaded(
        recommendations: recommendations,
        recommendationType: 'comprehensive',
        generatedAt: DateTime.now(),
      ));

      // إرسال إشعار بالتوصيات الجديدة
      await _sendRecommendationsNotification(recommendations, 'comprehensive');

      if (kDebugMode) {
        print('✅ تم إنتاج التوصيات الشاملة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنتاج التوصيات الشاملة: $e');
      }
      emit(SmartRecommendationsError(message: 'فشل في إنتاج التوصيات: $e'));
    }
  }

  /// إنتاج التوصيات اليومية
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [location]: الموقع الجغرافي (اختياري)
  /// - [forceRefresh]: إجبار التحديث (اختياري)
  Future<void> generateDailyRecommendations({
    required String cropType,
    String? location,
    bool forceRefresh = false,
  }) async {
    try {
      // التحقق من الحاجة للتحديث
      if (!forceRefresh && _shouldUseCachedDailyRecommendations()) {
        emit(DailyRecommendationsLoaded(
          dailyRecommendations: _lastDailyRecommendations!,
          date: _lastDailyUpdate!,
        ));
        return;
      }

      emit(SmartRecommendationsLoading());

      if (kDebugMode) {
        print('📅 بدء إنتاج التوصيات اليومية للمحصول: $cropType');
      }

      final recommendations = await _recommendationsService.generateDailyRecommendations(
        cropType: cropType,
        location: location,
      );

      _lastDailyRecommendations = recommendations;
      _lastDailyUpdate = DateTime.now();

      emit(DailyRecommendationsLoaded(
        dailyRecommendations: recommendations,
        date: _lastDailyUpdate!,
      ));

      // إرسال إشعار بالتوصيات اليومية
      await _sendRecommendationsNotification(recommendations, 'daily');

      if (kDebugMode) {
        print('✅ تم إنتاج التوصيات اليومية بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنتاج التوصيات اليومية: $e');
      }
      emit(SmartRecommendationsError(message: 'فشل في إنتاج التوصيات اليومية: $e'));
    }
  }

  /// إنتاج توصيات الطوارئ
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [emergencyType]: نوع الطوارئ
  /// - [severity]: مستوى الخطورة
  Future<void> generateEmergencyRecommendations({
    required String cropType,
    required String emergencyType,
    required String severity,
  }) async {
    try {
      emit(SmartRecommendationsLoading());

      if (kDebugMode) {
        print('🚨 بدء إنتاج توصيات الطوارئ: $emergencyType');
      }

      final recommendations = await _recommendationsService.generateEmergencyRecommendations(
        cropType: cropType,
        emergencyType: emergencyType,
        severity: severity,
      );

      emit(EmergencyRecommendationsLoaded(
        emergencyRecommendations: recommendations,
        emergencyType: emergencyType,
        severity: severity,
      ));

      // إرسال إشعار عاجل
      await _sendEmergencyNotification(recommendations, emergencyType, severity);

      if (kDebugMode) {
        print('✅ تم إنتاج توصيات الطوارئ بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنتاج توصيات الطوارئ: $e');
      }
      emit(SmartRecommendationsError(message: 'فشل في إنتاج توصيات الطوارئ: $e'));
    }
  }

  /// تحديث التوصيات تلقائياً
  /// 
  /// المعلمات:
  /// - [cropType]: نوع المحصول
  /// - [location]: الموقع الجغرافي (اختياري)
  Future<void> autoUpdateRecommendations({
    required String cropType,
    String? location,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 بدء التحديث التلقائي للتوصيات');
      }

      // تحديث التوصيات اليومية
      await generateDailyRecommendations(
        cropType: cropType,
        location: location,
        forceRefresh: true,
      );

      if (kDebugMode) {
        print('✅ تم التحديث التلقائي للتوصيات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحديث التلقائي: $e');
      }
    }
  }

  /// التحقق من الحاجة لاستخدام التوصيات اليومية المحفوظة
  /// 
  /// الإرجاع: [bool] true إذا كان يمكن استخدام التوصيات المحفوظة
  bool _shouldUseCachedDailyRecommendations() {
    if (_lastDailyRecommendations == null || _lastDailyUpdate == null) {
      return false;
    }

    final now = DateTime.now();
    final lastUpdate = _lastDailyUpdate!;

    // استخدام التوصيات المحفوظة إذا كانت من نفس اليوم
    return now.year == lastUpdate.year &&
        now.month == lastUpdate.month &&
        now.day == lastUpdate.day;
  }

  /// إرسال إشعار بالتوصيات
  /// 
  /// المعلمات:
  /// - [recommendations]: التوصيات
  /// - [type]: نوع التوصيات
  Future<void> _sendRecommendationsNotification(
    Map<String, dynamic> recommendations,
    String type,
  ) async {
    try {
      String title, body;

      switch (type) {
        case 'comprehensive':
          title = '🧠 توصيات ذكية جديدة';
          body = 'تم إنتاج توصيات شاملة مخصصة لمحصولك';
          break;
        case 'daily':
          title = '📅 توصيات اليوم';
          body = 'توصيات يومية جديدة متاحة لمزرعتك';
          break;
        default:
          title = '💡 توصيات جديدة';
          body = 'توصيات زراعية جديدة متاحة';
      }

      await EnhancedNotificationService.showLocalNotification(
        title: title,
        body: body,
        payload: 'recommendations_$type',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار التوصيات: $e');
      }
    }
  }

  /// إرسال إشعار طوارئ
  /// 
  /// المعلمات:
  /// - [recommendations]: توصيات الطوارئ
  /// - [emergencyType]: نوع الطوارئ
  /// - [severity]: مستوى الخطورة
  Future<void> _sendEmergencyNotification(
    Map<String, dynamic> recommendations,
    String emergencyType,
    String severity,
  ) async {
    try {
      String title, body;

      switch (emergencyType) {
        case 'pest_outbreak':
          title = '🐛 تحذير: انتشار آفات';
          body = 'تم اكتشاف آفات في مزرعتك - إجراء فوري مطلوب';
          break;
        case 'disease_outbreak':
          title = '🦠 تحذير: انتشار مرض';
          body = 'تم اكتشاف مرض في النباتات - علاج عاجل مطلوب';
          break;
        case 'weather_extreme':
          title = '🌡️ تحذير طقس';
          body = 'ظروف جوية قاسية - حماية النباتات مطلوبة';
          break;
        case 'water_shortage':
          title = '💧 تحذير: نقص المياه';
          body = 'نقص في المياه - تطبيق إجراءات توفير المياه';
          break;
        default:
          title = '🚨 تحذير زراعي';
          body = 'حالة طوارئ في المزرعة - راجع التوصيات';
      }

      await EnhancedNotificationService.showLocalNotification(
        title: title,
        body: body,
        payload: 'emergency_$emergencyType',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار الطوارئ: $e');
      }
    }
  }

  /// الحصول على آخر توصيات
  /// 
  /// الإرجاع: [Map<String, dynamic>?] آخر توصيات أو null
  Map<String, dynamic>? get lastRecommendations => _lastRecommendations;

  /// الحصول على آخر توصيات يومية
  /// 
  /// الإرجاع: [Map<String, dynamic>?] آخر توصيات يومية أو null
  Map<String, dynamic>? get lastDailyRecommendations => _lastDailyRecommendations;

  /// الحصول على تاريخ آخر تحديث يومي
  /// 
  /// الإرجاع: [DateTime?] تاريخ آخر تحديث أو null
  DateTime? get lastDailyUpdate => _lastDailyUpdate;

  /// تنظيف الموارد
  @override
  Future<void> close() {
    _lastRecommendations = null;
    _lastDailyRecommendations = null;
    _lastDailyUpdate = null;
    return super.close();
  }
}
