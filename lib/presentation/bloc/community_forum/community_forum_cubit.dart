import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../data/repositories/advisor_repository.dart';
import '../../../data/repositories/storage_repository.dart';
import '../advisor/advisor_cubit.dart';

/// حالات صفحة المنتدى المجتمعي
abstract class CommunityForumState {}

/// الحالة الأولية
class CommunityForumInitial extends CommunityForumState {}

/// حالة تحميل البيانات
class CommunityForumLoading extends CommunityForumState {}

/// حالة تحميل البيانات بنجاح
class CommunityForumLoaded extends CommunityForumState {
  /// فهرس التبويب الرئيسي المحدد (0: المنتدى المجتمعي، 1: المرشد الزراعي)
  final int mainTabIndex;
  
  /// فهرس التبويب الفرعي للمنتدى المجتمعي
  final int forumTabIndex;
  
  /// ما إذا كان في وضع البحث
  final bool isSearchMode;
  
  /// ما إذا كان شريط التبويب مخفيًا
  final bool isAppBarHidden;
  
  /// موقع التمرير السابق
  final double previousScrollOffset;
  
  /// ما إذا كان يتم تحميل البيانات
  final bool isLoading;

  CommunityForumLoaded({
    this.mainTabIndex = 0,
    this.forumTabIndex = 0,
    this.isSearchMode = false,
    this.isAppBarHidden = false,
    this.previousScrollOffset = 0.0,
    this.isLoading = false,
  });

  /// نسخ الحالة مع تحديث بعض القيم
  CommunityForumLoaded copyWith({
    int? mainTabIndex,
    int? forumTabIndex,
    bool? isSearchMode,
    bool? isAppBarHidden,
    double? previousScrollOffset,
    bool? isLoading,
  }) {
    return CommunityForumLoaded(
      mainTabIndex: mainTabIndex ?? this.mainTabIndex,
      forumTabIndex: forumTabIndex ?? this.forumTabIndex,
      isSearchMode: isSearchMode ?? this.isSearchMode,
      isAppBarHidden: isAppBarHidden ?? this.isAppBarHidden,
      previousScrollOffset: previousScrollOffset ?? this.previousScrollOffset,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// حالة خطأ
class CommunityForumError extends CommunityForumState {
  final String message;
  
  CommunityForumError(this.message);
}

/// Cubit لإدارة حالة صفحة المنتدى المجتمعي
class CommunityForumCubit extends Cubit<CommunityForumState> {
  /// متحكم التبويب الرئيسي
  late TabController mainTabController;
  
  /// متحكم التبويب الفرعي للمنتدى المجتمعي
  late TabController forumTabController;
  
  /// متحكم حقل البحث
  final TextEditingController searchController = TextEditingController();
  
  /// متحكم التمرير
  final ScrollController scrollController = ScrollController();
  
  /// AdvisorCubit للتعامل مع المرشدين الزراعيين
  late AdvisorCubit advisorCubit;
  
  /// معرف المستخدم الحالي
  final String uid;

  CommunityForumCubit({
    required this.uid,
    required TickerProvider vsync,
  }) : super(CommunityForumInitial()) {
    _initializeControllers(vsync);
    _initializeAdvisorCubit();
    _setupScrollListener();
  }

  /// تهيئة المتحكمات
  void _initializeControllers(TickerProvider vsync) {
    // تهيئة متحكم التبويب الرئيسي
    mainTabController = TabController(length: 2, vsync: vsync);
    mainTabController.addListener(_handleMainTabChange);
    
    // تهيئة متحكم التبويب الفرعي للمنتدى المجتمعي
    forumTabController = TabController(length: 4, vsync: vsync);
    forumTabController.addListener(_handleForumTabChange);
    
    // إضافة تأخير بسيط لتحسين تجربة المستخدم عند تغيير التبويب
    forumTabController.animation?.addListener(() {
      if (!forumTabController.indexIsChanging) {
        _emitCurrentState(); // إعادة بناء الواجهة لتحديث التأثيرات المرئية
      }
    });
  }

  /// تهيئة AdvisorCubit
  void _initializeAdvisorCubit() {
    advisorCubit = AdvisorCubit(
      advisorRepository: AdvisorRepository(
        firestore: FirebaseFirestore.instance,
        storageRepository: StorageRepositoryFactory.create(),
      ),
    );
    
    // تحميل المرشدين عند بدء التطبيق
    advisorCubit.getAdvisors();
  }

  /// إعداد مستمع التمرير
  void _setupScrollListener() {
    scrollController.addListener(_handleScroll);
  }

  /// معالجة تغيير التبويب الرئيسي
  void _handleMainTabChange() {
    if (state is CommunityForumLoaded) {
      final currentState = state as CommunityForumLoaded;
      emit(currentState.copyWith(mainTabIndex: mainTabController.index));
    }
  }

  /// معالجة تغيير التبويب الفرعي للمنتدى المجتمعي
  void _handleForumTabChange() {
    if (!forumTabController.indexIsChanging && state is CommunityForumLoaded) {
      final currentState = state as CommunityForumLoaded;
      emit(currentState.copyWith(forumTabIndex: forumTabController.index));
    }
  }

  /// معالجة التمرير لإخفاء/إظهار شريط التبويب
  void _handleScroll() {
    if (state is! CommunityForumLoaded) return;
    
    final currentState = state as CommunityForumLoaded;
    
    // الحد الأدنى للتمرير لتغيير حالة شريط التبويب
    const double scrollThreshold = 20.0;
    
    // الحصول على موقع التمرير الحالي
    final currentScrollOffset = scrollController.offset;
    
    // تحديد اتجاه التمرير (لأعلى أو لأسفل)
    final isScrollingDown = currentScrollOffset > currentState.previousScrollOffset;
    
    // إذا كان التمرير لأسفل وشريط التبويب مرئيًا، نخفي شريط التبويب فقط
    if (isScrollingDown &&
        !currentState.isAppBarHidden &&
        currentScrollOffset > scrollThreshold) {
      emit(currentState.copyWith(
        isAppBarHidden: true,
        previousScrollOffset: currentScrollOffset,
      ));
    }
    // إذا كان التمرير لأعلى وشريط التبويب مخفيًا، نظهر شريط التبويب فقط
    else if (!isScrollingDown && currentState.isAppBarHidden) {
      emit(currentState.copyWith(
        isAppBarHidden: false,
        previousScrollOffset: currentScrollOffset,
      ));
    } else {
      // تحديث موقع التمرير السابق فقط
      emit(currentState.copyWith(previousScrollOffset: currentScrollOffset));
    }
  }

  /// تبديل وضع البحث
  void toggleSearchMode() {
    if (state is CommunityForumLoaded) {
      final currentState = state as CommunityForumLoaded;
      final newSearchMode = !currentState.isSearchMode;
      
      if (!newSearchMode) {
        // إذا تم إلغاء وضع البحث، نقوم بمسح حقل البحث
        searchController.clear();
      }
      
      emit(currentState.copyWith(isSearchMode: newSearchMode));
    }
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    if (state is CommunityForumLoaded) {
      final currentState = state as CommunityForumLoaded;
      emit(currentState.copyWith(isLoading: isLoading));
    }
  }

  /// إرسال الحالة الحالية (لإعادة بناء الواجهة)
  void _emitCurrentState() {
    if (state is CommunityForumLoaded) {
      emit(state as CommunityForumLoaded);
    }
  }

  /// تهيئة الصفحة
  void initialize() {
    emit(CommunityForumLoaded());
  }

  @override
  Future<void> close() {
    mainTabController.dispose();
    forumTabController.dispose();
    searchController.dispose();
    scrollController.dispose();
    advisorCubit.close();
    return super.close();
  }
}
