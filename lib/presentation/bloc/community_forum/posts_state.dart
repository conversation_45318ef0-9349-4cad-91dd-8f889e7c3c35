import 'package:equatable/equatable.dart';

import '../../../data/models/community_forum/comment_model.dart';
import '../../../data/models/community_forum/post_model.dart';

/// أنواع ترتيب المنشورات
enum PostSortType {
  /// الأحدث أولاً
  newest,

  /// الأكثر إعجابًا أولاً
  mostLiked,

  /// الأكثر تعليقًا أولاً
  mostCommented,

  /// الأكثر مشاهدة أولاً
  mostViewed,
}

/// حالات كيوبت المنشورات
abstract class PostsState extends Equatable {
  const PostsState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class PostsInitial extends PostsState {}

/// حالة تحميل المنشورات
class PostsLoading extends PostsState {}

/// حالة نجاح تحميل المنشورات
class PostsLoaded extends PostsState {
  /// قائمة المنشورات الحالية (حسب نوع الترتيب الحالي)
  final List<PostModel> posts;

  /// قائمة المنشورات الأصلية (غير مرتبة)
  final List<PostModel> originalPosts;

  /// قائمة المنشورات مرتبة حسب الأحدث
  final List<PostModel> newestPosts;

  /// قائمة المنشورات مرتبة حسب الأكثر إعجابًا
  final List<PostModel> mostLikedPosts;

  /// قائمة المنشورات مرتبة حسب الأكثر تعليقًا
  final List<PostModel> mostCommentedPosts;

  /// قائمة المنشورات مرتبة حسب الأكثر مشاهدة
  final List<PostModel> mostViewedPosts;

  /// ما إذا كان قد تم الوصول إلى الحد الأقصى من المنشورات
  final bool hasReachedMax;

  /// نوع ترتيب المنشورات
  final PostSortType sortType;

  /// مصطلح البحث الحالي
  final String searchQuery;

  /// ما إذا كان في وضع البحث
  final bool isSearchMode;

  /// منشئ حالة تحميل المنشورات بنجاح
  const PostsLoaded({
    required this.posts,
    required this.originalPosts,
    required this.newestPosts,
    required this.mostLikedPosts,
    required this.mostCommentedPosts,
    required this.mostViewedPosts,
    this.hasReachedMax = false,
    this.sortType = PostSortType.newest,
    this.searchQuery = '',
    this.isSearchMode = false,
  });

  @override
  List<Object?> get props => [
    posts,
    originalPosts,
    newestPosts,
    mostLikedPosts,
    mostCommentedPosts,
    mostViewedPosts,
    hasReachedMax,
    sortType,
    searchQuery,
    isSearchMode
  ];

  /// إنشاء نسخة جديدة من الحالة مع تحديث بعض الحقول
  PostsLoaded copyWith({
    List<PostModel>? posts,
    List<PostModel>? originalPosts,
    List<PostModel>? newestPosts,
    List<PostModel>? mostLikedPosts,
    List<PostModel>? mostCommentedPosts,
    List<PostModel>? mostViewedPosts,
    bool? hasReachedMax,
    PostSortType? sortType,
    String? searchQuery,
    bool? isSearchMode,
  }) {
    return PostsLoaded(
      posts: posts ?? this.posts,
      originalPosts: originalPosts ?? this.originalPosts,
      newestPosts: newestPosts ?? this.newestPosts,
      mostLikedPosts: mostLikedPosts ?? this.mostLikedPosts,
      mostCommentedPosts: mostCommentedPosts ?? this.mostCommentedPosts,
      mostViewedPosts: mostViewedPosts ?? this.mostViewedPosts,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      sortType: sortType ?? this.sortType,
      searchQuery: searchQuery ?? this.searchQuery,
      isSearchMode: isSearchMode ?? this.isSearchMode,
    );
  }
}

/// حالة فشل تحميل المنشورات
class PostsError extends PostsState {
  final String message;

  const PostsError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة البحث في المنشورات
class PostsSearching extends PostsState {
  @override
  List<Object?> get props => [];
}

/// حالة جاري حذف منشور
class PostDeleting extends PostsState {
  /// معرف المنشور الذي يتم حذفه
  final String postId;

  /// منشئ حالة جاري حذف منشور
  const PostDeleting(this.postId);

  @override
  List<Object?> get props => [postId];
}

/// حالة تحميل إنشاء منشور جديد
class PostCreating extends PostsState {}

/// حالة نجاح إنشاء منشور جديد
class PostCreated extends PostsState {
  final PostModel post;

  const PostCreated(this.post);

  @override
  List<Object?> get props => [post];
}

/// حالة فشل إنشاء منشور جديد
class PostCreationError extends PostsState {
  final String message;

  const PostCreationError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// حالة تحميل تحديث منشور
class PostUpdating extends PostsState {}

/// حالة نجاح تحديث منشور
class PostUpdated extends PostsState {
  final PostModel post;

  const PostUpdated(this.post);

  @override
  List<Object?> get props => [post];
}

/// حالة فشل تحديث منشور
class PostUpdateError extends PostsState {
  final String message;

  const PostUpdateError(this.message);

  @override
  List<Object?> get props => [message];
}

// تم استبدال هذا التعريف بالتعريف الذي يتضمن معرف المنشور

/// حالة نجاح حذف منشور
class PostDeleted extends PostsState {
  final String postId;

  const PostDeleted(this.postId);

  @override
  List<Object?> get props => [postId];
}

/// حالة فشل حذف منشور
class PostDeleteError extends PostsState {
  final String message;

  const PostDeleteError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة تحميل التعليقات
class CommentsLoading extends PostsState {
  final String postId;

  const CommentsLoading(this.postId);

  @override
  List<Object?> get props => [postId];

  @override
  String toString() => 'CommentsLoading(postId: $postId)';
}

/// حالة نجاح تحميل التعليقات
class CommentsLoaded extends PostsState {
  final String postId;
  final List<CommentModel> comments;
  final bool hasReachedMax;

  const CommentsLoaded({
    required this.postId,
    required this.comments,
    this.hasReachedMax = false,
  });

  @override
  List<Object?> get props => [postId, comments, hasReachedMax];

  @override
  String toString() => 'CommentsLoaded(postId: $postId, comments: ${comments.length}, hasReachedMax: $hasReachedMax)';

  CommentsLoaded copyWith({
    String? postId,
    List<CommentModel>? comments,
    bool? hasReachedMax,
  }) {
    return CommentsLoaded(
      postId: postId ?? this.postId,
      comments: comments ?? this.comments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

/// حالة فشل تحميل التعليقات
class CommentsError extends PostsState {
  final String postId;
  final String message;

  const CommentsError({
    required this.postId,
    required this.message,
  });

  @override
  List<Object?> get props => [postId, message];
}

/// حالة تحميل إضافة تعليق
class CommentAdding extends PostsState {
  final String postId;

  const CommentAdding({required this.postId});

  @override
  List<Object?> get props => [postId];
}

/// حالة نجاح إضافة تعليق
class CommentAdded extends PostsState {
  final String postId;
  final CommentModel comment;

  const CommentAdded({
    required this.postId,
    required this.comment,
  });

  @override
  List<Object?> get props => [postId, comment];
}

/// حالة فشل إضافة تعليق
class CommentAddError extends PostsState {
  final String postId;
  final String message;

  const CommentAddError({
    required this.postId,
    required this.message,
  });

  @override
  List<Object?> get props => [postId, message];
}

/// حالة تحميل حذف تعليق
class CommentDeleting extends PostsState {
  final String postId;
  final String commentId;

  const CommentDeleting({
    required this.postId,
    required this.commentId,
  });

  @override
  List<Object?> get props => [postId, commentId];
}

/// حالة نجاح حذف تعليق
class CommentDeleted extends PostsState {
  final String postId;
  final String commentId;

  const CommentDeleted({
    required this.postId,
    required this.commentId,
  });

  @override
  List<Object?> get props => [postId, commentId];
}

/// حالة فشل حذف تعليق
class CommentDeleteError extends PostsState {
  final String postId;
  final String commentId;
  final String message;

  const CommentDeleteError({
    required this.postId,
    required this.commentId,
    required this.message,
  });

  @override
  List<Object?> get props => [postId, commentId, message];
}
