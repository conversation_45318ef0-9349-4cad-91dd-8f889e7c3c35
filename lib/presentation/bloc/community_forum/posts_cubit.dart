import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/utils/logging/logger_service.dart';
import '../../../data/models/community_forum/comment_model.dart';
import '../../../data/models/community_forum/post_model.dart';
import '../../../domain/repositories/post_repository_interface.dart';
import '../../../domain/repositories/storage_repository_interface.dart';
import 'posts_state.dart';

/// كيوبت إدارة المنشورات
///
/// يستخدم هذا الكيوبت لإدارة حالة المنشورات في المنتدى المجتمعي.
class PostsCubit extends Cubit<PostsState> {
  /// مستودع المنشورات
  final PostRepositoryInterface _postRepository;

  /// مستودع التخزين
  final StorageRepositoryInterface _storageRepository;

  /// عدد المنشورات التي يتم تحميلها في كل مرة
  static const int _postsLimit = 10;

  /// معرف آخر منشور تم تحميله (للتحميل التدريجي)
  String? _lastPostId;

  /// قاموس يحتوي على التعليقات لكل منشور
  final Map<String, List<CommentModel>> _commentsMap = {};

  /// قاموس يحتوي على معرف آخر تعليق تم تحميله لكل منشور
  final Map<String, String?> _lastCommentIdMap = {};

  /// نوع ترتيب المنشورات الحالي
  PostSortType _currentSortType = PostSortType.newest;

  /// مصطلح البحث الحالي
  String _currentSearchQuery = '';

  /// مجموعة لتتبع المنشورات التي تمت مشاهدتها في الجلسة الحالية
  final Set<String> _viewedPosts = {};

  /// منشئ كيوبت المنشورات
  PostsCubit({
    required PostRepositoryInterface postRepository,
    required StorageRepositoryInterface storageRepository,
  })  : _postRepository = postRepository,
        _storageRepository = storageRepository,
        super(PostsInitial());

  /// متغير لمنع التكرار المستمر لعملية تحميل المنشورات
  bool _isLoadingPosts = false;

  /// متغير لتتبع العملية الحالية
  String _currentOperation = '';

  /// العمليات المتاحة
  static const String OPERATION_LOAD_POSTS = 'load_posts';
  static const String OPERATION_LOAD_COMMENTS = 'load_comments';
  static const String OPERATION_ADD_COMMENT = 'add_comment';
  static const String OPERATION_DELETE_COMMENT = 'delete_comment';
  static const String OPERATION_LIKE_POST = 'like_post';
  static const String OPERATION_UNLIKE_POST = 'unlike_post';
  static const String OPERATION_CREATE_POST = 'create_post';
  static const String OPERATION_UPDATE_POST = 'update_post';
  static const String OPERATION_DELETE_POST = 'delete_post';
  static const String OPERATION_INCREMENT_VIEWS = 'increment_views';
  static const String OPERATION_SEARCH_POSTS = 'search_posts';

  /// الحصول على المنشورات
  ///
  /// يقوم بتحميل المنشورات من المستودع.
  /// إذا كان [refresh] صحيحًا، يتم إعادة تحميل المنشورات من البداية.
  Future<void> getPosts({bool refresh = false, PostSortType? sortType}) async {
    // منع التكرار المستمر لعملية تحميل المنشورات
    if (_isLoadingPosts || _currentOperation == OPERATION_LOAD_POSTS) {
      print('جاري تحميل المنشورات بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _isLoadingPosts = true;
    _currentOperation = OPERATION_LOAD_POSTS;
    print('بدء عملية: $_currentOperation');

    try {
      // تحديث نوع الترتيب إذا تم توفيره
      if (sortType != null) {
        _currentSortType = sortType;
        refresh = true; // إعادة تحميل المنشورات عند تغيير نوع الترتيب
      }

      // إذا كانت هذه هي المرة الأولى أو تم طلب التحديث
      if (state is PostsInitial || refresh) {
        print('تغيير الحالة إلى PostsLoading');
        emit(PostsLoading());
        _lastPostId = null;

        print('جاري جلب المنشورات من المستودع...');
        final posts = await _postRepository.getPosts(limit: _postsLimit);
        print('تم جلب ${posts.length} منشور');
        final sortedPosts = _sortPosts(posts, _currentSortType);

        if (sortedPosts.isEmpty) {
          print('لا توجد منشورات. تغيير الحالة إلى PostsLoaded مع قائمة فارغة');
          emit(PostsLoaded(
              posts: [],
              originalPosts: [],
              newestPosts: [],
              mostLikedPosts: [],
              mostCommentedPosts: [],
              mostViewedPosts: [],
              hasReachedMax: true,
              sortType: _currentSortType));
        } else {
          _lastPostId = posts.last.id;

          // ترتيب وتصفية المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(posts, PostSortType.newest);

          // تصفية المنشورات التي لها إعجاب واحد أو أكثر
          final mostLikedPosts = _sortPosts(
            posts.where((post) => post.likesCount >= 1).toList(),
            PostSortType.mostLiked
          );

          // تصفية المنشورات التي لها أكثر من خمسة تعليقات
          final mostCommentedPosts = _sortPosts(
            posts.where((post) => post.commentsCount > 5).toList(),
            PostSortType.mostCommented
          );

          // تصفية المنشورات التي لها أكثر من 15 مشاهدة
          final mostViewedPosts = _sortPosts(
            posts.where((post) => post.viewsCount > 15).toList(),
            PostSortType.mostViewed
          );

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (_currentSortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          print('تم ترتيب المنشورات. تغيير الحالة إلى PostsLoaded مع ${currentPosts.length} منشور');
          emit(PostsLoaded(
              posts: currentPosts,
              originalPosts: posts,
              newestPosts: newestPosts,
              mostLikedPosts: mostLikedPosts,
              mostCommentedPosts: mostCommentedPosts,
              mostViewedPosts: mostViewedPosts,
              hasReachedMax: posts.length < _postsLimit,
              sortType: _currentSortType));
        }
      }
      // إذا كانت الحالة هي CommentsLoaded، نقوم بتحميل المنشورات
      else if (state is CommentsLoaded) {
        print('تغيير الحالة من CommentsLoaded إلى PostsLoading');
        emit(PostsLoading());
        _lastPostId = null;

        print('جاري جلب المنشورات من المستودع...');
        final posts = await _postRepository.getPosts(limit: _postsLimit);
        print('تم جلب ${posts.length} منشور');
        final sortedPosts = _sortPosts(posts, _currentSortType);

        if (sortedPosts.isEmpty) {
          print('لا توجد منشورات. تغيير الحالة إلى PostsLoaded مع قائمة فارغة');
          emit(PostsLoaded(
              posts: [],
              originalPosts: [],
              newestPosts: [],
              mostLikedPosts: [],
              mostCommentedPosts: [],
              mostViewedPosts: [],
              hasReachedMax: true,
              sortType: _currentSortType));
        } else {
          _lastPostId = posts.last.id;

          // ترتيب المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(posts, PostSortType.newest);
          final mostLikedPosts = _sortPosts(posts, PostSortType.mostLiked);
          final mostCommentedPosts = _sortPosts(posts, PostSortType.mostCommented);
          final mostViewedPosts = _sortPosts(posts, PostSortType.mostViewed);

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (_currentSortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          print('تم ترتيب المنشورات. تغيير الحالة إلى PostsLoaded مع ${currentPosts.length} منشور');
          emit(PostsLoaded(
              posts: currentPosts,
              originalPosts: posts,
              newestPosts: newestPosts,
              mostLikedPosts: mostLikedPosts,
              mostCommentedPosts: mostCommentedPosts,
              mostViewedPosts: mostViewedPosts,
              hasReachedMax: posts.length < _postsLimit,
              sortType: _currentSortType));
        }
      }
      // إذا كانت هناك منشورات محملة بالفعل وتم طلب المزيد
      else if (state is PostsLoaded && !(state as PostsLoaded).hasReachedMax) {
        final currentState = state as PostsLoaded;

        final posts = await _postRepository.getPosts(
          limit: _postsLimit,
          lastPostId: _lastPostId,
        );

        if (posts.isEmpty) {
          emit(currentState.copyWith(hasReachedMax: true));
        } else {
          _lastPostId = posts.last.id;
          final allPosts = [...currentState.posts, ...posts];
          final sortedPosts = _sortPosts(allPosts, _currentSortType);

          // ترتيب المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(allPosts, PostSortType.newest);
          final mostLikedPosts = _sortPosts(allPosts, PostSortType.mostLiked);
          final mostCommentedPosts = _sortPosts(allPosts, PostSortType.mostCommented);
          final mostViewedPosts = _sortPosts(allPosts, PostSortType.mostViewed);

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (_currentSortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: allPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: posts.length < _postsLimit,
            sortType: _currentSortType,
          ));
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على المنشورات',
        error: e,
        tag: 'PostsCubit',
      );
      emit(PostsError('فشل في تحميل المنشورات: ${e.toString()}'));
    } finally {
      // إعادة تعيين متغيرات التتبع بعد الانتهاء من تحميل المنشورات
      _isLoadingPosts = false;
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_LOAD_POSTS');
    }
  }

  /// ترتيب المنشورات حسب نوع الترتيب
  List<PostModel> _sortPosts(List<PostModel> posts, PostSortType sortType) {
    // نسخ القائمة لتجنب تعديل القائمة الأصلية
    final sortedPosts = List<PostModel>.from(posts);

    switch (sortType) {
      case PostSortType.newest:
        sortedPosts.sort((a, b) => DateTime.parse(b.createdAt)
            .compareTo(DateTime.parse(a.createdAt)));
        return sortedPosts;
      case PostSortType.mostLiked:
        sortedPosts.sort((a, b) => b.likesCount.compareTo(a.likesCount));
        return sortedPosts;
      case PostSortType.mostCommented:
        sortedPosts.sort((a, b) => b.commentsCount.compareTo(a.commentsCount));
        return sortedPosts;
      case PostSortType.mostViewed:
        sortedPosts.sort((a, b) => b.viewsCount.compareTo(a.viewsCount));
        return sortedPosts;
    }
  }

  /// الحصول على نوع الترتيب الحالي
  PostSortType getCurrentSortType() {
    return _currentSortType;
  }

  /// تغيير نوع ترتيب المنشورات
  ///
  /// المعلمات:
  /// - [sortType]: نوع الترتيب الجديد
  Future<void> changeSortType(PostSortType sortType) async {
    // إذا كان نوع الترتيب هو نفسه، لا داعي للتغيير
    if (_currentSortType == sortType) return;

    // تحديث نوع الترتيب الحالي
    _currentSortType = sortType;

    // إذا كانت الحالة هي PostsLoaded، نستخدم المنشورات المخزنة بدلاً من جلبها من جديد
    if (state is PostsLoaded) {
      final currentState = state as PostsLoaded;

      // تطبيق المعايير على المنشورات الأصلية
      List<PostModel> filteredPosts = [];

      switch (sortType) {
        case PostSortType.newest:
          // لا نطبق أي معايير على تبويب الأحدث
          filteredPosts = currentState.newestPosts;
          break;
        case PostSortType.mostLiked:
          // نعرض فقط المنشورات التي لها إعجاب واحد أو أكثر
          filteredPosts = currentState.originalPosts
              .where((post) => post.likesCount >= 1)
              .toList();
          // ثم نرتبها حسب عدد الإعجابات
          filteredPosts.sort((a, b) => b.likesCount.compareTo(a.likesCount));
          break;
        case PostSortType.mostCommented:
          // نعرض فقط المنشورات التي لها أكثر من 5 تعليقات
          filteredPosts = currentState.originalPosts
              .where((post) => post.commentsCount > 5)
              .toList();
          // ثم نرتبها حسب عدد التعليقات
          filteredPosts.sort((a, b) => b.commentsCount.compareTo(a.commentsCount));
          break;
        case PostSortType.mostViewed:
          // نعرض فقط المنشورات التي لها أكثر من 15 مشاهدة
          filteredPosts = currentState.originalPosts
              .where((post) => post.viewsCount > 15)
              .toList();
          // ثم نرتبها حسب عدد المشاهدات
          filteredPosts.sort((a, b) => b.viewsCount.compareTo(a.viewsCount));
          break;
        default:
          filteredPosts = currentState.newestPosts;
      }

      // إصدار حالة جديدة مع نوع الترتيب الجديد والمنشورات المناسبة
      // لا نستخدم copyWith لأنه لا يدعم جميع الحقول الجديدة
      emit(PostsLoaded(
        posts: filteredPosts,
        originalPosts: currentState.originalPosts,
        newestPosts: currentState.newestPosts,
        mostLikedPosts: filteredPosts,
        mostCommentedPosts: filteredPosts,
        mostViewedPosts: filteredPosts,
        hasReachedMax: currentState.hasReachedMax,
        sortType: sortType,
        searchQuery: currentState.searchQuery,
        isSearchMode: currentState.isSearchMode,
      ));

      return;
    }

    // إذا لم تكن الحالة هي PostsLoaded، نقوم بجلب المنشورات من جديد
    await getPosts(refresh: true);
  }

  /// البحث في المنشورات
  ///
  /// المعلمات:
  /// - [query]: مصطلح البحث
  Future<void> searchPosts(String query) async {
    // منع التكرار المستمر لعملية البحث عن منشورات
    if (_currentOperation == OPERATION_SEARCH_POSTS) {
      print('جاري البحث عن منشورات بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_SEARCH_POSTS;
    print('بدء عملية: $_currentOperation');

    try {
      // تخزين مصطلح البحث الحالي
      _currentSearchQuery = query.trim();

      // إذا كان مصطلح البحث فارغًا، العودة إلى القائمة العادية
      if (_currentSearchQuery.isEmpty) {
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          // لا نستخدم copyWith لأنه لا يدعم جميع الحقول الجديدة
          emit(PostsLoaded(
            posts: currentState.posts,
            originalPosts: currentState.originalPosts,
            newestPosts: currentState.newestPosts,
            mostLikedPosts: currentState.mostLikedPosts,
            mostCommentedPosts: currentState.mostCommentedPosts,
            mostViewedPosts: currentState.mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: '',
            isSearchMode: false,
          ));
        }
        return;
      }

      // إظهار حالة البحث
      emit(PostsSearching());

      // البحث في المنشورات
      final searchResults =
          await _postRepository.searchPosts(query: _currentSearchQuery);

      // إظهار نتائج البحث
      emit(PostsLoaded(
        posts: searchResults,
        originalPosts: searchResults, // نستخدم نفس النتائج لجميع القوائم في وضع البحث
        newestPosts: searchResults,
        mostLikedPosts: searchResults,
        mostCommentedPosts: searchResults,
        mostViewedPosts: searchResults,
        hasReachedMax: true, // لا يمكن تحميل المزيد في وضع البحث
        sortType: _currentSortType,
        searchQuery: _currentSearchQuery,
        isSearchMode: true,
      ));
    } catch (e) {
      LoggerService.error(
        'خطأ في البحث عن المنشورات',
        error: e,
        tag: 'PostsCubit',
      );
      emit(PostsError('فشل في البحث عن المنشورات: ${e.toString()}'));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_SEARCH_POSTS');
    }
  }

  /// إلغاء وضع البحث والعودة إلى القائمة العادية
  Future<void> clearSearch() async {
    _currentSearchQuery = '';
    await getPosts(refresh: true);
  }

  /// حذف منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف المنشور
  Future<bool> deletePost(String postId, String userId) async {
    // منع التكرار المستمر لعملية حذف منشور
    if (_currentOperation == OPERATION_DELETE_POST) {
      print('جاري حذف منشور بالفعل. تجاهل الطلب الجديد.');
      return false;
    }

    _currentOperation = OPERATION_DELETE_POST;
    print('بدء عملية: $_currentOperation');

    try {
      // التحقق من وجود المنشور
      final post = getPostById(postId);
      if (post == null) {
        LoggerService.warning(
          'محاولة حذف منشور غير موجود: $postId',
          tag: 'PostsCubit',
        );
        return false;
      }

      // التحقق من أن المستخدم هو منشئ المنشور
      if (post.userId != userId) {
        LoggerService.warning(
          'محاولة حذف منشور بواسطة مستخدم غير مصرح له: $userId',
          tag: 'PostsCubit',
        );
        return false;
      }

      // إظهار حالة جاري حذف المنشور
      emit(PostDeleting(postId));

      // حذف المنشور من قاعدة البيانات والتخزين
      final success = await _postRepository.deletePost(postId, userId);

      if (success) {
        // تحديث قائمة المنشورات المحلية
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts =
              currentState.posts.where((p) => p.id != postId).toList();

          emit(PostsLoaded(
            posts: updatedPosts,
            originalPosts: currentState.originalPosts.where((p) => p.id != postId).toList(),
            newestPosts: currentState.newestPosts.where((p) => p.id != postId).toList(),
            mostLikedPosts: currentState.mostLikedPosts.where((p) => p.id != postId).toList(),
            mostCommentedPosts: currentState.mostCommentedPosts.where((p) => p.id != postId).toList(),
            mostViewedPosts: currentState.mostViewedPosts.where((p) => p.id != postId).toList(),
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }

        LoggerService.info(
          'تم حذف المنشور بنجاح: $postId',
          tag: 'PostsCubit',
        );

        return true;
      } else {
        // إعادة الحالة السابقة في حالة الفشل
        if (state is PostsLoaded) {
          emit(state);
        } else {
          await getPosts(refresh: true);
        }

        LoggerService.error(
          'فشل في حذف المنشور: $postId',
          tag: 'PostsCubit',
        );

        return false;
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف المنشور',
        error: e,
        tag: 'PostsCubit',
      );

      // إعادة الحالة السابقة في حالة الخطأ
      if (state is PostsLoaded) {
        emit(state);
      } else {
        await getPosts(refresh: true);
      }

      return false;
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_DELETE_POST');
    }
  }

  /// إنشاء منشور جديد
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم الذي ينشئ المنشور
  /// - [userName]: اسم المستخدم الذي ينشئ المنشور
  /// - [userImage]: صورة المستخدم الذي ينشئ المنشور
  /// - [text]: نص المنشور (اختياري)
  /// - [imageFiles]: ملفات الصور المرفقة بالمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو المرفق بالمنشور (اختياري)
  /// - [hashtags]: قائمة الوسوم (هاشتاج) المرفقة بالمنشور (اختياري)
  Future<void> createPost({
    required String userId,
    required String userName,
    required String userImage,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? hashtags,
  }) async {
    // منع التكرار المستمر لعملية إنشاء منشور
    if (_currentOperation == OPERATION_CREATE_POST) {
      print('جاري إنشاء منشور بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_CREATE_POST;
    print('بدء عملية: $_currentOperation');

    try {
      emit(PostCreating());

      final post = await _postRepository.createPost(
        userId: userId,
        userName: userName,
        userImage: userImage,
        text: text,
        imageFiles: imageFiles,
        videoFile: videoFile,
        hashtags: hashtags,
      );

      if (post != null) {
        emit(PostCreated(post));

        // تحديث قائمة المنشورات إذا كانت محملة بالفعل
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          // إضافة المنشور الجديد في بداية القائمة (في الأعلى)
          final updatedOriginalPosts = [post, ...currentState.originalPosts];

          // إعادة ترتيب وتصفية المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);

          // تصفية المنشورات التي لها إعجاب واحد أو أكثر
          final mostLikedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.likesCount >= 1).toList(),
            PostSortType.mostLiked
          );

          // تصفية المنشورات التي لها أكثر من خمسة تعليقات
          final mostCommentedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.commentsCount > 5).toList(),
            PostSortType.mostCommented
          );

          // تصفية المنشورات التي لها أكثر من 15 مشاهدة
          final mostViewedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.viewsCount > 15).toList(),
            PostSortType.mostViewed
          );

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        } else {
          // إعادة تحميل المنشورات
          getPosts(refresh: true);
        }
      } else {
        emit(const PostCreationError(message: 'فشل في إنشاء المنشور'));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء منشور جديد',
        error: e,
        tag: 'PostsCubit',
      );
      emit(PostCreationError(message: 'فشل في إنشاء المنشور: ${e.toString()}'));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_CREATE_POST');
    }
  }

  /// تحديث منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور المراد تحديثه
  /// - [text]: النص الجديد للمنشور (اختياري)
  /// - [imageFiles]: ملفات الصور الجديدة للمنشور (اختياري)
  /// - [videoFile]: ملف الفيديو الجديد للمنشور (اختياري)
  /// - [deleteImages]: قائمة روابط الصور المراد حذفها (اختياري)
  /// - [deleteVideo]: ما إذا كان يجب حذف الفيديو الحالي (اختياري)
  Future<void> updatePost({
    required String postId,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? deleteImages,
    bool deleteVideo = false,
  }) async {
    // منع التكرار المستمر لعملية تحديث منشور
    if (_currentOperation == OPERATION_UPDATE_POST) {
      print('جاري تحديث منشور بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_UPDATE_POST;
    print('بدء عملية: $_currentOperation');

    try {
      emit(PostUpdating());

      final post = await _postRepository.updatePost(
        postId: postId,
        text: text,
        imageFiles: imageFiles,
        videoFile: videoFile,
        deleteImages: deleteImages,
        deleteVideo: deleteVideo,
      );

      if (post != null) {
        emit(PostUpdated(post));

        // تحديث المنشور في قائمة المنشورات إذا كانت محملة بالفعل
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts = currentState.posts.map((p) {
            return p.id == post.id ? post : p;
          }).toList();

          // تحديث المنشور في جميع القوائم
          final updatedOriginalPosts = currentState.originalPosts.map((p) {
            return p.id == post.id ? post : p;
          }).toList();

          // إعادة ترتيب المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);
          final mostLikedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostLiked);
          final mostCommentedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostCommented);
          final mostViewedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostViewed);

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }
      } else {
        emit(const PostUpdateError('فشل في تحديث المنشور'));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث منشور',
        error: e,
        tag: 'PostsCubit',
      );
      emit(PostUpdateError('فشل في تحديث المنشور: ${e.toString()}'));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_UPDATE_POST');
    }
  }

  // تم استبدال هذه الدالة بالدالة الجديدة التي تتطلب معرف المستخدم

  /// إضافة إعجاب لمنشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يبدي الإعجاب
  Future<void> likePost(String postId, String userId) async {
    // منع التكرار المستمر لعملية الإعجاب بمنشور
    if (_currentOperation == OPERATION_LIKE_POST) {
      print('جاري الإعجاب بمنشور بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_LIKE_POST;
    print('بدء عملية: $_currentOperation');

    try {
      final success = await _postRepository.likePost(postId, userId);

      if (success) {
        // تحديث المنشور في قائمة المنشورات إذا كانت محملة بالفعل
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts = currentState.posts.map((post) {
            if (post.id == postId) {
              // إضافة المستخدم إلى قائمة المعجبين وزيادة عدد الإعجابات
              final updatedLikedBy = List<String>.from(post.likedBy)
                ..add(userId);
              return post.copyWith(
                likedBy: updatedLikedBy,
                likesCount: post.likesCount + 1,
              );
            }
            return post;
          }).toList();

          // تحديث المنشور في جميع القوائم
          final updatedOriginalPosts = currentState.originalPosts.map((post) {
            if (post.id == postId) {
              final updatedLikedBy = List<String>.from(post.likedBy ?? [])..add(userId);
              return post.copyWith(
                likedBy: updatedLikedBy,
                likesCount: post.likesCount + 1,
              );
            }
            return post;
          }).toList();

          // إعادة ترتيب المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);
          final mostLikedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostLiked);
          final mostCommentedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostCommented);
          final mostViewedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostViewed);

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة إعجاب لمنشور',
        error: e,
        tag: 'PostsCubit',
      );
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_LIKE_POST');
    }
  }

  /// إزالة إعجاب من منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يزيل الإعجاب
  Future<void> unlikePost(String postId, String userId) async {
    // منع التكرار المستمر لعملية إزالة الإعجاب من منشور
    if (_currentOperation == OPERATION_UNLIKE_POST) {
      print('جاري إزالة الإعجاب من منشور بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_UNLIKE_POST;
    print('بدء عملية: $_currentOperation');

    try {
      final success = await _postRepository.unlikePost(postId, userId);

      if (success) {
        // تحديث المنشور في قائمة المنشورات إذا كانت محملة بالفعل
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts = currentState.posts.map((post) {
            if (post.id == postId) {
              // إزالة المستخدم من قائمة المعجبين وتقليل عدد الإعجابات
              final updatedLikedBy = List<String>.from(post.likedBy)
                ..remove(userId);
              return post.copyWith(
                likedBy: updatedLikedBy,
                likesCount: post.likesCount - 1,
              );
            }
            return post;
          }).toList();

          // تحديث المنشور في جميع القوائم
          final updatedOriginalPosts = currentState.originalPosts.map((post) {
            if (post.id == postId) {
              final updatedLikedBy = List<String>.from(post.likedBy ?? [])..remove(userId);
              return post.copyWith(
                likedBy: updatedLikedBy,
                likesCount: post.likesCount - 1,
              );
            }
            return post;
          }).toList();

          // إعادة ترتيب المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);
          final mostLikedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostLiked);
          final mostCommentedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostCommented);
          final mostViewedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostViewed);

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في إزالة إعجاب من منشور',
        error: e,
        tag: 'PostsCubit',
      );
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_UNLIKE_POST');
    }
  }

  // متغير لتخزين حالة المنشورات السابقة
  PostsState? _previousPostsState;

  /// الحصول على تعليقات منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [refresh]: ما إذا كان يجب إعادة تحميل التعليقات من البداية
  Future<void> getComments(String postId, {bool refresh = false}) async {
    // منع التكرار المستمر لعملية تحميل التعليقات
    if (_currentOperation == OPERATION_LOAD_COMMENTS) {
      print('جاري تحميل التعليقات بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    // التحقق مما إذا كانت التعليقات محملة بالفعل ولم يتم طلب التحديث
    if (!refresh && state is CommentsLoaded && (state as CommentsLoaded).postId == postId) {
      print('التعليقات محملة بالفعل للمنشور: $postId. تجاهل الطلب.');
      return;
    }

    // التحقق مما إذا كانت الحالة هي PostsLoading أو PostsLoaded
    // لمنع التداخل بين حالات المنشورات والتعليقات
    if (state is PostsLoading) {
      print('الحالة الحالية هي PostsLoading. لا يمكن تحميل التعليقات الآن.');
      return;
    }

    // إذا كانت الحالة هي PostsLoaded، نحفظ الحالة للعودة إليها بعد تحميل التعليقات
    if (state is PostsLoaded) {
      _previousPostsState = state;
    }

    _currentOperation = OPERATION_LOAD_COMMENTS;
    print('بدء عملية: $_currentOperation');

    try {
      print('جاري تحميل التعليقات للمنشور: $postId');
      emit(CommentsLoading(postId));

      // إذا كان هذا هو التحميل الأول أو تم طلب التحديث
      if (!_commentsMap.containsKey(postId) || refresh) {
        _lastCommentIdMap[postId] = null;

        final comments = await _postRepository.getComments(postId: postId);
        print('تم جلب ${comments.length} تعليق للمنشور: $postId');

        _commentsMap[postId] = comments;

        if (comments.isNotEmpty) {
          _lastCommentIdMap[postId] = comments.last.id;
        }

        // إضافة تأخير قصير قبل العودة إلى حالة CommentsLoaded
        await Future.delayed(const Duration(milliseconds: 500));

        print('العودة إلى حالة CommentsLoaded للمنشور: $postId');
        emit(CommentsLoaded(
          postId: postId,
          comments: comments,
          hasReachedMax: comments.length < 20, // افتراض أن الحد الأقصى هو 20
        ));
        print('تم تحميل التعليقات بنجاح للمنشور: $postId');
      }
      // إذا كانت هناك تعليقات محملة بالفعل وتم طلب المزيد
      else {
        final comments = await _postRepository.getComments(
          postId: postId,
          lastCommentId: _lastCommentIdMap[postId],
        );
        print('تم جلب ${comments.length} تعليق إضافي للمنشور: $postId');

        if (comments.isEmpty) {
          // إضافة تأخير قصير قبل العودة إلى حالة CommentsLoaded
          await Future.delayed(const Duration(milliseconds: 500));

          print('العودة إلى حالة CommentsLoaded للمنشور: $postId');
          emit(CommentsLoaded(
            postId: postId,
            comments: _commentsMap[postId] ?? [],
            hasReachedMax: true,
          ));
          print('لا توجد تعليقات إضافية للمنشور: $postId');
        } else {
          _lastCommentIdMap[postId] = comments.last.id;
          _commentsMap[postId] = [...(_commentsMap[postId] ?? []), ...comments];

          // إضافة تأخير قصير قبل العودة إلى حالة CommentsLoaded
          await Future.delayed(const Duration(milliseconds: 500));

          print('العودة إلى حالة CommentsLoaded للمنشور: $postId');
          emit(CommentsLoaded(
            postId: postId,
            comments: _commentsMap[postId] ?? [],
            hasReachedMax: comments.length < 20, // افتراض أن الحد الأقصى هو 20
          ));
          print('تم تحميل التعليقات الإضافية بنجاح للمنشور: $postId');
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على تعليقات منشور',
        error: e,
        tag: 'PostsCubit',
      );
      emit(CommentsError(
        postId: postId,
        message: 'فشل في تحميل التعليقات: ${e.toString()}',
      ));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_LOAD_COMMENTS');
      print('الحالة النهائية: ${state.runtimeType}');

      // إذا كانت هناك حالة سابقة للمنشورات، نعود إليها
      if (_previousPostsState != null) {
        print('العودة إلى حالة المنشورات السابقة: ${_previousPostsState.runtimeType}');
        emit(_previousPostsState!);
        _previousPostsState = null;
      }
    }
  }

  /// إضافة تعليق على منشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم الذي يضيف التعليق
  /// - [userName]: اسم المستخدم الذي يضيف التعليق
  /// - [userImage]: صورة المستخدم الذي يضيف التعليق
  /// - [text]: نص التعليق
  Future<void> addComment({
    required String postId,
    required String userId,
    required String userName,
    required String userImage,
    required String text,
  }) async {
    // منع التكرار المستمر لعملية إضافة تعليق
    if (_currentOperation == OPERATION_ADD_COMMENT) {
      print('جاري إضافة تعليق بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_ADD_COMMENT;
    print('بدء عملية: $_currentOperation');

    try {
      print('جاري إضافة تعليق على المنشور: $postId');
      emit(CommentAdding(postId: postId));

      final comment = await _postRepository.addComment(
        postId: postId,
        userId: userId,
        userName: userName,
        userImage: userImage,
        text: text,
      );

      if (comment != null) {
        // إضافة التعليق إلى قائمة التعليقات المحلية
        if (_commentsMap.containsKey(postId)) {
          _commentsMap[postId] = [comment, ...(_commentsMap[postId] ?? [])];
        } else {
          _commentsMap[postId] = [comment];
        }

        print('تم إضافة التعليق بنجاح على المنشور: $postId');
        emit(CommentAdded(
          postId: postId,
          comment: comment,
        ));

        // بعد إضافة التعليق، نعود إلى حالة CommentsLoaded
        print('العودة إلى حالة CommentsLoaded للمنشور: $postId');

        // إضافة تأخير قصير قبل العودة إلى حالة CommentsLoaded
        await Future.delayed(const Duration(milliseconds: 500));

        emit(CommentsLoaded(
          postId: postId,
          comments: _commentsMap[postId] ?? [],
          hasReachedMax: false,
        ));

        // تحديث عدد التعليقات في المنشور
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts = currentState.posts.map((post) {
            if (post.id == postId) {
              return post.copyWith(
                commentsCount: post.commentsCount + 1,
              );
            }
            return post;
          }).toList();

          // تحديث المنشور في جميع القوائم
          final updatedOriginalPosts = currentState.originalPosts.map((post) {
            if (post.id == postId) {
              return post.copyWith(
                commentsCount: post.commentsCount + 1,
              );
            }
            return post;
          }).toList();

          // إعادة ترتيب وتصفية المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);

          // تصفية المنشورات التي لها إعجاب واحد أو أكثر
          final mostLikedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.likesCount >= 1).toList(),
            PostSortType.mostLiked
          );

          // تصفية المنشورات التي لها أكثر من خمسة تعليقات
          final mostCommentedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.commentsCount > 5).toList(),
            PostSortType.mostCommented
          );

          // تصفية المنشورات التي لها أكثر من 15 مشاهدة
          final mostViewedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.viewsCount > 15).toList(),
            PostSortType.mostViewed
          );

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }
      } else {
        emit(CommentAddError(
          postId: postId,
          message: 'فشل في إضافة التعليق',
        ));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة تعليق على منشور',
        error: e,
        tag: 'PostsCubit',
      );
      emit(CommentAddError(
        postId: postId,
        message: 'فشل في إضافة التعليق: ${e.toString()}',
      ));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_ADD_COMMENT');
    }
  }

  /// حذف تعليق
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [commentId]: معرف التعليق المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف التعليق
  Future<void> deleteComment(String postId, String commentId, String userId) async {
    // منع التكرار المستمر لعملية حذف تعليق
    if (_currentOperation == OPERATION_DELETE_COMMENT) {
      print('جاري حذف تعليق بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_DELETE_COMMENT;
    print('بدء عملية: $_currentOperation');

    try {
      // التحقق من وجود التعليق
      final comments = _commentsMap[postId] ?? [];
      final comment = comments.firstWhere(
        (c) => c.id == commentId,
        orElse: () => null as CommentModel,
      );

      // إذا لم يتم العثور على التعليق
      if (comment == null) {
        emit(CommentDeleteError(
          postId: postId,
          commentId: commentId,
          message: 'لم يتم العثور على التعليق',
        ));
        return;
      }

      // التحقق من أن المستخدم هو صاحب التعليق
      if (comment.userId != userId) {
        emit(CommentDeleteError(
          postId: postId,
          commentId: commentId,
          message: 'لا يمكنك حذف تعليق لم تقم بإنشائه',
        ));
        return;
      }

      emit(CommentDeleting(
        postId: postId,
        commentId: commentId,
      ));

      final success = await _postRepository.deleteComment(postId, commentId);

      if (success) {
        // حذف التعليق من قائمة التعليقات المحلية
        if (_commentsMap.containsKey(postId)) {
          _commentsMap[postId] = (_commentsMap[postId] ?? [])
              .where((comment) => comment.id != commentId)
              .toList();
        }

        emit(CommentDeleted(
          postId: postId,
          commentId: commentId,
        ));

        // بعد حذف التعليق، نعود إلى حالة CommentsLoaded
        emit(CommentsLoaded(
          postId: postId,
          comments: _commentsMap[postId] ?? [],
          hasReachedMax: false,
        ));

        // تحديث عدد التعليقات في المنشور
        if (state is PostsLoaded) {
          final currentState = state as PostsLoaded;
          final updatedPosts = currentState.posts.map((post) {
            if (post.id == postId && post.commentsCount > 0) {
              return post.copyWith(
                commentsCount: post.commentsCount - 1,
              );
            }
            return post;
          }).toList();

          // تحديث المنشور في جميع القوائم
          final updatedOriginalPosts = currentState.originalPosts.map((post) {
            if (post.id == postId && post.commentsCount > 0) {
              return post.copyWith(
                commentsCount: post.commentsCount - 1,
              );
            }
            return post;
          }).toList();

          // إعادة ترتيب وتصفية المنشورات بطرق مختلفة
          final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);

          // تصفية المنشورات التي لها إعجاب واحد أو أكثر
          final mostLikedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.likesCount >= 1).toList(),
            PostSortType.mostLiked
          );

          // تصفية المنشورات التي لها أكثر من خمسة تعليقات
          final mostCommentedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.commentsCount > 5).toList(),
            PostSortType.mostCommented
          );

          // تصفية المنشورات التي لها أكثر من 15 مشاهدة
          final mostViewedPosts = _sortPosts(
            updatedOriginalPosts.where((post) => post.viewsCount > 15).toList(),
            PostSortType.mostViewed
          );

          // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
          List<PostModel> currentPosts;
          switch (currentState.sortType) {
            case PostSortType.newest:
              currentPosts = newestPosts;
              break;
            case PostSortType.mostLiked:
              currentPosts = mostLikedPosts;
              break;
            case PostSortType.mostCommented:
              currentPosts = mostCommentedPosts;
              break;
            case PostSortType.mostViewed:
              currentPosts = mostViewedPosts;
              break;
            default:
              currentPosts = newestPosts;
          }

          emit(PostsLoaded(
            posts: currentPosts,
            originalPosts: updatedOriginalPosts,
            newestPosts: newestPosts,
            mostLikedPosts: mostLikedPosts,
            mostCommentedPosts: mostCommentedPosts,
            mostViewedPosts: mostViewedPosts,
            hasReachedMax: currentState.hasReachedMax,
            sortType: currentState.sortType,
            searchQuery: currentState.searchQuery,
            isSearchMode: currentState.isSearchMode,
          ));
        }
      } else {
        emit(CommentDeleteError(
          postId: postId,
          commentId: commentId,
          message: 'فشل في حذف التعليق',
        ));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف تعليق',
        error: e,
        tag: 'PostsCubit',
      );
      emit(CommentDeleteError(
        postId: postId,
        commentId: commentId,
        message: 'فشل في حذف التعليق: ${e.toString()}',
      ));
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_DELETE_COMMENT');
    }
  }

  /// التحقق مما إذا كان المستخدم قد أعجب بالمنشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [userId]: معرف المستخدم
  ///
  /// يعيد true إذا كان المستخدم قد أعجب بالمنشور، false خلاف ذلك
  bool isPostLikedByUser(String postId, String userId) {
    if (state is PostsLoaded) {
      final currentState = state as PostsLoaded;
      final post = currentState.posts.firstWhere(
        (post) => post.id == postId,
        orElse: () => PostModel(
          id: '',
          userId: '',
          userName: '',
          userImage: '',
          createdAt: '',
          lastUpdated: '',
        ),
      );

      return post.likedBy.contains(userId);
    }
    return false;
  }

  /// الحصول على منشور محدد
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  ///
  /// يعيد المنشور المطلوب أو null إذا لم يتم العثور عليه
  PostModel? getPostById(String postId) {
    if (state is PostsLoaded) {
      final currentState = state as PostsLoaded;
      try {
        return currentState.posts.firstWhere((post) => post.id == postId);
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  /// زيادة عدد مشاهدات المنشور
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  /// - [force]: إجبار زيادة المشاهدات حتى لو تمت مشاهدة المنشور من قبل
  Future<void> incrementPostViews(String postId, {bool force = false}) async {
    // منع التكرار المستمر لعملية زيادة عدد مشاهدات منشور
    if (_currentOperation == OPERATION_INCREMENT_VIEWS) {
      print('جاري زيادة عدد مشاهدات منشور بالفعل. تجاهل الطلب الجديد.');
      return;
    }

    _currentOperation = OPERATION_INCREMENT_VIEWS;
    print('بدء عملية: $_currentOperation');

    try {
      // التحقق من وجود المنشور
      final post = getPostById(postId);
      if (post == null) return;

      // التحقق مما إذا كان المنشور قد تمت مشاهدته بالفعل في هذه الجلسة
      if (!force && _viewedPosts.contains(postId)) {
        // تمت مشاهدة المنشور بالفعل، لا حاجة لزيادة العداد
        return;
      }

      // إضافة المنشور إلى قائمة المنشورات التي تمت مشاهدتها
      _viewedPosts.add(postId);

      // زيادة عدد المشاهدات في قاعدة البيانات
      await _postRepository.incrementPostViews(postId);

      // تحديث المنشور في الحالة المحلية
      if (state is PostsLoaded) {
        final currentState = state as PostsLoaded;
        final updatedPosts = currentState.posts.map((p) {
          if (p.id == postId) {
            return p.copyWith(viewsCount: p.viewsCount + 1);
          }
          return p;
        }).toList();

        // تحديث المنشور في جميع القوائم
        final updatedOriginalPosts = currentState.originalPosts.map((p) {
          if (p.id == postId) {
            return p.copyWith(viewsCount: p.viewsCount + 1);
          }
          return p;
        }).toList();

        // إعادة ترتيب المنشورات بطرق مختلفة
        final newestPosts = _sortPosts(updatedOriginalPosts, PostSortType.newest);
        final mostLikedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostLiked);
        final mostCommentedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostCommented);
        final mostViewedPosts = _sortPosts(updatedOriginalPosts, PostSortType.mostViewed);

        // اختيار القائمة المناسبة حسب نوع الترتيب الحالي
        List<PostModel> currentPosts;
        switch (currentState.sortType) {
          case PostSortType.newest:
            currentPosts = newestPosts;
            break;
          case PostSortType.mostLiked:
            currentPosts = mostLikedPosts;
            break;
          case PostSortType.mostCommented:
            currentPosts = mostCommentedPosts;
            break;
          case PostSortType.mostViewed:
            currentPosts = mostViewedPosts;
            break;
          default:
            currentPosts = newestPosts;
        }

        emit(PostsLoaded(
          posts: currentPosts,
          originalPosts: updatedOriginalPosts,
          newestPosts: newestPosts,
          mostLikedPosts: mostLikedPosts,
          mostCommentedPosts: mostCommentedPosts,
          mostViewedPosts: mostViewedPosts,
          hasReachedMax: currentState.hasReachedMax,
          sortType: currentState.sortType,
          searchQuery: currentState.searchQuery,
          isSearchMode: currentState.isSearchMode,
        ));
      }

      LoggerService.info(
        'تمت زيادة عدد مشاهدات المنشور: $postId',
        tag: 'PostsCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في زيادة عدد مشاهدات المنشور',
        error: e,
        tag: 'PostsCubit',
      );
    } finally {
      // إعادة تعيين متغير العملية الحالية
      _currentOperation = '';
      print('انتهاء عملية: $OPERATION_INCREMENT_VIEWS');
    }
  }

  /// استخراج الوسوم (هاشتاج) من نص المنشور
  ///
  /// المعلمات:
  /// - [text]: نص المنشور
  ///
  /// يعيد قائمة الوسوم المستخرجة من النص
  static List<String> extractHashtags(String? text) {
    if (text == null || text.isEmpty) return [];

    // البحث عن الوسوم باستخدام تعبير نمطي
    final RegExp hashtagRegExp = RegExp(r'#(\w+)', multiLine: true);
    final matches = hashtagRegExp.allMatches(text);

    // استخراج الوسوم وإزالة التكرار
    final hashtags = matches.map((match) => match.group(1)!).toSet().toList();

    return hashtags;
  }

  /// الحصول على تعليقات منشور محدد
  ///
  /// المعلمات:
  /// - [postId]: معرف المنشور
  ///
  /// يعيد قائمة التعليقات للمنشور المطلوب
  List<CommentModel> getCommentsForPost(String postId) {
    return _commentsMap[postId] ?? [];
  }

  /// إعادة تعيين حالة المنشورات
  void resetState() {
    print('إعادة تعيين حالة المنشورات إلى PostsInitial');
    emit(PostsInitial());
    // لا نستدعي getPosts هنا لتجنب الحلقة اللانهائية
    // سيتم استدعاؤها من الواجهة عند الحاجة
  }
}
