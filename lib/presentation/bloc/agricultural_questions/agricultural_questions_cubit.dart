import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/models/agricultural_advisor/consultation_model.dart';
import '../advisor/advisor_cubit.dart';
import '../advisor/advisor_state.dart';

/// حالات صفحة الأسئلة الزراعية
abstract class AgriculturalQuestionsState {}

/// الحالة الأولية
class AgriculturalQuestionsInitial extends AgriculturalQuestionsState {}

/// حالة تحميل البيانات
class AgriculturalQuestionsLoading extends AgriculturalQuestionsState {}

/// حالة تحميل البيانات بنجاح
class AgriculturalQuestionsLoaded extends AgriculturalQuestionsState {
  /// متحكم التبويب
  final TabController tabController;
  
  /// قائمة الاستشارات الحقيقية
  final List<ConsultationModel> consultations;
  
  /// قائمة الأسئلة الاحتياطية
  final List<Map<String, dynamic>> fallbackQuestions;
  
  /// قائمة الأسئلة المميزة
  final List<Map<String, dynamic>> featuredQuestions;
  
  /// ما إذا كان يتم تحميل البيانات
  final bool isLoading;

  AgriculturalQuestionsLoaded({
    required this.tabController,
    required this.consultations,
    required this.fallbackQuestions,
    required this.featuredQuestions,
    this.isLoading = false,
  });

  /// نسخ الحالة مع تحديث بعض القيم
  AgriculturalQuestionsLoaded copyWith({
    TabController? tabController,
    List<ConsultationModel>? consultations,
    List<Map<String, dynamic>>? fallbackQuestions,
    List<Map<String, dynamic>>? featuredQuestions,
    bool? isLoading,
  }) {
    return AgriculturalQuestionsLoaded(
      tabController: tabController ?? this.tabController,
      consultations: consultations ?? this.consultations,
      fallbackQuestions: fallbackQuestions ?? this.fallbackQuestions,
      featuredQuestions: featuredQuestions ?? this.featuredQuestions,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// حالة خطأ
class AgriculturalQuestionsError extends AgriculturalQuestionsState {
  final String message;
  
  AgriculturalQuestionsError(this.message);
}

/// Cubit لإدارة حالة صفحة الأسئلة الزراعية
class AgriculturalQuestionsCubit extends Cubit<AgriculturalQuestionsState> {
  /// متحكم التبويب
  late TabController _tabController;
  
  /// AdvisorCubit للتعامل مع الاستشارات
  final AdvisorCubit advisorCubit;

  AgriculturalQuestionsCubit({
    required TickerProvider vsync,
    required this.advisorCubit,
  }) : super(AgriculturalQuestionsInitial()) {
    _initializeTabController(vsync);
  }

  /// تهيئة متحكم التبويب
  void _initializeTabController(TickerProvider vsync) {
    _tabController = TabController(length: 3, vsync: vsync);
  }

  /// تهيئة الصفحة
  void initialize() {
    emit(AgriculturalQuestionsLoaded(
      tabController: _tabController,
      consultations: [],
      fallbackQuestions: _getFallbackQuestions(),
      featuredQuestions: _getFeaturedQuestions(),
      isLoading: true,
    ));
    
    loadConsultations();
  }

  /// تحميل الاستشارات
  Future<void> loadConsultations() async {
    try {
      if (state is AgriculturalQuestionsLoaded) {
        final currentState = state as AgriculturalQuestionsLoaded;
        emit(currentState.copyWith(isLoading: true));
      }

      // محاولة تحميل الاستشارات من قاعدة البيانات
      await advisorCubit.getAllConsultations();

      final advisorState = advisorCubit.state;
      List<ConsultationModel> consultations = [];

      if (advisorState is ConsultationsLoaded) {
        consultations = advisorState.consultations;
      }

      if (state is AgriculturalQuestionsLoaded) {
        final currentState = state as AgriculturalQuestionsLoaded;
        emit(currentState.copyWith(
          consultations: consultations,
          isLoading: false,
        ));
      }
    } catch (e) {
      if (state is AgriculturalQuestionsLoaded) {
        final currentState = state as AgriculturalQuestionsLoaded;
        emit(currentState.copyWith(isLoading: false));
      } else {
        emit(AgriculturalQuestionsError('حدث خطأ في تحميل البيانات: ${e.toString()}'));
      }
    }
  }

  /// الحصول على البيانات الاحتياطية
  List<Map<String, dynamic>> _getFallbackQuestions() {
    return [
      {
        'id': '1',
        'user': 'أحمد محمد',
        'userImage': null,
        'crop': 'طماطم',
        'problem': 'ظهور بقع صفراء على أوراق الطماطم وذبول في بعض الأجزاء',
        'area': '500',
        'date': 'منذ 3 أيام',
        'image': 'assets/images/tomato_disease.jpg',
        'status': 'تمت الإجابة',
        'rating': 4.5,
        'answers': 3,
        'views': 120,
      },
      {
        'id': '2',
        'user': 'محمد علي',
        'userImage': null,
        'crop': 'قمح',
        'problem': 'ظهور حشرات صغيرة على سنابل القمح وبدأت تؤثر على المحصول',
        'area': '2000',
        'date': 'منذ 5 أيام',
        'image': 'assets/images/wheat_pests.jpg',
        'status': 'تمت الإجابة',
        'rating': 5.0,
        'answers': 5,
        'views': 210,
      },
      {
        'id': '3',
        'user': 'خالد أحمد',
        'userImage': null,
        'crop': 'خيار',
        'problem': 'تشوه في ثمار الخيار وصغر حجمها عن المعتاد',
        'area': '300',
        'date': 'منذ يومين',
        'image': 'assets/images/cucumber_problem.jpg',
        'status': 'بانتظار الرد',
        'rating': 0,
        'answers': 0,
        'views': 45,
      },
      {
        'id': '4',
        'user': 'فاطمة محمد',
        'userImage': null,
        'crop': 'ذرة',
        'problem': 'ضعف نمو نباتات الذرة رغم توفر الري والأسمدة المناسبة',
        'area': '1500',
        'date': 'منذ أسبوع',
        'image': 'assets/images/corn_growth.jpg',
        'status': 'تمت الإجابة',
        'rating': 4.0,
        'answers': 2,
        'views': 98,
      },
      {
        'id': '5',
        'user': 'عبدالله سعيد',
        'userImage': null,
        'crop': 'فلفل',
        'problem': 'تساقط الأزهار قبل تكوين الثمار في محصول الفلفل',
        'area': '400',
        'date': 'منذ 4 أيام',
        'image': 'assets/images/pepper_flowers.jpg',
        'status': 'تمت الإجابة',
        'rating': 3.5,
        'answers': 4,
        'views': 156,
      },
    ];
  }

  /// الحصول على الأسئلة المميزة
  List<Map<String, dynamic>> _getFeaturedQuestions() {
    return [
      {
        'id': '6',
        'user': 'سعيد محمود',
        'userImage': null,
        'crop': 'زيتون',
        'problem': 'كيفية مكافحة ذبابة الزيتون بطرق عضوية',
        'area': '5000',
        'date': 'منذ شهر',
        'image': 'assets/images/olive_fly.jpg',
        'status': 'تمت الإجابة',
        'rating': 5.0,
        'answers': 12,
        'views': 1250,
        'featured': true,
      },
      {
        'id': '7',
        'user': 'عمر خالد',
        'userImage': null,
        'crop': 'عنب',
        'problem': 'أفضل طرق تقليم العنب لزيادة الإنتاجية',
        'area': '2500',
        'date': 'منذ 3 أسابيع',
        'image': 'assets/images/grape_pruning.jpg',
        'status': 'تمت الإجابة',
        'rating': 4.8,
        'answers': 8,
        'views': 980,
        'featured': true,
      },
    ];
  }



  /// إعادة تحميل البيانات
  void refresh() {
    loadConsultations();
  }

  @override
  Future<void> close() {
    _tabController.dispose();
    return super.close();
  }
}
