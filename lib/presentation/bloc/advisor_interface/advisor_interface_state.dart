import 'package:equatable/equatable.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';

/// حالة واجهة الاستشاري
/// وفق المعيار #2: Clean Architecture - طبقة العرض
class AdvisorInterfaceState extends Equatable {
  /// الفهرس الحالي للصفحة
  final int currentIndex;
  
  /// معرف المرشد الحالي
  final String currentAdvisorId;
  
  /// هل يتم التحميل حالياً
  final bool isLoading;
  
  /// وقت آخر تحديث للبيانات
  final DateTime? lastDataUpdate;
  
  /// الاستشارات المحفوظة
  final List<ConsultationModel>? cachedConsultations;
  
  /// الإحصائيات المحفوظة
  final Map<String, dynamic>? cachedStats;
  
  /// رسالة الخطأ
  final String? error;
  
  /// رسالة النجاح
  final String? successMessage;

  const AdvisorInterfaceState({
    this.currentIndex = 0,
    this.currentAdvisorId = 'general_advisor',
    this.isLoading = false,
    this.lastDataUpdate,
    this.cachedConsultations,
    this.cachedStats,
    this.error,
    this.successMessage,
  });

  /// إنشاء نسخة محدثة من الحالة
  AdvisorInterfaceState copyWith({
    int? currentIndex,
    String? currentAdvisorId,
    bool? isLoading,
    DateTime? lastDataUpdate,
    List<ConsultationModel>? cachedConsultations,
    Map<String, dynamic>? cachedStats,
    String? error,
    String? successMessage,
  }) {
    return AdvisorInterfaceState(
      currentIndex: currentIndex ?? this.currentIndex,
      currentAdvisorId: currentAdvisorId ?? this.currentAdvisorId,
      isLoading: isLoading ?? this.isLoading,
      lastDataUpdate: lastDataUpdate ?? this.lastDataUpdate,
      cachedConsultations: cachedConsultations ?? this.cachedConsultations,
      cachedStats: cachedStats ?? this.cachedStats,
      error: error,
      successMessage: successMessage,
    );
  }

  /// التحقق من صحة الـ Cache
  bool get isCacheValid {
    if (lastDataUpdate == null) return false;
    return DateTime.now().difference(lastDataUpdate!) < const Duration(minutes: 2);
  }

  /// الحصول على عدد الاستشارات المحفوظة
  int get cachedConsultationsCount {
    return cachedConsultations?.length ?? 0;
  }

  /// التحقق من وجود بيانات محفوظة
  bool get hasCachedData {
    return cachedConsultations != null || cachedStats != null;
  }

  /// الحصول على نص الصفحة الحالية
  String get currentPageName {
    const pageNames = [
      'لوحة التحكم',
      'الاستشارات',
      'المواعيد',
      'مراقبة النبات',
      'التقارير',
    ];
    
    if (currentIndex < pageNames.length) {
      return pageNames[currentIndex];
    }
    return 'غير معروف';
  }

  /// التحقق من وجود خطأ
  bool get hasError => error != null && error!.isNotEmpty;

  /// التحقق من وجود رسالة نجاح
  bool get hasSuccessMessage => successMessage != null && successMessage!.isNotEmpty;

  /// الحصول على وقت آخر تحديث بصيغة نصية
  String get lastUpdateText {
    if (lastDataUpdate == null) return 'لم يتم التحديث بعد';
    
    final difference = DateTime.now().difference(lastDataUpdate!);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  @override
  List<Object?> get props => [
        currentIndex,
        currentAdvisorId,
        isLoading,
        lastDataUpdate,
        cachedConsultations,
        cachedStats,
        error,
        successMessage,
      ];

  @override
  String toString() {
    return 'AdvisorInterfaceState('
        'currentIndex: $currentIndex, '
        'currentAdvisorId: $currentAdvisorId, '
        'isLoading: $isLoading, '
        'lastDataUpdate: $lastDataUpdate, '
        'cachedConsultationsCount: $cachedConsultationsCount, '
        'hasError: $hasError, '
        'hasSuccessMessage: $hasSuccessMessage'
        ')';
  }
}
