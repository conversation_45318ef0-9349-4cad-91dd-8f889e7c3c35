import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'advisor_interface_state.dart';

/// Cubit لإدارة حالة واجهة الاستشاري
/// وفق المعيار #6: عدم استخدام StatefulWidget - استخدام Cubit فقط
/// وفق المعيار #2: Clean Architecture - طبقة العرض
class AdvisorInterfaceCubit extends Cubit<AdvisorInterfaceState> {
  Timer? _refreshTimer;
  
  AdvisorInterfaceCubit() : super(const AdvisorInterfaceState());

  /// تهيئة الواجهة
  void initialize() {
    emit(state.copyWith(
      currentAdvisorId: 'general_advisor',
      isLoading: true,
    ));
    
    _loadAdvisorData();
    _setupAutoRefresh();
  }

  /// تحميل بيانات المرشد
  Future<void> _loadAdvisorData() async {
    try {
      debugPrint('🔍 تحميل بيانات المرشد: ${state.currentAdvisorId}');
      
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));
      
      emit(state.copyWith(
        isLoading: false,
        lastDataUpdate: DateTime.now(),
      ));
      
      debugPrint('✅ تم تحميل بيانات المرشد بنجاح');
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'خطأ في تحميل بيانات المرشد: $e',
      ));
      debugPrint('❌ خطأ في تحميل بيانات المرشد: $e');
    }
  }

  /// إعداد التحديث التلقائي
  void _setupAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!isClosed) {
        refreshData();
      } else {
        timer.cancel();
      }
    });
  }

  /// تحديث البيانات
  Future<void> refreshData({bool forceRefresh = false}) async {
    try {
      // التحقق من صحة الـ Cache
      if (!forceRefresh && _isCacheValid()) {
        debugPrint('📦 استخدام البيانات المحفوظة');
        return;
      }

      debugPrint('🔄 تحديث البيانات...');
      
      // محاكاة تحديث البيانات
      await Future.delayed(const Duration(seconds: 1));
      
      emit(state.copyWith(
        lastDataUpdate: DateTime.now(),
        error: null,
      ));
      
      debugPrint('✅ تم تحديث البيانات بنجاح');
    } catch (e) {
      emit(state.copyWith(
        error: 'خطأ في تحديث البيانات: $e',
      ));
      debugPrint('❌ خطأ في تحديث البيانات: $e');
    }
  }

  /// التحقق من صحة الـ Cache
  bool _isCacheValid() {
    if (state.lastDataUpdate == null) return false;
    return DateTime.now().difference(state.lastDataUpdate!) < 
           const Duration(minutes: 2);
  }

  /// تغيير الصفحة الحالية
  void changeCurrentIndex(int index) {
    emit(state.copyWith(currentIndex: index));
    
    final pageNames = [
      'dashboard',
      'consultations', 
      'appointments',
      'plant_monitoring',
      'reports',
    ];
    
    if (index < pageNames.length) {
      debugPrint('📱 تم الانتقال إلى صفحة: ${pageNames[index]}');
    }
  }

  /// عرض رسالة نجاح
  void showSuccessMessage(String message) {
    emit(state.copyWith(
      successMessage: message,
      error: null,
    ));
    
    // مسح الرسالة بعد 3 ثوان
    Timer(const Duration(seconds: 3), () {
      if (!isClosed) {
        emit(state.copyWith(successMessage: null));
      }
    });
  }

  /// عرض رسالة خطأ
  void showErrorMessage(String message) {
    emit(state.copyWith(
      error: message,
      successMessage: null,
    ));
    
    // مسح الرسالة بعد 5 ثوان
    Timer(const Duration(seconds: 5), () {
      if (!isClosed) {
        emit(state.copyWith(error: null));
      }
    });
  }

  /// تحديث معرف المرشد
  void updateAdvisorId(String advisorId) {
    emit(state.copyWith(
      currentAdvisorId: advisorId,
      isLoading: true,
    ));
    _loadAdvisorData();
  }

  /// تحديث الاستشارات المحفوظة
  void updateCachedConsultations(List<ConsultationModel> consultations) {
    emit(state.copyWith(
      cachedConsultations: consultations,
      lastDataUpdate: DateTime.now(),
    ));
    debugPrint('📦 تم تحديث الاستشارات المحفوظة: ${consultations.length}');
  }

  /// تحديث الإحصائيات المحفوظة
  void updateCachedStats(Map<String, dynamic> stats) {
    emit(state.copyWith(
      cachedStats: stats,
      lastDataUpdate: DateTime.now(),
    ));
    debugPrint('📊 تم تحديث الإحصائيات المحفوظة');
  }

  /// مسح الـ Cache
  void clearCache() {
    emit(state.copyWith(
      cachedConsultations: null,
      cachedStats: null,
      lastDataUpdate: null,
    ));
    debugPrint('🗑️ تم مسح الـ Cache');
  }

  /// إعادة تعيين الحالة
  void reset() {
    _refreshTimer?.cancel();
    emit(const AdvisorInterfaceState());
    debugPrint('🔄 تم إعادة تعيين حالة واجهة الاستشاري');
  }

  /// تفعيل/إلغاء تفعيل التحديث التلقائي
  void toggleAutoRefresh(bool enabled) {
    if (enabled) {
      _setupAutoRefresh();
      debugPrint('✅ تم تفعيل التحديث التلقائي');
    } else {
      _refreshTimer?.cancel();
      debugPrint('❌ تم إلغاء تفعيل التحديث التلقائي');
    }
  }

  /// الحصول على وقت آخر تحديث بصيغة نصية
  String getLastUpdateText() {
    if (state.lastDataUpdate == null) return 'لم يتم التحديث بعد';
    
    final difference = DateTime.now().difference(state.lastDataUpdate!);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  @override
  Future<void> close() {
    _refreshTimer?.cancel();
    return super.close();
  }
}
