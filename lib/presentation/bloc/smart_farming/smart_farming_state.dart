import 'package:equatable/equatable.dart';
import '../../../data/models/smart_farming/index.dart';

/// حالات كيوبت الزراعة الذكية
/// تدير جميع الحالات المختلفة لنظام الزراعة الذكية
/// وفق المعايير الـ18 - فصل الحالات في ملف منفصل
abstract class SmartFarmingState extends Equatable {
  const SmartFarmingState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية للزراعة الذكية
class SmartFarmingInitial extends SmartFarmingState {
  const SmartFarmingInitial();
}

/// حالة تحميل بيانات الزراعة الذكية
class SmartFarmingLoading extends SmartFarmingState {
  const SmartFarmingLoading();
}

/// حالة تحميل البيانات بنجاح
class SmartFarmingLoaded extends SmartFarmingState {
  /// قائمة سجلات المحاصيل
  final List<CropRecordModel> cropRecords;
  
  /// بيانات المستشعرات لكل محصول
  final Map<String, SensorDataModel> sensorData;
  
  /// إحصائيات المحاصيل
  final Map<String, dynamic>? statistics;
  
  /// التبويب النشط حالياً
  final int activeTabIndex;

  const SmartFarmingLoaded({
    required this.cropRecords,
    required this.sensorData,
    this.statistics,
    this.activeTabIndex = 0,
  });

  /// إنشاء نسخة محدثة من الحالة
  SmartFarmingLoaded copyWith({
    List<CropRecordModel>? cropRecords,
    Map<String, SensorDataModel>? sensorData,
    Map<String, dynamic>? statistics,
    int? activeTabIndex,
  }) {
    return SmartFarmingLoaded(
      cropRecords: cropRecords ?? this.cropRecords,
      sensorData: sensorData ?? this.sensorData,
      statistics: statistics ?? this.statistics,
      activeTabIndex: activeTabIndex ?? this.activeTabIndex,
    );
  }

  @override
  List<Object?> get props => [
        cropRecords,
        sensorData,
        statistics,
        activeTabIndex,
      ];
}

/// حالة خطأ في الزراعة الذكية
class SmartFarmingError extends SmartFarmingState {
  /// رسالة الخطأ
  final String message;
  
  /// تفاصيل الخطأ (اختيارية)
  final String? details;
  
  /// نوع الخطأ
  final SmartFarmingErrorType type;

  const SmartFarmingError({
    required this.message,
    this.details,
    this.type = SmartFarmingErrorType.general,
  });

  @override
  List<Object?> get props => [message, details, type];
}

/// حالة إضافة محصول جديد
class SmartFarmingAddingCrop extends SmartFarmingState {
  const SmartFarmingAddingCrop();
}

/// حالة تحديث محصول
class SmartFarmingUpdatingCrop extends SmartFarmingState {
  /// معرف المحصول قيد التحديث
  final String cropId;

  const SmartFarmingUpdatingCrop({required this.cropId});

  @override
  List<Object?> get props => [cropId];
}

/// حالة حذف محصول
class SmartFarmingDeletingCrop extends SmartFarmingState {
  /// معرف المحصول قيد الحذف
  final String cropId;

  const SmartFarmingDeletingCrop({required this.cropId});

  @override
  List<Object?> get props => [cropId];
}

/// حالة تحديث بيانات المستشعرات
class SmartFarmingSensorUpdating extends SmartFarmingState {
  /// معرف المحصول قيد تحديث مستشعراته
  final String cropId;
  
  /// البيانات الحالية
  final List<CropRecordModel> cropRecords;
  final Map<String, SensorDataModel> sensorData;

  const SmartFarmingSensorUpdating({
    required this.cropId,
    required this.cropRecords,
    required this.sensorData,
  });

  @override
  List<Object?> get props => [cropId, cropRecords, sensorData];
}

/// حالة نجاح العملية مع رسالة
class SmartFarmingSuccess extends SmartFarmingState {
  /// رسالة النجاح
  final String message;
  
  /// البيانات المحدثة
  final List<CropRecordModel> cropRecords;
  final Map<String, SensorDataModel> sensorData;

  const SmartFarmingSuccess({
    required this.message,
    required this.cropRecords,
    required this.sensorData,
  });

  @override
  List<Object?> get props => [message, cropRecords, sensorData];
}

/// أنواع أخطاء الزراعة الذكية
enum SmartFarmingErrorType {
  /// خطأ عام
  general,
  
  /// خطأ في الشبكة
  network,
  
  /// خطأ في قاعدة البيانات
  database,
  
  /// خطأ في المستشعرات
  sensor,
  
  /// خطأ في التحقق من البيانات
  validation,
  
  /// خطأ في الصلاحيات
  permission,
}

/// امتداد لأنواع الأخطاء لإضافة الأسماء العربية
extension SmartFarmingErrorTypeExtension on SmartFarmingErrorType {
  /// الاسم المعروض باللغة العربية
  String get displayName {
    switch (this) {
      case SmartFarmingErrorType.general:
        return 'خطأ عام';
      case SmartFarmingErrorType.network:
        return 'خطأ في الاتصال';
      case SmartFarmingErrorType.database:
        return 'خطأ في قاعدة البيانات';
      case SmartFarmingErrorType.sensor:
        return 'خطأ في المستشعرات';
      case SmartFarmingErrorType.validation:
        return 'خطأ في البيانات المدخلة';
      case SmartFarmingErrorType.permission:
        return 'خطأ في الصلاحيات';
    }
  }

  /// أيقونة الخطأ
  String get icon {
    switch (this) {
      case SmartFarmingErrorType.general:
        return '⚠️';
      case SmartFarmingErrorType.network:
        return '🌐';
      case SmartFarmingErrorType.database:
        return '💾';
      case SmartFarmingErrorType.sensor:
        return '📡';
      case SmartFarmingErrorType.validation:
        return '✏️';
      case SmartFarmingErrorType.permission:
        return '🔒';
    }
  }
}
