import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/smart_farming/index.dart';
import '../../../core/shared/index.dart';
import 'smart_farming_state.dart';

/// كيوبت إدارة حالة الزراعة الذكية
/// يدير جميع العمليات المتعلقة بالزراعة الذكية
/// وفق المعايير الـ18 - استخدام Cubit فقط بدون StatefulWidget
class SmartFarmingCubit extends Cubit<SmartFarmingState> {
  SmartFarmingCubit() : super(const SmartFarmingInitial());

  /// الخدمات المطلوبة
  final UnifiedAnalyticsService _analytics = UnifiedAnalyticsService();
  final UnifiedNotificationService _notifications = UnifiedNotificationService();

  /// قائمة سجلات المحاصيل
  List<CropRecordModel> _cropRecords = [];

  /// بيانات المستشعرات الحالية
  Map<String, SensorDataModel> _sensorData = {};

  /// تحميل سجلات المحاصيل
  Future<void> loadCropRecords() async {
    try {
      emit(const SmartFarmingLoading());

      // محاكاة تحميل البيانات من قاعدة البيانات
      await Future.delayed(const Duration(seconds: 1));

      // بيانات تجريبية للمحاصيل اليمنية
      _cropRecords = [
        CropRecordModel(
          id: 'crop_001',
          name: 'قمح شتوي - الحقل الأول',
          type: 'قمح',
          area: 5.2,
          plantingDate: '2024-10-15',
          expectedHarvestDate: '2025-04-15',
          status: CropStatus.growing,
          growthStage: GrowthStage.vegetative,
          growthProgress: 45.0,
          location: 'منطقة الحقول الشمالية',
          province: 'صنعاء',
          notes: 'نمو جيد، يحتاج مراقبة الري',
          lastUpdated: DateTime.now().toIso8601String(),
          farmerId: 'farmer_001',
        ),
        CropRecordModel(
          id: 'crop_002',
          name: 'طماطم البيت المحمي',
          type: 'طماطم',
          area: 1.5,
          plantingDate: '2024-02-20',
          expectedHarvestDate: '2024-06-20',
          status: CropStatus.flowering,
          growthStage: GrowthStage.flowering,
          growthProgress: 70.0,
          location: 'البيت المحمي الأول',
          province: 'تعز',
          notes: 'إزهار ممتاز، متوقع إنتاج عالي',
          lastUpdated: DateTime.now().toIso8601String(),
          farmerId: 'farmer_001',
        ),
        CropRecordModel(
          id: 'crop_003',
          name: 'ذرة رفيعة صيفية',
          type: 'ذرة رفيعة',
          area: 3.0,
          plantingDate: '2024-06-01',
          expectedHarvestDate: '2024-10-01',
          status: CropStatus.growing,
          growthStage: GrowthStage.fruiting,
          growthProgress: 85.0,
          location: 'الحقل الجنوبي',
          province: 'إب',
          notes: 'قريب من النضج، مراقبة الآفات',
          lastUpdated: DateTime.now().toIso8601String(),
          farmerId: 'farmer_001',
        ),
      ];

      // تحميل بيانات المستشعرات
      await _loadSensorData();

      emit(SmartFarmingLoaded(
        cropRecords: _cropRecords,
        sensorData: _sensorData,
      ));

      // تسجيل في التحليلات
      await _analytics.trackEvent('smart_farming_loaded', parameters: {
        'crops_count': _cropRecords.length,
        'sensors_count': _sensorData.length,
      });

    } catch (e) {
      emit(SmartFarmingError(
        message: 'خطأ في تحميل بيانات الزراعة الذكية: $e',
      ));
    }
  }

  /// إضافة سجل محصول جديد
  Future<void> addCropRecord(CropRecordModel cropRecord) async {
    try {
      emit(const SmartFarmingLoading());

      // محاكاة حفظ في قاعدة البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      _cropRecords.add(cropRecord);

      emit(SmartFarmingLoaded(
        cropRecords: _cropRecords,
        sensorData: _sensorData,
      ));

      // إرسال إشعار نجاح
      await _notifications.sendToFarmer(
        farmerId: cropRecord.farmerId,
        title: 'تم إضافة محصول جديد',
        body: 'تم إضافة ${cropRecord.name} بنجاح',
        data: {'crop_id': cropRecord.id},
      );

      // تسجيل في التحليلات
      await _analytics.trackEvent('crop_record_added', parameters: {
        'crop_type': cropRecord.type,
        'area': cropRecord.area,
        'province': cropRecord.province,
      });

    } catch (e) {
      emit(SmartFarmingError(
        message: 'خطأ في إضافة المحصول: $e',
      ));
    }
  }

  /// تحديث سجل محصول
  Future<void> updateCropRecord(CropRecordModel updatedCrop) async {
    try {
      emit(const SmartFarmingLoading());

      // محاكاة تحديث في قاعدة البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _cropRecords.indexWhere((crop) => crop.id == updatedCrop.id);
      if (index != -1) {
        _cropRecords[index] = updatedCrop;

        emit(SmartFarmingLoaded(
          cropRecords: _cropRecords,
          sensorData: _sensorData,
        ));

        // تسجيل في التحليلات
        await _analytics.trackEvent('crop_record_updated', parameters: {
          'crop_id': updatedCrop.id,
          'growth_progress': updatedCrop.growthProgress,
        });
      }

    } catch (e) {
      emit(SmartFarmingError(
        message: 'خطأ في تحديث المحصول: $e',
      ));
    }
  }

  /// حذف سجل محصول
  Future<void> deleteCropRecord(String cropId) async {
    try {
      emit(const SmartFarmingLoading());

      // محاكاة حذف من قاعدة البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      _cropRecords.removeWhere((crop) => crop.id == cropId);
      _sensorData.remove(cropId);

      emit(SmartFarmingLoaded(
        cropRecords: _cropRecords,
        sensorData: _sensorData,
      ));

      // تسجيل في التحليلات
      await _analytics.trackEvent('crop_record_deleted', parameters: {
        'crop_id': cropId,
      });

    } catch (e) {
      emit(SmartFarmingError(
        message: 'خطأ في حذف المحصول: $e',
      ));
    }
  }

  /// تحديث بيانات المستشعرات
  Future<void> updateSensorData(String cropId) async {
    try {
      // محاكاة قراءة بيانات جديدة من المستشعرات
      await Future.delayed(const Duration(milliseconds: 300));

      final newSensorData = _generateMockSensorData(cropId);
      _sensorData[cropId] = newSensorData;

      // إصدار حالة محدثة فقط إذا كانت الحالة الحالية محملة
      if (state is SmartFarmingLoaded) {
        emit(SmartFarmingLoaded(
          cropRecords: _cropRecords,
          sensorData: _sensorData,
        ));
      }

      // فحص إذا كانت هناك تحذيرات
      await _checkForAlerts(newSensorData);

    } catch (e) {
      // لا نصدر خطأ هنا لأن هذا تحديث دوري
      print('خطأ في تحديث بيانات المستشعرات: $e');
    }
  }

  /// تحميل بيانات المستشعرات
  Future<void> _loadSensorData() async {
    for (final crop in _cropRecords) {
      _sensorData[crop.id] = _generateMockSensorData(crop.id);
    }
  }

  /// توليد بيانات تجريبية للمستشعرات
  SensorDataModel _generateMockSensorData(String cropId) {
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    
    return SensorDataModel(
      id: 'sensor_${cropId}_${DateTime.now().millisecondsSinceEpoch}',
      cropId: cropId,
      soilMoisture: 45.0 + (random % 30), // 45-75%
      temperature: 22.0 + (random % 12), // 22-34°C
      humidity: 50.0 + (random % 30), // 50-80%
      lightIntensity: 30000.0 + (random % 20000), // 30000-50000 لوكس
      soilPH: 6.0 + (random % 20) / 10, // 6.0-8.0
      nitrogenLevel: NutrientLevel.values[random % NutrientLevel.values.length],
      phosphorusLevel: NutrientLevel.values[random % NutrientLevel.values.length],
      potassiumLevel: NutrientLevel.values[random % NutrientLevel.values.length],
      windSpeed: 5.0 + (random % 15), // 5-20 كم/ساعة
      rainfall: (random % 10).toDouble(), // 0-10 مم
      timestamp: DateTime.now().toIso8601String(),
      status: SensorStatus.active,
    );
  }

  /// فحص التحذيرات بناءً على بيانات المستشعرات
  Future<void> _checkForAlerts(SensorDataModel sensorData) async {
    final alerts = <String>[];

    // فحص رطوبة التربة
    if (sensorData.soilMoisture < 30) {
      alerts.add('رطوبة التربة منخفضة - يحتاج ري');
    } else if (sensorData.soilMoisture > 80) {
      alerts.add('رطوبة التربة عالية - قلل الري');
    }

    // فحص درجة الحرارة
    if (sensorData.temperature > 35) {
      alerts.add('درجة الحرارة عالية جداً - قد تضر بالمحصول');
    } else if (sensorData.temperature < 15) {
      alerts.add('درجة الحرارة منخفضة - قد تؤثر على النمو');
    }

    // فحص مستوى المغذيات
    if (sensorData.nitrogenLevel == NutrientLevel.low) {
      alerts.add('مستوى النيتروجين منخفض - أضف سماد نيتروجيني');
    }

    // إرسال التحذيرات إن وجدت
    if (alerts.isNotEmpty) {
      final crop = _cropRecords.firstWhere((c) => c.id == sensorData.cropId);
      
      await _notifications.sendUrgentNotification(
        title: 'تحذير من المستشعرات',
        body: alerts.first,
        targetType: 'farmer',
        data: {
          'crop_id': sensorData.cropId,
          'crop_name': crop.name,
          'alerts': alerts,
        },
      );
    }
  }

  /// الحصول على إحصائيات المحاصيل
  Map<String, dynamic> getCropStatistics() {
    if (_cropRecords.isEmpty) {
      return {
        'total_crops': 0,
        'total_area': 0.0,
        'average_progress': 0.0,
        'status_distribution': <String, int>{},
      };
    }

    final totalArea = _cropRecords.fold<double>(0, (sum, crop) => sum + crop.area);
    final averageProgress = _cropRecords.fold<double>(0, (sum, crop) => sum + crop.growthProgress) / _cropRecords.length;
    
    final statusDistribution = <String, int>{};
    for (final crop in _cropRecords) {
      statusDistribution[crop.status.displayName] = 
          (statusDistribution[crop.status.displayName] ?? 0) + 1;
    }

    return {
      'total_crops': _cropRecords.length,
      'total_area': totalArea,
      'average_progress': averageProgress,
      'status_distribution': statusDistribution,
    };
  }
}
