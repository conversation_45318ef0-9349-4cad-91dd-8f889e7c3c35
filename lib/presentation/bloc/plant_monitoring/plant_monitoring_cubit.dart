import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:io';

import 'package:agriculture/presentation/bloc/plant_monitoring/plant_monitoring_state.dart';
import 'package:agriculture/domain/entities/plant_monitoring_request.dart';
import 'package:agriculture/core/constants/plant_monitoring_constants.dart';
import 'package:agriculture/data/services/plant_monitoring_service.dart';
import 'package:agriculture/data/services/ai_analysis_service.dart';

/// Cubit لإدارة حالة مراقبة النبات
/// 
/// يدير جميع العمليات المتعلقة بطلب مراقبة النبات
/// يتبع مبادئ Clean Architecture ولا يستخدم StatefulWidget
class PlantMonitoringCubit extends Cubit<PlantMonitoringState> {
  
  /// خدمة مراقبة النبات
  final PlantMonitoringService _plantMonitoringService;
  
  /// خدمة الموقع
  final LocationService _locationService;
  
  /// خدمة تحليل الذكاء الاصطناعي
  final AIAnalysisService _aiAnalysisService;
  
  /// أداة اختيار الصور
  final ImagePicker _imagePicker = ImagePicker();

  /// بيانات النموذج الحالية
  String _farmerName = '';
  String _phone = '';
  String _farmLocation = '';
  String _farmSize = '';
  String _selectedCropType = PlantMonitoringConstants.cropTypes.first;
  String _selectedMonitoringType = PlantMonitoringConstants.monitoringTypes.first;
  String _selectedUrgency = PlantMonitoringConstants.urgencyLevels[2]; // عادية
  String _problemDescription = '';
  String _notes = '';
  List<File> _selectedImages = [];
  GpsLocation? _currentLocation;
  DateTime? _scheduledDate;
  TimeOfDay? _scheduledTime;
  double _estimatedCost = PlantMonitoringConstants.baseCost;

  /// منشئ Cubit مراقبة النبات
  PlantMonitoringCubit({
    required PlantMonitoringService plantMonitoringService,
    required LocationService locationService,
    required AIAnalysisService aiAnalysisService,
  }) : _plantMonitoringService = plantMonitoringService,
       _locationService = locationService,
       _aiAnalysisService = aiAnalysisService,
       super(const PlantMonitoringInitial()) {
    _initializeForm();
  }

  /// تهيئة النموذج
  void _initializeForm() {
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث اسم المزارع
  void updateFarmerName(String name) {
    _farmerName = name;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث رقم الهاتف
  void updatePhone(String phone) {
    _phone = phone;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث موقع المزرعة
  void updateFarmLocation(String location) {
    _farmLocation = location;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث مساحة المزرعة
  void updateFarmSize(String size) {
    _farmSize = size;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث نوع المحصول
  void updateCropType(String cropType) {
    _selectedCropType = cropType;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث نوع المراقبة
  void updateMonitoringType(String monitoringType) {
    _selectedMonitoringType = monitoringType;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث مستوى الأولوية
  void updateUrgency(String urgency) {
    _selectedUrgency = urgency;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث وصف المشكلة
  void updateProblemDescription(String description) {
    _problemDescription = description;
    _calculateEstimatedCost();
    _emitFormState();
  }

  /// تحديث الملاحظات
  void updateNotes(String notes) {
    _notes = notes;
    _emitFormState();
  }

  /// تحديث التاريخ المجدول
  void updateScheduledDate(DateTime? date) {
    _scheduledDate = date;
    _emitFormState();
  }

  /// تحديث الوقت المجدول
  void updateScheduledTime(TimeOfDay? time) {
    _scheduledTime = time;
    _emitFormState();
  }

  /// اختيار صور النبات من الكاميرا
  Future<void> pickImageFromCamera() async {
    try {
      if (_selectedImages.length >= PlantMonitoringConstants.maxImagesCount) {
        emit(PlantMonitoringError(
          message: 'يمكنك إرفاق ${PlantMonitoringConstants.maxImagesCount} صور كحد أقصى',
          errorType: 'image_limit',
        ));
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: PlantMonitoringConstants.maxImageWidth,
        maxHeight: PlantMonitoringConstants.maxImageHeight,
        imageQuality: PlantMonitoringConstants.imageQuality,
      );

      if (image != null) {
        _selectedImages.add(File(image.path));
        _calculateEstimatedCost();
        _emitFormState();
      }
    } catch (e) {
      emit(PlantMonitoringError(
        message: 'فشل في التقاط الصورة',
        errorType: 'camera_error',
        errorDetails: e.toString(),
      ));
    }
  }

  /// اختيار صور النبات من المعرض
  Future<void> pickImagesFromGallery() async {
    try {
      final int remainingSlots = PlantMonitoringConstants.maxImagesCount - _selectedImages.length;
      
      if (remainingSlots <= 0) {
        emit(PlantMonitoringError(
          message: 'يمكنك إرفاق ${PlantMonitoringConstants.maxImagesCount} صور كحد أقصى',
          errorType: 'image_limit',
        ));
        return;
      }

      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: PlantMonitoringConstants.maxImageWidth,
        maxHeight: PlantMonitoringConstants.maxImageHeight,
        imageQuality: PlantMonitoringConstants.imageQuality,
      );

      if (images.isNotEmpty) {
        final List<XFile> imagesToAdd = images.take(remainingSlots).toList();
        _selectedImages.addAll(imagesToAdd.map((image) => File(image.path)));
        _calculateEstimatedCost();
        _emitFormState();
      }
    } catch (e) {
      emit(PlantMonitoringError(
        message: 'فشل في اختيار الصور',
        errorType: 'gallery_error',
        errorDetails: e.toString(),
      ));
    }
  }

  /// حذف صورة من القائمة
  void removeImage(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      _selectedImages.removeAt(index);
      _calculateEstimatedCost();
      _emitFormState();
    }
  }

  /// تحديد الموقع الجغرافي الحالي
  Future<void> getCurrentLocation() async {
    try {
      emit(const PlantMonitoringLoading(message: 'جاري تحديد الموقع...'));
      
      final Position? position = await _locationService.getCurrentLocation();
      
      if (position != null) {
        _currentLocation = GpsLocation(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          timestamp: DateTime.now(),
        );
        
        emit(PlantMonitoringLocationUpdated(location: _currentLocation!));
        _calculateEstimatedCost();
        _emitFormState();
      } else {
        emit(const PlantMonitoringLocationError(
          message: 'فشل في تحديد الموقع. تأكد من تفعيل GPS وإعطاء الإذن',
        ));
      }
    } catch (e) {
      emit(PlantMonitoringLocationError(
        message: 'خطأ في تحديد الموقع: ${e.toString()}',
      ));
    }
  }

  /// تحليل الصور بالذكاء الاصطناعي
  Future<void> analyzeImagesWithAI() async {
    if (_selectedImages.isEmpty) {
      emit(const PlantMonitoringError(
        message: 'يرجى إرفاق صور للتحليل',
        errorType: 'no_images',
      ));
      return;
    }

    try {
      emit(const PlantMonitoringAIAnalyzing());
      
      // رفع الصور أولاً
      final List<String> imageUrls = await _plantMonitoringService.uploadImages(
        _selectedImages,
        'ai_analysis',
      );
      
      // تحليل الصور
      final Map<String, dynamic> analysisResult = await _aiAnalysisService.analyzeImages(
        imageUrls,
        _selectedCropType,
      );
      
      emit(PlantMonitoringAIAnalysisSuccess(analysisResult: analysisResult));
      
    } catch (e) {
      emit(PlantMonitoringAIAnalysisError(
        message: 'فشل في تحليل الصور: ${e.toString()}',
      ));
    }
  }

  /// إرسال طلب مراقبة النبات
  Future<void> submitRequest() async {
    try {
      // التحقق من صحة البيانات
      final Map<String, String> validationErrors = _validateForm();
      if (validationErrors.isNotEmpty) {
        emit(PlantMonitoringValidationError(validationErrors: validationErrors));
        return;
      }

      emit(const PlantMonitoringSubmitting());

      // رفع الصور
      List<String> imageUrls = [];
      if (_selectedImages.isNotEmpty) {
        emit(const PlantMonitoringImagesUploading(uploadedCount: 0, totalCount: 0));
        
        for (int i = 0; i < _selectedImages.length; i++) {
          emit(PlantMonitoringImagesUploading(
            uploadedCount: i,
            totalCount: _selectedImages.length,
          ));
        }
        
        imageUrls = await _plantMonitoringService.uploadImages(
          _selectedImages,
          PlantMonitoringConstants.storageFolder,
        );
        
        emit(PlantMonitoringImagesUploading(
          uploadedCount: _selectedImages.length,
          totalCount: _selectedImages.length,
        ));
      }

      // إنشاء طلب المراقبة
      final PlantMonitoringRequest request = PlantMonitoringRequest(
        id: '', // سيتم تعيينه من الخدمة
        userId: '', // سيتم تعيينه من الخدمة
        farmerName: _farmerName,
        phone: _phone,
        farmLocation: _farmLocation,
        farmSize: double.tryParse(_farmSize) ?? 0.0,
        cropType: _selectedCropType,
        monitoringType: _selectedMonitoringType,
        urgency: _selectedUrgency,
        problemDescription: _problemDescription.isNotEmpty ? _problemDescription : null,
        notes: _notes.isNotEmpty ? _notes : null,
        plantImageUrls: imageUrls,
        imageCount: imageUrls.length,
        estimatedCost: _estimatedCost,
        gpsLocation: _currentLocation,
        scheduledDate: _scheduledDate,
        scheduledTime: _scheduledTime != null 
            ? '${_scheduledTime!.hour}:${_scheduledTime!.minute}' 
            : null,
        status: 'pending',
        aiAnalysisStatus: 'pending',
        priority: _getPriorityFromUrgency(_selectedUrgency),
        estimatedDuration: PlantMonitoringConstants.estimatedDurations[_selectedMonitoringType] ?? 120,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إرسال الطلب
      final PlantMonitoringRequest savedRequest = await _plantMonitoringService.submitRequest(request);

      emit(PlantMonitoringSubmitSuccess(
        request: savedRequest,
        message: PlantMonitoringConstants.successMessage,
      ));

      // إعادة تعيين النموذج
      _resetForm();

    } catch (e) {
      emit(PlantMonitoringError(
        message: 'فشل في إرسال الطلب: ${e.toString()}',
        errorType: 'submit_error',
        errorDetails: e.toString(),
      ));
    }
  }

  /// حساب التكلفة المقدرة
  void _calculateEstimatedCost() {
    double cost = PlantMonitoringConstants.baseCost;

    // إضافة تكلفة نوع المراقبة
    cost += PlantMonitoringConstants.monitoringTypeCosts[_selectedMonitoringType] ?? 0.0;

    // إضافة تكلفة المساحة
    final double farmSizeValue = double.tryParse(_farmSize) ?? 0.0;
    if (farmSizeValue > 0) {
      cost += (farmSizeValue / 1000) * PlantMonitoringConstants.costPerThousandSqm;
    }

    // إضافة تكلفة الأولوية
    switch (_selectedUrgency) {
      case 'عاجلة جداً':
        cost *= PlantMonitoringConstants.urgentMultiplier;
        break;
      case 'عاجلة':
        cost *= PlantMonitoringConstants.highPriorityMultiplier;
        break;
      case 'عادية':
        cost *= PlantMonitoringConstants.normalMultiplier;
        break;
      case 'غير عاجلة':
        cost *= PlantMonitoringConstants.lowPriorityMultiplier;
        break;
    }

    // إضافة تكلفة تحليل الصور
    cost += _selectedImages.length * PlantMonitoringConstants.aiAnalysisCostPerImage;

    // إضافة تكلفة النقل
    if (_currentLocation != null) {
      cost += PlantMonitoringConstants.baseTransportCost;
    }

    // خصم للعملاء الجدد
    if (_farmerName.isNotEmpty && _phone.isNotEmpty) {
      cost *= 0.9; // خصم 10%
    }

    _estimatedCost = cost;
  }

  /// إرسال حالة النموذج المحدثة
  void _emitFormState() {
    emit(PlantMonitoringFormUpdated(
      farmerName: _farmerName,
      phone: _phone,
      farmLocation: _farmLocation,
      farmSize: _farmSize,
      selectedCropType: _selectedCropType,
      selectedMonitoringType: _selectedMonitoringType,
      selectedUrgency: _selectedUrgency,
      problemDescription: _problemDescription,
      notes: _notes,
      selectedImages: List.from(_selectedImages),
      currentLocation: _currentLocation,
      scheduledDate: _scheduledDate,
      scheduledTime: _scheduledTime != null 
          ? '${_scheduledTime!.hour}:${_scheduledTime!.minute}' 
          : null,
      estimatedCost: _estimatedCost,
      isFormValid: _isFormValid(),
    ));
  }

  /// التحقق من صحة النموذج
  bool _isFormValid() {
    return _farmerName.isNotEmpty &&
           _phone.isNotEmpty &&
           _farmLocation.isNotEmpty &&
           _farmSize.isNotEmpty &&
           double.tryParse(_farmSize) != null &&
           double.tryParse(_farmSize)! > 0;
  }

  /// التحقق من صحة البيانات مع إرجاع الأخطاء
  Map<String, String> _validateForm() {
    final Map<String, String> errors = {};

    if (_farmerName.isEmpty) {
      errors['farmerName'] = PlantMonitoringConstants.nameRequired;
    }

    if (_phone.isEmpty) {
      errors['phone'] = PlantMonitoringConstants.phoneRequired;
    } else if (!RegExp(r'^\+967[0-9]{9}$').hasMatch(_phone)) {
      errors['phone'] = PlantMonitoringConstants.invalidPhoneFormat;
    }

    if (_farmLocation.isEmpty) {
      errors['farmLocation'] = PlantMonitoringConstants.locationRequired;
    }

    if (_farmSize.isEmpty) {
      errors['farmSize'] = PlantMonitoringConstants.farmSizeRequired;
    } else {
      final double? size = double.tryParse(_farmSize);
      if (size == null || size <= 0) {
        errors['farmSize'] = PlantMonitoringConstants.invalidFarmSize;
      }
    }

    return errors;
  }

  /// تحويل مستوى الأولوية إلى أولوية المهمة
  String _getPriorityFromUrgency(String urgency) {
    switch (urgency) {
      case 'عاجلة جداً':
        return 'urgent';
      case 'عاجلة':
        return 'high';
      case 'عادية':
        return 'normal';
      case 'غير عاجلة':
        return 'low';
      default:
        return 'normal';
    }
  }

  /// إعادة تعيين النموذج
  void _resetForm() {
    _farmerName = '';
    _phone = '';
    _farmLocation = '';
    _farmSize = '';
    _selectedCropType = PlantMonitoringConstants.cropTypes.first;
    _selectedMonitoringType = PlantMonitoringConstants.monitoringTypes.first;
    _selectedUrgency = PlantMonitoringConstants.urgencyLevels[2];
    _problemDescription = '';
    _notes = '';
    _selectedImages.clear();
    _currentLocation = null;
    _scheduledDate = null;
    _scheduledTime = null;
    _estimatedCost = PlantMonitoringConstants.baseCost;
    
    _emitFormState();
  }
}
