import 'package:equatable/equatable.dart';
import 'package:agriculture/domain/entities/plant_monitoring_request.dart';
import 'dart:io';

/// حالات مراقبة النبات
/// 
/// تمثل جميع الحالات الممكنة لواجهة مراقبة النبات
abstract class PlantMonitoringState extends Equatable {
  const PlantMonitoringState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class PlantMonitoringInitial extends PlantMonitoringState {
  const PlantMonitoringInitial();
}

/// حالة التحميل
class PlantMonitoringLoading extends PlantMonitoringState {
  /// رسالة التحميل
  final String message;

  const PlantMonitoringLoading({
    this.message = 'جاري المعالجة...',
  });

  @override
  List<Object?> get props => [message];
}

/// حالة النموذج المحدث
class PlantMonitoringFormUpdated extends PlantMonitoringState {
  /// اسم المزارع
  final String farmerName;
  
  /// رقم الهاتف
  final String phone;
  
  /// موقع المزرعة
  final String farmLocation;
  
  /// مساحة المزرعة
  final String farmSize;
  
  /// نوع المحصول المختار
  final String selectedCropType;
  
  /// نوع المراقبة المختار
  final String selectedMonitoringType;
  
  /// مستوى الأولوية المختار
  final String selectedUrgency;
  
  /// وصف المشكلة
  final String problemDescription;
  
  /// ملاحظات إضافية
  final String notes;
  
  /// الصور المختارة
  final List<File> selectedImages;
  
  /// الموقع الجغرافي الحالي
  final GpsLocation? currentLocation;
  
  /// التاريخ المجدول
  final DateTime? scheduledDate;
  
  /// الوقت المجدول
  final String? scheduledTime;
  
  /// التكلفة المقدرة
  final double estimatedCost;
  
  /// حالة صحة النموذج
  final bool isFormValid;

  const PlantMonitoringFormUpdated({
    required this.farmerName,
    required this.phone,
    required this.farmLocation,
    required this.farmSize,
    required this.selectedCropType,
    required this.selectedMonitoringType,
    required this.selectedUrgency,
    required this.problemDescription,
    required this.notes,
    required this.selectedImages,
    this.currentLocation,
    this.scheduledDate,
    this.scheduledTime,
    required this.estimatedCost,
    required this.isFormValid,
  });

  /// إنشاء نسخة جديدة مع تحديث بعض الحقول
  PlantMonitoringFormUpdated copyWith({
    String? farmerName,
    String? phone,
    String? farmLocation,
    String? farmSize,
    String? selectedCropType,
    String? selectedMonitoringType,
    String? selectedUrgency,
    String? problemDescription,
    String? notes,
    List<File>? selectedImages,
    GpsLocation? currentLocation,
    DateTime? scheduledDate,
    String? scheduledTime,
    double? estimatedCost,
    bool? isFormValid,
  }) {
    return PlantMonitoringFormUpdated(
      farmerName: farmerName ?? this.farmerName,
      phone: phone ?? this.phone,
      farmLocation: farmLocation ?? this.farmLocation,
      farmSize: farmSize ?? this.farmSize,
      selectedCropType: selectedCropType ?? this.selectedCropType,
      selectedMonitoringType: selectedMonitoringType ?? this.selectedMonitoringType,
      selectedUrgency: selectedUrgency ?? this.selectedUrgency,
      problemDescription: problemDescription ?? this.problemDescription,
      notes: notes ?? this.notes,
      selectedImages: selectedImages ?? this.selectedImages,
      currentLocation: currentLocation ?? this.currentLocation,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  @override
  List<Object?> get props => [
    farmerName,
    phone,
    farmLocation,
    farmSize,
    selectedCropType,
    selectedMonitoringType,
    selectedUrgency,
    problemDescription,
    notes,
    selectedImages,
    currentLocation,
    scheduledDate,
    scheduledTime,
    estimatedCost,
    isFormValid,
  ];
}

/// حالة تحديد الموقع
class PlantMonitoringLocationUpdated extends PlantMonitoringState {
  /// الموقع الجغرافي المحدد
  final GpsLocation location;

  const PlantMonitoringLocationUpdated({
    required this.location,
  });

  @override
  List<Object?> get props => [location];
}

/// حالة رفع الصور
class PlantMonitoringImagesUploading extends PlantMonitoringState {
  /// عدد الصور المرفوعة
  final int uploadedCount;
  
  /// العدد الإجمالي للصور
  final int totalCount;

  const PlantMonitoringImagesUploading({
    required this.uploadedCount,
    required this.totalCount,
  });

  /// نسبة التقدم
  double get progress => totalCount > 0 ? uploadedCount / totalCount : 0.0;

  @override
  List<Object?> get props => [uploadedCount, totalCount];
}

/// حالة تحليل الذكاء الاصطناعي
class PlantMonitoringAIAnalyzing extends PlantMonitoringState {
  /// رسالة التحليل
  final String message;

  const PlantMonitoringAIAnalyzing({
    this.message = 'جاري تحليل الصور بالذكاء الاصطناعي...',
  });

  @override
  List<Object?> get props => [message];
}

/// حالة نجح تحليل الذكاء الاصطناعي
class PlantMonitoringAIAnalysisSuccess extends PlantMonitoringState {
  /// نتيجة التحليل
  final Map<String, dynamic> analysisResult;

  const PlantMonitoringAIAnalysisSuccess({
    required this.analysisResult,
  });

  @override
  List<Object?> get props => [analysisResult];
}

/// حالة إرسال الطلب
class PlantMonitoringSubmitting extends PlantMonitoringState {
  /// رسالة الإرسال
  final String message;

  const PlantMonitoringSubmitting({
    this.message = 'جاري إرسال طلب مراقبة النبات...',
  });

  @override
  List<Object?> get props => [message];
}

/// حالة نجح الإرسال
class PlantMonitoringSubmitSuccess extends PlantMonitoringState {
  /// طلب المراقبة المرسل
  final PlantMonitoringRequest request;
  
  /// رسالة النجاح
  final String message;

  const PlantMonitoringSubmitSuccess({
    required this.request,
    this.message = 'تم إرسال طلب مراقبة النبات بنجاح',
  });

  @override
  List<Object?> get props => [request, message];
}

/// حالة الخطأ
class PlantMonitoringError extends PlantMonitoringState {
  /// رسالة الخطأ
  final String message;
  
  /// نوع الخطأ
  final String errorType;
  
  /// تفاصيل الخطأ
  final String? errorDetails;

  const PlantMonitoringError({
    required this.message,
    this.errorType = 'general',
    this.errorDetails,
  });

  @override
  List<Object?> get props => [message, errorType, errorDetails];
}

/// حالة خطأ في التحقق من صحة البيانات
class PlantMonitoringValidationError extends PlantMonitoringState {
  /// أخطاء التحقق
  final Map<String, String> validationErrors;

  const PlantMonitoringValidationError({
    required this.validationErrors,
  });

  @override
  List<Object?> get props => [validationErrors];
}

/// حالة خطأ في الشبكة
class PlantMonitoringNetworkError extends PlantMonitoringState {
  /// رسالة خطأ الشبكة
  final String message;

  const PlantMonitoringNetworkError({
    this.message = 'خطأ في الاتصال بالشبكة',
  });

  @override
  List<Object?> get props => [message];
}

/// حالة خطأ في الموقع
class PlantMonitoringLocationError extends PlantMonitoringState {
  /// رسالة خطأ الموقع
  final String message;

  const PlantMonitoringLocationError({
    this.message = 'فشل في تحديد الموقع',
  });

  @override
  List<Object?> get props => [message];
}

/// حالة خطأ في رفع الصور
class PlantMonitoringImageUploadError extends PlantMonitoringState {
  /// رسالة خطأ رفع الصور
  final String message;
  
  /// الصور التي فشل رفعها
  final List<File> failedImages;

  const PlantMonitoringImageUploadError({
    this.message = 'فشل في رفع بعض الصور',
    required this.failedImages,
  });

  @override
  List<Object?> get props => [message, failedImages];
}

/// حالة خطأ في تحليل الذكاء الاصطناعي
class PlantMonitoringAIAnalysisError extends PlantMonitoringState {
  /// رسالة خطأ التحليل
  final String message;

  const PlantMonitoringAIAnalysisError({
    this.message = 'فشل في تحليل الصور بالذكاء الاصطناعي',
  });

  @override
  List<Object?> get props => [message];
}
