import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_state.dart';
import 'package:agriculture/presentation/bloc/advisor_virtual_interface/advisor_virtual_interface_state.dart';
// import 'package:agriculture/core/services/bridge/farmer_advisor_bridge.dart';
// import 'package:agriculture/core/services/notifications/unified_notification_service.dart';
// import 'package:agriculture/core/services/analytics/unified_analytics_service.dart';
// import 'package:agriculture/core/services/consultation/unified_consultation_service.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';

/// Cubit واجهة الاستشاري الافتراضية
class AdvisorVirtualInterfaceCubit extends Cubit<AdvisorVirtualInterfaceState> {

  /// Cubit المرشد
  late final AdvisorCubit _advisorCubit;

  /// Stream للاستشارات الجديدة
  StreamSubscription? _consultationsStream;

  /// البيانات المحفوظة مؤقتاً
  List<ConsultationModel>? _cachedConsultations;

  AdvisorVirtualInterfaceCubit() : super(const AdvisorVirtualInterfaceInitial());

  /// تهيئة الـ Cubit
  void initialize() async {
    try {
      emit(const AdvisorVirtualInterfaceLoading());

      // تحميل البيانات الأولية
      await loadAllRequests();

      emit(const AdvisorVirtualInterfaceLoaded());
    } catch (e) {
      emit(AdvisorVirtualInterfaceError(
        message: 'خطأ في تهيئة الواجهة: $e',
      ));
    }
  }



  /// تحميل جميع الطلبات مع نظام Cache ذكي
  Future<void> loadAllRequests({bool forceRefresh = false}) async {
    try {
      // إذا كان Cache صالح ولا نريد تحديث قسري، استخدم البيانات المحفوظة
      if (!forceRefresh && state.isCacheValid && _cachedConsultations != null) {
        LoggerService.debug(
          'استخدام البيانات المحفوظة - آخر تحديث: ${state.lastUpdate}',
          tag: 'AdvisorVirtualInterface',
        );
        return;
      }

      LoggerService.debug('بدء تحميل الاستشارات للمرشد: ${state.currentAdvisorId}', tag: 'AdvisorVirtualInterface');

      // إظهار مؤشر التحميل فقط للتحديث الأول
      if (state.lastUpdate == null) {
        emit(const AdvisorVirtualInterfaceLoading());
      }

      // التأكد من أن AdvisorCubit مهيأ
      if (_advisorCubit == null) {
        LoggerService.warning('AdvisorCubit غير مهيأ، سيتم إنشاء بيانات تجريبية', tag: 'AdvisorVirtualInterface');
        // إنشاء بيانات تجريبية للاختبار
        _createSampleData();
        return;
      }

      // تحميل الاستشارات للمرشد الحقيقي - مثل واجهة أسئلة المرشد
      LoggerService.info('🔄 تحميل جميع الاستشارات من AdvisorCubit', tag: 'AdvisorVirtualInterface');
      await _advisorCubit.getAllConsultations();

      // انتظار قليل للتأكد من تحديث الحالة
      await Future.delayed(const Duration(milliseconds: 500));

      // تحديث وقت آخر تحديث
      emit(AdvisorVirtualInterfaceCacheUpdated(
        lastUpdate: DateTime.now(),
        currentIndex: state.currentIndex,
        currentAdvisorId: state.currentAdvisorId,
      ));

      LoggerService.info('✅ تم تحميل الطلبات بنجاح', tag: 'AdvisorVirtualInterface');
    } catch (e) {
      LoggerService.error('❌ فشل في تحميل الاستشارات', error: e, tag: 'AdvisorVirtualInterface');

      emit(AdvisorVirtualInterfaceError(
        message: 'فشل في تحميل الاستشارات: $e',
        currentIndex: state.currentIndex,
        currentAdvisorId: state.currentAdvisorId,
      ));
    }
  }

  /// تغيير التبويب الحالي
  void changeTab(int index) {
    if (state is AdvisorVirtualInterfaceLoaded) {
      final currentState = state as AdvisorVirtualInterfaceLoaded;
      emit(AdvisorVirtualInterfaceTabChanged(
        currentIndex: index,
        currentAdvisorId: currentState.currentAdvisorId,
        lastUpdate: currentState.lastUpdate,
        hasNewConsultations: currentState.hasNewConsultations,
        newConsultationsCount: currentState.newConsultationsCount,
      ));
    }
  }

  /// معالجة الاستشارة الجديدة
  void onNewConsultation(ConsultationModel consultation) {
    if (state is AdvisorVirtualInterfaceLoaded) {
      final currentState = state as AdvisorVirtualInterfaceLoaded;
      emit(AdvisorVirtualInterfaceNewConsultation(
        currentIndex: currentState.currentIndex,
        currentAdvisorId: currentState.currentAdvisorId,
        lastUpdate: currentState.lastUpdate,
        hasNewConsultations: true,
        newConsultationsCount: currentState.newConsultationsCount + 1,
      ));
    }
  }

  /// معالجة تحديث الاستشارات
  void onConsultationsUpdated(List<ConsultationModel> consultations) {
    _cachedConsultations = consultations;
    if (state is AdvisorVirtualInterfaceLoaded) {
      final currentState = state as AdvisorVirtualInterfaceLoaded;
      emit(AdvisorVirtualInterfaceCacheUpdated(
        currentIndex: currentState.currentIndex,
        currentAdvisorId: currentState.currentAdvisorId,
        lastUpdate: DateTime.now(),
        hasNewConsultations: currentState.hasNewConsultations,
        newConsultationsCount: currentState.newConsultationsCount,
      ));
    }
  }

  /// تحديث يدوي للبيانات
  Future<void> refresh() async {
    await loadAllRequests(forceRefresh: true);
  }

  /// إعادة تعيين الإشعارات
  void clearNotifications() {
    if (state is AdvisorVirtualInterfaceLoaded) {
      final currentState = state as AdvisorVirtualInterfaceLoaded;
      emit(currentState.copyWith(
        hasNewConsultations: false,
        newConsultationsCount: 0,
      ));
    }
  }

  /// الحصول على البيانات المحفوظة
  List<ConsultationModel>? get cachedConsultations => _cachedConsultations;

  /// تنظيف الموارد
  @override
  Future<void> close() {
    _consultationsStream?.cancel();
    return super.close();
  }

  /// ربط AdvisorCubit
  void setAdvisorCubit(AdvisorCubit advisorCubit) {
    _advisorCubit = advisorCubit;
    LoggerService.info('✅ تم ربط AdvisorCubit بنجاح', tag: 'AdvisorVirtualInterface');
  }

  /// إنشاء بيانات تجريبية للاختبار
  void _createSampleData() {
    LoggerService.info('📝 إنشاء بيانات تجريبية للاختبار', tag: 'AdvisorVirtualInterface');

    // إنشاء استشارات تجريبية
    _cachedConsultations = [
      ConsultationModel(
        id: '1',
        userId: 'user1',
        userName: 'أحمد محمد',
        advisorId: 'general_advisor',
        advisorName: 'المرشد الزراعي',
        cropType: 'طماطم',
        problemDescription: 'أوراق النبات تصفر وتذبل، ما السبب؟',
        area: '100',
        status: ConsultationStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
      ),
      ConsultationModel(
        id: '2',
        userId: 'user2',
        userName: 'فاطمة علي',
        advisorId: 'general_advisor',
        advisorName: 'المرشد الزراعي',
        cropType: 'خيار',
        problemDescription: 'متى أفضل وقت لزراعة الخيار في المنطقة الجنوبية؟',
        area: '50',
        status: ConsultationStatus.answered,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)).toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
        advisorResponse: 'أفضل وقت لزراعة الخيار في المنطقة الجنوبية هو في بداية الربيع أو أواخر الصيف.',
      ),
      ConsultationModel(
        id: '3',
        userId: 'user3',
        userName: 'محمد حسن',
        advisorId: 'general_advisor',
        advisorName: 'المرشد الزراعي',
        cropType: 'فلفل',
        problemDescription: 'نظام الري لا يعمل بشكل صحيح، النباتات تذبل',
        area: '75',
        status: ConsultationStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
      ),
    ];

    LoggerService.info('✅ تم إنشاء ${_cachedConsultations!.length} استشارة تجريبية', tag: 'AdvisorVirtualInterface');
  }
}
