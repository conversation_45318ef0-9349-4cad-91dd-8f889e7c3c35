/// حالات واجهة الاستشاري الافتراضية
/// 
/// تحتوي على جميع الحالات المطلوبة لإدارة واجهة الاستشاري
/// مع نظام Cache ذكي لضمان استقرار البيانات

/// الحالة الأساسية لواجهة الاستشاري الافتراضية
abstract class AdvisorVirtualInterfaceState {
  /// الفهرس الحالي للتبويب
  final int currentIndex;
  
  /// معرف المرشد الحالي
  final String currentAdvisorId;
  
  /// آخر وقت تحديث
  final DateTime? lastUpdate;
  
  /// مدة صلاحية الـ Cache
  final Duration cacheValidDuration;

  const AdvisorVirtualInterfaceState({
    this.currentIndex = 0,
    this.currentAdvisorId = 'general_advisor',
    this.lastUpdate,
    this.cacheValidDuration = const Duration(minutes: 5),
  });

  /// فحص صحة الـ Cache
  bool get isCacheValid {
    if (lastUpdate == null) return false;
    return DateTime.now().difference(lastUpdate!) < cacheValidDuration;
  }
}

/// حالة التحميل الأولي
class AdvisorVirtualInterfaceInitial extends AdvisorVirtualInterfaceState {
  const AdvisorVirtualInterfaceInitial() : super();
}

/// حالة التحميل
class AdvisorVirtualInterfaceLoading extends AdvisorVirtualInterfaceState {
  const AdvisorVirtualInterfaceLoading({
    super.currentIndex,
    super.currentAdvisorId,
    super.lastUpdate,
    super.cacheValidDuration,
  });
}

/// حالة تحميل البيانات بنجاح
class AdvisorVirtualInterfaceLoaded extends AdvisorVirtualInterfaceState {
  /// هل توجد استشارات جديدة
  final bool hasNewConsultations;
  
  /// عدد الاستشارات الجديدة
  final int newConsultationsCount;

  const AdvisorVirtualInterfaceLoaded({
    super.currentIndex,
    super.currentAdvisorId,
    super.lastUpdate,
    super.cacheValidDuration,
    this.hasNewConsultations = false,
    this.newConsultationsCount = 0,
  });

  /// إنشاء نسخة محدثة من الحالة
  AdvisorVirtualInterfaceLoaded copyWith({
    int? currentIndex,
    String? currentAdvisorId,
    DateTime? lastUpdate,
    Duration? cacheValidDuration,
    bool? hasNewConsultations,
    int? newConsultationsCount,
  }) {
    return AdvisorVirtualInterfaceLoaded(
      currentIndex: currentIndex ?? this.currentIndex,
      currentAdvisorId: currentAdvisorId ?? this.currentAdvisorId,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      cacheValidDuration: cacheValidDuration ?? this.cacheValidDuration,
      hasNewConsultations: hasNewConsultations ?? this.hasNewConsultations,
      newConsultationsCount: newConsultationsCount ?? this.newConsultationsCount,
    );
  }
}

/// حالة الخطأ
class AdvisorVirtualInterfaceError extends AdvisorVirtualInterfaceState {
  /// رسالة الخطأ
  final String message;
  
  /// تفاصيل الخطأ
  final String? details;

  const AdvisorVirtualInterfaceError({
    required this.message,
    this.details,
    super.currentIndex,
    super.currentAdvisorId,
    super.lastUpdate,
    super.cacheValidDuration,
  });
}

/// حالة تحديث التبويب
class AdvisorVirtualInterfaceTabChanged extends AdvisorVirtualInterfaceLoaded {
  const AdvisorVirtualInterfaceTabChanged({
    required super.currentIndex,
    super.currentAdvisorId,
    super.lastUpdate,
    super.cacheValidDuration,
    super.hasNewConsultations,
    super.newConsultationsCount,
  });
}

/// حالة وصول استشارة جديدة
class AdvisorVirtualInterfaceNewConsultation extends AdvisorVirtualInterfaceLoaded {
  const AdvisorVirtualInterfaceNewConsultation({
    super.currentIndex,
    super.currentAdvisorId,
    super.lastUpdate,
    super.cacheValidDuration,
    required super.hasNewConsultations,
    required super.newConsultationsCount,
  });
}

/// حالة تحديث البيانات من الـ Cache
class AdvisorVirtualInterfaceCacheUpdated extends AdvisorVirtualInterfaceLoaded {
  const AdvisorVirtualInterfaceCacheUpdated({
    super.currentIndex,
    super.currentAdvisorId,
    required super.lastUpdate,
    super.cacheValidDuration,
    super.hasNewConsultations,
    super.newConsultationsCount,
  });
}
