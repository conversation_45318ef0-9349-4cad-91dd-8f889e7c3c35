part of 'crops_cubit.dart';

sealed class CropsState {}

final class CropsInitial extends CropsState {}

final class GetPlantReferralDataSuccess extends CropsState {
  GetPlantReferralDataSuccess();
}

final class GetPlantReferralDataFailed extends CropsState {
  GetPlantReferralDataFailed();
}

final class GetPlantOperationsSuccess extends CropsState {
  GetPlantOperationsSuccess();
}

final class GetPlantOperationsFailed extends CropsState {
  GetPlantOperationsFailed();
}

final class GetPlantCategoriesLoading extends CropsState {}

final class GetPlantCategoriesSuccess extends CropsState {}

final class GetPlantCategoriesFailed extends CropsState {}

final class GetPlantOperationCategoriesSuccess extends CropsState {}

final class GetPlantOperationCategoriesFailed extends CropsState {}

final class GetDiseasesSuccess extends CropsState {}

final class GetDiseasesFailed extends CropsState {}

final class GetDiseaseTypesSuccess extends CropsState {}

final class GetDiseaseTypesFailed extends CropsState {}

final class GetDiseaseReferralInfoSuccess extends CropsState {}

final class GetDiseaseReferralInfoFailed extends CropsState {}

final class GetPlantsLoading extends CropsState {}

final class GetPlantsSuccess extends CropsState {}

final class GetPlantsFailed extends CropsState {}
