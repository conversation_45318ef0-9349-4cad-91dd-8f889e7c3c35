import 'package:agriculture/core/utils/logging/logging.dart';
import 'package:agriculture/data/datasources/local/database/database_helper.dart';
import 'package:agriculture/data/models/crops/disease_model.dart';
import 'package:agriculture/data/models/crops/disease_referral_Info_model.dart';
import 'package:agriculture/data/models/crops/disease_type_model.dart';
import 'package:agriculture/data/models/crops/plant_category_model.dart';
import 'package:agriculture/data/models/crops/plant_operation_category_model.dart';
import 'package:agriculture/data/models/crops/plant_operation_model.dart';
import 'package:agriculture/data/models/crops/plant_referral_data_model.dart';
import 'package:agriculture/data/models/crops/plants_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:sqflite/sqflite.dart';

part 'crops_state.dart';

class CropsCubit extends Cubit<CropsState> {
  CropsCubit() : super(CropsInitial());

  static CropsCubit get(context) => BlocProvider.of(context);

  /// قائمة فئات المحاصيل
  List<PlantCategory> plantCategories = [];

  /// تحميل فئات المحاصيل من قاعدة البيانات
  Future<void> getPlantCategories() async {
    try {
      // إرسال حالة التحميل
      emit(GetPlantCategoriesLoading());

      // الحصول على قاعدة البيانات
      Database database = await DatabaseHelper().database;

      // استعلام عن فئات المحاصيل
      List<Map<String, dynamic>> results =
          await database.query('plant__categories');

      // تحويل النتائج إلى نماذج
      plantCategories =
          results.map((map) => PlantCategory.fromJson(map)).toList();

      // إرسال حالة النجاح
      emit(GetPlantCategoriesSuccess());
      printInfo("Plant categories loaded: ${plantCategories.length}");
    } catch (e) {
      // إرسال حالة الفشل
      emit(GetPlantCategoriesFailed());
      printError('Error loading plant categories: $e');
    }
  }

  /// قائمة المحاصيل
  List<Plant> plants = [];

  /// تحميل المحاصيل من قاعدة البيانات
  Future<void> getPlants() async {
    try {
      // إرسال حالة التحميل
      emit(GetPlantsLoading());

      // الحصول على قاعدة البيانات
      Database database = await DatabaseHelper().database;

      // استعلام عن المحاصيل
      List<Map<String, dynamic>> results = await database.query('plants');

      // تحويل النتائج إلى نماذج
      plants = results.map((map) => Plant.fromMap(map)).toList();

      // إرسال حالة النجاح
      emit(GetPlantsSuccess());
      printInfo("Plants loaded: ${plants.length}");
    } catch (e) {
      // إرسال حالة الفشل
      emit(GetPlantsFailed());
      printError('Error loading plants: $e');
    }
  }

  //      معلومات
  List<PlantReferralData> plantReferralData = [];
  Future<void> getPlantReferralData() async {
    try {
      Database database = await DatabaseHelper().database;
      List<Map<String, dynamic>> results =
          await database.query('plant__referral__data');
      plantReferralData =
          results.map((map) => PlantReferralData.fromMap(map)).toList();
      emit(GetPlantReferralDataSuccess());
    } catch (e) {
      emit(GetPlantReferralDataFailed());
      printError('Error loading plant referral data: $e');
    }
  }

//العمليات
  List<PlantOperationCategory> plantOperationCategories = [];
  Future<void> getPlantOperationCategories() async {
    try {
      Database database = await DatabaseHelper().database;
      List<Map<String, dynamic>> results =
          await database.query('plant__operation__categories');
      plantOperationCategories =
          results.map((map) => PlantOperationCategory.fromMap(map)).toList();
      emit(GetPlantOperationCategoriesSuccess());
    } catch (e) {
      emit(GetPlantOperationCategoriesFailed());
      printError('Error loading plant operation categories: $e');
    }
  }

  /// قائمة عمليات المحاصيل
  List<PlantOperation> plantOperations = [];

  /// تحميل عمليات المحاصيل من قاعدة البيانات
  Future<void> getPlantOperations() async {
    try {
      // الحصول على قاعدة البيانات
      Database database = await DatabaseHelper().database;

      // استعلام عن عمليات المحاصيل
      List<Map<String, dynamic>> results =
          await database.query('plant__operations');

      // طباعة عدد النتائج للتشخيص
      printInfo('Found ${results.length} plant operations in database');

      // تحويل النتائج إلى نماذج
      plantOperations = [];
      for (var map in results) {
        try {
          // التحقق من وجود القيم المطلوبة
          if (map['plant_id'] != null &&
              (map['operation_category_id'] != null ||
                  map['category_id'] != null)) {
            plantOperations.add(PlantOperation.fromMap(map));
          } else {
            // تسجيل السجلات التي تحتوي على قيم null
            printWarning('Skipping plant operation with null values: $map');
          }
        } catch (e) {
          // طباعة معلومات عن السجل الذي سبب الخطأ
          printError('Error parsing plant operation: $map, Error: $e');
        }
      }

      // إرسال حالة النجاح
      emit(GetPlantOperationsSuccess());
      printInfo(
          'Successfully loaded ${plantOperations.length} plant operations');
    } catch (e, stackTrace) {
      // إرسال حالة الفشل مع معلومات أكثر تفصيلاً
      emit(GetPlantOperationsFailed());
      printError('Error loading plant operations: $e');
      printError('Stack trace: $stackTrace');
    }
  }

//   ت  الافا
  List<Disease> diseases = [];
  Future<void> getDiseases() async {
    try {
      Database database = await DatabaseHelper().database;
      List<Map<String, dynamic>> results = await database.query('diseases');
      diseases = results.map((map) => Disease.fromMap(map)).toList();
      emit(GetDiseasesSuccess());
    } catch (e) {
      emit(GetDiseasesFailed());
      printError('Error loading diseases: $e');
    }
  }

//    الوصف
  List<DiseaseReferralInfo> diseaseReferralInfo = [];

  Future<void> getDiseaseReferralInfo() async {
    try {
      Database database = await DatabaseHelper().database;
      List<Map<String, dynamic>> results =
          await database.query('disease__referral__infos');
      diseaseReferralInfo =
          results.map((map) => DiseaseReferralInfo.fromMap(map)).toList();
      emit(GetDiseaseReferralInfoSuccess());
    } catch (e) {
      emit(GetDiseaseReferralInfoFailed());
      printError('Error loading disease referral info: $e');
    }
  }

  List<DiseaseType> diseaseTypes = [];
  Future<void> getDiseaseTypes() async {
    try {
      Database database = await DatabaseHelper().database;
      List<Map<String, dynamic>> results =
          await database.query('disease__types');
      diseaseTypes = results.map((map) => DiseaseType.fromMap(map)).toList();
      emit(GetDiseaseTypesSuccess());
    } catch (e) {
      emit(GetDiseaseTypesFailed());
      printError('Error loading disease types: $e');
    }
  }
}
