import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'data/repositories/auth_repository.dart';
import 'data/repositories/storage_repository.dart';
import 'imports.dart';
import 'routing/navigation_service.dart';

/// الكلاس الرئيسي للتطبيق
///
/// يمثل هذا الكلاس نقطة الدخول الرئيسية للتطبيق ويحتوي على إعدادات التطبيق الأساسية
/// مثل الثيم والمسارات ومزودي BLoC.
class App extends StatelessWidget {
  /// منشئ الكلاس
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(create: (context) => AuthRepository()),
        RepositoryProvider<StorageRepositoryInterface>(
          create: (context) => StorageRepositoryFactory.create(),
        ),
      ],
      child: MultiBlocProvider(
        providers: [...AppRouter.allBlocProviders()],
        child: MaterialApp(
          // إعدادات عامة
          debugShowCheckedModeBanner: false,
          title: AppConstants.appName,

          // إعدادات التنقل
          navigatorKey: NavigationService().navigatorKey,
          onGenerateRoute: AppRouter.onGenerateRoute,
          home: FutureBuilder<String>(
            future: InitialRouteHelper.determineInitialRoute(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                // عرض شاشة التحميل أثناء تحديد المسار الأولي
                return Scaffold(
                  body: Center(child: CircularProgressIndicator()),
                );
              } else if (snapshot.hasError) {
                // عرض رسالة خطأ إذا حدث خطأ أثناء تحميل التطبيق
                return Scaffold(
                  body: Center(child: Text('حدث خطأ أثناء تحميل التطبيق')),
                );
              } else {
                // التحقق من المسار الأولي
                final initialRoute = snapshot.data ?? RouteConstants.onBoarding;

                // إذا كان المسار هو صفحة الترحيب أو صفحات المصادقة، نعرضها مباشرة
                if (initialRoute == RouteConstants.onBoarding ||
                    RouteConstants.authRoutes.contains(initialRoute)) {
                  // استخدام WidgetsBinding.instance.addPostFrameCallback لتأخير التنقل
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Navigator.of(context).pushReplacementNamed(initialRoute);
                  });

                  // عرض شاشة التحميل حتى يتم التنقل
                  return Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  );
                } else {
                  // إذا كان المستخدم مسجل الدخول، نعرض شريط التنقل السفلي
                  return const AppBottomNavigationBar();
                }
              }
            },
          ),

          // إعدادات الثيم
          theme: AppTheme.lightTheme(),
          darkTheme: AppTheme.darkTheme(),
          themeMode: ThemeMode.light, // استخدام الوضع النهاري دائمًا
          // إعدادات التوطين
          locale: const Locale('ar'),
          supportedLocales: const [Locale('ar'), Locale('en')],
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
        ),
      ),
    );
  }
}
