import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jiffy/jiffy.dart';

import '../../data/datasources/local/shared_prefs.dart';
import '../../data/repositories/auth_repository.dart';
import '../constants/constants_url.dart';
import '../services/enhanced_firebase_service.dart';
import '../utils/cubit_observer.dart';
import '../utils/logging/logger_service.dart';
import '../utils/services/api_keys_service.dart';
import '../utils/services/cache_service.dart';
import '../utils/services_manager.dart';

import 'initialization_constants.dart';

/// خدمة تهيئة التطبيق
///
/// توفر هذه الخدمة وظائف لتهيئة مكونات التطبيق المختلفة مثل Firebase و Google Drive.
class AppInitializationService {
  // منع إنشاء نسخة من الكلاس
  AppInitializationService._();

  /// تهيئة جميع مكونات التطبيق
  ///
  /// تقوم هذه الدالة بتهيئة جميع مكونات التطبيق بالترتيب الصحيح.
  static Future<void> initializeApp() async {
    try {
      LoggerService.debug(
        'بدء تهيئة التطبيق',
        tag: 'AppInitialization',
      );

      // تهيئة Jiffy للتاريخ باللغة العربية
      await Jiffy.setLocale('ar');
      LoggerService.debug(
        'تم تهيئة Jiffy للتاريخ باللغة العربية',
        tag: 'AppInitialization',
      );

      // تهيئة Flutter
      WidgetsFlutterBinding.ensureInitialized();
      LoggerService.debug(
        'تم تهيئة Flutter',
        tag: 'AppInitialization',
      );

      // تهيئة خدمة التسجيل
      await _initializeLoggerService();

      // تهيئة التخزين المحلي
      await _initializeSharedPrefs();

      // تهيئة خدمة التخزين الآمن
      await _initializeSecureStorageService();

      // تهيئة خدمة التخزين المؤقت
      await _initializeCacheService();

      // تهيئة خدمة إدارة مفاتيح API
      await _initializeApiKeysService();

      // تهيئة Firebase - يجب أن تكون قبل أي استخدام لـ FirebaseAuth
      await _initializeFirebase();

      // تهيئة المتغيرات العامة (بعد تهيئة Firebase)
      await initializeGlobalVariables();

      // تهيئة BLoC Observer
      _initializeBlocObserver();

      // تهيئة خدمات الطقس
      await _initializeWeatherServices();

      // تهيئة مدير الخدمات المتقدمة
      await _initializeServicesManager();

      // التحقق من حالة تسجيل الدخول وتصحيح أي تناقضات
      await _synchronizeAuthenticationState();

      LoggerService.info(
        InitializationConstants.appInitialized,
        tag: 'AppInitialization',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في تهيئة التطبيق: $e',
        error: e,
        tag: 'AppInitialization',
      );
      rethrow;
    }
  }

  /// تهيئة خدمة التسجيل
  static Future<void> _initializeLoggerService() async {
    try {
      // تهيئة خدمة التسجيل (إذا كانت تحتاج إلى تهيئة)
      // await LoggerService.init();

      LoggerService.info('تم تهيئة خدمة التسجيل', tag: 'AppInitialization');
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة التسجيل: $e');
    }
  }

  /// تهيئة Firebase مع معالجة محسنة للأخطاء
  static Future<void> _initializeFirebase() async {
    try {
      final success = await EnhancedFirebaseService.initialize();

      if (success) {
        LoggerService.info(InitializationConstants.firebaseInitialized,
            tag: 'AppInitialization');
      } else {
        LoggerService.warning('Firebase غير متاح - سيتم تشغيل التطبيق في وضع عدم الاتصال',
            tag: 'AppInitialization');
      }
    } catch (e) {
      LoggerService.error('فشل في تهيئة Firebase: $e',
          tag: 'AppInitialization');
      // لا نرمي الخطأ لأن التطبيق يمكن أن يعمل بدون Firebase
    }
  }

  /// تهيئة BLoC Observer
  static void _initializeBlocObserver() {
    try {
      // تعيين مراقب BLoC المخصص
      Bloc.observer = MyBlocObserver();

      LoggerService.info('تم تهيئة مراقب BLoC', tag: 'AppInitialization');
    } catch (e) {
      LoggerService.error('فشل في تهيئة مراقب BLoC',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة خدمات الطقس
  static Future<void> _initializeWeatherServices() async {
    try {
      // التأكد من وجود مفتاح API للطقس
      final weatherApiKey = ApiKeysService.getApiKey('weather_api_key');
      if (weatherApiKey.isEmpty) {
        // إذا لم يكن مفتاح API للطقس متاحًا، تعيين المفتاح الافتراضي
        await ApiKeysService.setApiKey('weather_api_key', 'c0e77dfadc4d891c92faec7481e71c0b');
        LoggerService.info('تم تعيين مفتاح API الافتراضي للطقس', tag: 'AppInitialization');
      }

      // تهيئة خدمات الطقس (إذا كانت تحتاج إلى تهيئة)
      // ApiWeatherWeek.init();
      // ApiWeather.init();

      LoggerService.info('تم تهيئة خدمات الطقس', tag: 'AppInitialization');
    } catch (e) {
      LoggerService.error('فشل في تهيئة خدمات الطقس',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة مدير الخدمات المتقدمة
  static Future<void> _initializeServicesManager() async {
    try {
      // تهيئة مدير الخدمات
      await ServicesManager().initialize();

      LoggerService.info('تم تهيئة مدير الخدمات المتقدمة بنجاح', tag: 'AppInitialization');

      // عرض حالة الخدمات في وضع التطوير
      if (kDebugMode) {
        final status = ServicesManager().getServicesStatus();
        LoggerService.debug('حالة الخدمات: $status', tag: 'AppInitialization');
      }
    } catch (e) {
      LoggerService.error('فشل في تهيئة مدير الخدمات المتقدمة',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة خدمة التخزين الآمن
  static Future<void> _initializeSecureStorageService() async {
    try {
      // تهيئة خدمة التخزين الآمن (إذا كانت تحتاج إلى تهيئة)
      // await SecureStorageService.init();

      LoggerService.info('تم تهيئة خدمة التخزين الآمن',
          tag: 'AppInitialization');
    } catch (e) {
      LoggerService.error('فشل في تهيئة خدمة التخزين الآمن',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة خدمة التخزين المؤقت
  static Future<void> _initializeCacheService() async {
    try {
      // تنظيف التخزين المؤقت القديم
      await CacheService.cleanDiskCache();

      LoggerService.info('تم تهيئة خدمة التخزين المؤقت',
          tag: 'AppInitialization');
    } catch (e) {
      LoggerService.error('فشل في تهيئة خدمة التخزين المؤقت',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة خدمة إدارة مفاتيح API
  static Future<void> _initializeApiKeysService() async {
    try {
      await ApiKeysService.init();
      LoggerService.info('تم تهيئة خدمة إدارة مفاتيح API',
          tag: 'AppInitialization');
    } catch (e) {
      LoggerService.error('فشل في تهيئة خدمة إدارة مفاتيح API',
          error: e, tag: 'AppInitialization');
    }
  }

  /// تهيئة التخزين المحلي
  static Future<void> _initializeSharedPrefs() async {
    await SharedPrefs.init();
    LoggerService.info(InitializationConstants.sharedPrefsInitialized,
        tag: 'AppInitialization');
  }

  /// مزامنة حالة المصادقة بين Firebase والتخزين المحلي
  ///
  /// تقوم هذه الدالة بالتحقق من حالة المستخدم في Firebase والتأكد من تطابقها مع المتغيرات المحلية.
  /// إذا كان هناك تناقض، يتم تصحيحه.
  static Future<void> _synchronizeAuthenticationState() async {
    try {
      LoggerService.debug(
        'بدء مزامنة حالة المصادقة. uid = "$uid"',
        tag: 'AppInitialization',
      );

      // التحقق من حالة تسجيل الدخول في Firebase
      final currentUser = getCurrentUser();

      if (currentUser != null) {
        LoggerService.info(
          "${InitializationConstants.userLoggedIn}${currentUser.uid}",
          tag: 'AppInitialization',
        );

        // التحقق من اكتمال بيانات البروفايل (بغض النظر عن تطابق uid)
        try {
          final authRepository = AuthRepository();
          final hasCompletedProfile =
              await authRepository.hasUserCompletedProfile(currentUser.uid);

          LoggerService.debug(
            'نتيجة التحقق من اكتمال بيانات البروفايل: $hasCompletedProfile',
            tag: 'AppInitialization',
          );

          // تخزين حالة اكتمال البروفايل
          await SharedPrefs.setBool('hasCompletedProfile', hasCompletedProfile);

          LoggerService.debug(
            'تم تخزين حالة اكتمال البروفايل: hasCompletedProfile=$hasCompletedProfile',
            tag: 'AppInitialization',
          );
        } catch (e) {
          LoggerService.error(
            'خطأ أثناء التحقق من اكتمال بيانات البروفايل: $e',
            error: e,
            tag: 'AppInitialization',
          );

          // في حالة حدوث خطأ، نفترض أن البروفايل غير مكتمل
          await SharedPrefs.setBool('hasCompletedProfile', false);
        }

        // التأكد من تطابق uid مع معرف المستخدم الحالي
        if (uid != currentUser.uid) {
          LoggerService.info(
            "${InitializationConstants.uidCorrected}$uid إلى ${currentUser.uid}",
            tag: 'AppInitialization',
          );

          uid = currentUser.uid;
          await SharedPrefs.setString('uid', uid);
          await SharedPrefs.setBool('isAuth', true);

          LoggerService.debug(
            'تم تصحيح uid وتحديث التخزين المحلي: uid = "$uid", isAuth = true',
            tag: 'AppInitialization',
          );
        }
      } else {
        LoggerService.info(
          InitializationConstants.userNotLoggedIn,
          tag: 'AppInitialization',
        );

        // إذا كان uid غير فارغ ولكن المستخدم غير مسجل الدخول في Firebase
        if (uid.isNotEmpty) {
          LoggerService.info(
            "${InitializationConstants.uidReset}$uid إلى ''",
            tag: 'AppInitialization',
          );

          uid = '';
          await SharedPrefs.remove('uid');
          await SharedPrefs.setBool('isAuth', false);

          LoggerService.debug(
            'تم إعادة تعيين متغيرات الجلسة: uid = "", isAuth = false',
            tag: 'AppInitialization',
          );
        }
      }

      LoggerService.debug(
        'اكتملت مزامنة حالة المصادقة. uid = "$uid"',
        tag: 'AppInitialization',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في مزامنة حالة المصادقة: $e',
        error: e,
        tag: 'AppInitialization',
      );
    }
  }
}
