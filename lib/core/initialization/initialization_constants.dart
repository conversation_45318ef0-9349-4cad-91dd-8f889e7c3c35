/// ثوابت التهيئة
///
/// يحتوي هذا الملف على الثوابت المستخدمة في عملية تهيئة التطبيق.
class InitializationConstants {
  // منع إنشاء نسخة من الكلاس
  InitializationConstants._();

  // رسائل التهيئة
  static const String appInitialized = 'تم تهيئة التطبيق بنجاح';
  static const String firebaseInitialized = 'تم تهيئة Firebase بنجاح';
  static const String googleDriveInitialized = 'تم تهيئة Google Drive بنجاح';
  static const String sharedPrefsInitialized = 'تم تهيئة التخزين المحلي بنجاح';
  
  // رسائل حالة المصادقة
  static const String uidLoaded = 'تم تحميل معرف المستخدم: ';
  static const String userLoggedIn = 'المستخدم مسجل الدخول: ';
  static const String userNotLoggedIn = 'المستخدم غير مسجل الدخول';
  static const String uidCorrected = 'تم تصحيح معرف المستخدم من ';
  static const String uidReset = 'تم إعادة تعيين معرف المستخدم من ';
  
  // مفاتيح التخزين المحلي
  static const String uidKey = 'uid';
  static const String isAuthKey = 'isAuth';
  
  // قيم افتراضية
  static const String emptyUid = '';
}
