import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:agriculture/data/repositories/advisor_repository.dart';
import 'package:agriculture/data/repositories/storage_repository.dart';
import 'package:agriculture/domain/repositories/advisor_repository_interface.dart';
import 'package:agriculture/domain/repositories/storage_repository_interface.dart';
import 'package:agriculture/data/datasources/remote/google_drive/google_drive_service.dart';
import 'package:agriculture/core/di/mock_storage_repository.dart';

/// مساعد حقن التبعيات لنظام المرشد الزراعي
class AdvisorInjection {
  /// إنشاء مثيل من AdvisorRepository مع جميع التبعيات المطلوبة
  static AdvisorRepositoryInterface createAdvisorRepository() {
    return AdvisorRepository(
      firestore: FirebaseFirestore.instance,
      storageRepository: createStorageRepository(),
    );
  }

  /// إنشاء مثيل من AdvisorRepository للاختبار (مع مستودع وهمي)
  static AdvisorRepositoryInterface createAdvisorRepositoryForTesting() {
    return AdvisorRepository(
      firestore: FirebaseFirestore.instance,
      storageRepository: createMockStorageRepository(),
    );
  }

  /// إنشاء مثيل من StorageRepository
  static StorageRepositoryInterface createStorageRepository() {
    return StorageRepository(
      GoogleDriveService(),
    );
  }

  /// إنشاء مثيل وهمي من StorageRepository للاختبار
  static StorageRepositoryInterface createMockStorageRepository() {
    return MockStorageRepository();
  }

  /// إنشاء مثيل من GoogleDriveService
  static GoogleDriveService createGoogleDriveService() {
    return GoogleDriveService();
  }
}
