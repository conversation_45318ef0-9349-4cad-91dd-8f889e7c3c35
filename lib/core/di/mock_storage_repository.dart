import 'dart:io';
import 'package:agriculture/domain/repositories/storage_repository_interface.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/core/utils/media/video_quality.dart';

/// مستودع تخزين وهمي للاختبار
///
/// يستخدم هذا المستودع للاختبار بدون الحاجة لإعداد Google Drive
class MockStorageRepository implements StorageRepositoryInterface {
  @override
  Future<String?> uploadFile(File file, String path, {bool compress = true}) async {
    try {
      LoggerService.info(
        'محاكاة رفع ملف: ${file.path} إلى المسار: $path',
        tag: 'MockStorageRepository',
      );

      // محاكاة تأخير الرفع
      await Future.delayed(const Duration(milliseconds: 500));

      // إرجاع رابط وهمي
      final fileName = path.split('/').last;
      return 'https://mock-storage.example.com/files/$fileName';
    } catch (e) {
      LoggerService.error(
        'خطأ في محاكاة رفع الملف',
        error: e,
        tag: 'MockStorageRepository',
      );
      return null;
    }
  }

  @override
  Future<String?> uploadCompressedImage(File file, String path, {
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      LoggerService.info(
        'محاكاة رفع صورة مضغوطة: ${file.path} (جودة: $quality)',
        tag: 'MockStorageRepository',
      );

      await Future.delayed(const Duration(milliseconds: 300));

      final fileName = path.split('/').last;
      return 'https://mock-storage.example.com/images/compressed/$fileName';
    } catch (e) {
      LoggerService.error(
        'خطأ في محاكاة رفع الصورة المضغوطة',
        error: e,
        tag: 'MockStorageRepository',
      );
      return null;
    }
  }

  @override
  Future<String?> uploadCompressedVideo(File file, String path, {
    VideoQuality quality = VideoQuality.MediumQuality,
  }) async {
    try {
      LoggerService.info(
        'محاكاة رفع فيديو مضغوط: ${file.path} (جودة: $quality)',
        tag: 'MockStorageRepository',
      );

      await Future.delayed(const Duration(milliseconds: 800));

      final fileName = path.split('/').last;
      return 'https://mock-storage.example.com/videos/compressed/$fileName';
    } catch (e) {
      LoggerService.error(
        'خطأ في محاكاة رفع الفيديو المضغوط',
        error: e,
        tag: 'MockStorageRepository',
      );
      return null;
    }
  }

  @override
  Future<bool> deleteFile(String fileUrl) async {
    try {
      LoggerService.info(
        'محاكاة حذف ملف: $fileUrl',
        tag: 'MockStorageRepository',
      );

      await Future.delayed(const Duration(milliseconds: 200));

      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في محاكاة حذف الملف',
        error: e,
        tag: 'MockStorageRepository',
      );
      return false;
    }
  }

  @override
  Future<String?> updateProfileImage(File file, String path, {
    String? oldImageUrl,
    bool compress = true,
  }) async {
    try {
      LoggerService.info(
        'محاكاة تحديث صورة الملف الشخصي: ${file.path}',
        tag: 'MockStorageRepository',
      );

      // محاكاة حذف الصورة القديمة إذا وجدت
      if (oldImageUrl != null) {
        LoggerService.info(
          'محاكاة حذف الصورة القديمة: $oldImageUrl',
          tag: 'MockStorageRepository',
        );
      }

      await Future.delayed(const Duration(milliseconds: 400));

      final fileName = path.split('/').last;
      return 'https://mock-storage.example.com/profiles/$fileName';
    } catch (e) {
      LoggerService.error(
        'خطأ في محاكاة تحديث صورة الملف الشخصي',
        error: e,
        tag: 'MockStorageRepository',
      );
      return null;
    }
  }
}
