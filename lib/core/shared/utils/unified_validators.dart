/// أدوات التحقق من صحة البيانات الموحدة
/// توفر دوال مساعدة للتحقق من صحة المدخلات
/// مع دعم اللغة العربية والتنسيقات المحلية
class UnifiedValidators {
  // منع إنشاء كائن من الكلاس
  UnifiedValidators._();

  /// التحقق من صحة البريد الإلكتروني
  /// [email] البريد الإلكتروني المراد فحصه
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    return emailRegex.hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف اليمني
  /// [phone] رقم الهاتف المراد فحصه
  static bool isValidYemeniPhone(String phone) {
    if (phone.isEmpty) return false;

    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // التحقق من الأنماط اليمنية
    final yemeniPhoneRegex = RegExp(r'^(\+967|967|0)?(7[0-8][0-9]{7})$');

    return yemeniPhoneRegex.hasMatch(cleanPhone);
  }

  /// التحقق من قوة كلمة المرور
  /// [password] كلمة المرور المراد فحصها
  static Map<String, dynamic> validatePassword(String password) {
    final result = {
      'isValid': false,
      'score': 0,
      'errors': <String>[],
      'suggestions': <String>[],
    };

    if (password.isEmpty) {
      (result['errors'] as List<String>).add('كلمة المرور مطلوبة');
      return result;
    }

    int score = 0;

    // الطول
    final errors = result['errors'] as List<String>;
    final suggestions = result['suggestions'] as List<String>;

    if (password.length >= 8) {
      score += 2;
    } else {
      errors.add('يجب أن تكون كلمة المرور 8 أحرف على الأقل');
    }

    // الأحرف الكبيرة
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      suggestions.add('أضف حروف كبيرة');
    }

    // الأحرف الصغيرة
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      suggestions.add('أضف حروف صغيرة');
    }

    // الأرقام
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      suggestions.add('أضف أرقام');
    }

    // الرموز الخاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
    } else {
      suggestions.add('أضف رموز خاصة');
    }

    result['score'] = score;
    result['isValid'] = score >= 4;

    return result;
  }

  /// التحقق من صحة النص العربي
  /// [text] النص المراد فحصه
  static bool isValidArabicText(String text) {
    if (text.isEmpty) return false;

    final arabicRegex = RegExp(r'^[\u0600-\u06FF\s]+$');
    return arabicRegex.hasMatch(text);
  }

  /// التحقق من صحة الرقم الوطني اليمني
  /// [nationalId] الرقم الوطني المراد فحصه
  static bool isValidYemeniNationalId(String nationalId) {
    if (nationalId.isEmpty || nationalId.length != 10) return false;

    // التحقق من أن جميع الأحرف أرقام
    if (!RegExp(r'^\d{10}$').hasMatch(nationalId)) return false;

    // التحقق من أن الرقم يبدأ بـ 01 (للرقم الوطني اليمني)
    if (!nationalId.startsWith('01')) return false;

    // التحقق الأساسي للرقم الوطني اليمني
    // يمكن إضافة خوارزمية تحقق أكثر تعقيداً حسب المعايير اليمنية
    return true;
  }

  /// التحقق من صحة المساحة الزراعية
  /// [area] المساحة بالمتر المربع
  static Map<String, dynamic> validateFarmArea(String area) {
    final result = {
      'isValid': false,
      'value': 0.0,
      'unit': 'متر مربع',
      'errors': <String>[],
    };

    final errors = result['errors'] as List<String>;

    if (area.isEmpty) {
      errors.add('المساحة مطلوبة');
      return result;
    }

    try {
      final value = double.parse(area);

      if (value <= 0) {
        errors.add('المساحة يجب أن تكون أكبر من صفر');
        return result;
      }

      if (value > 1000000) {
        errors.add('المساحة كبيرة جداً');
        return result;
      }

      result['isValid'] = true;
      result['value'] = value;

      // تحويل الوحدات للعرض
      if (value >= 10000) {
        result['unit'] = 'هكتار';
        result['value'] = value / 10000;
      }
    } catch (e) {
      errors.add('المساحة يجب أن تكون رقم صحيح');
    }

    return result;
  }

  /// التحقق من صحة نوع المحصول
  /// [cropType] نوع المحصول
  static bool isValidCropType(String cropType) {
    if (cropType.isEmpty) return false;

    final validCrops = [
      'طماطم',
      'خيار',
      'فلفل',
      'باذنجان',
      'كوسا',
      'بامية',
      'قمح',
      'شعير',
      'ذرة',
      'أرز',
      'دخن',
      'برتقال',
      'تفاح',
      'عنب',
      'تمر',
      'زيتون',
      'رمان',
      'خس',
      'جرجير',
      'سبانخ',
      'ملوخية',
      'بقدونس',
      'بصل',
      'ثوم',
      'جزر',
      'فجل',
      'لفت',
    ];

    return validCrops.contains(cropType) || cropType == 'أخرى';
  }

  /// التحقق من صحة وصف المشكلة الزراعية
  /// [description] وصف المشكلة
  static Map<String, dynamic> validateProblemDescription(String description) {
    final result = {
      'isValid': false,
      'wordCount': 0,
      'errors': <String>[],
      'suggestions': <String>[],
    };

    if (description.isEmpty) {
      (result['errors'] as List<String>).add('وصف المشكلة مطلوب');
      return result;
    }

    final words = description.trim().split(RegExp(r'\s+'));
    result['wordCount'] = words.length;

    if (words.length < 5) {
      (result['errors'] as List<String>).add(
        'وصف المشكلة قصير جداً (5 كلمات على الأقل)',
      );
      (result['suggestions'] as List<String>).add('أضف تفاصيل أكثر عن المشكلة');
    } else if (words.length > 200) {
      (result['errors'] as List<String>).add(
        'وصف المشكلة طويل جداً (200 كلمة كحد أقصى)',
      );
      (result['suggestions'] as List<String>).add(
        'اختصر الوصف واذكر النقاط المهمة فقط',
      );
    } else {
      result['isValid'] = true;
    }

    // اقتراحات لتحسين الوصف
    if (!description.contains('أوراق') &&
        !description.contains('ثمار') &&
        !description.contains('جذور') &&
        !description.contains('ساق')) {
      (result['suggestions'] as List<String>).add(
        'اذكر الجزء المتأثر من النبات',
      );
    }

    if (!description.contains('لون') &&
        !description.contains('بقع') &&
        !description.contains('ذبول') &&
        !description.contains('تساقط')) {
      (result['suggestions'] as List<String>).add('اذكر الأعراض المرئية');
    }

    return result;
  }

  /// التحقق من صحة التاريخ
  /// [dateString] التاريخ كنص
  static bool isValidDate(String dateString) {
    if (dateString.isEmpty) return false;

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();

      // التاريخ يجب أن يكون في المستقبل للمواعيد
      return date.isAfter(now);
    } catch (e) {
      return false;
    }
  }

  /// التحقق من صحة الملف المرفوع
  /// [fileName] اسم الملف
  /// [fileSize] حجم الملف بالبايت
  static Map<String, dynamic> validateUploadedFile(
    String fileName,
    int fileSize,
  ) {
    final result = {'isValid': false, 'errors': <String>[], 'fileType': ''};

    if (fileName.isEmpty) {
      (result['errors'] as List<String>).add('اسم الملف مطلوب');
      return result;
    }

    // التحقق من امتداد الملف
    final allowedExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.pdf',
      '.doc',
      '.docx',
    ];
    final extension = fileName.toLowerCase().substring(
      fileName.lastIndexOf('.'),
    );

    if (!allowedExtensions.contains(extension)) {
      (result['errors'] as List<String>).add('نوع الملف غير مدعوم');
      return result;
    }

    result['fileType'] = extension;

    // التحقق من حجم الملف (5 ميجا كحد أقصى)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (fileSize > maxSize) {
      (result['errors'] as List<String>).add(
        'حجم الملف كبير جداً (5 ميجا كحد أقصى)',
      );
      return result;
    }

    result['isValid'] = true;
    return result;
  }

  /// دالة شاملة للتحقق من بيانات الاستشارة
  /// [data] بيانات الاستشارة
  static Map<String, dynamic> validateConsultationData(
    Map<String, dynamic> data,
  ) {
    final result = {'isValid': true, 'errors': <String, List<String>>{}};

    // التحقق من نوع المحصول
    if (!isValidCropType(data['cropType'] ?? '')) {
      (result['errors'] as Map<String, List<String>>)['cropType'] = [
        'نوع المحصول غير صحيح',
      ];
      result['isValid'] = false;
    }

    // التحقق من وصف المشكلة
    final descriptionValidation = validateProblemDescription(
      data['problemDescription'] ?? '',
    );
    if (!descriptionValidation['isValid']) {
      (result['errors'] as Map<String, List<String>>)['problemDescription'] =
          descriptionValidation['errors'] as List<String>;
      result['isValid'] = false;
    }

    // التحقق من المساحة إن وجدت
    if (data['area'] != null && data['area'].toString().isNotEmpty) {
      final areaValidation = validateFarmArea(data['area'].toString());
      if (!areaValidation['isValid']) {
        (result['errors'] as Map<String, List<String>>)['area'] =
            areaValidation['errors'] as List<String>;
        result['isValid'] = false;
      }
    }

    return result;
  }
}
