import 'package:intl/intl.dart';

/// أداة تحويل التواريخ الموحدة
/// توفر دوال مساعدة لتحويل وتنسيق التواريخ
/// مع دعم اللغة العربية والتنسيقات المختلفة
class DateConverter {
  // منع إنشاء كائن من الكلاس
  DateConverter._();

  // ===== تنسيقات التاريخ =====
  
  /// تنسيق التاريخ الكامل (يوم/شهر/سنة ساعة:دقيقة)
  static const String fullDateFormat = 'dd/MM/yyyy HH:mm';
  
  /// تنسيق التاريخ فقط (يوم/شهر/سنة)
  static const String dateOnlyFormat = 'dd/MM/yyyy';
  
  /// تنسيق الوقت فقط (ساعة:دقيقة)
  static const String timeOnlyFormat = 'HH:mm';
  
  /// تنسيق التاريخ المختصر (يوم/شهر)
  static const String shortDateFormat = 'dd/MM';

  // ===== أسماء الأيام بالعربية =====
  static const List<String> arabicDays = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
  ];

  // ===== أسماء الشهور بالعربية =====
  static const List<String> arabicMonths = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  // ===== دوال التحويل الأساسية =====

  /// تحويل نص إلى تاريخ
  /// [dateString] النص المراد تحويله
  /// [format] تنسيق التاريخ (اختياري)
  static DateTime? stringToDate(String dateString, [String? format]) {
    try {
      if (format != null) {
        final formatter = DateFormat(format);
        return formatter.parse(dateString);
      } else {
        // محاولة تحويل تلقائي
        return DateTime.tryParse(dateString);
      }
    } catch (e) {
      return null;
    }
  }

  /// تحويل تاريخ إلى نص
  /// [date] التاريخ المراد تحويله
  /// [format] تنسيق الإخراج
  static String dateToString(DateTime date, [String format = fullDateFormat]) {
    try {
      final formatter = DateFormat(format);
      return formatter.format(date);
    } catch (e) {
      return date.toString();
    }
  }

  /// تحويل تاريخ إلى نص عربي
  /// [date] التاريخ المراد تحويله
  /// [includeTime] هل يتم تضمين الوقت
  static String dateToArabicString(DateTime date, {bool includeTime = true}) {
    try {
      final dayName = arabicDays[date.weekday % 7];
      final monthName = arabicMonths[date.month - 1];
      final day = date.day.toString();
      final year = date.year.toString();
      
      String result = '$dayName، $day $monthName $year';
      
      if (includeTime) {
        final hour = date.hour.toString().padLeft(2, '0');
        final minute = date.minute.toString().padLeft(2, '0');
        result += ' - $hour:$minute';
      }
      
      return result;
    } catch (e) {
      return dateToString(date);
    }
  }

  // ===== دوال التنسيق المتقدمة =====

  /// تحويل تاريخ إلى نص نسبي (منذ كم من الوقت)
  /// [date] التاريخ المراد تحويله
  /// [referenceDate] التاريخ المرجعي (افتراضياً الآن)
  static String dateToRelativeString(DateTime date, [DateTime? referenceDate]) {
    final now = referenceDate ?? DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? 'أمس' : 'منذ ${difference.inDays} أيام';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? 'منذ ساعة' : 'منذ ${difference.inHours} ساعات';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? 'منذ دقيقة' : 'منذ ${difference.inMinutes} دقائق';
    } else {
      return 'الآن';
    }
  }

  /// تحويل تاريخ إلى نص مختصر
  /// [date] التاريخ المراد تحويله
  static String dateToShortString(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'اليوم ${dateToString(date, timeOnlyFormat)}';
    } else if (dateOnly == yesterday) {
      return 'أمس ${dateToString(date, timeOnlyFormat)}';
    } else if (now.difference(date).inDays < 7) {
      return '${arabicDays[date.weekday % 7]} ${dateToString(date, timeOnlyFormat)}';
    } else {
      return dateToString(date, dateOnlyFormat);
    }
  }

  // ===== دوال المقارنة =====

  /// التحقق من كون التاريخ اليوم
  /// [date] التاريخ المراد فحصه
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// التحقق من كون التاريخ أمس
  /// [date] التاريخ المراد فحصه
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// التحقق من كون التاريخ في هذا الأسبوع
  /// [date] التاريخ المراد فحصه
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday % 7));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return date.isAfter(startOfWeek) && date.isBefore(endOfWeek);
  }

  /// التحقق من كون التاريخ في هذا الشهر
  /// [date] التاريخ المراد فحصه
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  // ===== دوال الحساب =====

  /// حساب العمر بالسنوات
  /// [birthDate] تاريخ الميلاد
  /// [referenceDate] التاريخ المرجعي (افتراضياً الآن)
  static int calculateAge(DateTime birthDate, [DateTime? referenceDate]) {
    final now = referenceDate ?? DateTime.now();
    int age = now.year - birthDate.year;
    
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  /// حساب الفرق بالأيام
  /// [startDate] التاريخ الأول
  /// [endDate] التاريخ الثاني
  static int daysBetween(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inDays;
  }

  /// حساب الفرق بالساعات
  /// [startDate] التاريخ الأول
  /// [endDate] التاريخ الثاني
  static int hoursBetween(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inHours;
  }

  /// حساب الفرق بالدقائق
  /// [startDate] التاريخ الأول
  /// [endDate] التاريخ الثاني
  static int minutesBetween(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inMinutes;
  }

  // ===== دوال التحقق من الصحة =====

  /// التحقق من صحة تنسيق التاريخ
  /// [dateString] النص المراد فحصه
  /// [format] التنسيق المطلوب
  static bool isValidDateFormat(String dateString, String format) {
    try {
      final formatter = DateFormat(format);
      formatter.parseStrict(dateString);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من كون التاريخ في المستقبل
  /// [date] التاريخ المراد فحصه
  static bool isFutureDate(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// التحقق من كون التاريخ في الماضي
  /// [date] التاريخ المراد فحصه
  static bool isPastDate(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  // ===== دوال مساعدة للاستشارات =====

  /// تنسيق تاريخ الاستشارة
  /// [consultationDate] تاريخ الاستشارة
  static String formatConsultationDate(DateTime consultationDate) {
    if (isToday(consultationDate)) {
      return 'اليوم ${dateToString(consultationDate, timeOnlyFormat)}';
    } else if (isYesterday(consultationDate)) {
      return 'أمس ${dateToString(consultationDate, timeOnlyFormat)}';
    } else {
      return dateToArabicString(consultationDate);
    }
  }

  /// حساب وقت الاستجابة
  /// [requestTime] وقت الطلب
  /// [responseTime] وقت الاستجابة
  static String calculateResponseTime(DateTime requestTime, DateTime responseTime) {
    final difference = responseTime.difference(requestTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }
}
