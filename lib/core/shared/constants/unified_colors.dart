import 'package:flutter/material.dart';

/// ألوان موحدة للمشروع
/// تحتوي على جميع الألوان المستخدمة في التطبيق
/// لضمان التناسق البصري عبر جميع الشاشات
class UnifiedColors {
  // منع إنشاء كائن من الكلاس
  UnifiedColors._();

  // ===== الألوان الأساسية =====
  
  /// اللون الأخضر الافتراضي للتطبيق
  static const Color primaryGreen = Color(0xFF4CAF50);
  
  /// اللون الأخضر الفاتح
  static const Color lightGreen = Color(0xFF8BC34A);
  
  /// اللون الأخضر الداكن
  static const Color darkGreen = Color(0xFF2E7D32);

  // ===== ألوان الحالة =====
  
  /// لون النجاح
  static const Color success = Color(0xFF4CAF50);
  
  /// لون التحذير
  static const Color warning = Color(0xFFFF9800);
  
  /// لون الخطر
  static const Color danger = Color(0xFFF44336);
  
  /// لون المعلومات
  static const Color info = Color(0xFF2196F3);

  // ===== ألوان الاستشارات =====
  
  /// لون الاستشارة الجديدة
  static const Color consultationNew = Color(0xFFFF9800);
  
  /// لون الاستشارة قيد المعالجة
  static const Color consultationInProgress = Color(0xFF2196F3);
  
  /// لون الاستشارة المكتملة
  static const Color consultationCompleted = Color(0xFF4CAF50);
  
  /// لون الاستشارة الملغاة
  static const Color consultationCancelled = Color(0xFFF44336);
  
  /// لون الاستشارة العاجلة
  static const Color consultationUrgent = Color(0xFFE91E63);

  // ===== ألوان النباتات =====
  
  /// لون النبات الصحي
  static const Color plantHealthy = Color(0xFF4CAF50);
  
  /// لون النبات الذي يحتاج عناية
  static const Color plantNeedsCare = Color(0xFFFF9800);
  
  /// لون النبات المريض
  static const Color plantSick = Color(0xFFF44336);
  
  /// لون النبات الميت
  static const Color plantDead = Color(0xFF795548);

  // ===== ألوان الخلفيات =====
  
  /// خلفية فاتحة
  static const Color backgroundLight = Color(0xFFFAFAFA);
  
  /// خلفية متوسطة
  static const Color backgroundMedium = Color(0xFFF5F5F5);
  
  /// خلفية داكنة
  static const Color backgroundDark = Color(0xFFEEEEEE);

  // ===== ألوان النصوص =====
  
  /// نص أساسي
  static const Color textPrimary = Color(0xFF212121);
  
  /// نص ثانوي
  static const Color textSecondary = Color(0xFF757575);
  
  /// نص فاتح
  static const Color textLight = Color(0xFFBDBDBD);
  
  /// نص على خلفية ملونة
  static const Color textOnColor = Colors.white;

  // ===== ألوان الحدود =====
  
  /// حدود فاتحة
  static const Color borderLight = Color(0xFFE0E0E0);
  
  /// حدود متوسطة
  static const Color borderMedium = Color(0xFFBDBDBD);
  
  /// حدود داكنة
  static const Color borderDark = Color(0xFF9E9E9E);

  // ===== تدرجات لونية =====
  
  /// تدرج أخضر
  static const LinearGradient greenGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGreen, lightGreen],
  );
  
  /// تدرج أزرق
  static const LinearGradient blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
  );
  
  /// تدرج برتقالي
  static const LinearGradient orangeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
  );
  
  /// تدرج أحمر
  static const LinearGradient redGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFF44336), Color(0xFFEF5350)],
  );

  // ===== ألوان الظلال =====
  
  /// ظل فاتح
  static BoxShadow get lightShadow => BoxShadow(
    color: Colors.black.withValues(alpha: 0.1),
    blurRadius: 4,
    offset: const Offset(0, 2),
  );
  
  /// ظل متوسط
  static BoxShadow get mediumShadow => BoxShadow(
    color: Colors.black.withValues(alpha: 0.15),
    blurRadius: 8,
    offset: const Offset(0, 4),
  );
  
  /// ظل قوي
  static BoxShadow get strongShadow => BoxShadow(
    color: Colors.black.withValues(alpha: 0.2),
    blurRadius: 16,
    offset: const Offset(0, 8),
  );

  // ===== دوال مساعدة =====
  
  /// الحصول على لون حسب حالة الاستشارة
  static Color getConsultationStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return consultationNew;
      case 'in_progress':
        return consultationInProgress;
      case 'completed':
      case 'answered':
        return consultationCompleted;
      case 'cancelled':
        return consultationCancelled;
      case 'urgent':
        return consultationUrgent;
      default:
        return textSecondary;
    }
  }
  
  /// الحصول على لون حسب صحة النبات
  static Color getPlantHealthColor(String health) {
    switch (health.toLowerCase()) {
      case 'healthy':
      case 'صحي':
        return plantHealthy;
      case 'needs_care':
      case 'يحتاج عناية':
        return plantNeedsCare;
      case 'sick':
      case 'مريض':
        return plantSick;
      case 'dead':
      case 'ميت':
        return plantDead;
      default:
        return textSecondary;
    }
  }
  
  /// الحصول على لون مع شفافية
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  /// تحويل لون إلى تدرج
  static LinearGradient colorToGradient(Color color) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        color,
        color.withValues(alpha: 0.8),
      ],
    );
  }
}
