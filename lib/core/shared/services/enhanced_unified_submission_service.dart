import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/data/models/unified_request/unified_request_model.dart';
import 'package:agriculture/presentation/bloc/advisor/advisor_cubit.dart';
import 'package:agriculture/core/constants/unified_submission_constants.dart';
import 'package:agriculture/core/shared/services/validation/unified_validation_service.dart';
import 'package:agriculture/core/shared/services/ui/dialog_service.dart';
import 'package:agriculture/core/shared/services/user/user_data_service.dart';

/// خدمة الإرسال الموحدة المحسنة
/// 
/// تدعم جميع أنواع الطلبات بطريقة موحدة ومتسقة
/// تتبع جميع المعايير الـ18 المحددة
class EnhancedUnifiedSubmissionService {
  
  /// إرسال طلب استشارة موحد
  static Future<bool> submitConsultation({
    required BuildContext context,
    required String cropType,
    required String problemDescription,
    String? area,
    List<File>? imageFiles,
    String? advisorId,
    RequestPriority priority = RequestPriority.normal,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = UnifiedValidationService.validateConsultationData(
        cropType: cropType,
        problemDescription: problemDescription,
        area: area,
      );

      if (!validationResult.isValid) {
        DialogService.showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = UserDataService.getCurrentUserData();

      // عرض مؤشر التحميل
      DialogService.showLoadingDialog(context, UnifiedSubmissionConstants.consultationLoadingMessage);

      // إنشاء النموذج الموحد
      final request = UnifiedRequestModel(
        id: '', // سيتم تعيينه من الخدمة
        type: RequestType.consultation,
        userId: userData.userId,
        userName: userData.userName,
        userImage: userData.userImage,
        userPhone: userData.userPhone,
        assignedToId: advisorId ?? UnifiedSubmissionConstants.defaultAdvisorId,
        title: '${UnifiedSubmissionConstants.consultationTitlePrefix} $cropType',
        description: problemDescription.trim(),
        status: RequestStatus.pending,
        priority: priority,
        specificData: {
          'cropType': cropType.trim(),
          'area': area?.trim() ?? UnifiedSubmissionConstants.notSpecifiedValue,
          'advisorId': advisorId ?? UnifiedSubmissionConstants.defaultAdvisorId,
        },
        images: imageFiles?.map((f) => f.path).toList(),
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // إرسال الطلب
      await _submitUnifiedRequest(context, request);

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        DialogService.dismissDialog(context);
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        DialogService.showSuccessDialog(
          context,
          title: UnifiedSubmissionConstants.consultationSuccessTitle,
          message: UnifiedSubmissionConstants.consultationSuccessMessage,
          details: {
            UnifiedSubmissionConstants.cropTypeLabel: cropType,
            UnifiedSubmissionConstants.areaLabel: area ?? UnifiedSubmissionConstants.notSpecifiedValue,
            UnifiedSubmissionConstants.imagesCountLabel: imageFiles?.length.toString() ?? UnifiedSubmissionConstants.zeroValue,
          },
        );
      }

      return true;
    } catch (e) {
      if (context.mounted) {
        return _handleError(context, '${UnifiedSubmissionConstants.consultationErrorMessage}: $e');
      }
      return false;
    }
  }

  /// إرسال طلب موعد موحد
  static Future<bool> submitAppointment({
    required BuildContext context,
    required String consultationType,
    required String appointmentDate,
    required TimeOfDay appointmentTime,
    required String problemDescription,
    String? userPhone,
    String? advisorId,
    RequestPriority priority = RequestPriority.normal,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = UnifiedValidationService.validateAppointmentData(
        consultationType: consultationType,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        problemDescription: problemDescription,
      );

      if (!validationResult.isValid) {
        DialogService.showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = UserDataService.getCurrentUserData();

      // عرض مؤشر التحميل
      DialogService.showLoadingDialog(context, UnifiedSubmissionConstants.appointmentLoadingMessage);

      // تحويل التاريخ والوقت
      final scheduledDate = DateTime.parse(appointmentDate);
      final timeString = '${appointmentTime.hour.toString().padLeft(2, '0')}:${appointmentTime.minute.toString().padLeft(2, '0')}';

      // إنشاء النموذج الموحد
      final request = UnifiedRequestModel(
        id: '', // سيتم تعيينه من الخدمة
        type: RequestType.appointment,
        userId: userData.userId,
        userName: userData.userName,
        userImage: userData.userImage,
        userPhone: userPhone ?? userData.userPhone,
        assignedToId: advisorId ?? UnifiedSubmissionConstants.defaultAdvisorId,
        title: '${UnifiedSubmissionConstants.appointmentTitlePrefix} $consultationType',
        description: problemDescription.trim(),
        status: RequestStatus.pending,
        priority: priority,
        specificData: {
          'consultationType': consultationType,
          'advisorId': advisorId ?? UnifiedSubmissionConstants.defaultAdvisorId,
        },
        scheduledDate: scheduledDate,
        scheduledTime: timeString,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // إرسال الطلب
      await _submitUnifiedRequest(context, request);

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        DialogService.dismissDialog(context);
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        DialogService.showSuccessDialog(
          context,
          title: UnifiedSubmissionConstants.appointmentSuccessTitle,
          message: UnifiedSubmissionConstants.appointmentSuccessMessage,
          details: {
            UnifiedSubmissionConstants.consultationTypeLabel: consultationType,
            UnifiedSubmissionConstants.dateLabel: appointmentDate,
            UnifiedSubmissionConstants.timeLabel: timeString,
          },
        );
      }

      return true;
    } catch (e) {
      if (context.mounted) {
        return _handleError(context, '${UnifiedSubmissionConstants.appointmentErrorMessage}: $e');
      }
      return false;
    }
  }

  /// إرسال طلب مراقبة النبات موحد
  static Future<bool> submitPlantMonitoring({
    required BuildContext context,
    required String plantType,
    required String location,
    required String monitoringType,
    String? notes,
    List<File>? imageFiles,
    RequestPriority priority = RequestPriority.normal,
    double? estimatedCost,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = UnifiedValidationService.validatePlantMonitoringData(
        plantType: plantType,
        location: location,
        monitoringType: monitoringType,
      );

      if (!validationResult.isValid) {
        DialogService.showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = UserDataService.getCurrentUserData();

      // عرض مؤشر التحميل
      DialogService.showLoadingDialog(context, UnifiedSubmissionConstants.plantMonitoringLoadingMessage);

      // تحضير الوصف التفصيلي
      final description = '''
طلب مراقبة نبات:
- نوع النبات: $plantType
- الموقع: $location
- نوع المراقبة: $monitoringType
${notes?.isNotEmpty == true ? '- ملاحظات: $notes' : ''}
''';

      // إنشاء النموذج الموحد
      final request = UnifiedRequestModel(
        id: '', // سيتم تعيينه من الخدمة
        type: RequestType.plantMonitoring,
        userId: userData.userId,
        userName: userData.userName,
        userImage: userData.userImage,
        userPhone: userData.userPhone,
        assignedToId: UnifiedSubmissionConstants.defaultAdvisorId, // يمكن تغييره لمهندس زراعي
        title: '${UnifiedSubmissionConstants.plantMonitoringTitlePrefix} $plantType',
        description: description.trim(),
        status: RequestStatus.pending,
        priority: priority,
        specificData: {
          'plantType': plantType.trim(),
          'location': location.trim(),
          'monitoringType': monitoringType,
          'notes': notes?.trim(),
        },
        images: imageFiles?.map((f) => f.path).toList(),
        estimatedCost: estimatedCost,
        notes: notes?.trim(),
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      // إرسال الطلب
      await _submitUnifiedRequest(context, request);

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        DialogService.dismissDialog(context);
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        DialogService.showSuccessDialog(
          context,
          title: UnifiedSubmissionConstants.plantMonitoringSuccessTitle,
          message: UnifiedSubmissionConstants.plantMonitoringSuccessMessage,
          details: {
            UnifiedSubmissionConstants.plantTypeLabel: plantType,
            UnifiedSubmissionConstants.locationLabel: location,
            UnifiedSubmissionConstants.monitoringTypeLabel: monitoringType,
            UnifiedSubmissionConstants.imagesCountLabel: imageFiles?.length.toString() ?? UnifiedSubmissionConstants.zeroValue,
          },
        );
      }

      return true;
    } catch (e) {
      if (context.mounted) {
        return _handleError(context, '${UnifiedSubmissionConstants.plantMonitoringErrorMessage}: $e');
      }
      return false;
    }
  }

  /// إرسال الطلب الموحد
  static Future<void> _submitUnifiedRequest(BuildContext context, UnifiedRequestModel request) async {
    // حسب نوع الطلب، استخدم الطريقة المناسبة
    switch (request.type) {
      case RequestType.consultation:
        await context.read<AdvisorCubit>().createConsultation(
          userId: request.userId,
          userName: request.userName,
          userImage: request.userImage,
          advisorId: request.assignedToId!,
          cropType: request.specificData['cropType'],
          problemDescription: request.description,
          area: request.specificData['area'],
          imageFiles: request.images?.map((path) => File(path)).toList(),
        );
        break;
        
      case RequestType.appointment:
        await context.read<AdvisorCubit>().bookAppointment(
          userId: request.userId,
          userName: request.userName,
          userPhone: request.userPhone ?? '',
          advisorId: request.assignedToId!,
          consultationType: request.specificData['consultationType'],
          appointmentDate: request.scheduledDate!.toIso8601String().split('T')[0],
          appointmentTime: request.scheduledTime!,
          problemDescription: request.description,
        );
        break;
        
      case RequestType.plantMonitoring:
        // إرسال كاستشارة مع تمييز خاص
        await context.read<AdvisorCubit>().createConsultation(
          userId: request.userId,
          userName: request.userName,
          userImage: request.userImage,
          advisorId: request.assignedToId!,
          cropType: request.specificData['plantType'],
          problemDescription: request.description,
          area: request.specificData['location'],
          imageFiles: request.images?.map((path) => File(path)).toList(),
        );
        break;
    }
  }

  // تم نقل جميع الدوال المساعدة إلى الخدمات المشتركة المنفصلة
  // وفق المعيار #3: التركيز على إنشاء الملفات التشاركية أولاً



  /// معالجة الأخطاء بطريقة موحدة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [message]: رسالة الخطأ
  ///
  /// الإرجاع: [bool] دائماً false للإشارة إلى الفشل
  static bool _handleError(BuildContext context, String message) {
    // إغلاق مؤشر التحميل إذا كان مفتوحاً
    DialogService.dismissDialog(context);

    // عرض رسالة الخطأ
    if (context.mounted) {
      DialogService.showErrorSnackBar(context, message);
    }
    return false;
  }
}
