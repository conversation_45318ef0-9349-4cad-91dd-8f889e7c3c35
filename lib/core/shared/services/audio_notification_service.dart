import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'notification_storage_service.dart';

/// خدمة التنبيهات الصوتية
/// 
/// توفر هذه الخدمة إمكانية تشغيل الأصوات للإشعارات المختلفة
/// مع إمكانية التحكم في تفعيل/إلغاء تفعيل الصوت
class AudioNotificationService {
  static final AudioPlayer _player = AudioPlayer();
  
  // مسارات الأصوات المختلفة
  static const String _defaultNotificationSound = 'sounds/notification.mp3';
  static const String _consultationSound = 'sounds/consultation_notification.mp3';
  static const String _appointmentSound = 'sounds/appointment_notification.mp3';
  static const String _responseSound = 'sounds/response_notification.mp3';
  static const String _urgentSound = 'sounds/urgent_notification.mp3';

  /// تشغيل صوت الإشعار الافتراضي
  static Future<void> playDefaultNotification() async {
    await _playSound(_defaultNotificationSound);
  }

  /// تشغيل صوت إشعار الاستشارة الجديدة
  static Future<void> playConsultationNotification() async {
    await _playSound(_consultationSound);
  }

  /// تشغيل صوت إشعار الموعد
  static Future<void> playAppointmentNotification() async {
    await _playSound(_appointmentSound);
  }

  /// تشغيل صوت إشعار الرد من المرشد
  static Future<void> playResponseNotification() async {
    await _playSound(_responseSound);
  }

  /// تشغيل صوت الإشعار العاجل
  static Future<void> playUrgentNotification() async {
    await _playSound(_urgentSound);
  }

  /// تشغيل صوت حسب نوع الإشعار
  /// [notificationType] نوع الإشعار
  static Future<void> playNotificationByType(String notificationType) async {
    switch (notificationType) {
      case 'consultation':
        await playConsultationNotification();
        break;
      case 'appointment':
        await playAppointmentNotification();
        break;
      case 'advisor_response':
        await playResponseNotification();
        break;
      case 'urgent':
        await playUrgentNotification();
        break;
      default:
        await playDefaultNotification();
    }
  }

  /// تشغيل الصوت الأساسي
  /// [soundPath] مسار ملف الصوت
  static Future<void> _playSound(String soundPath) async {
    try {
      // التحقق من تفعيل الصوت
      final isSoundEnabled = await NotificationStorageService.isSoundEnabled();
      if (!isSoundEnabled) return;

      // تشغيل الصوت من الـ assets
      await _player.play(AssetSource(soundPath));
      
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت: $e');
      // في حالة فشل تشغيل الصوت المخصص، تشغيل صوت النظام
      await _playSystemSound();
    }
  }

  /// تشغيل صوت النظام الافتراضي
  static Future<void> _playSystemSound() async {
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت النظام: $e');
    }
  }

  /// إيقاف تشغيل الصوت الحالي
  static Future<void> stopSound() async {
    try {
      await _player.stop();
    } catch (e) {
      debugPrint('خطأ في إيقاف الصوت: $e');
    }
  }

  /// تعيين مستوى الصوت
  /// [volume] مستوى الصوت (0.0 - 1.0)
  static Future<void> setVolume(double volume) async {
    try {
      await _player.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      debugPrint('خطأ في تعيين مستوى الصوت: $e');
    }
  }

  /// تشغيل إشعار مع اهتزاز
  /// [notificationType] نوع الإشعار
  /// [withVibration] تفعيل الاهتزاز
  static Future<void> playNotificationWithVibration({
    required String notificationType,
    bool withVibration = true,
  }) async {
    // تشغيل الصوت
    await playNotificationByType(notificationType);
    
    // تشغيل الاهتزاز إذا كان مفعلاً
    if (withVibration) {
      await _triggerVibration();
    }
  }

  /// تشغيل الاهتزاز
  static Future<void> _triggerVibration() async {
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      debugPrint('خطأ في تشغيل الاهتزاز: $e');
    }
  }

  /// إنشاء وتشغيل إشعار كامل للمزارع
  /// [farmerUserId] معرف المزارع
  /// [advisorName] اسم المرشد
  /// [consultationId] معرف الاستشارة
  /// [response] نص الرد
  static Future<void> notifyFarmerOfResponse({
    required String farmerUserId,
    required String advisorName,
    required String consultationId,
    required String response,
  }) async {
    try {
      // حفظ الإشعار في التخزين المحلي
      await NotificationStorageService.createFarmerNotification(
        farmerUserId: farmerUserId,
        advisorName: advisorName,
        consultationId: consultationId,
        response: response,
      );

      // تشغيل الصوت والاهتزاز
      await playNotificationWithVibration(
        notificationType: 'advisor_response',
        withVibration: true,
      );

      debugPrint('تم إرسال إشعار للمزارع: $farmerUserId');

    } catch (e) {
      debugPrint('خطأ في إرسال إشعار للمزارع: $e');
    }
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    try {
      await _player.dispose();
    } catch (e) {
      debugPrint('خطأ في تنظيف موارد الصوت: $e');
    }
  }

  /// اختبار تشغيل جميع الأصوات
  static Future<void> testAllSounds() async {
    final sounds = [
      'default',
      'consultation',
      'appointment',
      'advisor_response',
      'urgent',
    ];

    for (String soundType in sounds) {
      debugPrint('اختبار صوت: $soundType');
      await playNotificationByType(soundType);
      await Future.delayed(const Duration(seconds: 2));
    }
  }

  /// الحصول على قائمة الأصوات المتاحة
  static List<Map<String, String>> getAvailableSounds() {
    return [
      {'type': 'default', 'name': 'الصوت الافتراضي', 'path': _defaultNotificationSound},
      {'type': 'consultation', 'name': 'صوت الاستشارة', 'path': _consultationSound},
      {'type': 'appointment', 'name': 'صوت الموعد', 'path': _appointmentSound},
      {'type': 'advisor_response', 'name': 'صوت رد المرشد', 'path': _responseSound},
      {'type': 'urgent', 'name': 'صوت عاجل', 'path': _urgentSound},
    ];
  }
}
