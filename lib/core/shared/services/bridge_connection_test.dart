import 'package:flutter/foundation.dart';
import 'farmer_advisor_bridge.dart';
import 'unified_consultation_service.dart';
import 'unified_notification_service.dart';
import 'unified_analytics_service.dart';

/// خدمة اختبار الاتصال بين المزارع والاستشاري
/// تتحقق من جاهزية جميع الخدمات للعمل معاً
/// وفق المعايير الـ18 للمشروع
class BridgeConnectionTest {
  static final BridgeConnectionTest _instance = BridgeConnectionTest._internal();
  factory BridgeConnectionTest() => _instance;
  BridgeConnectionTest._internal();

  // الخدمات المطلوبة للاختبار
  final FarmerAdvisorBridge _bridge = FarmerAdvisorBridge();
  final UnifiedConsultationService _consultationService = UnifiedConsultationService();
  final UnifiedNotificationService _notificationService = UnifiedNotificationService();
  final UnifiedAnalyticsService _analyticsService = UnifiedAnalyticsService();

  /// تشغيل اختبار شامل للاتصال
  /// يتحقق من جميع الخدمات وفق المعايير الـ18
  Future<Map<String, dynamic>> runFullConnectionTest() async {
    final testResults = {
      'overall_status': 'testing',
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, Map<String, dynamic>>{},
      'summary': {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'warnings': 0,
      },
    };

    debugPrint('🧪 بدء اختبار الاتصال الشامل...');

    try {
      // اختبار 1: تهيئة الخدمات
      final tests = testResults['tests'] as Map<String, Map<String, dynamic>>;
      tests['service_initialization'] = await _testServiceInitialization();

      // اختبار 2: إرسال استشارة
      tests['consultation_flow'] = await _testConsultationFlow();

      // اختبار 3: نظام الإشعارات
      tests['notification_system'] = await _testNotificationSystem();

      // اختبار 4: التحليلات
      tests['analytics_tracking'] = await _testAnalyticsTracking();

      // اختبار 5: الجسر الموحد
      tests['bridge_communication'] = await _testBridgeCommunication();

      // حساب النتائج الإجمالية
      _calculateSummary(testResults);

      debugPrint('✅ انتهى اختبار الاتصال الشامل');
    } catch (e) {
      testResults['overall_status'] = 'error';
      testResults['error'] = e.toString();
      debugPrint('❌ خطأ في اختبار الاتصال: $e');
    }

    return testResults;
  }

  /// اختبار تهيئة الخدمات
  Future<Map<String, dynamic>> _testServiceInitialization() async {
    final result = {
      'name': 'تهيئة الخدمات',
      'status': 'testing',
      'details': <String, dynamic>{},
      'duration_ms': 0,
    };

    final startTime = DateTime.now();

    try {
      // تهيئة جميع الخدمات
      final details = result['details'] as Map<String, dynamic>;

      await _bridge.initialize();
      details['bridge'] = 'initialized';

      // ملاحظة: UnifiedConsultationService لا تحتاج initialize
      details['consultation'] = 'ready';

      await _notificationService.initialize();
      details['notification'] = 'initialized';

      await _analyticsService.initialize();
      details['analytics'] = 'initialized';

      result['status'] = 'passed';
      debugPrint('✅ اختبار تهيئة الخدمات: نجح');
    } catch (e) {
      result['status'] = 'failed';
      result['error'] = e.toString();
      debugPrint('❌ اختبار تهيئة الخدمات: فشل - $e');
    }

    result['duration_ms'] = DateTime.now().difference(startTime).inMilliseconds;
    return result;
  }

  /// اختبار تدفق الاستشارة
  Future<Map<String, dynamic>> _testConsultationFlow() async {
    final result = {
      'name': 'تدفق الاستشارة',
      'status': 'testing',
      'details': <String, dynamic>{},
      'duration_ms': 0,
    };

    final startTime = DateTime.now();

    try {
      // إنشاء استشارة تجريبية
      final consultation = await _consultationService.createConsultation(
        farmerId: 'test_farmer_001',
        cropType: 'طماطم',
        problemDescription: 'اختبار تدفق الاستشارة - أوراق النبات تتحول للون الأصفر',
        area: '100',
        priority: 'normal',
      );

      if (consultation != null) {
        final details = result['details'] as Map<String, dynamic>;
        details['consultation_created'] = true;
        details['consultation_id'] = consultation.id;

        // اختبار إرسال عبر الجسر
        final bridgeSuccess = await _bridge.sendInstantConsultation(
          farmerId: 'test_farmer_001',
          consultation: consultation,
          priority: 'normal',
        );

        details['bridge_sent'] = bridgeSuccess;

        if (bridgeSuccess) {
          result['status'] = 'passed';
          debugPrint('✅ اختبار تدفق الاستشارة: نجح');
        } else {
          result['status'] = 'failed';
          result['error'] = 'فشل في إرسال الاستشارة عبر الجسر';
        }
      } else {
        result['status'] = 'failed';
        result['error'] = 'فشل في إنشاء الاستشارة';
      }
    } catch (e) {
      result['status'] = 'failed';
      result['error'] = e.toString();
      debugPrint('❌ اختبار تدفق الاستشارة: فشل - $e');
    }

    result['duration_ms'] = DateTime.now().difference(startTime).inMilliseconds;
    return result;
  }

  /// اختبار نظام الإشعارات
  Future<Map<String, dynamic>> _testNotificationSystem() async {
    final result = {
      'name': 'نظام الإشعارات',
      'status': 'testing',
      'details': <String, dynamic>{},
      'duration_ms': 0,
    };

    final startTime = DateTime.now();

    try {
      final details = result['details'] as Map<String, dynamic>;

      // اختبار إشعار للاستشاري
      await _notificationService.sendToAdvisor(
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار تجريبي للاستشاري',
        data: {'test': true},
      );
      details['advisor_notification'] = 'sent';

      // اختبار إشعار للمزارع
      await _notificationService.sendToFarmer(
        farmerId: 'test_farmer_001',
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار تجريبي للمزارع',
        data: {'test': true},
      );
      details['farmer_notification'] = 'sent';

      // اختبار الإشعار العاجل
      await _notificationService.sendUrgentNotification(
        title: 'اختبار عاجل',
        body: 'هذا إشعار عاجل تجريبي',
        targetType: 'advisor',
        data: {'urgent': true},
      );
      details['urgent_notification'] = 'sent';

      result['status'] = 'passed';
      debugPrint('✅ اختبار نظام الإشعارات: نجح');
    } catch (e) {
      result['status'] = 'failed';
      result['error'] = e.toString();
      debugPrint('❌ اختبار نظام الإشعارات: فشل - $e');
    }

    result['duration_ms'] = DateTime.now().difference(startTime).inMilliseconds;
    return result;
  }

  /// اختبار التحليلات
  Future<Map<String, dynamic>> _testAnalyticsTracking() async {
    final result = {
      'name': 'تتبع التحليلات',
      'status': 'testing',
      'details': <String, dynamic>{},
      'duration_ms': 0,
    };

    final startTime = DateTime.now();

    try {
      final details = result['details'] as Map<String, dynamic>;

      // تسجيل أحداث مختلفة
      await _analyticsService.trackEvent('test_event', parameters: {'test': true});
      details['event_tracked'] = true;

      await _analyticsService.trackScreenView('test_screen');
      details['screen_tracked'] = true;

      await _analyticsService.trackConsultationCreated(
        consultationType: 'test',
        cropType: 'طماطم',
        farmerId: 'test_farmer_001',
      );
      details['consultation_tracked'] = true;

      result['status'] = 'passed';
      debugPrint('✅ اختبار التحليلات: نجح');
    } catch (e) {
      result['status'] = 'failed';
      result['error'] = e.toString();
      debugPrint('❌ اختبار التحليلات: فشل - $e');
    }

    result['duration_ms'] = DateTime.now().difference(startTime).inMilliseconds;
    return result;
  }

  /// اختبار التواصل عبر الجسر
  Future<Map<String, dynamic>> _testBridgeCommunication() async {
    final result = {
      'name': 'التواصل عبر الجسر',
      'status': 'testing',
      'details': <String, dynamic>{},
      'duration_ms': 0,
    };

    final startTime = DateTime.now();

    try {
      final details = result['details'] as Map<String, dynamic>;

      // اختبار إرسال رد الاستشاري
      final responseSuccess = await _bridge.sendAdvisorResponse(
        advisorId: 'test_advisor_001',
        farmerId: 'test_farmer_001',
        response: 'هذا رد تجريبي من الاستشاري',
        consultationId: 'test_consultation_001',
      );
      details['advisor_response'] = responseSuccess;

      // اختبار مراقبة النبات
      final monitoringSuccess = await _bridge.sendPlantMonitoringRequest(
        farmerId: 'test_farmer_001',
        plantData: {
          'type': 'طماطم',
          'area': '100',
          'symptoms': 'اصفرار الأوراق',
        },
        monitoringType: 'فحص الأمراض',
      );
      details['plant_monitoring'] = monitoringSuccess;

      if (responseSuccess && monitoringSuccess) {
        result['status'] = 'passed';
        debugPrint('✅ اختبار التواصل عبر الجسر: نجح');
      } else {
        result['status'] = 'warning';
        result['warning'] = 'بعض وظائف الجسر لا تعمل بشكل كامل';
      }
    } catch (e) {
      result['status'] = 'failed';
      result['error'] = e.toString();
      debugPrint('❌ اختبار التواصل عبر الجسر: فشل - $e');
    }

    result['duration_ms'] = DateTime.now().difference(startTime).inMilliseconds;
    return result;
  }

  /// حساب ملخص النتائج
  void _calculateSummary(Map<String, dynamic> testResults) {
    final tests = testResults['tests'] as Map<String, Map<String, dynamic>>;
    final summary = testResults['summary'] as Map<String, dynamic>;

    summary['total_tests'] = tests.length;
    summary['passed_tests'] = tests.values.where((test) => test['status'] == 'passed').length;
    summary['failed_tests'] = tests.values.where((test) => test['status'] == 'failed').length;
    summary['warnings'] = tests.values.where((test) => test['status'] == 'warning').length;

    // تحديد الحالة الإجمالية
    if (summary['failed_tests'] > 0) {
      testResults['overall_status'] = 'failed';
    } else if (summary['warnings'] > 0) {
      testResults['overall_status'] = 'warning';
    } else {
      testResults['overall_status'] = 'passed';
    }

    debugPrint('📊 ملخص الاختبار: ${summary['passed_tests']}/${summary['total_tests']} نجح');
  }

  /// اختبار سريع للاتصال
  Future<bool> quickConnectionTest() async {
    try {
      debugPrint('⚡ اختبار سريع للاتصال...');

      // تهيئة سريعة
      await _bridge.initialize();
      await _notificationService.initialize();

      // اختبار بسيط
      await _notificationService.sendToAdvisor(
        title: 'اختبار سريع',
        body: 'الاتصال يعمل بشكل صحيح',
      );

      debugPrint('✅ الاختبار السريع نجح');
      return true;
    } catch (e) {
      debugPrint('❌ الاختبار السريع فشل: $e');
      return false;
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _bridge.dispose();
    _consultationService.dispose();
    _notificationService.dispose();
    _analyticsService.dispose();
    debugPrint('🧹 تم تنظيف موارد اختبار الاتصال');
  }
}
