import 'package:flutter/foundation.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/data/models/agricultural_advisor/appointment_model.dart';
import 'package:agriculture/core/shared/services/unified_notification_service.dart';
import 'package:agriculture/core/shared/services/unified_analytics_service.dart';

/// خدمة الجسر الموحد بين المزارع والاستشاري الزراعي
/// تهدف إلى ربط واجهة المزارع (reach_engineer) مع واجهة الاستشاري (advisor_virtual_interface)
/// وتوفير تواصل سلس وآمن بين الطرفين
class FarmerAdvisorBridge {
  static final FarmerAdvisorBridge _instance = FarmerAdvisorBridge._internal();
  factory FarmerAdvisorBridge() => _instance;
  FarmerAdvisorBridge._internal();

  final UnifiedNotificationService _notificationService = UnifiedNotificationService();
  final UnifiedAnalyticsService _analyticsService = UnifiedAnalyticsService();

  /// قائمة المستمعين لتحديثات الاستشارات
  final List<Function(ConsultationModel)> _consultationListeners = [];
  
  /// قائمة المستمعين لتحديثات المواعيد
  final List<Function(AppointmentModel)> _appointmentListeners = [];

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      await _notificationService.initialize();
      await _analyticsService.initialize();
      debugPrint('🌉 تم تهيئة جسر التواصل بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة جسر التواصل: $e');
    }
  }

  /// إرسال استشارة فورية من المزارع إلى الاستشاري
  /// [farmerId] معرف المزارع
  /// [consultation] بيانات الاستشارة
  /// [priority] مستوى الأولوية (عادي، عاجل، طارئ)
  Future<bool> sendInstantConsultation({
    required String farmerId,
    required ConsultationModel consultation,
    String priority = 'normal',
  }) async {
    try {
      // تسجيل الحدث في التحليلات
      await _analyticsService.trackEvent(
        'consultation_sent',
        parameters: {
          'farmer_id': farmerId,
          'crop_type': consultation.cropType,
          'priority': priority,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // إرسال إشعار للاستشاري
      await _notificationService.sendToAdvisor(
        title: 'استشارة جديدة',
        body: 'استشارة ${consultation.cropType} من المزارع $farmerId',
        data: {
          'type': 'consultation',
          'consultation_id': consultation.id,
          'priority': priority,
        },
      );

      // إشعار المستمعين
      for (final listener in _consultationListeners) {
        listener(consultation);
      }

      debugPrint('✅ تم إرسال الاستشارة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الاستشارة: $e');
      return false;
    }
  }

  /// حجز موعد مع الاستشاري
  /// [farmerId] معرف المزارع
  /// [appointment] بيانات الموعد
  Future<bool> bookAppointment({
    required String farmerId,
    required AppointmentModel appointment,
  }) async {
    try {
      // تسجيل الحدث
      await _analyticsService.trackEvent(
        'appointment_booked',
        parameters: {
          'farmer_id': farmerId,
          'appointment_date': appointment.appointmentDate,
          'appointment_time': appointment.appointmentTime,
          'appointment_type': appointment.consultationType,
        },
      );

      // إرسال إشعار للاستشاري
      await _notificationService.sendToAdvisor(
        title: 'موعد جديد',
        body: 'تم حجز موعد ${appointment.consultationType} في ${appointment.appointmentDate} الساعة ${appointment.appointmentTime}',
        data: {
          'type': 'appointment',
          'appointment_id': appointment.id,
          'farmer_id': farmerId,
          'consultation_type': appointment.consultationType,
        },
      );

      // إشعار المستمعين
      for (final listener in _appointmentListeners) {
        listener(appointment);
      }

      debugPrint('✅ تم حجز الموعد بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حجز الموعد: $e');
      return false;
    }
  }

  /// إرسال طلب مراقبة نبات
  /// [farmerId] معرف المزارع
  /// [plantData] بيانات النبات
  /// [monitoringType] نوع المراقبة (صحة، نمو، آفات)
  Future<bool> sendPlantMonitoringRequest({
    required String farmerId,
    required Map<String, dynamic> plantData,
    required String monitoringType,
  }) async {
    try {
      // تسجيل الحدث
      await _analyticsService.trackEvent(
        'plant_monitoring_requested',
        parameters: {
          'farmer_id': farmerId,
          'plant_type': plantData['type'],
          'monitoring_type': monitoringType,
          'area': plantData['area'],
        },
      );

      // إرسال إشعار للاستشاري
      await _notificationService.sendToAdvisor(
        title: 'طلب مراقبة نبات',
        body: 'طلب مراقبة ${plantData['type']} - النوع: $monitoringType',
        data: {
          'type': 'plant_monitoring',
          'farmer_id': farmerId,
          'plant_data': plantData,
          'monitoring_type': monitoringType,
        },
      );

      debugPrint('✅ تم إرسال طلب مراقبة النبات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال طلب مراقبة النبات: $e');
      return false;
    }
  }

  /// إرسال رد من الاستشاري إلى المزارع
  /// [advisorId] معرف الاستشاري
  /// [farmerId] معرف المزارع
  /// [response] نص الرد
  /// [consultationId] معرف الاستشارة
  Future<bool> sendAdvisorResponse({
    required String advisorId,
    required String farmerId,
    required String response,
    required String consultationId,
    List<String>? attachments,
  }) async {
    try {
      // تسجيل الحدث
      await _analyticsService.trackEvent(
        'advisor_response_sent',
        parameters: {
          'advisor_id': advisorId,
          'farmer_id': farmerId,
          'consultation_id': consultationId,
          'response_length': response.length,
          'has_attachments': attachments?.isNotEmpty ?? false,
        },
      );

      // إرسال إشعار للمزارع
      await _notificationService.sendToFarmer(
        farmerId: farmerId,
        title: 'رد من الاستشاري',
        body: 'تم الرد على استشارتك',
        data: {
          'type': 'advisor_response',
          'consultation_id': consultationId,
          'advisor_id': advisorId,
        },
      );

      debugPrint('✅ تم إرسال رد الاستشاري بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال رد الاستشاري: $e');
      return false;
    }
  }

  /// إضافة مستمع لتحديثات الاستشارات
  void addConsultationListener(Function(ConsultationModel) listener) {
    _consultationListeners.add(listener);
  }

  /// إزالة مستمع تحديثات الاستشارات
  void removeConsultationListener(Function(ConsultationModel) listener) {
    _consultationListeners.remove(listener);
  }

  /// إضافة مستمع لتحديثات المواعيد
  void addAppointmentListener(Function(AppointmentModel) listener) {
    _appointmentListeners.add(listener);
  }

  /// إزالة مستمع تحديثات المواعيد
  void removeAppointmentListener(Function(AppointmentModel) listener) {
    _appointmentListeners.remove(listener);
  }

  /// تنظيف الموارد
  void dispose() {
    _consultationListeners.clear();
    _appointmentListeners.clear();
    debugPrint('🧹 تم تنظيف موارد جسر التواصل');
  }
}
