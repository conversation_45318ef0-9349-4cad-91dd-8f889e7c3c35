import 'package:flutter/foundation.dart';
import 'package:agriculture/data/models/agricultural_advisor/consultation_model.dart';
import 'package:agriculture/data/models/agricultural_advisor/appointment_model.dart';
import 'package:agriculture/core/shared/services/unified_analytics_service.dart';
import 'package:agriculture/core/shared/services/unified_notification_service.dart';

/// خدمة الاستشارات الموحدة
/// تدير جميع عمليات الاستشارات الزراعية
/// وتوفر واجهة موحدة للتعامل مع البيانات
class UnifiedConsultationService {
  static final UnifiedConsultationService _instance = UnifiedConsultationService._internal();
  factory UnifiedConsultationService() => _instance;
  UnifiedConsultationService._internal();

  final UnifiedAnalyticsService _analytics = UnifiedAnalyticsService();
  final UnifiedNotificationService _notifications = UnifiedNotificationService();

  /// قائمة الاستشارات النشطة
  final List<ConsultationModel> _activeConsultations = [];
  
  /// قائمة المواعيد النشطة
  final List<AppointmentModel> _activeAppointments = [];

  /// قائمة المستمعين لتحديثات الاستشارات
  final List<Function(List<ConsultationModel>)> _consultationListeners = [];

  /// قائمة المستمعين لتحديثات المواعيد
  final List<Function(List<AppointmentModel>)> _appointmentListeners = [];

  /// إنشاء استشارة جديدة
  /// [farmerId] معرف المزارع
  /// [cropType] نوع المحصول
  /// [problemDescription] وصف المشكلة
  /// [area] المساحة المزروعة
  /// [images] الصور المرفقة
  /// [priority] مستوى الأولوية
  Future<ConsultationModel?> createConsultation({
    required String farmerId,
    required String cropType,
    required String problemDescription,
    String? area,
    List<String>? images,
    String priority = 'normal',
  }) async {
    try {
      final consultation = ConsultationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: farmerId,
        userName: 'مزارع_$farmerId',
        userImage: null,
        advisorId: 'general_advisor',
        advisorName: 'المرشد العام',
        cropType: cropType,
        problemDescription: problemDescription,
        area: area ?? '',
        images: images,
        status: ConsultationStatus.pending,
        rating: null,
        ratingComment: null,
        advisorResponse: null,
        createdAt: DateTime.now().toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
        respondedAt: null,
      );

      // إضافة للقائمة النشطة
      _activeConsultations.add(consultation);

      // تسجيل في التحليلات
      await _analytics.trackConsultationCreated(
        consultationType: 'instant',
        cropType: cropType,
        farmerId: farmerId,
        priority: priority,
      );

      // إرسال إشعار للاستشاري
      await _notifications.sendToAdvisor(
        title: 'استشارة جديدة',
        body: 'استشارة $cropType من المزارع',
        data: {
          'type': 'consultation',
          'consultation_id': consultation.id,
          'priority': priority,
        },
      );

      // إشعار المستمعين
      _notifyConsultationListeners();

      debugPrint('✅ تم إنشاء الاستشارة بنجاح: ${consultation.id}');
      return consultation;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الاستشارة: $e');
      return null;
    }
  }

  /// الرد على استشارة
  /// [consultationId] معرف الاستشارة
  /// [advisorId] معرف الاستشاري
  /// [response] نص الرد
  /// [recommendations] التوصيات
  /// [attachments] المرفقات
  Future<bool> respondToConsultation({
    required String consultationId,
    required String advisorId,
    required String response,
    String? recommendations,
    List<String>? attachments,
  }) async {
    try {
      // البحث عن الاستشارة
      final consultationIndex = _activeConsultations.indexWhere(
        (c) => c.id == consultationId,
      );

      if (consultationIndex == -1) {
        debugPrint('❌ لم يتم العثور على الاستشارة: $consultationId');
        return false;
      }

      // تحديث حالة الاستشارة
      final consultation = _activeConsultations[consultationIndex];
      final updatedConsultation = consultation.copyWith(
        status: ConsultationStatus.answered,
        lastUpdated: DateTime.now().toIso8601String(),
      );

      _activeConsultations[consultationIndex] = updatedConsultation;

      // حساب وقت الاستجابة
      final createdTime = DateTime.parse(consultation.createdAt);
      final responseTime = DateTime.now().difference(createdTime).inMinutes;

      // تسجيل في التحليلات
      await _analytics.trackAdvisorResponse(
        consultationId: consultationId,
        advisorId: advisorId,
        responseTime: responseTime,
        hasAttachments: attachments?.isNotEmpty ?? false,
      );

      // إرسال إشعار للمزارع
      await _notifications.sendToFarmer(
        farmerId: consultation.userId,
        title: 'رد من الاستشاري',
        body: 'تم الرد على استشارة ${consultation.cropType}',
        data: {
          'type': 'advisor_response',
          'consultation_id': consultationId,
        },
      );

      // إشعار المستمعين
      _notifyConsultationListeners();

      debugPrint('✅ تم الرد على الاستشارة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في الرد على الاستشارة: $e');
      return false;
    }
  }

  /// حجز موعد جديد
  /// [farmerId] معرف المزارع
  /// [advisorId] معرف الاستشاري
  /// [scheduledDate] تاريخ الموعد
  /// [type] نوع الموعد
  /// [notes] ملاحظات
  Future<AppointmentModel?> bookAppointment({
    required String farmerId,
    required String advisorId,
    required String scheduledDate,
    required String type,
    String? notes,
  }) async {
    try {
      // تحليل التاريخ والوقت
      final dateTime = DateTime.parse(scheduledDate);
      final formattedDate = '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      final formattedTime = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';

      final appointment = AppointmentModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: farmerId,
        userName: 'مزارع_$farmerId',
        userPhone: '77xxxxxxx',
        advisorId: advisorId,
        advisorName: 'المرشد العام',
        consultationType: type,
        appointmentDate: formattedDate,
        appointmentTime: formattedTime,
        problemDescription: notes ?? 'لا توجد ملاحظات',
        status: AppointmentStatus.pending,
        advisorNotes: notes,
        createdAt: DateTime.now().toIso8601String(),
        lastUpdated: DateTime.now().toIso8601String(),
        confirmedAt: null,
      );

      // إضافة للقائمة النشطة
      _activeAppointments.add(appointment);

      // تسجيل في التحليلات
      await _analytics.trackAppointmentBooked(
        appointmentType: type,
        farmerId: farmerId,
        scheduledDate: scheduledDate,
      );

      // إرسال إشعار للاستشاري
      await _notifications.sendToAdvisor(
        title: 'موعد جديد',
        body: 'تم حجز موعد ${appointment.consultationType} في ${appointment.appointmentDate}',
        data: {
          'type': 'appointment',
          'appointment_id': appointment.id,
        },
      );

      // إشعار المستمعين
      _notifyAppointmentListeners();

      debugPrint('✅ تم حجز الموعد بنجاح: ${appointment.id}');
      return appointment;
    } catch (e) {
      debugPrint('❌ خطأ في حجز الموعد: $e');
      return null;
    }
  }

  /// الحصول على جميع الاستشارات
  List<ConsultationModel> getAllConsultations() {
    return List.from(_activeConsultations);
  }

  /// الحصول على الاستشارات حسب الحالة
  List<ConsultationModel> getConsultationsByStatus(ConsultationStatus status) {
    return _activeConsultations.where((c) => c.status == status).toList();
  }

  /// الحصول على جميع المواعيد
  List<AppointmentModel> getAllAppointments() {
    return List.from(_activeAppointments);
  }

  /// الحصول على المواعيد حسب التاريخ
  List<AppointmentModel> getAppointmentsByDate(String date) {
    return _activeAppointments.where((a) => a.appointmentDate.contains(date)).toList();
  }

  /// تقييم الاستشارة
  /// [consultationId] معرف الاستشارة
  /// [rating] التقييم (1-5)
  /// [feedback] التعليق
  Future<bool> rateConsultation({
    required String consultationId,
    required int rating,
    String? feedback,
  }) async {
    try {
      // تسجيل في التحليلات
      await _analytics.trackFarmerRating(
        consultationId: consultationId,
        rating: rating,
        feedback: feedback,
      );

      debugPrint('✅ تم تقييم الاستشارة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تقييم الاستشارة: $e');
      return false;
    }
  }

  /// إضافة مستمع لتحديثات الاستشارات
  void addConsultationListener(Function(List<ConsultationModel>) listener) {
    _consultationListeners.add(listener);
  }

  /// إزالة مستمع تحديثات الاستشارات
  void removeConsultationListener(Function(List<ConsultationModel>) listener) {
    _consultationListeners.remove(listener);
  }

  /// إضافة مستمع لتحديثات المواعيد
  void addAppointmentListener(Function(List<AppointmentModel>) listener) {
    _appointmentListeners.add(listener);
  }

  /// إزالة مستمع تحديثات المواعيد
  void removeAppointmentListener(Function(List<AppointmentModel>) listener) {
    _appointmentListeners.remove(listener);
  }

  /// إشعار مستمعي الاستشارات
  void _notifyConsultationListeners() {
    for (final listener in _consultationListeners) {
      try {
        listener(getAllConsultations());
      } catch (e) {
        debugPrint('❌ خطأ في إشعار مستمع الاستشارات: $e');
      }
    }
  }

  /// إشعار مستمعي المواعيد
  void _notifyAppointmentListeners() {
    for (final listener in _appointmentListeners) {
      try {
        listener(getAllAppointments());
      } catch (e) {
        debugPrint('❌ خطأ في إشعار مستمع المواعيد: $e');
      }
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _consultationListeners.clear();
    _appointmentListeners.clear();
    _activeConsultations.clear();
    _activeAppointments.clear();
    debugPrint('🧹 تم تنظيف موارد خدمة الاستشارات');
  }
}
