import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../presentation/bloc/advisor/advisor_cubit.dart';
import '../../../presentation/bloc/community_forum/posts_cubit.dart';
import '../../../presentation/bloc/auth/core/auth_cubit.dart';

/// خدمة الإرسال الموحدة
/// تدير جميع عمليات إرسال الطلبات والمنشورات
/// وتوفر واجهة موحدة مع التحقق من صحة البيانات
class UnifiedSubmissionService {
  static final UnifiedSubmissionService _instance = UnifiedSubmissionService._internal();
  factory UnifiedSubmissionService() => _instance;
  UnifiedSubmissionService._internal();

  /// إرسال استشارة زراعية
  /// [context] سياق التطبيق
  /// [cropType] نوع المحصول
  /// [problemDescription] وصف المشكلة
  /// [area] المساحة المزروعة
  /// [imageFiles] الصور المرفقة
  /// [advisorId] معرف المرشد (اختياري - افتراضي: general_advisor)
  static Future<bool> submitConsultation({
    required BuildContext context,
    required String cropType,
    required String problemDescription,
    String? area,
    List<File>? imageFiles,
    String? advisorId,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = _validateConsultationData(
        cropType: cropType,
        problemDescription: problemDescription,
      );
      
      if (!validationResult.isValid) {
        _showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = _getUserData(context);
      
      // عرض مؤشر التحميل
      _showLoadingDialog(context, 'جاري إرسال الاستشارة...');

      // إرسال الاستشارة
      await context.read<AdvisorCubit>().createConsultation(
        userId: userData['userId']!,
        userName: userData['userName']!,
        userImage: userData['userImage'],
        advisorId: advisorId ?? 'general_advisor',
        cropType: cropType.trim(),
        problemDescription: problemDescription.trim(),
        area: area?.trim() ?? '',
        imageFiles: imageFiles,
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // تأخير بسيط لضمان إغلاق الحوار السابق
      await Future.delayed(const Duration(milliseconds: 300));

      // عرض رسالة النجاح مع إمكانية التوجيه
      if (context.mounted) {
        _showSuccessDialog(
          context,
          title: 'تم إرسال الاستشارة بنجاح!',
          message: 'تم تقديم استشارتك إلى المرشد الزراعي وسيتم الرد عليها قريباً',
          details: {
            'نوع المحصول': cropType,
            'المساحة': area ?? 'غير محددة',
            'وصف المشكلة': problemDescription.length > 50
                ? '${problemDescription.substring(0, 50)}...'
                : problemDescription,
          },
          onNavigateToAdvisor: () {
            // التوجيه لواجهة أسئلة المرشد
            if (context.mounted) {
              Navigator.of(context).pushNamed('/advisor_questions');
            }
          },
        );
      }

      return true;
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ أثناء إرسال الاستشارة: $e');
      }
      return false;
    }
  }

  /// إرسال منشور في المنتدى
  /// [context] سياق التطبيق
  /// [text] نص المنشور
  /// [imageFiles] الصور المرفقة
  /// [videoFile] الفيديو المرفق
  /// [hashtags] الوسوم
  static Future<bool> submitPost({
    required BuildContext context,
    String? text,
    List<File>? imageFiles,
    File? videoFile,
    List<String>? hashtags,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = _validatePostData(
        text: text,
        imageFiles: imageFiles,
        videoFile: videoFile,
      );
      
      if (!validationResult.isValid) {
        _showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = _getUserData(context);
      
      // عرض مؤشر التحميل
      _showLoadingDialog(context, 'جاري نشر المنشور...');

      // إرسال المنشور
      await context.read<PostsCubit>().createPost(
        userId: userData['userId']!,
        userName: userData['userName']!,
        userImage: userData['userImage'] ?? '',
        text: text?.trim(),
        imageFiles: imageFiles,
        videoFile: videoFile,
        hashtags: hashtags,
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        _showSuccessDialog(
          context,
          title: 'تم نشر المنشور بنجاح!',
          message: 'تم نشر منشورك في المنتدى وهو متاح الآن للجميع',
        );
      }

      return true;
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ أثناء نشر المنشور: $e');
      }
      return false;
    }
  }

  /// نشر منشور في المنتدى (alias)
  static Future<bool> createForumPost({
    required BuildContext context,
    String? text,
    List<dynamic>? imageFiles,
    dynamic videoFile,
    List<String>? hashtags,
  }) async {
    try {
      // التحقق من وجود محتوى
      if ((text == null || text.trim().isEmpty) &&
          (imageFiles == null || imageFiles.isEmpty) &&
          videoFile == null) {
        _showValidationErrors(context, ['يرجى إضافة نص أو صورة أو فيديو للمنشور']);
        return false;
      }

      // عرض مؤشر التحميل
      _showLoadingDialog(context, 'جاري نشر المنشور...');

      // الحصول على بيانات المستخدم
      final userAccount = context.read<AuthCubit>().currentUser;
      final userId = userAccount?.id ?? '';
      final userName = userAccount?.name ?? 'مستخدم غير معروف';
      final userImage = userAccount?.image ?? '';

      // نشر المنشور
      await context.read<PostsCubit>().createPost(
        userId: userId,
        userName: userName,
        userImage: userImage,
        text: text?.isNotEmpty == true ? text : null,
        imageFiles: imageFiles?.isNotEmpty == true ? List<File>.from(imageFiles!) : null,
        videoFile: videoFile,
        hashtags: hashtags?.isNotEmpty == true ? hashtags : null,
      );

      // إخفاء مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        _showSuccessDialog(
          context,
          title: 'تم نشر المنشور بنجاح!',
          message: 'تم نشر منشورك في المنتدى الزراعي',
          details: text != null ? {'المحتوى': text.length > 50 ? '${text.substring(0, 50)}...' : text} : null,
        );
      }

      return true;
    } catch (e) {
      // إخفاء مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة الخطأ
      if (context.mounted) {
        _showErrorDialog(context, 'فشل في نشر المنشور: ${e.toString()}');
      }
      return false;
    }
  }

  /// حجز موعد مع المرشد
  /// [context] سياق التطبيق
  /// [consultationType] نوع الاستشارة
  /// [appointmentDate] تاريخ الموعد
  /// [appointmentTime] وقت الموعد
  /// [problemDescription] وصف المشكلة
  /// [userPhone] رقم هاتف المستخدم
  static Future<bool> bookAppointment({
    required BuildContext context,
    required String consultationType,
    required String appointmentDate,
    required String appointmentTime,
    required String problemDescription,
    String? userPhone,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = _validateAppointmentData(
        consultationType: consultationType,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        problemDescription: problemDescription,
      );
      
      if (!validationResult.isValid) {
        _showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = _getUserData(context);
      
      // عرض مؤشر التحميل
      _showLoadingDialog(context, 'جاري حجز الموعد...');

      // حجز الموعد
      await context.read<AdvisorCubit>().bookAppointment(
        userId: userData['userId']!,
        userName: userData['userName']!,
        userPhone: userPhone ?? userData['userPhone'] ?? '',
        advisorId: 'general_advisor',
        consultationType: consultationType,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        problemDescription: problemDescription.trim(),
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        _showSuccessDialog(
          context,
          title: 'تم حجز الموعد بنجاح!',
          message: 'تم حجز موعدك مع المرشد الزراعي وسيتم التواصل معك قريباً',
          details: {
            'نوع الاستشارة': consultationType,
            'التاريخ': appointmentDate,
            'الوقت': appointmentTime,
          },
        );
      }

      return true;
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (context.mounted) {
        try {
          Navigator.of(context).pop();
        } catch (_) {
          // تجاهل الخطأ إذا لم يكن هناك حوار مفتوح
        }
      }

      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ أثناء حجز الموعد: $e');
      }
      return false;
    }
  }

  /// الحصول على بيانات المستخدم
  static Map<String, String?> _getUserData(BuildContext context) {
    final userAccount = context.read<AuthCubit>().currentUser;
    return {
      'userId': userAccount?.id ?? 'guest_user_${DateTime.now().millisecondsSinceEpoch}',
      'userName': userAccount?.name ?? 'مستخدم ضيف',
      'userImage': userAccount?.image,
      'userPhone': userAccount?.phone,
    };
  }

  /// إرسال طلب مراقبة النبات
  /// [context] سياق التطبيق
  /// [plantType] نوع النبات
  /// [location] موقع المزرعة
  /// [monitoringType] نوع المراقبة
  /// [notes] ملاحظات إضافية
  /// [imageFiles] الصور المرفقة
  static Future<bool> submitPlantMonitoring({
    required BuildContext context,
    required String plantType,
    required String location,
    required String monitoringType,
    String? notes,
    List<File>? imageFiles,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = _validatePlantMonitoringData(
        plantType: plantType,
        location: location,
        monitoringType: monitoringType,
      );

      if (!validationResult.isValid) {
        _showValidationErrors(context, validationResult.errors);
        return false;
      }

      // الحصول على بيانات المستخدم
      final userData = _getUserData(context);

      // عرض مؤشر التحميل
      _showLoadingDialog(context, 'جاري إرسال طلب مراقبة النبات...');

      // تحضير وصف المشكلة مع تفاصيل المراقبة
      final String monitoringDescription = '''
طلب مراقبة نبات:
- نوع النبات: $plantType
- الموقع: $location
- نوع المراقبة: $monitoringType
${notes?.isNotEmpty == true ? '- ملاحظات: $notes' : ''}
''';

      // إرسال طلب المراقبة كاستشارة
      await context.read<AdvisorCubit>().createConsultation(
        userId: userData['userId']!,
        userName: userData['userName']!,
        userImage: userData['userImage'],
        advisorId: 'general_advisor',
        cropType: plantType.trim(),
        problemDescription: monitoringDescription.trim(),
        area: location.trim(),
        imageFiles: imageFiles,
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // تأخير بسيط لضمان إغلاق الحوار السابق
      await Future.delayed(const Duration(milliseconds: 300));

      // عرض رسالة النجاح
      if (context.mounted) {
        _showSuccessDialog(
          context,
          title: 'تم إرسال طلب مراقبة النبات بنجاح!',
          message: 'تم تقديم طلبك للمرشد الزراعي وسيتم التواصل معك قريباً',
          details: {
            'نوع النبات': plantType,
            'الموقع': location,
            'نوع المراقبة': monitoringType,
          },
        );
      }

      return true;
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (context.mounted) {
        try {
          Navigator.of(context).pop();
        } catch (_) {
          // تجاهل الخطأ إذا لم يكن هناك حوار مفتوح
        }
      }

      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ أثناء إرسال طلب مراقبة النبات: $e');
      }
      return false;
    }
  }

  /// التحقق من صحة بيانات الاستشارة
  static ValidationResult _validateConsultationData({
    required String cropType,
    required String problemDescription,
  }) {
    final errors = <String>[];

    if (cropType.trim().isEmpty) {
      errors.add('يرجى تحديد نوع المحصول');
    }

    if (problemDescription.trim().isEmpty) {
      errors.add('يرجى وصف المشكلة');
    } else if (problemDescription.trim().length < 10) {
      errors.add('وصف المشكلة يجب أن يكون 10 أحرف على الأقل');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات المنشور
  static ValidationResult _validatePostData({
    String? text,
    List<File>? imageFiles,
    File? videoFile,
  }) {
    final errors = <String>[];

    // يجب أن يحتوي المنشور على نص أو صورة أو فيديو
    if ((text == null || text.trim().isEmpty) &&
        (imageFiles == null || imageFiles.isEmpty) &&
        videoFile == null) {
      errors.add('يرجى إضافة نص أو صورة أو فيديو للمنشور');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات الموعد
  static ValidationResult _validateAppointmentData({
    required String consultationType,
    required String appointmentDate,
    required String appointmentTime,
    required String problemDescription,
  }) {
    final errors = <String>[];

    if (consultationType.trim().isEmpty) {
      errors.add('يرجى تحديد نوع الاستشارة');
    }

    if (appointmentDate.trim().isEmpty) {
      errors.add('يرجى تحديد تاريخ الموعد');
    }

    if (appointmentTime.trim().isEmpty) {
      errors.add('يرجى تحديد وقت الموعد');
    }

    if (problemDescription.trim().isEmpty) {
      errors.add('يرجى وصف المشكلة');
    } else if (problemDescription.trim().length < 10) {
      errors.add('وصف المشكلة يجب أن يكون 10 أحرف على الأقل');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// التحقق من صحة بيانات مراقبة النبات
  static ValidationResult _validatePlantMonitoringData({
    required String plantType,
    required String location,
    required String monitoringType,
  }) {
    final errors = <String>[];

    // التحقق من نوع النبات
    if (plantType.trim().isEmpty) {
      errors.add('يرجى إدخال نوع النبات');
    } else if (plantType.trim().length < 2) {
      errors.add('نوع النبات يجب أن يكون أكثر من حرفين');
    }

    // التحقق من الموقع
    if (location.trim().isEmpty) {
      errors.add('يرجى إدخال موقع المزرعة');
    } else if (location.trim().length < 3) {
      errors.add('موقع المزرعة يجب أن يكون أكثر من 3 أحرف');
    }

    // التحقق من نوع المراقبة
    if (monitoringType.trim().isEmpty) {
      errors.add('يرجى تحديد نوع المراقبة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }



  /// عرض أخطاء التحقق من صحة البيانات
  static void _showValidationErrors(BuildContext context, List<String> errors) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.orange.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'تحقق من البيانات',
                style: TextStyle(fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'يرجى إصلاح الأخطاء التالية:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              ...errors.map((error) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        error,
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('حسناً'),
            ),
          ],
        );
      },
    );
  }

  /// عرض مؤشر التحميل
  static void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  /// عرض رسالة النجاح مع إمكانية التوجيه
  static void _showSuccessDialog(
    BuildContext context, {
    required String title,
    required String message,
    Map<String, String>? details,
    VoidCallback? onNavigateToAdvisor,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message,
                style: const TextStyle(fontSize: 16),
              ),
              if (details != null && details.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                const Text(
                  'تفاصيل الطلب:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                ...details.entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${entry.key}: ',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('ممتاز'),
            ),
            if (onNavigateToAdvisor != null)
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onNavigateToAdvisor();
                },
                icon: const Icon(Icons.person, size: 16),
                label: const Text('أسئلة المرشد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        );
      },
    );
  }

  /// عرض رسالة الخطأ
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.error,
                color: Colors.red.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'حدث خطأ',
                style: TextStyle(fontSize: 18),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('حسناً'),
            ),
          ],
        );
      },
    );
  }
}

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });
}
