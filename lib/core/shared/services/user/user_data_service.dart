import 'package:agriculture/data/datasources/local/shared_prefs.dart';
import 'package:agriculture/core/constants/unified_submission_constants.dart';

/// خدمة إدارة بيانات المستخدم المشتركة
/// 
/// وفق المعيار #3: التركيز على إنشاء الملفات التشاركية أولاً
/// وفق المعيار #5: تجزئة الوظائف والمهام في وحدات صغيرة مستقلة
/// وفق المعيار #12: إضافة تعليقات باللغة العربية لكل وظيفة
class UserDataService {
  
  /// الحصول على بيانات المستخدم الحالي
  /// 
  /// الإرجاع: [UserData] يحتوي على بيانات المستخدم
  static UserData getCurrentUserData() {
    return UserData(
      userId: SharedPrefs.getString(UnifiedSubmissionConstants.userIdKey) ?? 
               UnifiedSubmissionConstants.anonymousUserId,
      userName: SharedPrefs.getString(UnifiedSubmissionConstants.userNameKey) ?? 
                UnifiedSubmissionConstants.defaultUserName,
      userImage: SharedPrefs.getString(UnifiedSubmissionConstants.userImageKey),
      userPhone: SharedPrefs.getString(UnifiedSubmissionConstants.userPhoneKey),
    );
  }

  /// الحصول على معرف المستخدم الحالي
  /// 
  /// الإرجاع: [String] معرف المستخدم
  static String getCurrentUserId() {
    return SharedPrefs.getString(UnifiedSubmissionConstants.userIdKey) ?? 
           UnifiedSubmissionConstants.anonymousUserId;
  }

  /// الحصول على اسم المستخدم الحالي
  /// 
  /// الإرجاع: [String] اسم المستخدم
  static String getCurrentUserName() {
    return SharedPrefs.getString(UnifiedSubmissionConstants.userNameKey) ?? 
           UnifiedSubmissionConstants.defaultUserName;
  }

  /// الحصول على صورة المستخدم الحالي
  /// 
  /// الإرجاع: [String?] رابط صورة المستخدم أو null
  static String? getCurrentUserImage() {
    return SharedPrefs.getString(UnifiedSubmissionConstants.userImageKey);
  }

  /// الحصول على رقم هاتف المستخدم الحالي
  /// 
  /// الإرجاع: [String?] رقم الهاتف أو null
  static String? getCurrentUserPhone() {
    return SharedPrefs.getString(UnifiedSubmissionConstants.userPhoneKey);
  }

  /// التحقق من وجود بيانات المستخدم
  /// 
  /// الإرجاع: [bool] true إذا كانت البيانات موجودة
  static bool hasUserData() {
    final userId = SharedPrefs.getString(UnifiedSubmissionConstants.userIdKey);
    return userId != null && userId.isNotEmpty && userId != UnifiedSubmissionConstants.anonymousUserId;
  }

  /// التحقق من صحة بيانات المستخدم
  /// 
  /// الإرجاع: [UserDataValidation] نتيجة التحقق
  static UserDataValidation validateUserData() {
    final userData = getCurrentUserData();
    final issues = <String>[];

    // التحقق من معرف المستخدم
    if (userData.userId == UnifiedSubmissionConstants.anonymousUserId) {
      issues.add('لم يتم تسجيل الدخول');
    }

    // التحقق من اسم المستخدم
    if (userData.userName == UnifiedSubmissionConstants.defaultUserName) {
      issues.add('لم يتم تحديد اسم المستخدم');
    }

    // التحقق من رقم الهاتف
    if (userData.userPhone == null || userData.userPhone!.isEmpty) {
      issues.add('لم يتم تحديد رقم الهاتف');
    }

    return UserDataValidation(
      isValid: issues.isEmpty,
      issues: issues,
      userData: userData,
    );
  }

  /// تحديث بيانات المستخدم في التخزين المحلي
  /// 
  /// المعلمات:
  /// - [userData]: بيانات المستخدم الجديدة
  /// 
  /// الإرجاع: [Future<bool>] true إذا تم التحديث بنجاح
  static Future<bool> updateUserData(UserData userData) async {
    try {
      await SharedPrefs.setString(UnifiedSubmissionConstants.userIdKey, userData.userId);
      await SharedPrefs.setString(UnifiedSubmissionConstants.userNameKey, userData.userName);
      
      if (userData.userImage != null) {
        await SharedPrefs.setString(UnifiedSubmissionConstants.userImageKey, userData.userImage!);
      }
      
      if (userData.userPhone != null) {
        await SharedPrefs.setString(UnifiedSubmissionConstants.userPhoneKey, userData.userPhone!);
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// مسح بيانات المستخدم من التخزين المحلي
  /// 
  /// الإرجاع: [Future<bool>] true إذا تم المسح بنجاح
  static Future<bool> clearUserData() async {
    try {
      await SharedPrefs.remove(UnifiedSubmissionConstants.userIdKey);
      await SharedPrefs.remove(UnifiedSubmissionConstants.userNameKey);
      await SharedPrefs.remove(UnifiedSubmissionConstants.userImageKey);
      await SharedPrefs.remove(UnifiedSubmissionConstants.userPhoneKey);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على بيانات المستخدم كـ Map للاستخدام في الطلبات
  /// 
  /// الإرجاع: [Map<String, String?>] بيانات المستخدم
  static Map<String, String?> getUserDataAsMap() {
    final userData = getCurrentUserData();
    return {
      'userId': userData.userId,
      'userName': userData.userName,
      'userImage': userData.userImage,
      'userPhone': userData.userPhone,
    };
  }
}

/// نموذج بيانات المستخدم
/// 
/// وفق المعيار #2: Clean Architecture - نموذج البيانات
class UserData {
  /// معرف المستخدم
  final String userId;
  
  /// اسم المستخدم
  final String userName;
  
  /// صورة المستخدم
  final String? userImage;
  
  /// رقم هاتف المستخدم
  final String? userPhone;

  /// إنشاء بيانات المستخدم
  /// 
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [userName]: اسم المستخدم
  /// - [userImage]: صورة المستخدم (اختياري)
  /// - [userPhone]: رقم الهاتف (اختياري)
  const UserData({
    required this.userId,
    required this.userName,
    this.userImage,
    this.userPhone,
  });

  /// إنشاء نسخة محدثة من بيانات المستخدم
  /// 
  /// المعلمات:
  /// - [userId]: معرف المستخدم الجديد
  /// - [userName]: اسم المستخدم الجديد
  /// - [userImage]: صورة المستخدم الجديدة
  /// - [userPhone]: رقم الهاتف الجديد
  /// 
  /// الإرجاع: [UserData] نسخة محدثة
  UserData copyWith({
    String? userId,
    String? userName,
    String? userImage,
    String? userPhone,
  }) {
    return UserData(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      userPhone: userPhone ?? this.userPhone,
    );
  }

  /// تحويل بيانات المستخدم إلى Map
  /// 
  /// الإرجاع: [Map<String, dynamic>] بيانات المستخدم
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userImage': userImage,
      'userPhone': userPhone,
    };
  }

  /// إنشاء بيانات المستخدم من Map
  /// 
  /// المعلمات:
  /// - [map]: البيانات في شكل Map
  /// 
  /// الإرجاع: [UserData] بيانات المستخدم
  factory UserData.fromMap(Map<String, dynamic> map) {
    return UserData(
      userId: map['userId'] ?? UnifiedSubmissionConstants.anonymousUserId,
      userName: map['userName'] ?? UnifiedSubmissionConstants.defaultUserName,
      userImage: map['userImage'],
      userPhone: map['userPhone'],
    );
  }

  @override
  String toString() {
    return 'UserData(userId: $userId, userName: $userName, userImage: $userImage, userPhone: $userPhone)';
  }
}

/// نتيجة التحقق من صحة بيانات المستخدم
/// 
/// وفق المعيار #2: Clean Architecture - نموذج البيانات
class UserDataValidation {
  /// هل البيانات صحيحة
  final bool isValid;
  
  /// قائمة المشاكل
  final List<String> issues;
  
  /// بيانات المستخدم
  final UserData userData;

  /// إنشاء نتيجة التحقق
  /// 
  /// المعلمات:
  /// - [isValid]: هل البيانات صحيحة
  /// - [issues]: قائمة المشاكل
  /// - [userData]: بيانات المستخدم
  const UserDataValidation({
    required this.isValid,
    required this.issues,
    required this.userData,
  });
}
