import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تخزين الإشعارات في LocalStorage
/// 
/// توفر هذه الخدمة إمكانية حفظ واسترجاع الإشعارات محلياً
/// لضمان عدم فقدان الإشعارات عند إعادة تشغيل التطبيق
class NotificationStorageService {
  static const String _notificationsKey = 'stored_notifications';
  static const String _unreadCountKey = 'unread_notifications_count';
  static const String _soundEnabledKey = 'notification_sound_enabled';

  /// حفظ إشعار جديد
  /// [notification] بيانات الإشعار المراد حفظه
  static Future<void> saveNotification(Map<String, dynamic> notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // إضافة timestamp للإشعار
      notification['timestamp'] = DateTime.now().toIso8601String();
      notification['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      notification['isRead'] = false;
      
      // استرجاع الإشعارات الحالية
      final notifications = await getStoredNotifications();
      
      // إضافة الإشعار الجديد في المقدمة
      notifications.insert(0, notification);
      
      // الاحتفاظ بآخر 100 إشعار فقط
      if (notifications.length > 100) {
        notifications.removeRange(100, notifications.length);
      }
      
      // حفظ القائمة المحدثة
      final jsonString = jsonEncode(notifications);
      await prefs.setString(_notificationsKey, jsonString);
      
      // تحديث عداد الإشعارات غير المقروءة
      await _updateUnreadCount();
      
    } catch (e) {
      debugPrint('خطأ في حفظ الإشعار: $e');
    }
  }

  /// استرجاع جميع الإشعارات المحفوظة
  static Future<List<Map<String, dynamic>>> getStoredNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_notificationsKey);

      if (jsonString == null) return [];

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.cast<Map<String, dynamic>>();

    } catch (e) {
      debugPrint('خطأ في استرجاع الإشعارات: $e');
      return [];
    }
  }

  /// تحديد إشعار كمقروء
  /// [notificationId] معرف الإشعار
  static Future<void> markAsRead(String notificationId) async {
    try {
      final notifications = await getStoredNotifications();
      
      for (var notification in notifications) {
        if (notification['id'] == notificationId) {
          notification['isRead'] = true;
          break;
        }
      }
      
      // حفظ التحديث
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(notifications);
      await prefs.setString(_notificationsKey, jsonString);
      
      // تحديث عداد الإشعارات غير المقروءة
      await _updateUnreadCount();
      
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  static Future<void> markAllAsRead() async {
    try {
      final notifications = await getStoredNotifications();

      for (var notification in notifications) {
        notification['isRead'] = true;
      }

      // حفظ التحديث
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(notifications);
      await prefs.setString(_notificationsKey, jsonString);

      // إعادة تعيين العداد
      await prefs.setInt(_unreadCountKey, 0);

    } catch (e) {
      debugPrint('خطأ في تحديد جميع الإشعارات كمقروءة: $e');
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static Future<int> getUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_unreadCountKey) ?? 0;
    } catch (e) {
      debugPrint('خطأ في استرجاع عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// تحديث عداد الإشعارات غير المقروءة
  static Future<void> _updateUnreadCount() async {
    try {
      final notifications = await getStoredNotifications();
      final unreadCount = notifications.where((n) => n['isRead'] == false).length;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_unreadCountKey, unreadCount);

    } catch (e) {
      debugPrint('خطأ في تحديث عداد الإشعارات: $e');
    }
  }

  /// حذف إشعار محدد
  /// [notificationId] معرف الإشعار المراد حذفه
  static Future<void> deleteNotification(String notificationId) async {
    try {
      final notifications = await getStoredNotifications();
      notifications.removeWhere((n) => n['id'] == notificationId);
      
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(notifications);
      await prefs.setString(_notificationsKey, jsonString);
      
      await _updateUnreadCount();

    } catch (e) {
      debugPrint('خطأ في حذف الإشعار: $e');
    }
  }

  /// حذف جميع الإشعارات
  static Future<void> clearAllNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_notificationsKey);
      await prefs.setInt(_unreadCountKey, 0);
    } catch (e) {
      debugPrint('خطأ في حذف جميع الإشعارات: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل الصوت
  /// [enabled] حالة تفعيل الصوت
  static Future<void> setSoundEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_soundEnabledKey, enabled);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الصوت: $e');
    }
  }

  /// التحقق من تفعيل الصوت
  static Future<bool> isSoundEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_soundEnabledKey) ?? true; // مفعل افتراضياً
    } catch (e) {
      debugPrint('خطأ في استرجاع إعدادات الصوت: $e');
      return true;
    }
  }

  /// الحصول على الإشعارات غير المقروءة فقط
  static Future<List<Map<String, dynamic>>> getUnreadNotifications() async {
    try {
      final notifications = await getStoredNotifications();
      return notifications.where((n) => n['isRead'] == false).toList();
    } catch (e) {
      debugPrint('خطأ في استرجاع الإشعارات غير المقروءة: $e');
      return [];
    }
  }

  /// إنشاء إشعار للمزارع عند رد المرشد
  /// [farmerUserId] معرف المزارع
  /// [advisorName] اسم المرشد
  /// [consultationId] معرف الاستشارة
  /// [response] نص الرد
  static Future<void> createFarmerNotification({
    required String farmerUserId,
    required String advisorName,
    required String consultationId,
    required String response,
  }) async {
    final notification = {
      'type': 'advisor_response',
      'title': 'رد جديد من المرشد الزراعي',
      'body': 'رد $advisorName على استشارتك',
      'farmerUserId': farmerUserId,
      'advisorName': advisorName,
      'consultationId': consultationId,
      'response': response.length > 100 ? '${response.substring(0, 100)}...' : response,
      'priority': 'high',
    };
    
    await saveNotification(notification);
  }
}
