/// مساعد تحويل الأنواع بأمان
///
/// يوفر دوال لتحويل الأنواع المختلفة بأمان لتجنب أخطاء التحويل
class TypeConversionHelper {
  /// تحويل قيمة إلى double بأمان
  static double? toDouble(dynamic value) {
    if (value == null) return null;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);

    return null;
  }

  /// تحويل قيمة إلى int بأمان
  static int? toInt(dynamic value) {
    if (value == null) return null;

    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);

    return null;
  }

  /// تحويل قيمة إلى String بأمان
  static String? toStringValue(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  /// تحويل قيمة إلى bool بأمان
  static bool? toBool(dynamic value) {
    if (value == null) return null;

    if (value is bool) return value;
    if (value is int) return value != 0;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
    }

    return null;
  }

  /// تحويل timestamp إلى DateTime بأمان
  static DateTime? toDateTime(dynamic value) {
    if (value == null) return null;

    try {
      if (value is DateTime) return value;
      if (value is String) return DateTime.parse(value);
      if (value is int) return DateTime.fromMillisecondsSinceEpoch(value * 1000);
      if (value is double) return DateTime.fromMillisecondsSinceEpoch((value.toInt()) * 1000);
    } catch (e) {
      return null;
    }

    return null;
  }

  /// تحويل قيمة إلى List بأمان
  static List<T>? toList<T>(dynamic value) {
    if (value == null) return null;

    if (value is List<T>) return value;
    if (value is List) return value.cast<T>();

    return null;
  }

  /// تحويل قيمة إلى Map بأمان
  static Map<String, dynamic>? toMap(dynamic value) {
    if (value == null) return null;

    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);

    return null;
  }
}
