import '../../constants/app_constants.dart';

/// خدمة التحقق من صحة النماذج
///
/// توفر هذه الخدمة دوال للتحقق من صحة المدخلات المختلفة مثل البريد الإلكتروني وكلمة المرور ورقم الهاتف
class FormValidators {
  /// التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (!RegExp(AppConstants.emailRegex).hasMatch(value)) {
      return AppConstants.validationEmail;
    }
    return null;
  }

  /// التحقق من صحة كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (value.length < AppConstants.minPasswordLength) {
      return AppConstants.validationPassword;
    }
    return null;
  }

  /// التحقق من تطابق كلمتي المرور
  static String? validatePasswordConfirmation(String? value, String password) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (value != password) {
      return AppConstants.validationPasswordMatch;
    }
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (!RegExp(AppConstants.phoneRegex).hasMatch(value)) {
      return AppConstants.validationPhone;
    }
    return null;
  }

  /// التحقق من صحة الاسم
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (value.length < 2) {
      return AppConstants.validationName;
    }
    return null;
  }

  /// التحقق من صحة اسم المستخدم
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (value.length < AppConstants.minUsernameLength ||
        value.length > AppConstants.maxUsernameLength) {
      return AppConstants.validationUsername;
    }
    return null;
  }

  /// التحقق من صحة رمز التحقق
  static String? validateOtp(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.validationRequired;
    }
    if (value.length != AppConstants.otpLength) {
      return AppConstants.validationOtp;
    }
    return null;
  }

  /// التحقق من صحة نوع المحصول
  static String? validateCropType(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى اختيار نوع المحصول';
    }
    return null;
  }

  /// التحقق من صحة وصف المشكلة الزراعية
  static String? validateProblemDescription(String? value, {int minLength = 20}) {
    if (value == null || value.isEmpty) {
      return 'يرجى كتابة وصف المشكلة';
    }
    if (value.trim().length < minLength) {
      return 'وصف المشكلة قصير جداً، يرجى إضافة تفاصيل أكثر ($minLength حرف على الأقل)';
    }
    if (value.trim().length > 500) {
      return 'وصف المشكلة طويل جداً، يرجى اختصاره (500 حرف كحد أقصى)';
    }
    return null;
  }

  /// التحقق من صحة المساحة الزراعية
  static String? validateArea(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال المساحة المتضررة';
    }

    final area = double.tryParse(value.trim());
    if (area == null) {
      return 'يرجى إدخال رقم صحيح للمساحة';
    }

    if (area <= 0) {
      return 'يرجى إدخال مساحة أكبر من صفر';
    }

    if (area > 1000000) {
      return 'المساحة كبيرة جداً، يرجى التحقق من الرقم';
    }

    return null;
  }

  /// التحقق من صحة رد المرشد الزراعي
  static String? validateAdvisorResponse(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى كتابة الرد';
    }

    if (value.trim().length < 10) {
      return 'الرد قصير جداً، يرجى كتابة رد مفصل أكثر (10 أحرف على الأقل)';
    }

    if (value.trim().length > 1000) {
      return 'الرد طويل جداً، يرجى اختصاره (1000 حرف كحد أقصى)';
    }

    return null;
  }

  /// التحقق من صحة التوصيات الزراعية
  static String? validateRecommendations(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 5) {
        return 'التوصيات قصيرة جداً، يرجى كتابة توصيات مفصلة أكثر';
      }

      if (value.trim().length > 500) {
        return 'التوصيات طويلة جداً، يرجى اختصارها (500 حرف كحد أقصى)';
      }
    }

    return null; // التوصيات اختيارية
  }

  /// التحقق من صحة تقييم الاستشارة
  static String? validateRating(double? value) {
    if (value == null) {
      return 'يرجى اختيار التقييم';
    }

    if (value < 1 || value > 5) {
      return 'التقييم يجب أن يكون بين 1 و 5 نجوم';
    }

    return null;
  }

  /// التحقق من صحة تعليق التقييم
  static String? validateRatingComment(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 3) {
        return 'التعليق قصير جداً';
      }

      if (value.trim().length > 200) {
        return 'التعليق طويل جداً، يرجى اختصاره (200 حرف كحد أقصى)';
      }
    }

    return null; // التعليق اختياري
  }
}
