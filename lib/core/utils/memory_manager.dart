import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/material.dart';

/// مدير الذاكرة لمنع خروج التطبيق المفاجئ
/// 
/// يراقب استخدام الذاكرة ويحسن الأداء لمنع crash التطبيق
class MemoryManager {
  static MemoryManager? _instance;
  static MemoryManager get instance => _instance ??= MemoryManager._();
  
  MemoryManager._();
  
  Timer? _memoryCheckTimer;
  bool _isMonitoring = false;
  
  /// بدء مراقبة الذاكرة
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    debugPrint('🔍 بدء مراقبة الذاكرة...');
    
    // فحص الذاكرة كل 30 ثانية
    _memoryCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkMemoryUsage();
    });
  }
  
  /// إيقاف مراقبة الذاكرة
  void stopMonitoring() {
    _isMonitoring = false;
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
    debugPrint('⏹️ تم إيقاف مراقبة الذاكرة');
  }
  
  /// فحص استخدام الذاكرة
  void _checkMemoryUsage() {
    if (!_isMonitoring) return;
    
    try {
      // تنظيف الذاكرة بشكل دوري
      _performMemoryCleanup();
      
      debugPrint('🧹 تم تنظيف الذاكرة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في فحص الذاكرة: $e');
    }
  }
  
  /// تنظيف الذاكرة
  void _performMemoryCleanup() {
    // تنظيف cache الصور
    PaintingBinding.instance.imageCache.clear();
    
    // تنظيف cache النصوص
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    // تشغيل garbage collector
    if (!kIsWeb) {
      // في Android/iOS فقط
      try {
        // تنظيف الذاكرة المؤقتة
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      } catch (e) {
        // تجاهل الأخطاء
      }
    }
  }
  
  /// تنظيف فوري للذاكرة
  void forceCleanup() {
    debugPrint('🧹 تنظيف فوري للذاكرة...');
    _performMemoryCleanup();
  }
  
  /// تحسين الأداء للصفحات الثقيلة
  void optimizeForHeavyPage() {
    debugPrint('⚡ تحسين الأداء للصفحة الثقيلة...');
    
    // تقليل cache الصور
    PaintingBinding.instance.imageCache.maximumSize = 50;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
    
    // تنظيف فوري
    forceCleanup();
  }
  
  /// استعادة الإعدادات الافتراضية
  void restoreDefaultSettings() {
    debugPrint('🔄 استعادة إعدادات الذاكرة الافتراضية...');
    
    // استعادة cache الصور الافتراضي
    PaintingBinding.instance.imageCache.maximumSize = 1000;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 100 << 20; // 100 MB
  }
  
  /// معلومات الذاكرة الحالية
  Map<String, dynamic> getMemoryInfo() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    return {
      'imageCacheSize': imageCache.currentSize,
      'imageCacheMaxSize': imageCache.maximumSize,
      'imageCacheSizeBytes': imageCache.currentSizeBytes,
      'imageCacheMaxSizeBytes': imageCache.maximumSizeBytes,
      'isMonitoring': _isMonitoring,
    };
  }
  
  /// تسجيل معلومات الذاكرة
  void logMemoryInfo() {
    final info = getMemoryInfo();
    debugPrint('📊 معلومات الذاكرة:');
    debugPrint('   - حجم cache الصور: ${info['imageCacheSize']}/${info['imageCacheMaxSize']}');
    debugPrint('   - حجم البايتات: ${(info['imageCacheSizeBytes'] / (1024 * 1024)).toStringAsFixed(2)} MB');
    debugPrint('   - المراقبة نشطة: ${info['isMonitoring']}');
  }
}

/// Mixin لإدارة الذاكرة في الصفحات
mixin MemoryManagementMixin {

  /// تحسين الذاكرة عند دخول الصفحة
  void optimizeMemoryOnEnter() {
    MemoryManager.instance.optimizeForHeavyPage();
  }

  /// استعادة الإعدادات عند الخروج من الصفحة
  void restoreMemoryOnExit() {
    MemoryManager.instance.restoreDefaultSettings();
  }

  /// تنظيف الذاكرة عند التخلص من الصفحة
  void cleanupMemoryOnDispose() {
    MemoryManager.instance.forceCleanup();
  }
}

/// ثوابت الأنماط المحسنة للأداء
class OptimizedStyles {
  // TextStyles محسنة مع const
  static const TextStyle titleStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.black87,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Colors.black54,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontSize: 14,
    color: Colors.black87,
    height: 1.4,
  );

  static const TextStyle captionStyle = TextStyle(
    fontSize: 12,
    color: Colors.grey,
  );

  // Padding محسن مع const
  static const EdgeInsets smallPadding = EdgeInsets.all(8.0);
  static const EdgeInsets mediumPadding = EdgeInsets.all(16.0);
  static const EdgeInsets largePadding = EdgeInsets.all(24.0);

  // BorderRadius محسن مع const
  static const BorderRadius smallRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius mediumRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(16));
}

/// خدمة الإشعارات للاستشارات الجديدة
class ConsultationNotificationService {
  static ConsultationNotificationService? _instance;
  static ConsultationNotificationService get instance => _instance ??= ConsultationNotificationService._();

  ConsultationNotificationService._();

  /// عرض إشعار للاستشارة الجديدة
  void showNewConsultationNotification(BuildContext context, {
    required String farmerName,
    required String cropType,
    VoidCallback? onTap,
  }) {
    // إشعار منبثق جميل
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Colors.green[50],
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  '🔔 استشارة جديدة!',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green[100]!, Colors.green[50]!],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.person, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'المزارع: $farmerName',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.agriculture, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'المحصول: $cropType',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.access_time, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'الآن',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'يحتاج المزارع لاستشارتك الخبيرة. اضغط "عرض الاستشارة" للرد عليه.',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text('لاحقاً'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.visibility),
              label: const Text('عرض الاستشارة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                onTap?.call();
              },
            ),
          ],
        );
      },
    );

    // تشغيل اهتزاز خفيف
    HapticFeedback.lightImpact();
  }

  /// عرض إشعار سريع (SnackBar)
  void showQuickNotification(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.notifications,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () {
            // يمكن إضافة منطق للانتقال للاستشارة
          },
        ),
      ),
    );
  }
}

/// خدمة مراقبة استقرار التطبيق
class AppStabilityService {
  static AppStabilityService? _instance;
  static AppStabilityService get instance => _instance ??= AppStabilityService._();

  AppStabilityService._();
  
  Timer? _stabilityTimer;
  int _errorCount = 0;
  DateTime? _lastError;
  
  /// بدء مراقبة استقرار التطبيق
  void startMonitoring() {
    debugPrint('🛡️ بدء مراقبة استقرار التطبيق...');
    
    // مراقبة الأخطاء كل دقيقة
    _stabilityTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkAppStability();
    });
    
    // بدء مراقبة الذاكرة أيضاً
    MemoryManager.instance.startMonitoring();
  }
  
  /// إيقاف المراقبة
  void stopMonitoring() {
    _stabilityTimer?.cancel();
    _stabilityTimer = null;
    MemoryManager.instance.stopMonitoring();
    debugPrint('⏹️ تم إيقاف مراقبة الاستقرار');
  }
  
  /// تسجيل خطأ
  void reportError(dynamic error, StackTrace? stackTrace) {
    _errorCount++;
    _lastError = DateTime.now();
    
    debugPrint('❌ تم تسجيل خطأ: $error');
    debugPrint('📊 عدد الأخطاء: $_errorCount');
    
    // إذا كان هناك أخطاء كثيرة، قم بتنظيف الذاكرة
    if (_errorCount > 5) {
      debugPrint('🧹 تنظيف الذاكرة بسبب كثرة الأخطاء...');
      MemoryManager.instance.forceCleanup();
      _errorCount = 0; // إعادة تعيين العداد
    }
  }
  
  /// فحص استقرار التطبيق
  void _checkAppStability() {
    // إذا لم تكن هناك أخطاء في آخر 5 دقائق، قم بتنظيف بسيط
    if (_lastError == null || 
        DateTime.now().difference(_lastError!) > const Duration(minutes: 5)) {
      MemoryManager.instance.forceCleanup();
      _errorCount = 0;
    }
  }
  
  /// الحصول على إحصائيات الاستقرار
  Map<String, dynamic> getStabilityStats() {
    return {
      'errorCount': _errorCount,
      'lastError': _lastError?.toIso8601String(),
      'memoryInfo': MemoryManager.instance.getMemoryInfo(),
    };
  }
}
