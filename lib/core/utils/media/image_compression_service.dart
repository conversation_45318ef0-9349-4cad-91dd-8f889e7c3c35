import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:cross_file/cross_file.dart';

import '../../constants/app_constants.dart';
import '../logging/logger_service.dart';

/// خدمة ضغط الصور
///
/// توفر هذه الخدمة وظائف لضغط الصور قبل رفعها لتقليل حجمها وتحسين الأداء
class ImageCompressionService {
  /// ضغط صورة
  ///
  /// المعلمات:
  /// - [file]: ملف الصورة المراد ضغطه
  /// - [quality]: جودة الصورة بعد الضغط (0-100)
  /// - [maxWidth]: العرض الأقصى للصورة بعد الضغط
  /// - [maxHeight]: الارتفاع الأقصى للصورة بعد الضغط
  ///
  /// يعيد ملف الصورة المضغوطة
  static Future<File?> compressImage({
    required File file,
    int quality = AppConstants.defaultImageQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // التحقق من وجود الملف
      if (!await file.exists()) {
        LoggerService.error(
          'الملف غير موجود: ${file.path}',
          tag: 'ImageCompressionService',
        );
        return null;
      }

      // الحصول على معلومات الملف
      final String fileName = path.basename(file.path);
      final String extension = path.extension(file.path).toLowerCase();

      // التحقق من أن الملف هو صورة
      if (!['.jpg', '.jpeg', '.png', '.webp', '.heic'].contains(extension)) {
        LoggerService.warning(
          'نوع الملف غير مدعوم للضغط: $extension',
          tag: 'ImageCompressionService',
        );
        return file; // إرجاع الملف الأصلي إذا كان النوع غير مدعوم
      }

      // الحصول على مسار مؤقت للصورة المضغوطة
      final Directory tempDir = await getTemporaryDirectory();
      final String targetPath = path.join(
        tempDir.path,
        'compressed_${const Uuid().v4()}$extension',
      );

      LoggerService.info(
        'بدء ضغط الصورة: $fileName',
        tag: 'ImageCompressionService',
      );

      // ضغط الصورة - استخدام الواجهة البرمجية الصحيحة للإصدار 2.1.0
      final XFile? compressedXFile = await FlutterImageCompress.compressAndGetFile(
        file.path,
        targetPath,
        quality: quality,
        minWidth: maxWidth ?? 1080, // العرض الافتراضي
        minHeight: maxHeight ?? 1920, // الارتفاع الافتراضي
      );

      // تحويل XFile إلى File
      final File? compressedFile = compressedXFile != null ? File(compressedXFile.path) : null;

      if (compressedFile == null) {
        LoggerService.error(
          'فشل ضغط الصورة: $fileName',
          tag: 'ImageCompressionService',
        );
        return file; // إرجاع الملف الأصلي في حالة الفشل
      }

      // مقارنة حجم الملف قبل وبعد الضغط
      final int originalSize = await file.length();
      final int compressedSize = await compressedFile.length();
      final double compressionRatio = originalSize / compressedSize;

      LoggerService.info(
        'تم ضغط الصورة بنجاح: $fileName، نسبة الضغط: ${compressionRatio.toStringAsFixed(2)}x، '
        'الحجم الأصلي: ${(originalSize / 1024).toStringAsFixed(2)} كيلوبايت، '
        'الحجم المضغوط: ${(compressedSize / 1024).toStringAsFixed(2)} كيلوبايت',
        tag: 'ImageCompressionService',
      );

      return compressedFile;
    } catch (e) {
      LoggerService.error(
        'خطأ في ضغط الصورة',
        error: e,
        tag: 'ImageCompressionService',
      );
      return file; // إرجاع الملف الأصلي في حالة حدوث خطأ
    }
  }

  /// ضغط قائمة من الصور
  ///
  /// المعلمات:
  /// - [files]: قائمة ملفات الصور المراد ضغطها
  /// - [quality]: جودة الصور بعد الضغط (0-100)
  /// - [maxWidth]: العرض الأقصى للصور بعد الضغط
  /// - [maxHeight]: الارتفاع الأقصى للصور بعد الضغط
  ///
  /// يعيد قائمة ملفات الصور المضغوطة
  static Future<List<File>> compressImages({
    required List<File> files,
    int quality = AppConstants.defaultImageQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final List<File> compressedFiles = [];

    for (final File file in files) {
      final File? compressedFile = await compressImage(
        file: file,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (compressedFile != null) {
        compressedFiles.add(compressedFile);
      }
    }

    return compressedFiles;
  }
}
