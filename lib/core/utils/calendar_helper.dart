import '../../data/models/landmarks/crest_months.dart';
import '../../data/models/landmarks/gorian_months.dart';
import '../../data/models/landmarks/hemiari_months.dart';
import '../../presentation/bloc/landmarks/information_cubit.dart';

/// أنواع التقويمات المدعومة
enum CalendarType {
  /// التقويم الميلادي
  gregorian,

  /// التقويم الكريستي
  crest,

  /// التقويم الحميري
  himyar,
}

/// مساعد التقويم
///
/// يوفر وظائف مساعدة للتعامل مع التقويمات المختلفة
class CalendarHelper {
  /// الحصول على اسم الشهر من التاريخ حسب نوع التقويم
  ///
  /// يستخدم جداول الشهور المخزنة في قاعدة البيانات
  static String getMonthName(
    DateTime date,
    CalendarType calendarType,
    InformationCubit cubit,
  ) {
    final int monthIndex = date.month;

    switch (calendarType) {
      case CalendarType.gregorian:
        // التحقق من وجود شهور التقويم الميلادي
        if (cubit.gorianMonths.isEmpty) {
          return _getDefaultMonthName(monthIndex);
        }

        // البحث عن الشهر بالمعرف
        final month = _findGorianMonthById(cubit.gorianMonths, monthIndex);
        return month?.name ?? _getDefaultMonthName(monthIndex);

      case CalendarType.crest:
        // التحقق من وجود شهور التقويم الكريستي
        if (cubit.crestMonths.isEmpty) {
          return _getDefaultMonthName(monthIndex);
        }

        // البحث عن الشهر بالمعرف
        final month = _findCrestMonthById(cubit.crestMonths, monthIndex);
        return month?.name ?? _getDefaultMonthName(monthIndex);

      case CalendarType.himyar:
        // التحقق من وجود شهور التقويم الحميري
        if (cubit.hemiariMonths.isEmpty) {
          return _getDefaultMonthName(monthIndex);
        }

        // البحث عن الشهر بالمعرف
        final month = _findHemiariMonthById(cubit.hemiariMonths, monthIndex);
        return month?.name ?? _getDefaultMonthName(monthIndex);
    }
  }

  /// البحث عن شهر بالمعرف في قائمة الشهور الميلادية
  static GorianMonths? _findGorianMonthById(List<GorianMonths> months, int id) {
    try {
      return months.firstWhere((month) => month.id == id);
    } catch (e) {
      return null;
    }
  }

  /// البحث عن شهر بالمعرف في قائمة الشهور الكريستية
  static CrestMonths? _findCrestMonthById(List<CrestMonths> months, int id) {
    try {
      return months.firstWhere((month) => month.id == id);
    } catch (e) {
      return null;
    }
  }

  /// البحث عن شهر بالمعرف في قائمة الشهور الحميرية
  static HemiariMonths? _findHemiariMonthById(
      List<HemiariMonths> months, int id) {
    try {
      return months.firstWhere((month) => month.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على اسم الشهر الافتراضي
  static String _getDefaultMonthName(int monthIndex) {
    const List<String> defaultMonthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    // التأكد من أن المؤشر ضمن النطاق
    if (monthIndex >= 1 && monthIndex <= 12) {
      return defaultMonthNames[monthIndex - 1];
    }

    return 'غير معروف';
  }

  /// تنسيق التاريخ بالشكل المطلوب (اليوم والشهر فقط)
  static String formatDate(
    DateTime date,
    CalendarType calendarType,
    InformationCubit cubit,
  ) {
    final int day = date.day;
    final String monthName = getMonthName(date, calendarType, cubit);

    // عرض اسم الشهر أولاً ثم رقم اليوم (في اللغة العربية يكون الترتيب من اليمين إلى اليسار)
    return '$monthName $day';
  }
}
