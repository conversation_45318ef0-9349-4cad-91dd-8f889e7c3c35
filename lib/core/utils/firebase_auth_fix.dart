/// إصلاح مشكلة PigeonUserDetails في Firebase Auth
/// 
/// هذا الملف يحتوي على إصلاحات لمشاكل التوافق بين إصدارات Firebase Auth
/// والمشاكل المتعلقة بـ Pigeon (أداة توليد الكود بين Flutter والمنصات الأصلية)

import 'package:firebase_auth/firebase_auth.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';

class FirebaseAuthFix {
  /// إصلاح مشكلة إنشاء المستخدم
  /// 
  /// يتعامل مع مشكلة type casting في PigeonUserDetails
  static Future<UserCredential?> createUserWithEmailAndPasswordSafe(
    String email,
    String password,
  ) async {
    try {
      LoggerService.debug(
        'محاولة إنشاء حساب جديد مع الإصلاح الآمن: $email',
        tag: 'FirebaseAuthFix',
      );

      // استخدام try-catch متعدد المستويات لمعالجة مشاكل Pigeon
      UserCredential? userCredential;
      
      try {
        userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );
      } on FirebaseAuthException catch (e) {
        LoggerService.error(
          'خطأ Firebase Auth: ${e.code} - ${e.message}',
          error: e,
          tag: 'FirebaseAuthFix',
        );
        rethrow;
      } catch (e) {
        // معالجة خاصة لمشكلة PigeonUserDetails
        if (e.toString().contains('PigeonUserDetails') || 
            e.toString().contains('List<Object?>')) {
          LoggerService.warning(
            'تم اكتشاف مشكلة PigeonUserDetails، محاولة إصلاح...',
            tag: 'FirebaseAuthFix',
          );
          
          // محاولة ثانية بعد تأخير قصير
          await Future.delayed(const Duration(milliseconds: 500));
          
          try {
            userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
              email: email,
              password: password,
            );
          } catch (retryError) {
            LoggerService.error(
              'فشل في المحاولة الثانية لإنشاء الحساب',
              error: retryError,
              tag: 'FirebaseAuthFix',
            );
            rethrow;
          }
        } else {
          LoggerService.error(
            'خطأ غير متوقع في إنشاء الحساب',
            error: e,
            tag: 'FirebaseAuthFix',
          );
          rethrow;
        }
      }

      if (userCredential?.user != null) {
        LoggerService.info(
          'تم إنشاء الحساب بنجاح: ${userCredential!.user!.uid}',
          tag: 'FirebaseAuthFix',
        );
      }

      return userCredential;
    } catch (e) {
      LoggerService.error(
        'خطأ عام في إنشاء الحساب',
        error: e,
        tag: 'FirebaseAuthFix',
      );
      rethrow;
    }
  }

  /// إصلاح مشكلة تسجيل الدخول
  static Future<UserCredential?> signInWithEmailAndPasswordSafe(
    String email,
    String password,
  ) async {
    try {
      LoggerService.debug(
        'محاولة تسجيل الدخول مع الإصلاح الآمن: $email',
        tag: 'FirebaseAuthFix',
      );

      UserCredential? userCredential;
      
      try {
        userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
      } on FirebaseAuthException catch (e) {
        LoggerService.error(
          'خطأ Firebase Auth في تسجيل الدخول: ${e.code} - ${e.message}',
          error: e,
          tag: 'FirebaseAuthFix',
        );
        rethrow;
      } catch (e) {
        // معالجة خاصة لمشكلة PigeonUserDetails
        if (e.toString().contains('PigeonUserDetails') || 
            e.toString().contains('List<Object?>')) {
          LoggerService.warning(
            'تم اكتشاف مشكلة PigeonUserDetails في تسجيل الدخول، محاولة إصلاح...',
            tag: 'FirebaseAuthFix',
          );
          
          // محاولة ثانية بعد تأخير قصير
          await Future.delayed(const Duration(milliseconds: 500));
          
          try {
            userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
              email: email,
              password: password,
            );
          } catch (retryError) {
            LoggerService.error(
              'فشل في المحاولة الثانية لتسجيل الدخول',
              error: retryError,
              tag: 'FirebaseAuthFix',
            );
            rethrow;
          }
        } else {
          LoggerService.error(
            'خطأ غير متوقع في تسجيل الدخول',
            error: e,
            tag: 'FirebaseAuthFix',
          );
          rethrow;
        }
      }

      if (userCredential?.user != null) {
        LoggerService.info(
          'تم تسجيل الدخول بنجاح: ${userCredential!.user!.uid}',
          tag: 'FirebaseAuthFix',
        );
      }

      return userCredential;
    } catch (e) {
      LoggerService.error(
        'خطأ عام في تسجيل الدخول',
        error: e,
        tag: 'FirebaseAuthFix',
      );
      rethrow;
    }
  }

  /// التحقق من حالة Firebase Auth
  static Future<bool> checkFirebaseAuthHealth() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      LoggerService.debug(
        'حالة Firebase Auth: ${currentUser != null ? "مسجل الدخول" : "غير مسجل الدخول"}',
        tag: 'FirebaseAuthFix',
      );
      return true;
    } catch (e) {
      LoggerService.error(
        'خطأ في التحقق من حالة Firebase Auth',
        error: e,
        tag: 'FirebaseAuthFix',
      );
      return false;
    }
  }
}
