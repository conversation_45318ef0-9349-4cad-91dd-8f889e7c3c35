import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../../../core/constants/app_constants.dart';
import '../logging/logger_service.dart';

/// خدمة التخزين المؤقت
///
/// توفر هذه الخدمة وظائف للتخزين المؤقت للبيانات في الذاكرة وعلى القرص.
/// تستخدم هذه الخدمة للتخزين المؤقت للبيانات التي يتم الحصول عليها من الإنترنت
/// لتقليل عدد الطلبات وتحسين الأداء.
class CacheService {
  // منع إنشاء نسخة من الكلاس
  CacheService._();

  // التخزين المؤقت في الذاكرة
  static final Map<String, _CacheEntry> _memoryCache = {};

  /// الحصول على قيمة من التخزين المؤقت
  ///
  /// المعلمات:
  /// - [key]: مفتاح القيمة
  /// - [maxAge]: العمر الأقصى للقيمة بالثواني (اختياري)
  ///
  /// يعيد القيمة المخزنة أو null إذا لم تكن موجودة أو منتهية الصلاحية
  static T? get<T>(String key, {Duration? maxAge}) {
    try {
      // التحقق من وجود القيمة في الذاكرة
      if (_memoryCache.containsKey(key)) {
        final entry = _memoryCache[key]!;

        // التحقق من صلاحية القيمة
        final maxAgeDuration = maxAge ?? Duration(seconds: AppConstants.maxCacheAge);
        if (DateTime.now().difference(entry.timestamp) <= maxAgeDuration) {
          LoggerService.debug(
            'تم استرداد القيمة من التخزين المؤقت في الذاكرة: $key',
            tag: 'CacheService',
          );
          return entry.value as T?;
        } else {
          // حذف القيمة منتهية الصلاحية
          _memoryCache.remove(key);
          LoggerService.debug(
            'تم حذف القيمة منتهية الصلاحية من التخزين المؤقت في الذاكرة: $key',
            tag: 'CacheService',
          );
        }
      }

      // محاولة استرداد القيمة من القرص
      return _getFromDisk<T>(key, maxAge: maxAge);
    } catch (e) {
      LoggerService.error(
        'خطأ في استرداد القيمة من التخزين المؤقت',
        error: e,
        tag: 'CacheService',
      );
      return null;
    }
  }

  /// تخزين قيمة في التخزين المؤقت
  ///
  /// المعلمات:
  /// - [key]: مفتاح القيمة
  /// - [value]: القيمة المراد تخزينها
  /// - [persistToDisk]: ما إذا كان يجب تخزين القيمة على القرص (اختياري)
  static Future<void> set<T>(String key, T value, {Duration? expiryDuration, bool persistToDisk = true}) async {
    try {
      // تخزين القيمة في الذاكرة
      _memoryCache[key] = _CacheEntry(value, DateTime.now());

      LoggerService.debug(
        'تم تخزين القيمة في التخزين المؤقت في الذاكرة: $key',
        tag: 'CacheService',
      );

      // تخزين القيمة على القرص إذا كان مطلوبًا
      if (persistToDisk) {
        await _saveToDisk<T>(key, value, expiryDuration: expiryDuration);
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين القيمة في التخزين المؤقت',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// حذف قيمة من التخزين المؤقت
  ///
  /// المعلمات:
  /// - [key]: مفتاح القيمة
  static Future<void> remove(String key) async {
    try {
      // حذف القيمة من الذاكرة
      _memoryCache.remove(key);

      LoggerService.debug(
        'تم حذف القيمة من التخزين المؤقت في الذاكرة: $key',
        tag: 'CacheService',
      );

      // حذف القيمة من القرص
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/$key.cache');
      if (await file.exists()) {
        await file.delete();
        LoggerService.debug(
          'تم حذف القيمة من التخزين المؤقت على القرص: $key',
          tag: 'CacheService',
        );
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف القيمة من التخزين المؤقت',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// حذف القيم التي تطابق نمطًا معينًا
  ///
  /// المعلمات:
  /// - [pattern]: النمط الذي يجب أن تطابقه المفاتيح
  static Future<void> deleteByPattern(String pattern) async {
    try {
      // حذف القيم من الذاكرة
      final keysToRemove = _memoryCache.keys.where((key) => key.contains(pattern)).toList();
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
      }

      LoggerService.debug(
        'تم حذف ${keysToRemove.length} قيمة من التخزين المؤقت في الذاكرة بنمط: $pattern',
        tag: 'CacheService',
      );

      // حذف القيم من القرص
      final cacheDir = await _getCacheDirectory();
      final files = await cacheDir.list().where((entity) =>
        entity is File &&
        entity.path.contains(pattern) &&
        entity.path.endsWith('.cache')
      ).toList();

      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }

      LoggerService.debug(
        'تم حذف ${files.length} ملف من التخزين المؤقت على القرص بنمط: $pattern',
        tag: 'CacheService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف القيم بنمط من التخزين المؤقت',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// حذف جميع القيم من التخزين المؤقت
  static Future<void> clear() async {
    try {
      // حذف جميع القيم من الذاكرة
      _memoryCache.clear();

      LoggerService.debug(
        'تم حذف جميع القيم من التخزين المؤقت في الذاكرة',
        tag: 'CacheService',
      );

      // حذف جميع القيم من القرص
      final cacheDir = await _getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create();
        LoggerService.debug(
          'تم حذف جميع القيم من التخزين المؤقت على القرص',
          tag: 'CacheService',
        );
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف جميع القيم من التخزين المؤقت',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// الحصول على حجم التخزين المؤقت على القرص
  ///
  /// يعيد حجم التخزين المؤقت على القرص بالبايت
  static Future<int> getDiskCacheSize() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) {
        return 0;
      }

      int size = 0;
      await for (final file in cacheDir.list(recursive: true, followLinks: false)) {
        if (file is File) {
          size += await file.length();
        }
      }

      return size;
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على حجم التخزين المؤقت على القرص',
        error: e,
        tag: 'CacheService',
      );
      return 0;
    }
  }

  /// تنظيف التخزين المؤقت على القرص
  ///
  /// حذف الملفات القديمة من التخزين المؤقت على القرص
  static Future<void> cleanDiskCache({Duration? maxAge}) async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (!await cacheDir.exists()) {
        return;
      }

      final maxAgeDuration = maxAge ?? Duration(seconds: AppConstants.maxCacheAge);
      final now = DateTime.now();

      await for (final file in cacheDir.list(recursive: false, followLinks: false)) {
        if (file is File) {
          final stat = await file.stat();
          if (now.difference(stat.modified) > maxAgeDuration) {
            await file.delete();
            LoggerService.debug(
              'تم حذف ملف قديم من التخزين المؤقت على القرص: ${file.path}',
              tag: 'CacheService',
            );
          }
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تنظيف التخزين المؤقت على القرص',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// الحصول على مجلد التخزين المؤقت
  static Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/cache');
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir;
  }

  /// تخزين قيمة على القرص
  static Future<void> _saveToDisk<T>(String key, T value, {Duration? expiryDuration}) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/$key.cache');

      // تحويل القيمة إلى JSON
      final Map<String, dynamic> data = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'type': T.toString(),
        'expiry': expiryDuration?.inMilliseconds ?? Duration(seconds: AppConstants.maxCacheAge).inMilliseconds,
      };

      // كتابة البيانات إلى الملف
      await file.writeAsString(json.encode(data));

      LoggerService.debug(
        'تم تخزين القيمة في التخزين المؤقت على القرص: $key',
        tag: 'CacheService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين القيمة في التخزين المؤقت على القرص',
        error: e,
        tag: 'CacheService',
      );
    }
  }

  /// استرداد قيمة من القرص
  static T? _getFromDisk<T>(String key, {Duration? maxAge}) {
    try {
      final cacheDir = _getCacheDirectory();
      final file = File('${cacheDir.then((dir) => '${dir.path}/$key.cache')}');

      // التحقق من وجود الملف
      if (!file.existsSync()) {
        return null;
      }

      // قراءة البيانات من الملف
      final jsonString = file.readAsStringSync();
      final data = json.decode(jsonString) as Map<String, dynamic>;

      // التحقق من صلاحية القيمة
      final timestamp = DateTime.fromMillisecondsSinceEpoch(data['timestamp'] as int);
      final maxAgeDuration = maxAge ?? Duration(seconds: AppConstants.maxCacheAge);

      if (DateTime.now().difference(timestamp) > maxAgeDuration) {
        // حذف الملف منتهي الصلاحية
        file.deleteSync();
        LoggerService.debug(
          'تم حذف ملف منتهي الصلاحية من التخزين المؤقت على القرص: $key',
          tag: 'CacheService',
        );
        return null;
      }

      // استرداد القيمة وتخزينها في الذاكرة
      final value = data['value'] as T;
      _memoryCache[key] = _CacheEntry(value, timestamp);

      LoggerService.debug(
        'تم استرداد القيمة من التخزين المؤقت على القرص: $key',
        tag: 'CacheService',
      );

      return value;
    } catch (e) {
      LoggerService.error(
        'خطأ في استرداد القيمة من التخزين المؤقت على القرص',
        error: e,
        tag: 'CacheService',
      );
      return null;
    }
  }
}

/// فئة داخلية لتمثيل إدخال في التخزين المؤقت
class _CacheEntry {
  final dynamic value;
  final DateTime timestamp;

  _CacheEntry(this.value, this.timestamp);
}
