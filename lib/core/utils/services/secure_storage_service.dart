import 'dart:convert';
import '../../../data/datasources/local/shared_prefs.dart';
import '../logging/logger_service.dart';

/// خدمة التخزين الآمن
///
/// توفر هذه الخدمة وظائف للتخزين الآمن للبيانات.
/// تستخدم هذه الخدمة SharedPrefs للتخزين الفعلي للبيانات.
class SecureStorageService {
  // منع إنشاء نسخة من الكلاس
  SecureStorageService._();

  /// حفظ قيمة نصية
  static Future<void> setString(String key, String value) async {
    try {
      await SharedPrefs.setString(key, value);
    } catch (e) {
      LoggerService.error('خطأ في حفظ قيمة نصية',
          error: e, tag: 'SecureStorageService');
    }
  }

  /// الحصول على قيمة نصية
  static Future<String?> getString(String key) async {
    try {
      return SharedPrefs.getString(key);
    } catch (e) {
      LoggerService.error('خطأ في الحصول على قيمة نصية',
          error: e, tag: 'SecureStorageService');
      return null;
    }
  }

  /// حفظ قيمة منطقية
  static Future<void> setBool(String key, bool value) async {
    try {
      await SharedPrefs.setBool(key, value);
    } catch (e) {
      LoggerService.error('خطأ في حفظ قيمة منطقية',
          error: e, tag: 'SecureStorageService');
    }
  }

  /// الحصول على قيمة منطقية
  static Future<bool?> getBool(String key) async {
    try {
      return SharedPrefs.getBool(key);
    } catch (e) {
      LoggerService.error('خطأ في الحصول على قيمة منطقية',
          error: e, tag: 'SecureStorageService');
      return null;
    }
  }

  /// حفظ قيمة عددية
  static Future<void> setInt(String key, int value) async {
    try {
      await SharedPrefs.setInt(key, value);
    } catch (e) {
      LoggerService.error('خطأ في حفظ قيمة عددية',
          error: e, tag: 'SecureStorageService');
    }
  }

  /// الحصول على قيمة عددية
  static Future<int?> getInt(String key) async {
    try {
      return SharedPrefs.getInt(key);
    } catch (e) {
      LoggerService.error('خطأ في الحصول على قيمة عددية',
          error: e, tag: 'SecureStorageService');
      return null;
    }
  }

  /// حفظ قيمة JSON
  static Future<void> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      await SharedPrefs.setString(key, jsonString);
    } catch (e) {
      LoggerService.error('خطأ في حفظ قيمة JSON',
          error: e, tag: 'SecureStorageService');
    }
  }

  /// الحصول على قيمة JSON
  static Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = SharedPrefs.getString(key);
      if (jsonString == null) return null;
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      LoggerService.error('خطأ في الحصول على قيمة JSON',
          error: e, tag: 'SecureStorageService');
      return null;
    }
  }

  /// حذف قيمة
  static Future<void> remove(String key) async {
    try {
      await SharedPrefs.remove(key);
    } catch (e) {
      LoggerService.error('خطأ في حذف قيمة',
          error: e, tag: 'SecureStorageService');
    }
  }

  /// حذف جميع القيم
  static Future<void> clear() async {
    try {
      await SharedPrefs.clear();
    } catch (e) {
      LoggerService.error('خطأ في حذف جميع القيم',
          error: e, tag: 'SecureStorageService');
    }
  }
}
