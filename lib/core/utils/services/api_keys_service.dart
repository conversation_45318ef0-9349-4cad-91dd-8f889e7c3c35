import 'dart:convert';
import 'package:flutter/services.dart';
import '../logging/logger_service.dart';
import 'secure_storage_service.dart';

/// خدمة إدارة مفاتيح API
///
/// توفر هذه الخدمة وظائف لإدارة مفاتيح API بشكل آمن.
class ApiKeysService {
  // منع إنشاء نسخة من الكلاس
  ApiKeysService._();

  // مفتاح التخزين الآمن
  static const String _storageKey = 'api_keys';

  // مفاتيح API
  static Map<String, String> _apiKeys = {};

  // ما إذا كانت الخدمة مهيأة
  static bool _isInitialized = false;

  /// تهيئة خدمة إدارة مفاتيح API
  static Future<void> init() async {
    if (_isInitialized) return;

    try {
      // محاولة استرداد المفاتيح من التخزين الآمن
      final storedKeys = await SecureStorageService.getJson(_storageKey);
      if (storedKeys != null) {
        _apiKeys = Map<String, String>.from(storedKeys);
        LoggerService.info('تم استرداد مفاتيح API من التخزين الآمن', tag: 'ApiKeysService');
      } else {
        // إذا لم تكن المفاتيح موجودة في التخزين الآمن، قم بتحميلها من ملف الأصول
        await _loadKeysFromAssets();
      }

      _isInitialized = true;
    } catch (e) {
      LoggerService.error('خطأ في تهيئة خدمة إدارة مفاتيح API', error: e, tag: 'ApiKeysService');
    }
  }

  /// تحميل المفاتيح من ملف الأصول
  static Future<void> _loadKeysFromAssets() async {
    try {
      // تحميل ملف المفاتيح من الأصول
      final jsonString = await rootBundle.loadString('assets/config/api_keys.json');
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      
      // تحويل القيم إلى نصوص
      _apiKeys = jsonMap.map((key, value) => MapEntry(key, value.toString()));
      
      // حفظ المفاتيح في التخزين الآمن
      await SecureStorageService.setJson(_storageKey, _apiKeys);
      
      LoggerService.info('تم تحميل مفاتيح API من ملف الأصول', tag: 'ApiKeysService');
    } catch (e) {
      LoggerService.error('خطأ في تحميل مفاتيح API من ملف الأصول', error: e, tag: 'ApiKeysService');
    }
  }

  /// الحصول على مفتاح API
  ///
  /// المعلمات:
  /// - [key]: اسم المفتاح
  /// - [defaultValue]: القيمة الافتراضية في حالة عدم وجود المفتاح
  static String getApiKey(String key, {String defaultValue = ''}) {
    if (!_isInitialized) {
      LoggerService.warning('خدمة إدارة مفاتيح API غير مهيأة', tag: 'ApiKeysService');
      return defaultValue;
    }

    return _apiKeys[key] ?? defaultValue;
  }

  /// تعيين مفتاح API
  ///
  /// المعلمات:
  /// - [key]: اسم المفتاح
  /// - [value]: قيمة المفتاح
  static Future<void> setApiKey(String key, String value) async {
    if (!_isInitialized) {
      await init();
    }

    try {
      // تحديث المفتاح في الذاكرة
      _apiKeys[key] = value;
      
      // حفظ المفاتيح في التخزين الآمن
      await SecureStorageService.setJson(_storageKey, _apiKeys);
      
      LoggerService.info('تم تعيين مفتاح API: $key', tag: 'ApiKeysService');
    } catch (e) {
      LoggerService.error('خطأ في تعيين مفتاح API', error: e, tag: 'ApiKeysService');
    }
  }

  /// حذف مفتاح API
  ///
  /// المعلمات:
  /// - [key]: اسم المفتاح
  static Future<void> removeApiKey(String key) async {
    if (!_isInitialized) {
      await init();
    }

    try {
      // حذف المفتاح من الذاكرة
      _apiKeys.remove(key);
      
      // حفظ المفاتيح في التخزين الآمن
      await SecureStorageService.setJson(_storageKey, _apiKeys);
      
      LoggerService.info('تم حذف مفتاح API: $key', tag: 'ApiKeysService');
    } catch (e) {
      LoggerService.error('خطأ في حذف مفتاح API', error: e, tag: 'ApiKeysService');
    }
  }

  /// الحصول على جميع مفاتيح API
  static Map<String, String> getAllApiKeys() {
    if (!_isInitialized) {
      LoggerService.warning('خدمة إدارة مفاتيح API غير مهيأة', tag: 'ApiKeysService');
      return {};
    }

    return Map<String, String>.from(_apiKeys);
  }

  /// حذف جميع مفاتيح API
  static Future<void> clearAllApiKeys() async {
    if (!_isInitialized) {
      await init();
    }

    try {
      // حذف جميع المفاتيح من الذاكرة
      _apiKeys.clear();
      
      // حذف المفاتيح من التخزين الآمن
      await SecureStorageService.remove(_storageKey);
      
      LoggerService.info('تم حذف جميع مفاتيح API', tag: 'ApiKeysService');
    } catch (e) {
      LoggerService.error('خطأ في حذف جميع مفاتيح API', error: e, tag: 'ApiKeysService');
    }
  }

  /// إعادة تحميل المفاتيح من ملف الأصول
  static Future<void> reloadKeysFromAssets() async {
    try {
      await _loadKeysFromAssets();
      LoggerService.info('تم إعادة تحميل مفاتيح API من ملف الأصول', tag: 'ApiKeysService');
    } catch (e) {
      LoggerService.error('خطأ في إعادة تحميل مفاتيح API من ملف الأصول', error: e, tag: 'ApiKeysService');
    }
  }
}
