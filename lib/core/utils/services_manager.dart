import 'package:flutter/foundation.dart';
import '../../services/index.dart';

/// مدير الخدمات
/// 
/// يدير تهيئة وتنسيق جميع الخدمات في التطبيق
/// يوفر واجهة موحدة للوصول إلى الخدمات
class ServicesManager {
  static final ServicesManager _instance = ServicesManager._internal();
  factory ServicesManager() => _instance;
  ServicesManager._internal();

  /// خدمة الأداء
  late PerformanceService _performanceService;
  
  /// خدمة التحليلات
  late AnalyticsService _analyticsService;
  
  /// خدمة الإشعارات
  late NotificationService _notificationService;

  /// هل تم تهيئة الخدمات
  bool _isInitialized = false;

  /// تهيئة جميع الخدمات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 بدء تهيئة الخدمات...');
      }

      // تهيئة خدمة الأداء
      _performanceService = PerformanceService();
      _performanceService.initialize();

      // تهيئة خدمة التحليلات
      _analyticsService = AnalyticsService();
      _analyticsService.initialize();

      // تهيئة خدمة الإشعارات
      _notificationService = NotificationService();
      
      // إنشاء إشعارات تجريبية
      NotificationService.generateSampleNotifications();

      // تحسين أداء التطبيق عند البدء
      PerformanceService.optimizeAppStartup();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ تم تهيئة جميع الخدمات بنجاح');
      }

      // تسجيل حدث تهيئة الخدمات
      _analyticsService.recordEvent('services_initialized', {
        'timestamp': DateTime.now().toIso8601String(),
        'services_count': 3,
      });

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة الخدمات: $e');
      }
      
      // تسجيل الخطأ في التحليلات إذا كانت متاحة
      try {
        _analyticsService.recordError('services_initialization_error', e.toString());
      } catch (_) {
        // تجاهل الخطأ إذا لم تكن خدمة التحليلات مهيأة بعد
      }
    }
  }

  /// الحصول على خدمة الأداء
  PerformanceService get performance {
    _ensureInitialized();
    return _performanceService;
  }

  /// الحصول على خدمة التحليلات
  AnalyticsService get analytics {
    _ensureInitialized();
    return _analyticsService;
  }

  /// الحصول على خدمة الإشعارات
  NotificationService get notifications {
    _ensureInitialized();
    return _notificationService;
  }

  /// التأكد من تهيئة الخدمات
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('الخدمات غير مهيأة. يرجى استدعاء initialize() أولاً');
    }
  }

  /// الحصول على بيانات الطقس
  Map<String, dynamic> getCurrentWeather({String location = 'صنعاء، اليمن'}) {
    _analyticsService.recordFeatureUsage('weather_check', {'location': location});
    return WeatherMockService.getCurrentWeather(location: location);
  }

  /// الحصول على توقعات الطقس
  List<Map<String, dynamic>> getWeatherForecast({String location = 'صنعاء، اليمن'}) {
    _analyticsService.recordFeatureUsage('weather_forecast', {'location': location});
    return WeatherMockService.getWeeklyForecast(location: location);
  }

  /// البحث في المحاصيل
  List<Map<String, dynamic>> searchCrops(String query) {
    _analyticsService.recordSearch(query, {'category': 'crops'});
    return CropsMockService.searchCrops(query);
  }

  /// البحث في المرشدين
  List<Map<String, dynamic>> searchAdvisors(String query) {
    _analyticsService.recordSearch(query, {'category': 'advisors'});
    return AdvisorsMockService.searchAdvisors(query);
  }

  /// البحث في المنشورات
  List<Map<String, dynamic>> searchPosts(String query) {
    _analyticsService.recordSearch(query, {'category': 'posts'});
    return PostsMockService.searchPosts(query);
  }

  /// إضافة إشعار طقس
  void addWeatherNotification({
    required String condition,
    required int temperature,
    String location = 'صنعاء',
  }) {
    NotificationService.createWeatherNotification(
      condition: condition,
      temperature: temperature,
      location: location,
    );
    
    _analyticsService.recordEvent('weather_notification_created', {
      'condition': condition,
      'temperature': temperature,
      'location': location,
    });
  }

  /// إضافة تذكير ري
  void addIrrigationReminder({
    required String cropName,
    required String fieldName,
    DateTime? scheduledTime,
  }) {
    NotificationService.createIrrigationReminder(
      cropName: cropName,
      fieldName: fieldName,
      scheduledTime: scheduledTime,
    );
    
    _analyticsService.recordEvent('irrigation_reminder_created', {
      'crop': cropName,
      'field': fieldName,
      'scheduled': scheduledTime != null,
    });
  }

  /// تسجيل زيارة صفحة
  void recordPageVisit(String pageName, [Map<String, dynamic>? parameters]) {
    _analyticsService.recordPageView(pageName, parameters);
  }

  /// تسجيل نقرة زر
  void recordButtonClick(String buttonName, [Map<String, dynamic>? parameters]) {
    _analyticsService.recordButtonClick(buttonName, parameters);
  }

  /// تسجيل مشاركة محتوى
  void recordContentShare(String contentType, String contentId) {
    _analyticsService.recordShare(contentType, contentId);
  }

  /// تسجيل خطأ
  void recordError(String errorType, String errorMessage, [Map<String, dynamic>? parameters]) {
    _analyticsService.recordError(errorType, errorMessage, parameters);
    _performanceService.recordError('$errorType: $errorMessage');
  }

  /// الحصول على تقرير الأداء
  Map<String, dynamic> getPerformanceReport() {
    _analyticsService.recordFeatureUsage('performance_report_viewed');
    return _performanceService.generatePerformanceReport();
  }

  /// الحصول على تقرير التحليلات
  Map<String, dynamic> getAnalyticsReport() {
    _analyticsService.recordFeatureUsage('analytics_report_viewed');
    return _analyticsService.generateAnalyticsReport();
  }

  /// الحصول على إحصائيات الإشعارات
  Map<String, dynamic> getNotificationStats() {
    _analyticsService.recordFeatureUsage('notification_stats_viewed');
    return NotificationService.getNotificationStats();
  }

  /// تنظيف الذاكرة
  void cleanupMemory() {
    _performanceService.recordError('manual_memory_cleanup');
    _analyticsService.recordFeatureUsage('memory_cleanup');
    
    if (kDebugMode) {
      print('🧹 تنظيف الذاكرة...');
    }
  }

  /// إعادة تعيين الإحصائيات
  void resetStats() {
    _performanceService.resetStats();
    _analyticsService.clearData();
    NotificationService.clearAllNotifications();
    
    _analyticsService.recordEvent('stats_reset', {
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    if (kDebugMode) {
      print('🔄 تم إعادة تعيين جميع الإحصائيات');
    }
  }

  /// الحصول على حالة الخدمات
  Map<String, dynamic> getServicesStatus() {
    return {
      'initialized': _isInitialized,
      'performance': {
        'status': 'active',
        'stats': _performanceService.getPerformanceStats(),
      },
      'analytics': {
        'status': 'active',
        'session': _analyticsService.getSessionStats(),
      },
      'notifications': {
        'status': 'active',
        'unread_count': NotificationService.getUnreadCount(),
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// إنهاء جميع الخدمات
  void dispose() {
    if (!_isInitialized) return;

    try {
      // إنهاء جلسة التحليلات
      _analyticsService.endSession();
      
      // تنظيف خدمة الأداء
      _performanceService.dispose();
      
      _isInitialized = false;
      
      if (kDebugMode) {
        print('🛑 تم إنهاء جميع الخدمات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنهاء الخدمات: $e');
      }
    }
  }

  /// مثال على الاستخدام المتقدم
  Future<void> demonstrateAdvancedUsage() async {
    if (kDebugMode) {
      print('🎯 عرض الاستخدام المتقدم للخدمات...');
    }

    // 1. فحص الطقس وإنشاء إشعار
    final weather = getCurrentWeather();
    final temperature = weather['temperature'] as int;
    
    if (temperature > 35) {
      addWeatherNotification(
        condition: weather['condition'],
        temperature: temperature,
      );
    }

    // 2. البحث في المحاصيل وتسجيل النتائج
    final crops = searchCrops('طماطم');
    _analyticsService.recordEvent('search_results', {
      'query': 'طماطم',
      'results_count': crops.length,
      'category': 'crops',
    });

    // 3. إنشاء تذكير ري مجدول
    addIrrigationReminder(
      cropName: 'طماطم',
      fieldName: 'الحقل الشمالي',
      scheduledTime: DateTime.now().add(Duration(hours: 6)),
    );

    // 4. عرض التقارير
    final performanceReport = getPerformanceReport();
    final analyticsReport = getAnalyticsReport();
    
    if (kDebugMode) {
      print('📊 تقرير الأداء: ${performanceReport['healthScore']}');
      print('📈 تقرير التحليلات: ${analyticsReport['session']['eventsCount']} حدث');
    }
  }
}
