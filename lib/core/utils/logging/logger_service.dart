import 'dart:io';
import 'package:flutter/foundation.dart';

import 'package:path_provider/path_provider.dart';

/// مستويات التسجيل
enum LogLevel {
  /// معلومات
  info,

  /// تحذير
  warning,

  /// خطأ
  error,

  /// تصحيح
  debug,
}

/// خدمة تسجيل الأحداث في التطبيق
///
/// توفر هذه الخدمة وظائف لتسجيل الأحداث المختلفة في التطبيق
/// مثل المعلومات والتحذيرات والأخطاء.
class LoggerService {
  // منع إنشاء نسخة من الكلاس
  LoggerService._();

  /// ما إذا كان التسجيل مفعلاً
  static bool _isEnabled = true;

  /// ما إذا كان التسجيل في ملف مفعلاً
  static bool _isFileLoggingEnabled = false;

  /// ملف التسجيل
  static File? _logFile;

  /// الحد الأقصى لحجم ملف التسجيل (5 ميجابايت)
  static const int _maxLogFileSize = 5 * 1024 * 1024;

  /// الحد الأقصى لعدد ملفات التسجيل (5 ملفات)
  static const int _maxLogFiles = 5;

  /// تهيئة خدمة التسجيل
  static Future<void> init() async {
    try {
      // تفعيل التسجيل
      enable();

      // تهيئة ملف التسجيل
      await _initLogFile();

      info('تم تهيئة خدمة التسجيل', tag: 'LoggerService');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة التسجيل: $e');
    }
  }

  /// تهيئة ملف التسجيل
  static Future<void> _initLogFile() async {
    try {
      if (!kDebugMode) {
        // الحصول على دليل التطبيق
        final appDir = await getApplicationDocumentsDirectory();
        final logsDir = Directory('${appDir.path}/logs');

        // إنشاء دليل التسجيل إذا لم يكن موجودًا
        if (!await logsDir.exists()) {
          await logsDir.create(recursive: true);
        }

        // إنشاء ملف التسجيل
        final now = DateTime.now();
        final formattedDate =
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
        _logFile = File('${logsDir.path}/app_log_$formattedDate.log');

        // تفعيل التسجيل في ملف
        _isFileLoggingEnabled = true;

        // تنظيف ملفات التسجيل القديمة
        await _cleanupOldLogFiles(logsDir);
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة ملف التسجيل: $e');
      _isFileLoggingEnabled = false;
    }
  }

  /// تنظيف ملفات التسجيل القديمة
  static Future<void> _cleanupOldLogFiles(Directory logsDir) async {
    try {
      // الحصول على قائمة ملفات التسجيل
      final logFiles = await logsDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.log'))
          .toList();

      // ترتيب الملفات حسب تاريخ التعديل (الأقدم أولاً)
      logFiles.sort((a, b) => (a as File)
          .lastModifiedSync()
          .compareTo((b as File).lastModifiedSync()));

      // حذف الملفات القديمة إذا تجاوز العدد الحد الأقصى
      if (logFiles.length > _maxLogFiles) {
        for (var i = 0; i < logFiles.length - _maxLogFiles; i++) {
          await (logFiles[i] as File).delete();
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف ملفات التسجيل القديمة: $e');
    }
  }

  /// التحقق من حجم ملف التسجيل وإنشاء ملف جديد إذا تجاوز الحد الأقصى
  static Future<void> _checkLogFileSize() async {
    try {
      if (_logFile != null && await _logFile!.exists()) {
        final fileSize = await _logFile!.length();

        if (fileSize > _maxLogFileSize) {
          // إنشاء ملف تسجيل جديد
          await _initLogFile();
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من حجم ملف التسجيل: $e');
    }
  }

  /// تفعيل التسجيل
  static void enable() {
    _isEnabled = true;
  }

  /// تعطيل التسجيل
  static void disable() {
    _isEnabled = false;
  }

  /// تسجيل معلومات
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [tag]: علامة التسجيل (اختياري)
  static void info(String message, {String tag = 'INFO'}) {
    _log(message, tag: tag, level: 'INFO');
  }

  /// تسجيل تحذير
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [error]: الخطأ (اختياري)
  /// - [tag]: علامة التسجيل (اختياري)
  static void warning(String message, {Object? error, String tag = 'WARNING'}) {
    _log(message, tag: tag, level: 'WARNING', error: error);
  }

  /// تسجيل خطأ
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [error]: الخطأ (اختياري)
  /// - [stackTrace]: تتبع المكدس (اختياري)
  /// - [tag]: علامة التسجيل (اختياري)
  static void error(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String tag = 'ERROR',
  }) {
    _log(
      message,
      tag: tag,
      level: 'ERROR',
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// تسجيل تصحيح
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [tag]: علامة التسجيل (اختياري)
  static void debug(String message, {String tag = 'DEBUG'}) {
    _log(message, tag: tag, level: 'DEBUG');
  }

  /// تسجيل معلومات عن الأداء
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [startTime]: وقت بدء العملية
  /// - [tag]: علامة التسجيل (اختياري)
  static void performance(String message, DateTime startTime,
      {String tag = 'PERFORMANCE'}) {
    final duration = DateTime.now().difference(startTime);
    _log('$message - استغرق ${duration.inMilliseconds}ms',
        tag: tag, level: 'INFO');
  }

  /// تسجيل معلومات عن الشبكة
  ///
  /// المعلمات:
  /// - [url]: عنوان URL
  /// - [method]: طريقة الطلب (GET, POST, etc.)
  /// - [statusCode]: رمز الحالة (اختياري)
  /// - [requestBody]: جسم الطلب (اختياري)
  /// - [responseBody]: جسم الاستجابة (اختياري)
  /// - [error]: الخطأ (اختياري)
  /// - [tag]: علامة التسجيل (اختياري)
  static void network(
    String url,
    String method, {
    int? statusCode,
    Object? requestBody,
    Object? responseBody,
    Object? error,
    String tag = 'NETWORK',
  }) {
    final message = StringBuffer();
    message.write('$method $url');

    if (statusCode != null) {
      message.write(' - Status: $statusCode');
    }

    _log(
      message.toString(),
      tag: tag,
      level: 'INFO',
      error: error,
    );

    if (requestBody != null) {
      debug('Request: $requestBody', tag: tag);
    }

    if (responseBody != null) {
      debug('Response: $responseBody', tag: tag);
    }
  }

  /// تسجيل رسالة
  ///
  /// المعلمات:
  /// - [message]: الرسالة المراد تسجيلها
  /// - [tag]: علامة التسجيل
  /// - [level]: مستوى التسجيل
  /// - [error]: الخطأ (اختياري)
  /// - [stackTrace]: تتبع المكدس (اختياري)
  static void _log(
    String message, {
    required String tag,
    required String level,
    Object? error,
    StackTrace? stackTrace,
  }) async {
    if (!_isEnabled) return;

    final DateTime now = DateTime.now();
    final String formattedTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
    final String logMessage = '[$formattedTime] [$level] [$tag] $message';

    // تسجيل الرسالة في وضع التصحيح
    if (kDebugMode) {
      debugPrint(logMessage);

      if (error != null) {
        debugPrint('Error: $error');
      }

      if (stackTrace != null) {
        debugPrint('StackTrace: $stackTrace');
      }
    }

    // تسجيل الرسالة في ملف
    if (_logFile != null && _isFileLoggingEnabled) {
      try {
        // التحقق من حجم ملف التسجيل
        await _checkLogFileSize();

        // بناء رسالة التسجيل
        String fileLogMessage = '$logMessage\n';

        if (error != null) {
          fileLogMessage += 'Error: $error\n';
        }

        if (stackTrace != null) {
          fileLogMessage += 'StackTrace: $stackTrace\n';
        }

        // كتابة الرسالة في ملف التسجيل
        await _logFile!.writeAsString(
          fileLogMessage,
          mode: FileMode.append,
        );
      } catch (e) {
        if (kDebugMode) {
          debugPrint('خطأ في كتابة السجل في الملف: $e');
        }
      }
    }
  }

  /// الحصول على سجلات التطبيق
  static Future<String> getLogs() async {
    try {
      if (_logFile != null && await _logFile!.exists()) {
        return await _logFile!.readAsString();
      }
    } catch (e) {
      debugPrint('خطأ في قراءة ملف السجل: $e');
    }

    return 'لا توجد سجلات متاحة';
  }

  /// حذف سجلات التطبيق
  static Future<void> clearLogs() async {
    try {
      if (_logFile != null && await _logFile!.exists()) {
        await _logFile!.writeAsString('');
        info('تم مسح سجلات التطبيق', tag: 'LoggerService');
      }
    } catch (e) {
      debugPrint('خطأ في مسح ملف السجل: $e');
    }
  }
}
