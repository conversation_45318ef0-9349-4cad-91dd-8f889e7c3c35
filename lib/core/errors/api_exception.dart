/// استثناء واجهة برمجة التطبيقات
///
/// يستخدم لتمثيل الأخطاء التي تحدث عند الاتصال بواجهات برمجة التطبيقات
class ApiException implements Exception {
  /// رسالة الخطأ
  final String message;

  /// رمز الحالة (اختياري)
  final int? statusCode;

  /// ما إذا كان الخطأ متعلقًا بالاتصال بالإنترنت
  final bool isConnectionError;

  /// إنشاء استثناء واجهة برمجة التطبيقات
  ApiException({
    required this.message,
    this.statusCode,
    this.isConnectionError = false,
  });

  @override
  String toString() {
    return 'ApiException: $message${statusCode != null ? ' (رمز الحالة: $statusCode)' : ''}';
  }
}
