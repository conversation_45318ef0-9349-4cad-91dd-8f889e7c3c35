import 'package:flutter/material.dart';

/// ثيم حقول الإدخال
class TextFieldTheme {
  // منع إنشاء نسخة من الكلاس
  TextFieldTheme._();

  // ألوان أساسية
  static const Color primaryColor = Color(0xFF0D7743);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color borderColor = Color(0xFFE0E0E0);

  /// ثيم حقول الإدخال للوضع النهاري
  static InputDecorationTheme lightInputDecorationTheme() {
    return InputDecorationTheme(
      // لون الحدود
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: borderColor, width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: primaryColor, width: 2.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: errorColor, width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: errorColor, width: 2.0),
        borderRadius: BorderRadius.circular(8.0),
      ),

      // الحشو الداخلي
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),

      // لون النص
      labelStyle: const TextStyle(color: textPrimaryColor),
      hintStyle: const TextStyle(color: textSecondaryColor),
      errorStyle: const TextStyle(color: errorColor, fontSize: 12.0),

      // الخلفية
      filled: true,
      fillColor: Colors.white,
    );
  }

  /// ثيم حقول الإدخال للوضع الليلي
  static InputDecorationTheme darkInputDecorationTheme() {
    return InputDecorationTheme(
      // لون الحدود
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xFF424242), width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: primaryColor, width: 2.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: errorColor, width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: errorColor, width: 2.0),
        borderRadius: BorderRadius.circular(8.0),
      ),

      // الحشو الداخلي
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),

      // لون النص
      labelStyle: const TextStyle(color: Colors.white),
      hintStyle: const TextStyle(color: Colors.grey),
      errorStyle: const TextStyle(color: errorColor, fontSize: 12.0),

      // الخلفية
      filled: true,
      fillColor: const Color(0xFF2C2C2C),
    );
  }
}
