import 'package:flutter/material.dart';
import 'text_field_theme.dart';

/// نظام ألوان التطبيق
class AppTheme {
  // ألوان أساسية
  static const Color primaryColor =
      Color(0xFF0D7743); // لون أخضر غامق مثل الأشجار
  static const Color primaryVariant = Color(0xFF0B5E36); // لون أخضر أغمق
  static const Color secondaryColor = Color(0xFF4CAF50); // لون أخضر فاتح
  static const Color accentColor = Color(0xFF8BC34A); // لون أخضر مصفر

  // ألوان خلفية
  static const Color backgroundColor = Colors.white; // تغيير إلى أبيض
  static const Color surfaceColor = Colors.white;
  static const Color cardColor = Colors.white;

  // ألوان النصوص
  static const Color textPrimaryColor = Color(0xFF212121); // لون أسود غامق
  static const Color textSecondaryColor = Color(0xFF757575); // لون رمادي
  static const Color textOnPrimaryColor =
      Colors.white; // لون أبيض للنصوص على الخلفية الخضراء

  // ألوان أخرى
  static const Color errorColor = Color(0xFFD32F2F); // لون أحمر للأخطاء
  static const Color successColor = Color(0xFF388E3C); // لون أخضر للنجاح
  static const Color warningColor = Color(0xFFFFA000); // لون برتقالي للتحذيرات
  static const Color dividerColor = Color(0xFFBDBDBD); // لون رمادي للفواصل

  // الخط الافتراضي
  static const String fontFamily = 'sultan';

  /// الثيم الرئيسي للتطبيق (الوضع النهاري)
  static ThemeData lightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        onPrimary: textOnPrimaryColor,
        secondary: secondaryColor,
        onSecondary: textOnPrimaryColor,
        surface: surfaceColor,
        onSurface: textPrimaryColor,
        error: errorColor,
        onError: textOnPrimaryColor,
      ),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      dividerColor: dividerColor,
      fontFamily: fontFamily,

      // ألوان النصوص في حقول الإدخال
      hintColor: textSecondaryColor,

      // تخصيص AppBar
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
        elevation: 0,
        centerTitle: true,
      ),

      // تخصيص النصوص في AppBar
      primaryTextTheme: const TextTheme(
        titleLarge: TextStyle(
            color: textOnPrimaryColor,
            fontSize: 20,
            fontWeight: FontWeight.bold),
        titleMedium: TextStyle(color: textOnPrimaryColor, fontSize: 16),
        titleSmall: TextStyle(color: textOnPrimaryColor, fontSize: 14),
      ),

      // تخصيص الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textOnPrimaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        ),
      ),

      // تخصيص البطاقات
      cardTheme: const CardThemeData(
        color: cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      // تخصيص حقول الإدخال
      inputDecorationTheme: TextFieldTheme.lightInputDecorationTheme(),

      // تخصيص لون النص في حقول الإدخال
      textTheme: const TextTheme(
        // هذا هو النص الذي يظهر في حقول الإدخال
        bodyLarge: TextStyle(color: textPrimaryColor, fontSize: 16),
        bodyMedium: TextStyle(color: textPrimaryColor, fontSize: 14),
        bodySmall: TextStyle(color: textSecondaryColor, fontSize: 12),
        titleLarge: TextStyle(
            color: textPrimaryColor, fontWeight: FontWeight.bold, fontSize: 20),
        titleMedium: TextStyle(
            color: textPrimaryColor, fontWeight: FontWeight.bold, fontSize: 16),
        titleSmall: TextStyle(
            color: textPrimaryColor, fontWeight: FontWeight.bold, fontSize: 14),
      ),

      // تخصيص ألوان النصوص في حقول الإدخال
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: primaryColor,
        selectionColor: Color(0x4D4CAF50), // لون شفاف للتحديد
        selectionHandleColor: primaryColor,
      ),

      // تخصيص الـ FloatingActionButton
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
      ),

      // تخصيص الـ BottomNavigationBar
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }

  /// الثيم الداكن للتطبيق (الوضع الليلي)
  static ThemeData darkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.dark(
        primary: primaryColor,
        onPrimary: textOnPrimaryColor,
        secondary: secondaryColor,
        onSecondary: textOnPrimaryColor,
        surface: Color(0xFF1E1E1E),
        onSurface: Colors.white,
        error: errorColor,
        onError: textOnPrimaryColor,
      ),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: const Color(0xFF121212),
      cardColor: const Color(0xFF1E1E1E),
      dividerColor: const Color(0xFF424242),
      fontFamily: fontFamily,

      // ألوان النصوص في حقول الإدخال
      hintColor: Colors.grey,

      // تخصيص AppBar
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
        elevation: 0,
        centerTitle: true,
      ),

      // تخصيص النصوص في AppBar
      primaryTextTheme: const TextTheme(
        titleLarge: TextStyle(
            color: textOnPrimaryColor,
            fontSize: 20,
            fontWeight: FontWeight.bold),
        titleMedium: TextStyle(color: textOnPrimaryColor, fontSize: 16),
        titleSmall: TextStyle(color: textOnPrimaryColor, fontSize: 14),
      ),

      // تخصيص الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textOnPrimaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        ),
      ),

      // تخصيص البطاقات
      cardTheme: const CardThemeData(
        color: Color(0xFF1E1E1E),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      // تخصيص حقول الإدخال
      inputDecorationTheme: TextFieldTheme.darkInputDecorationTheme(),

      // تخصيص لون النص في حقول الإدخال
      textTheme: const TextTheme(
        // هذا هو النص الذي يظهر في حقول الإدخال
        bodyLarge: TextStyle(color: Colors.white, fontSize: 16),
        bodyMedium: TextStyle(color: Colors.white, fontSize: 14),
        bodySmall: TextStyle(color: Colors.white70, fontSize: 12),
        titleLarge: TextStyle(
            color: Colors.white, fontWeight: FontWeight.bold, fontSize: 20),
        titleMedium: TextStyle(
            color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
        titleSmall: TextStyle(
            color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
      ),

      // تخصيص ألوان النصوص في حقول الإدخال
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: primaryColor,
        selectionColor: Color(0x4D4CAF50), // لون شفاف للتحديد
        selectionHandleColor: primaryColor,
      ),

      // تخصيص الـ FloatingActionButton
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
      ),

      // تخصيص الـ BottomNavigationBar
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF1E1E1E),
        selectedItemColor: primaryColor,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }
}
