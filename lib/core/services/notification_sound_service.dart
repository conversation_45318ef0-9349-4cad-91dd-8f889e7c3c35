import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الإشعارات الصوتية
class NotificationSoundService {
  static final NotificationSoundService _instance = NotificationSoundService._internal();
  factory NotificationSoundService() => _instance;
  NotificationSoundService._internal();

  static const String _soundEnabledKey = 'notification_sound_enabled';
  static const String _soundVolumeKey = 'notification_sound_volume';
  
  late AudioPlayer _audioPlayer;
  bool _isInitialized = false;
  bool _soundEnabled = true;
  double _volume = 0.7;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _audioPlayer = AudioPlayer();
      await _loadSettings();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات الصوتية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات الصوتية: $e');
    }
  }

  /// تحميل الإعدادات من SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _soundEnabled = prefs.getBool(_soundEnabledKey) ?? true;
      _volume = prefs.getDouble(_soundVolumeKey) ?? 0.7;
      debugPrint('📱 تم تحميل إعدادات الصوت: مفعل=$_soundEnabled، مستوى الصوت=$_volume');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الصوت: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_soundEnabledKey, _soundEnabled);
      await prefs.setDouble(_soundVolumeKey, _volume);
      debugPrint('💾 تم حفظ إعدادات الصوت');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الصوت: $e');
    }
  }

  /// تشغيل صوت الإشعار حسب النوع
  Future<void> playNotificationSound(String notificationType) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (!_soundEnabled) {
      debugPrint('🔇 الصوت معطل، لن يتم تشغيل الإشعار');
      return;
    }

    try {
      String soundFile = _getSoundFileForType(notificationType);
      await _audioPlayer.setVolume(_volume);
      await _audioPlayer.play(AssetSource(soundFile));
      debugPrint('🔊 تم تشغيل صوت الإشعار: $soundFile');
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل صوت الإشعار: $e');
      // تشغيل الصوت الافتراضي كبديل
      await _playDefaultSound();
    }
  }

  /// تحديد ملف الصوت حسب نوع الإشعار
  String _getSoundFileForType(String notificationType) {
    switch (notificationType) {
      case 'consultation_reply':
        return 'sounds/consultation_reply.mp3';
      case 'new_consultation':
        return 'sounds/new_consultation.mp3';
      case 'weather_alert':
        return 'sounds/weather_alert.mp3';
      case 'pest_alert':
        return 'sounds/pest_alert.mp3';
      case 'irrigation_reminder':
        return 'sounds/irrigation_reminder.mp3';
      case 'urgent':
        return 'sounds/urgent_notification.mp3';
      default:
        return 'sounds/default_notification.mp3';
    }
  }

  /// تشغيل الصوت الافتراضي
  Future<void> _playDefaultSound() async {
    try {
      await _audioPlayer.setVolume(_volume);
      await _audioPlayer.play(AssetSource('sounds/default_notification.mp3'));
      debugPrint('🔊 تم تشغيل الصوت الافتراضي');
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل الصوت الافتراضي: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل الصوت
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
    debugPrint('🔊 تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} الصوت');
  }

  /// تعديل مستوى الصوت (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    await _saveSettings();
    debugPrint('🔊 تم تعديل مستوى الصوت إلى: $_volume');
  }

  /// الحصول على حالة الصوت
  bool get isSoundEnabled => _soundEnabled;

  /// الحصول على مستوى الصوت
  double get volume => _volume;

  /// اختبار الصوت
  Future<void> testSound(String notificationType) async {
    debugPrint('🧪 اختبار صوت الإشعار: $notificationType');
    await playNotificationSound(notificationType);
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
      _isInitialized = false;
      debugPrint('🧹 تم تنظيف موارد خدمة الإشعارات الصوتية');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف موارد الصوت: $e');
    }
  }
}
