import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import 'dart:convert';
import 'enhanced_firebase_service.dart';

/// خدمة الإشعارات المحسنة
/// 
/// وفق المعيار #2: Clean Architecture - Infrastructure Layer
/// وفق المعيار #5: تجزئة الوظائف في وحدات صغيرة
/// وفق المعيار #12: تعليقات عربية شاملة
class EnhancedNotificationService {
  /// مثيل الإشعارات المحلية
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  /// مثيل Firebase Messaging
  static FirebaseMessaging? _firebaseMessaging;

  /// هل تم تهيئة الخدمة
  static bool _isInitialized = false;

  /// معرف القناة الافتراضي
  static const String _defaultChannelId = 'agriculture_notifications';

  /// اسم القناة الافتراضي
  static const String _defaultChannelName = 'إشعارات التطبيق الزراعي';

  /// وصف القناة الافتراضي
  static const String _defaultChannelDescription = 'إشعارات عامة للتطبيق الزراعي';

  /// تهيئة خدمة الإشعارات
  /// 
  /// الإرجاع: [bool] true إذا تم التهيئة بنجاح
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // تهيئة Firebase Messaging إذا كان متاحاً
      await _initializeFirebaseMessaging();

      // طلب الأذونات
      await _requestPermissions();

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('🔔 تم تهيئة خدمة الإشعارات بنجاح');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ فشل في تهيئة خدمة الإشعارات: $e');
      }
      return false;
    }
  }

  /// تهيئة الإشعارات المحلية
  static Future<void> _initializeLocalNotifications() async {
    // إعدادات Android
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    // إعدادات iOS
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // إعدادات التهيئة
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    // تهيئة الإشعارات
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إنشاء قناة الإشعارات لـ Android
    await _createNotificationChannels();
  }

  /// تهيئة Firebase Messaging
  static Future<void> _initializeFirebaseMessaging() async {
    try {
      final firebaseMessaging = EnhancedFirebaseService.getFirebaseMessaging();
      if (firebaseMessaging == null) {
        if (kDebugMode) {
          debugPrint('⚠️ Firebase Messaging غير متاح');
        }
        return;
      }

      _firebaseMessaging = firebaseMessaging;

      // الحصول على FCM Token
      final token = await _firebaseMessaging!.getToken();
      if (token != null && kDebugMode) {
        debugPrint('🔑 FCM Token: $token');
      }

      // الاستماع للرسائل في المقدمة
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // الاستماع للرسائل عند فتح التطبيق
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

      // التحقق من الرسائل عند بدء التطبيق
      final initialMessage = await _firebaseMessaging!.getInitialMessage();
      if (initialMessage != null) {
        _handleInitialMessage(initialMessage);
      }

      if (kDebugMode) {
        debugPrint('🔥 تم تهيئة Firebase Messaging بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ تعذر تهيئة Firebase Messaging: $e');
      }
    }
  }

  /// إنشاء قنوات الإشعارات
  static Future<void> _createNotificationChannels() async {
    // القناة الافتراضية
    const defaultChannel = AndroidNotificationChannel(
      _defaultChannelId,
      _defaultChannelName,
      description: _defaultChannelDescription,
      importance: Importance.high,
      playSound: true,
    );

    // قناة الاستشارات
    const consultationChannel = AndroidNotificationChannel(
      'consultations',
      'إشعارات الاستشارات',
      description: 'إشعارات متعلقة بالاستشارات الزراعية',
      importance: Importance.high,
      playSound: true,
    );

    // قناة المواعيد
    const appointmentChannel = AndroidNotificationChannel(
      'appointments',
      'إشعارات المواعيد',
      description: 'إشعارات متعلقة بالمواعيد',
      importance: Importance.max,
      playSound: true,
    );

    // قناة مراقبة النبات
    const plantMonitoringChannel = AndroidNotificationChannel(
      'plant_monitoring',
      'إشعارات مراقبة النبات',
      description: 'إشعارات متعلقة بمراقبة النبات',
      importance: Importance.high,
      playSound: true,
    );

    // إنشاء القنوات
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(defaultChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(consultationChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(appointmentChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(plantMonitoringChannel);
  }

  /// طلب الأذونات
  static Future<void> _requestPermissions() async {
    // طلب إذن الإشعارات
    final notificationStatus = await Permission.notification.request();
    
    if (kDebugMode) {
      debugPrint('🔔 حالة إذن الإشعارات: $notificationStatus');
    }

    // طلب أذونات Firebase Messaging
    if (_firebaseMessaging != null) {
      final settings = await _firebaseMessaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (kDebugMode) {
        debugPrint('🔥 أذونات Firebase: ${settings.authorizationStatus}');
      }
    }
  }

  /// إرسال إشعار محلي
  /// 
  /// المعلمات:
  /// - [title]: عنوان الإشعار
  /// - [body]: محتوى الإشعار
  /// - [channelId]: معرف القناة (اختياري)
  /// - [payload]: البيانات الإضافية (اختياري)
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? channelId,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      const androidDetails = AndroidNotificationDetails(
        _defaultChannelId,
        _defaultChannelName,
        channelDescription: _defaultChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        id,
        title,
        body,
        details,
        payload: payload,
      );

      if (kDebugMode) {
        debugPrint('📱 تم إرسال إشعار محلي: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إرسال الإشعار المحلي: $e');
      }
    }
  }

  /// إرسال إشعار مجدول
  /// 
  /// المعلمات:
  /// - [title]: عنوان الإشعار
  /// - [body]: محتوى الإشعار
  /// - [scheduledDate]: تاريخ الإرسال
  /// - [channelId]: معرف القناة (اختياري)
  /// - [payload]: البيانات الإضافية (اختياري)
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? channelId,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      const androidDetails = AndroidNotificationDetails(
        _defaultChannelId,
        _defaultChannelName,
        channelDescription: _defaultChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails();

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        details,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      if (kDebugMode) {
        debugPrint('⏰ تم جدولة إشعار: $title في $scheduledDate');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جدولة الإشعار: $e');
      }
    }
  }

  /// إرسال إشعار استشارة
  /// 
  /// المعلمات:
  /// - [title]: عنوان الإشعار
  /// - [body]: محتوى الإشعار
  /// - [consultationId]: معرف الاستشارة
  static Future<void> sendConsultationNotification({
    required String title,
    required String body,
    required String consultationId,
  }) async {
    final payload = json.encode({
      'type': 'consultation',
      'id': consultationId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    await showLocalNotification(
      title: title,
      body: body,
      channelId: 'consultations',
      payload: payload,
    );
  }

  /// إرسال إشعار موعد
  /// 
  /// المعلمات:
  /// - [title]: عنوان الإشعار
  /// - [body]: محتوى الإشعار
  /// - [appointmentId]: معرف الموعد
  static Future<void> sendAppointmentNotification({
    required String title,
    required String body,
    required String appointmentId,
  }) async {
    final payload = json.encode({
      'type': 'appointment',
      'id': appointmentId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    await showLocalNotification(
      title: title,
      body: body,
      channelId: 'appointments',
      payload: payload,
    );
  }

  /// إرسال إشعار مراقبة النبات
  /// 
  /// المعلمات:
  /// - [title]: عنوان الإشعار
  /// - [body]: محتوى الإشعار
  /// - [requestId]: معرف طلب المراقبة
  static Future<void> sendPlantMonitoringNotification({
    required String title,
    required String body,
    required String requestId,
  }) async {
    final payload = json.encode({
      'type': 'plant_monitoring',
      'id': requestId,
      'timestamp': DateTime.now().toIso8601String(),
    });

    await showLocalNotification(
      title: title,
      body: body,
      channelId: 'plant_monitoring',
      payload: payload,
    );
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      debugPrint('🔔 تم النقر على إشعار: ${response.payload}');
    }

    if (response.payload != null) {
      try {
        final data = json.decode(response.payload!);
        _handleNotificationAction(data);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في معالجة payload الإشعار: $e');
        }
      }
    }
  }

  /// معالجة إجراء الإشعار
  static void _handleNotificationAction(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final id = data['id'] as String?;

    if (type == null || id == null) return;

    // TODO: تنفيذ التنقل حسب نوع الإشعار
    switch (type) {
      case 'consultation':
        // التنقل إلى صفحة الاستشارة
        break;
      case 'appointment':
        // التنقل إلى صفحة الموعد
        break;
      case 'plant_monitoring':
        // التنقل إلى صفحة مراقبة النبات
        break;
    }
  }

  /// معالجة الرسائل في المقدمة
  static void _handleForegroundMessage(RemoteMessage message) {
    if (kDebugMode) {
      debugPrint('🔥 رسالة Firebase في المقدمة: ${message.notification?.title}');
    }

    // عرض إشعار محلي للرسائل في المقدمة
    if (message.notification != null) {
      showLocalNotification(
        title: message.notification!.title ?? 'إشعار جديد',
        body: message.notification!.body ?? '',
        payload: json.encode(message.data),
      );
    }
  }

  /// معالجة الرسائل عند فتح التطبيق
  static void _handleMessageOpenedApp(RemoteMessage message) {
    if (kDebugMode) {
      debugPrint('🔥 تم فتح التطبيق من رسالة Firebase: ${message.notification?.title}');
    }

    _handleNotificationAction(message.data);
  }

  /// معالجة الرسالة الأولية
  static void _handleInitialMessage(RemoteMessage message) {
    if (kDebugMode) {
      debugPrint('🔥 رسالة Firebase أولية: ${message.notification?.title}');
    }

    _handleNotificationAction(message.data);
  }

  /// الحصول على FCM Token
  ///
  /// الإرجاع: [String?] FCM Token أو null
  static Future<String?> getFCMToken() async {
    try {
      if (_firebaseMessaging == null) {
        return null;
      }

      return await _firebaseMessaging!.getToken();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في الحصول على FCM Token: $e');
      }
      return null;
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    try {
      await _localNotifications.cancelAll();
      
      if (kDebugMode) {
        debugPrint('🗑️ تم إلغاء جميع الإشعارات');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إلغاء الإشعارات: $e');
      }
    }
  }

  /// إلغاء إشعار محدد
  ///
  /// المعلمات:
  /// - [id]: معرف الإشعار
  static Future<void> cancelNotification(int id) async {
    try {
      await _localNotifications.cancel(id);

      if (kDebugMode) {
        debugPrint('🗑️ تم إلغاء الإشعار: $id');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إلغاء الإشعار: $e');
      }
    }
  }

  /// الحصول على حالة الخدمة
  /// 
  /// الإرجاع: [Map<String, dynamic>] معلومات الحالة
  static Map<String, dynamic> getStatus() {
    return {
      'isInitialized': _isInitialized,
      'hasFirebaseMessaging': _firebaseMessaging != null,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
