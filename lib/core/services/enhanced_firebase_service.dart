import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

/// خدمة Firebase محسنة مع معالجة أفضل للأخطاء
/// 
/// وفق المعيار #2: Clean Architecture - Infrastructure Layer
/// وفق المعيار #5: تجزئة الوظائف في وحدات صغيرة
/// وفق المعيار #12: تعليقات عربية شاملة
class EnhancedFirebaseService {
  /// حالة تهيئة Firebase
  static bool _isInitialized = false;
  
  /// هل Firebase متاح
  static bool _isAvailable = false;
  
  /// رسالة الخطأ إذا كان Firebase غير متاح
  static String? _errorMessage;

  /// تهيئة Firebase مع معالجة محسنة للأخطاء
  /// 
  /// الإرجاع: [bool] true إذا تم التهيئة بنجاح
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return _isAvailable;
    }

    try {
      // محاولة تهيئة Firebase
      await Firebase.initializeApp();
      
      // التحقق من توفر الخدمات
      await _checkFirebaseServices();
      
      _isInitialized = true;
      _isAvailable = true;
      
      if (kDebugMode) {
        print('[Firebase] تم تهيئة Firebase بنجاح');
      }
      
      return true;
    } catch (e) {
      _isInitialized = true;
      _isAvailable = false;
      _errorMessage = e.toString();
      
      if (kDebugMode) {
        print('[Firebase] فشل في تهيئة Firebase: $e');
        print('[Firebase] سيتم تشغيل التطبيق في وضع عدم الاتصال');
      }
      
      return false;
    }
  }

  /// التحقق من توفر خدمات Firebase
  static Future<void> _checkFirebaseServices() async {
    try {
      // التحقق من Firestore
      await FirebaseFirestore.instance.disableNetwork();
      await FirebaseFirestore.instance.enableNetwork();
      
      // التحقق من Auth
      FirebaseAuth.instance.currentUser;
      
      // التحقق من Storage
      FirebaseStorage.instance.ref();

      // التحقق من Messaging
      FirebaseMessaging.instance.getToken();

      if (kDebugMode) {
        print('[Firebase] جميع خدمات Firebase متاحة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] تحذير: بعض خدمات Firebase قد لا تكون متاحة: $e');
      }
    }
  }

  /// الحصول على حالة Firebase
  /// 
  /// الإرجاع: [Map<String, dynamic>] معلومات الحالة
  static Map<String, dynamic> getStatus() {
    return {
      'isInitialized': _isInitialized,
      'isAvailable': _isAvailable,
      'errorMessage': _errorMessage,
      'services': {
        'firestore': _isAvailable,
        'auth': _isAvailable,
        'storage': _isAvailable,
        'messaging': _isAvailable,
      },
    };
  }

  /// الحصول على مرجع Firestore مع معالجة الأخطاء
  /// 
  /// الإرجاع: [FirebaseFirestore?] مرجع Firestore أو null
  static FirebaseFirestore? getFirestore() {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('[Firebase] Firestore غير متاح: $_errorMessage');
      }
      return null;
    }
    
    try {
      return FirebaseFirestore.instance;
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في الوصول إلى Firestore: $e');
      }
      return null;
    }
  }

  /// الحصول على مرجع Auth مع معالجة الأخطاء
  /// 
  /// الإرجاع: [FirebaseAuth?] مرجع Auth أو null
  static FirebaseAuth? getAuth() {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('[Firebase] Auth غير متاح: $_errorMessage');
      }
      return null;
    }
    
    try {
      return FirebaseAuth.instance;
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في الوصول إلى Auth: $e');
      }
      return null;
    }
  }

  /// الحصول على مرجع Storage مع معالجة الأخطاء
  ///
  /// الإرجاع: [FirebaseStorage?] مرجع Storage أو null
  static FirebaseStorage? getStorage() {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('[Firebase] Storage غير متاح: $_errorMessage');
      }
      return null;
    }

    try {
      return FirebaseStorage.instance;
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في الوصول إلى Storage: $e');
      }
      return null;
    }
  }

  /// الحصول على مرجع Messaging مع معالجة الأخطاء
  ///
  /// الإرجاع: [FirebaseMessaging?] مرجع Messaging أو null
  static FirebaseMessaging? getFirebaseMessaging() {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('[Firebase] Messaging غير متاح: $_errorMessage');
      }
      return null;
    }

    try {
      return FirebaseMessaging.instance;
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في الوصول إلى Messaging: $e');
      }
      return null;
    }
  }

  /// تنفيذ عملية Firestore مع معالجة الأخطاء
  /// 
  /// المعلمات:
  /// - [operation]: العملية المطلوب تنفيذها
  /// - [fallback]: القيمة البديلة في حالة الفشل
  /// 
  /// الإرجاع: [T] نتيجة العملية أو القيمة البديلة
  static Future<T> executeFirestoreOperation<T>(
    Future<T> Function(FirebaseFirestore firestore) operation,
    T fallback,
  ) async {
    final firestore = getFirestore();
    if (firestore == null) {
      if (kDebugMode) {
        print('[Firebase] تم استخدام البيانات البديلة لأن Firestore غير متاح');
      }
      return fallback;
    }

    try {
      return await operation(firestore);
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في عملية Firestore: $e');
        print('[Firebase] تم استخدام البيانات البديلة');
      }
      return fallback;
    }
  }

  /// تنفيذ عملية Auth مع معالجة الأخطاء
  /// 
  /// المعلمات:
  /// - [operation]: العملية المطلوب تنفيذها
  /// - [fallback]: القيمة البديلة في حالة الفشل
  /// 
  /// الإرجاع: [T] نتيجة العملية أو القيمة البديلة
  static Future<T> executeAuthOperation<T>(
    Future<T> Function(FirebaseAuth auth) operation,
    T fallback,
  ) async {
    final auth = getAuth();
    if (auth == null) {
      if (kDebugMode) {
        print('[Firebase] تم استخدام البيانات البديلة لأن Auth غير متاح');
      }
      return fallback;
    }

    try {
      return await operation(auth);
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في عملية Auth: $e');
        print('[Firebase] تم استخدام البيانات البديلة');
      }
      return fallback;
    }
  }

  /// تنفيذ عملية Storage مع معالجة الأخطاء
  /// 
  /// المعلمات:
  /// - [operation]: العملية المطلوب تنفيذها
  /// - [fallback]: القيمة البديلة في حالة الفشل
  /// 
  /// الإرجاع: [T] نتيجة العملية أو القيمة البديلة
  static Future<T> executeStorageOperation<T>(
    Future<T> Function(FirebaseStorage storage) operation,
    T fallback,
  ) async {
    final storage = getStorage();
    if (storage == null) {
      if (kDebugMode) {
        print('[Firebase] تم استخدام البيانات البديلة لأن Storage غير متاح');
      }
      return fallback;
    }

    try {
      return await operation(storage);
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في عملية Storage: $e');
        print('[Firebase] تم استخدام البيانات البديلة');
      }
      return fallback;
    }
  }

  /// إعادة تعيين حالة Firebase (للاختبار)
  static void reset() {
    _isInitialized = false;
    _isAvailable = false;
    _errorMessage = null;
  }

  /// التحقق من الاتصال بالإنترنت
  /// 
  /// الإرجاع: [bool] true إذا كان هناك اتصال
  static Future<bool> checkConnectivity() async {
    if (!_isAvailable) {
      return false;
    }

    try {
      final firestore = getFirestore();
      if (firestore == null) {
        return false;
      }

      // محاولة قراءة بسيطة للتحقق من الاتصال
      await firestore.collection('_connectivity_test').limit(1).get();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] لا يوجد اتصال بالإنترنت: $e');
      }
      return false;
    }
  }

  /// تفعيل وضع عدم الاتصال لـ Firestore
  static Future<void> enableOfflineMode() async {
    try {
      final firestore = getFirestore();
      if (firestore != null) {
        await firestore.disableNetwork();
        if (kDebugMode) {
          print('[Firebase] تم تفعيل وضع عدم الاتصال');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في تفعيل وضع عدم الاتصال: $e');
      }
    }
  }

  /// تفعيل وضع الاتصال لـ Firestore
  static Future<void> enableOnlineMode() async {
    try {
      final firestore = getFirestore();
      if (firestore != null) {
        await firestore.enableNetwork();
        if (kDebugMode) {
          print('[Firebase] تم تفعيل وضع الاتصال');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[Firebase] خطأ في تفعيل وضع الاتصال: $e');
      }
    }
  }
}
