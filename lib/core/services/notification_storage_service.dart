import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:agriculture/data/models/notification/notification_model.dart';

/// خدمة التخزين المحلي للإشعارات
class NotificationStorageService {
  static final NotificationStorageService _instance = NotificationStorageService._internal();
  factory NotificationStorageService() => _instance;
  NotificationStorageService._internal();

  static const String _boxName = 'notifications';
  static const int _maxNotifications = 100; // الحد الأقصى للإشعارات المحفوظة
  
  Box<NotificationModel>? _box;
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await Hive.initFlutter();
      
      // تسجيل المحولات إذا لم تكن مسجلة
      // ملاحظة: يجب تسجيل المحولات في main.dart قبل استخدام هذه الخدمة
      // if (!Hive.isAdapterRegistered(0)) {
      //   Hive.registerAdapter(NotificationModelAdapter());
      // }
      // if (!Hive.isAdapterRegistered(1)) {
      //   Hive.registerAdapter(NotificationPriorityAdapter());
      // }
      
      _box = await Hive.openBox<NotificationModel>(_boxName);
      _isInitialized = true;
      
      debugPrint('✅ تم تهيئة خدمة تخزين الإشعارات - العدد الحالي: ${_box!.length}');
      
      // تنظيف الإشعارات القديمة
      await _cleanOldNotifications();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة تخزين الإشعارات: $e');
    }
  }

  /// التأكد من التهيئة
  void _ensureInitialized() {
    if (!_isInitialized || _box == null) {
      throw Exception('خدمة تخزين الإشعارات غير مهيأة. يرجى استدعاء initialize() أولاً');
    }
  }

  /// إضافة إشعار جديد
  Future<void> addNotification(NotificationModel notification) async {
    _ensureInitialized();
    
    try {
      await _box!.put(notification.id, notification);
      debugPrint('✅ تم حفظ الإشعار: ${notification.title}');
      
      // تنظيف الإشعارات الزائدة
      await _limitNotifications();
      
      // إحصائيات
      await _logStatistics();
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الإشعار: $e');
    }
  }

  /// جلب جميع الإشعارات مرتبة حسب التاريخ
  List<NotificationModel> getAllNotifications() {
    _ensureInitialized();
    
    try {
      final notifications = _box!.values.toList();
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return notifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإشعارات: $e');
      return [];
    }
  }

  /// جلب الإشعارات غير المقروءة
  List<NotificationModel> getUnreadNotifications() {
    _ensureInitialized();
    
    try {
      final unreadNotifications = _box!.values
          .where((notification) => !notification.isRead)
          .toList();
      unreadNotifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return unreadNotifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإشعارات غير المقروءة: $e');
      return [];
    }
  }

  /// جلب إشعارات حسب النوع
  List<NotificationModel> getNotificationsByType(String type) {
    _ensureInitialized();
    
    try {
      final notifications = _box!.values
          .where((notification) => notification.type == type)
          .toList();
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return notifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب إشعارات النوع $type: $e');
      return [];
    }
  }

  /// جلب إشعارات مستخدم معين
  List<NotificationModel> getNotificationsForUser(String userId) {
    _ensureInitialized();
    
    try {
      final notifications = _box!.values
          .where((notification) => notification.userId == userId)
          .toList();
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return notifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب إشعارات المستخدم $userId: $e');
      return [];
    }
  }

  /// تحديث حالة القراءة
  Future<void> markAsRead(String notificationId) async {
    _ensureInitialized();
    
    try {
      final notification = _box!.get(notificationId);
      if (notification != null) {
        notification.isRead = true;
        await notification.save();
        debugPrint('✅ تم تحديث حالة القراءة للإشعار: $notificationId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث حالة القراءة: $e');
    }
  }

  /// تحديث جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    _ensureInitialized();
    
    try {
      final unreadNotifications = getUnreadNotifications();
      for (final notification in unreadNotifications) {
        notification.isRead = true;
        await notification.save();
      }
      debugPrint('✅ تم تحديث جميع الإشعارات كمقروءة (${unreadNotifications.length})');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث جميع الإشعارات: $e');
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    _ensureInitialized();
    
    try {
      await _box!.delete(notificationId);
      debugPrint('✅ تم حذف الإشعار: $notificationId');
    } catch (e) {
      debugPrint('❌ خطأ في حذف الإشعار: $e');
    }
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    _ensureInitialized();
    
    try {
      final count = _box!.length;
      await _box!.clear();
      debugPrint('✅ تم مسح جميع الإشعارات ($count)');
    } catch (e) {
      debugPrint('❌ خطأ في مسح الإشعارات: $e');
    }
  }

  /// عدد الإشعارات غير المقروءة
  int getUnreadCount() {
    _ensureInitialized();
    
    try {
      return _box!.values.where((notification) => !notification.isRead).length;
    } catch (e) {
      debugPrint('❌ خطأ في حساب الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// تحديد عدد الإشعارات المحفوظة
  Future<void> _limitNotifications() async {
    try {
      if (_box!.length > _maxNotifications) {
        final notifications = getAllNotifications();
        final toDelete = notifications.skip(_maxNotifications).toList();
        
        for (final notification in toDelete) {
          await _box!.delete(notification.id);
        }
        
        debugPrint('🧹 تم حذف ${toDelete.length} إشعار قديم');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديد عدد الإشعارات: $e');
    }
  }

  /// تنظيف الإشعارات القديمة (أكثر من 30 يوم)
  Future<void> _cleanOldNotifications() async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final oldNotifications = _box!.values
          .where((notification) => notification.timestamp.isBefore(thirtyDaysAgo))
          .toList();
      
      for (final notification in oldNotifications) {
        await _box!.delete(notification.id);
      }
      
      if (oldNotifications.isNotEmpty) {
        debugPrint('🧹 تم حذف ${oldNotifications.length} إشعار قديم (أكثر من 30 يوم)');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الإشعارات القديمة: $e');
    }
  }

  /// طباعة إحصائيات الإشعارات
  Future<void> _logStatistics() async {
    try {
      final total = _box!.length;
      final unread = getUnreadCount();
      final types = <String, int>{};
      
      for (final notification in _box!.values) {
        types[notification.type] = (types[notification.type] ?? 0) + 1;
      }
      
      debugPrint('📊 إحصائيات الإشعارات: المجموع=$total، غير مقروءة=$unread، الأنواع=$types');
    } catch (e) {
      debugPrint('❌ خطأ في حساب الإحصائيات: $e');
    }
  }

  /// إغلاق الخدمة
  Future<void> dispose() async {
    try {
      await _box?.close();
      _isInitialized = false;
      debugPrint('🧹 تم إغلاق خدمة تخزين الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في إغلاق خدمة التخزين: $e');
    }
  }
}
