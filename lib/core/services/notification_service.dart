import 'package:flutter/foundation.dart';
import 'package:agriculture/data/models/notification/notification_model.dart';
import 'package:agriculture/core/services/notification_storage_service.dart';
import 'package:agriculture/core/services/notification_sound_service.dart';

/// الخدمة الرئيسية للإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final NotificationStorageService _storageService = NotificationStorageService();
  final NotificationSoundService _soundService = NotificationSoundService();
  
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _storageService.initialize();
      await _soundService.initialize();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات الرئيسية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// إرسال إشعار جديد
  Future<void> sendNotification({
    required String title,
    required String body,
    required String type,
    String? userId,
    String? consultationId,
    String? advisorId,
    String? imageUrl,
    Map<String, dynamic>? data,
    NotificationPriority priority = NotificationPriority.normal,
    bool playSound = true,
  }) async {
    try {
      // إنشاء الإشعار
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        body: body,
        type: type,
        timestamp: DateTime.now(),
        userId: userId,
        consultationId: consultationId,
        advisorId: advisorId,
        imageUrl: imageUrl,
        data: data,
        priority: priority,
      );

      // حفظ الإشعار
      await _storageService.addNotification(notification);

      // تشغيل الصوت إذا كان مطلوب
      if (playSound) {
        await _soundService.playNotificationSound(type);
      }

      debugPrint('📢 تم إرسال إشعار: $title');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
    }
  }

  /// إشعار رد على الاستشارة للمزارع
  Future<void> sendConsultationReplyNotification({
    required String farmerId,
    required String consultationId,
    required String advisorName,
    required String replyPreview,
  }) async {
    await sendNotification(
      title: 'رد جديد على استشارتك',
      body: 'رد المرشد $advisorName: ${replyPreview.length > 50 ? '${replyPreview.substring(0, 50)}...' : replyPreview}',
      type: NotificationTypes.consultationReply,
      userId: farmerId,
      consultationId: consultationId,
      priority: NotificationPriority.high,
      data: {
        'advisorName': advisorName,
        'consultationId': consultationId,
        'action': 'view_consultation_reply',
      },
    );
  }

  /// إشعار استشارة جديدة للمرشد
  Future<void> sendNewConsultationNotification({
    required String advisorId,
    required String consultationId,
    required String farmerName,
    required String cropType,
    required String problemPreview,
  }) async {
    await sendNotification(
      title: 'استشارة زراعية جديدة',
      body: 'من المزارع $farmerName حول $cropType: ${problemPreview.length > 50 ? '${problemPreview.substring(0, 50)}...' : problemPreview}',
      type: NotificationTypes.newConsultation,
      userId: advisorId,
      consultationId: consultationId,
      priority: NotificationPriority.high,
      data: {
        'farmerName': farmerName,
        'cropType': cropType,
        'consultationId': consultationId,
        'action': 'view_new_consultation',
      },
    );
  }

  /// إشعار تحذير الطقس
  Future<void> sendWeatherAlertNotification({
    required String userId,
    required String alertType,
    required String description,
    String? location,
  }) async {
    await sendNotification(
      title: 'تحذير طقس',
      body: description,
      type: NotificationTypes.weatherAlert,
      userId: userId,
      priority: NotificationPriority.high,
      data: {
        'alertType': alertType,
        'location': location,
        'action': 'view_weather_details',
      },
    );
  }

  /// إشعار تحذير الآفات
  Future<void> sendPestAlertNotification({
    required String userId,
    required String pestType,
    required String cropType,
    required String description,
  }) async {
    await sendNotification(
      title: 'تحذير آفة زراعية',
      body: 'تم رصد $pestType في محاصيل $cropType. $description',
      type: NotificationTypes.pestAlert,
      userId: userId,
      priority: NotificationPriority.urgent,
      data: {
        'pestType': pestType,
        'cropType': cropType,
        'action': 'view_pest_details',
      },
    );
  }

  /// إشعار تذكير الري
  Future<void> sendIrrigationReminderNotification({
    required String userId,
    required String cropType,
    required String fieldName,
  }) async {
    await sendNotification(
      title: 'تذكير الري',
      body: 'حان وقت ري $cropType في $fieldName',
      type: NotificationTypes.irrigationReminder,
      userId: userId,
      priority: NotificationPriority.normal,
      data: {
        'cropType': cropType,
        'fieldName': fieldName,
        'action': 'mark_irrigation_done',
      },
    );
  }

  /// جلب جميع الإشعارات
  List<NotificationModel> getAllNotifications() {
    return _storageService.getAllNotifications();
  }

  /// جلب الإشعارات غير المقروءة
  List<NotificationModel> getUnreadNotifications() {
    return _storageService.getUnreadNotifications();
  }

  /// جلب إشعارات مستخدم معين
  List<NotificationModel> getNotificationsForUser(String userId) {
    return _storageService.getNotificationsForUser(userId);
  }

  /// عدد الإشعارات غير المقروءة
  int getUnreadCount() {
    return _storageService.getUnreadCount();
  }

  /// عدد الإشعارات غير المقروءة لمستخدم معين
  int getUnreadCountForUser(String userId) {
    return _storageService.getNotificationsForUser(userId)
        .where((notification) => !notification.isRead)
        .length;
  }

  /// تحديث حالة القراءة
  Future<void> markAsRead(String notificationId) async {
    await _storageService.markAsRead(notificationId);
  }

  /// تحديث جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    await _storageService.markAllAsRead();
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    await _storageService.deleteNotification(notificationId);
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    await _storageService.clearAllNotifications();
  }

  /// تفعيل/إلغاء تفعيل الصوت
  Future<void> setSoundEnabled(bool enabled) async {
    await _soundService.setSoundEnabled(enabled);
  }

  /// تعديل مستوى الصوت
  Future<void> setVolume(double volume) async {
    await _soundService.setVolume(volume);
  }

  /// الحصول على حالة الصوت
  bool get isSoundEnabled => _soundService.isSoundEnabled;

  /// الحصول على مستوى الصوت
  double get volume => _soundService.volume;

  /// اختبار الصوت
  Future<void> testSound(String notificationType) async {
    await _soundService.testSound(notificationType);
  }

  /// إغلاق الخدمة
  Future<void> dispose() async {
    await _storageService.dispose();
    await _soundService.dispose();
    _isInitialized = false;
    debugPrint('🧹 تم إغلاق خدمة الإشعارات الرئيسية');
  }
}
