import 'package:flutter/material.dart';
import 'package:agriculture/data/models/floating_action/fab_action_model.dart';
import 'package:agriculture/core/constants/floating_action_button_constants.dart';
import 'package:agriculture/core/services/notification_service.dart';

/// خدمة الزر العائم الذكي للاستشاري الزراعي
/// وفق المعيار #3: الملفات المشتركة أولاً
/// وفق المعيار #5: تجزئة الوظائف في وحدات صغيرة
class FloatingActionButtonService {
  static final FloatingActionButtonService _instance = FloatingActionButtonService._internal();
  factory FloatingActionButtonService() => _instance;
  FloatingActionButtonService._internal();

  final NotificationService _notificationService = NotificationService();

  /// إنشاء إجراءات الاستشاري الافتراضية
  List<FabActionModel> createAdvisorActions(BuildContext context) {
    return [
      // مزامنة التقويم الزراعي
      FabActionModel(
        id: 'calendar_sync',
        title: FloatingActionButtonConstants.actionTitles['calendar_sync']!,
        description: FloatingActionButtonConstants.actionDescriptions['calendar_sync']!,
        icon: Icons.calendar_today,
        color: Color(FloatingActionButtonConstants.actionColors['calendar_sync']!),
        priority: FloatingActionButtonConstants.actionPriority['calendar_sync']!,
        onTap: () => _handleCalendarSync(context),
        notificationChannelId: FloatingActionButtonConstants.notificationChannels['calendar_sync'],
        notificationSound: FloatingActionButtonConstants.notificationSounds['calendar_sync'],
      ),

      // تحليل صورة الاستشارة
      FabActionModel(
        id: 'image_analysis',
        title: FloatingActionButtonConstants.actionTitles['image_analysis']!,
        description: FloatingActionButtonConstants.actionDescriptions['image_analysis']!,
        icon: Icons.photo_camera,
        color: Color(FloatingActionButtonConstants.actionColors['image_analysis']!),
        priority: FloatingActionButtonConstants.actionPriority['image_analysis']!,
        onTap: () => _handleImageAnalysis(context),
      ),

      // اقتراح خطة علاجية
      FabActionModel(
        id: 'treatment_plan',
        title: FloatingActionButtonConstants.actionTitles['treatment_plan']!,
        description: FloatingActionButtonConstants.actionDescriptions['treatment_plan']!,
        icon: Icons.auto_fix_high,
        color: Color(FloatingActionButtonConstants.actionColors['treatment_plan']!),
        priority: FloatingActionButtonConstants.actionPriority['treatment_plan']!,
        onTap: () => _handleTreatmentPlan(context),
      ),

      // تحذير الطقس
      FabActionModel(
        id: 'weather_alert',
        title: FloatingActionButtonConstants.actionTitles['weather_alert']!,
        description: FloatingActionButtonConstants.actionDescriptions['weather_alert']!,
        icon: Icons.cloud,
        color: Color(FloatingActionButtonConstants.actionColors['weather_alert']!),
        priority: FloatingActionButtonConstants.actionPriority['weather_alert']!,
        onTap: () => _handleWeatherAlert(context),
        notificationChannelId: FloatingActionButtonConstants.notificationChannels['weather_alert'],
        notificationSound: FloatingActionButtonConstants.notificationSounds['weather_alert'],
      ),

      // كشف الآفات
      FabActionModel(
        id: 'pest_detection',
        title: FloatingActionButtonConstants.actionTitles['pest_detection']!,
        description: FloatingActionButtonConstants.actionDescriptions['pest_detection']!,
        icon: Icons.bug_report,
        color: Color(FloatingActionButtonConstants.actionColors['pest_detection']!),
        priority: FloatingActionButtonConstants.actionPriority['pest_detection']!,
        onTap: () => _handlePestDetection(context),
        notificationChannelId: FloatingActionButtonConstants.notificationChannels['pest_detection'],
        notificationSound: FloatingActionButtonConstants.notificationSounds['pest_detection'],
      ),

      // تذكير الري
      FabActionModel(
        id: 'irrigation_reminder',
        title: FloatingActionButtonConstants.actionTitles['irrigation_reminder']!,
        description: FloatingActionButtonConstants.actionDescriptions['irrigation_reminder']!,
        icon: Icons.water_drop,
        color: Color(FloatingActionButtonConstants.actionColors['irrigation_reminder']!),
        priority: FloatingActionButtonConstants.actionPriority['irrigation_reminder']!,
        onTap: () => _handleIrrigationReminder(context),
        notificationChannelId: FloatingActionButtonConstants.notificationChannels['irrigation_reminder'],
        notificationSound: FloatingActionButtonConstants.notificationSounds['irrigation_reminder'],
      ),
    ];
  }

  /// معالجة مزامنة التقويم الزراعي
  Future<void> _handleCalendarSync(BuildContext context) async {
    try {
      debugPrint('🗓️ بدء مزامنة التقويم الزراعي');
      
      // عرض حوار التحميل
      _showLoadingDialog(context, 'جاري مزامنة التقويم...');

      // محاكاة عملية المزامنة
      await Future.delayed(const Duration(seconds: 2));

      // إغلاق حوار التحميل
      Navigator.of(context).pop();

      // عرض رسالة النجاح
      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['calendar_sync']!,
      );

      // إرسال إشعار
      await _notificationService.sendNotification(
        title: 'مزامنة التقويم',
        body: 'تمت مزامنة التواريخ المهمة مع التقويم بنجاح',
        type: 'calendar_sync',
      );

      debugPrint('✅ تمت مزامنة التقويم بنجاح');
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق حوار التحميل
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['calendar_sync']!,
      );
      debugPrint('❌ خطأ في مزامنة التقويم: $e');
    }
  }

  /// معالجة تحليل الصورة
  Future<void> _handleImageAnalysis(BuildContext context) async {
    try {
      debugPrint('📸 بدء تحليل الصورة');
      
      _showLoadingDialog(context, 'جاري تحليل الصورة...');
      await Future.delayed(const Duration(seconds: 3));
      Navigator.of(context).pop();

      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['image_analysis']!,
      );

      debugPrint('✅ تم تحليل الصورة بنجاح');
    } catch (e) {
      Navigator.of(context).pop();
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['image_analysis']!,
      );
      debugPrint('❌ خطأ في تحليل الصورة: $e');
    }
  }

  /// معالجة خطة العلاج
  Future<void> _handleTreatmentPlan(BuildContext context) async {
    try {
      debugPrint('💊 بدء إنشاء خطة العلاج');
      
      _showLoadingDialog(context, 'جاري إنشاء خطة العلاج...');
      await Future.delayed(const Duration(seconds: 2));
      Navigator.of(context).pop();

      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['treatment_plan']!,
      );

      debugPrint('✅ تم إنشاء خطة العلاج بنجاح');
    } catch (e) {
      Navigator.of(context).pop();
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['treatment_plan']!,
      );
      debugPrint('❌ خطأ في إنشاء خطة العلاج: $e');
    }
  }

  /// معالجة تحذير الطقس
  Future<void> _handleWeatherAlert(BuildContext context) async {
    try {
      debugPrint('🌤️ بدء إرسال تحذير الطقس');
      
      _showLoadingDialog(context, 'جاري إرسال تحذير الطقس...');
      await Future.delayed(const Duration(seconds: 1));
      Navigator.of(context).pop();

      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['weather_alert']!,
      );

      await _notificationService.sendWeatherAlertNotification(
        userId: 'current_user',
        alertType: 'عاصفة رملية',
        description: 'تحذير: عاصفة رملية متوقعة خلال 24 ساعة',
      );

      debugPrint('✅ تم إرسال تحذير الطقس بنجاح');
    } catch (e) {
      Navigator.of(context).pop();
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['weather_alert']!,
      );
      debugPrint('❌ خطأ في إرسال تحذير الطقس: $e');
    }
  }

  /// معالجة كشف الآفات
  Future<void> _handlePestDetection(BuildContext context) async {
    try {
      debugPrint('🐛 بدء فحص الآفات');
      
      _showLoadingDialog(context, 'جاري فحص الآفات...');
      await Future.delayed(const Duration(seconds: 2));
      Navigator.of(context).pop();

      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['pest_detection']!,
      );

      await _notificationService.sendPestAlertNotification(
        userId: 'current_user',
        pestType: 'المن الأخضر',
        cropType: 'الطماطم',
        description: 'تم رصد آفة المن الأخضر في محاصيل الطماطم',
      );

      debugPrint('✅ تم فحص الآفات بنجاح');
    } catch (e) {
      Navigator.of(context).pop();
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['pest_detection']!,
      );
      debugPrint('❌ خطأ في فحص الآفات: $e');
    }
  }

  /// معالجة تذكير الري
  Future<void> _handleIrrigationReminder(BuildContext context) async {
    try {
      debugPrint('💧 بدء جدولة تذكير الري');
      
      _showLoadingDialog(context, 'جاري جدولة تذكير الري...');
      await Future.delayed(const Duration(seconds: 1));
      Navigator.of(context).pop();

      _showSuccessMessage(
        context,
        FloatingActionButtonConstants.successMessages['irrigation_reminder']!,
      );

      await _notificationService.sendIrrigationReminderNotification(
        userId: 'current_user',
        cropType: 'الخضروات',
        fieldName: 'الحقل الشمالي',
      );

      debugPrint('✅ تم جدولة تذكير الري بنجاح');
    } catch (e) {
      Navigator.of(context).pop();
      _showErrorMessage(
        context,
        FloatingActionButtonConstants.errorMessages['irrigation_reminder']!,
      );
      debugPrint('❌ خطأ في جدولة تذكير الري: $e');
    }
  }

  /// عرض حوار التحميل
  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 20),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  /// عرض رسالة النجاح
  void _showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  /// عرض رسالة الخطأ
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
