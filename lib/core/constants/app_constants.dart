
/// ثوابت عامة للتطبيق
///
/// يحتوي هذا الكلاس على الثوابت العامة المستخدمة في التطبيق.
class AppConstants {
  // منع إنشاء نسخة من الكلاس
  AppConstants._();

  /// معلومات التطبيق
  static const String appName = 'تطبيق الزراعة';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  static const String appPackageName = 'com.example.agriculture';
  static const String appBundleId = 'com.example.agriculture';
  static const String appStoreId = '';
  static const String appPlayStoreId = '';

  /// معلومات المطور
  static const String developerName = 'مطور التطبيق';
  static const String developerEmail = '<EMAIL>';
  static const String developerWebsite = 'https://example.com';

  /// روابط التطبيق
  static const String privacyPolicyUrl = 'https://example.com/privacy-policy';
  static const String termsOfServiceUrl =
      'https://example.com/terms-of-service';
  static const String supportUrl = 'https://example.com/support';
  static const String contactUrl = 'https://example.com/contact';
  static const String faqUrl = 'https://example.com/faq';
  static const String aboutUrl = 'https://example.com/about';
  static const String rateAppUrl =
      'https://play.google.com/store/apps/details?id=$appPackageName';
  static const String shareAppUrl =
      'https://play.google.com/store/apps/details?id=$appPackageName';

  /// روابط التواصل الاجتماعي
  static const String facebookUrl = 'https://facebook.com/example';
  static const String twitterUrl = 'https://twitter.com/example';
  static const String instagramUrl = 'https://instagram.com/example';
  static const String youtubeUrl = 'https://youtube.com/example';
  static const String linkedinUrl = 'https://linkedin.com/company/example';

  /// إعدادات التطبيق
  static const int splashScreenDuration = 2000; // بالميلي ثانية
  static const int animationDuration = 300; // بالميلي ثانية
  static const int debounceTime = 500; // بالميلي ثانية
  static const int throttleTime = 500; // بالميلي ثانية
  static const int maxRetryAttempts = 3;
  static const int maxCacheSize = 100 * 1024 * 1024; // 100 ميجابايت
  static const int maxCacheAge = 7 * 24 * 60 * 60; // 7 أيام بالثواني

  /// إعدادات الوسائط
  static const int defaultImageQuality = 80; // جودة الصور المضغوطة (0-100)
  static const int defaultImageMaxWidth = 1080; // العرض الأقصى للصور المضغوطة
  static const int defaultImageMaxHeight = 1920; // الارتفاع الأقصى للصور المضغوطة
  // جودة الفيديو المضغوط - تم تعطيل الضغط مؤقتًا
  static const int maxImageUploadSize = 10 * 1024 * 1024; // 10 ميجابايت
  static const int maxVideoUploadSize = 100 * 1024 * 1024; // 100 ميجابايت
  static const int maxVideoUploadDuration = 5 * 60; // 5 دقائق بالثواني

  /// إعدادات الشبكة
  static const int connectionTimeout = 30000; // 30 ثانية
  static const int receiveTimeout = 30000; // 30 ثانية
  static const int sendTimeout = 30000; // 30 ثانية
  static const String baseUrl = 'https://api.example.com';

  /// إعدادات التحقق
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 30;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  static const int otpLength = 6;
  static const int otpExpiryTime = 120; // بالثواني

  /// إعدادات المحتوى
  static const int maxPostLength = 1000;
  static const int maxCommentLength = 500;
  static const int maxImageSize = 5 * 1024 * 1024; // 5 ميجابايت
  static const int maxVideoSize = 50 * 1024 * 1024; // 50 ميجابايت
  static const int maxVideoLength = 60; // بالثواني

  /// إعدادات الخريطة
  static const double defaultLatitude = 15.3694; // صنعاء، اليمن
  static const double defaultLongitude = 44.1910; // صنعاء، اليمن
  static const double defaultZoom = 12.0;
  static const double minZoom = 5.0;
  static const double maxZoom = 18.0;

  /// إعدادات التخزين المحلي
  static const String prefsKeyToken = 'token';
  static const String prefsKeyUserId = 'userId';
  static const String prefsKeyUserName = 'userName';
  static const String prefsKeyUserEmail = 'userEmail';
  static const String prefsKeyUserPhone = 'userPhone';
  static const String prefsKeyUserRole = 'userRole';
  static const String prefsKeyUserAvatar = 'userAvatar';
  static const String prefsKeyLanguage = 'language';
  static const String prefsKeyThemeMode = 'themeMode';
  static const String prefsKeyFirstRun = 'firstRun';
  static const String prefsKeyLastLogin = 'lastLogin';
  static const String prefsKeyRecentSearches = 'recentSearches';
  static const String prefsKeyNotificationsEnabled = 'notificationsEnabled';
  static const String prefsKeyIsAuth = 'isAuth';
  static const String prefsKeyIsOnBoarding = 'isOnBoarding';

  /// إعدادات Firebase
  static const String firebaseCollectionUsers = 'users';
  static const String firebaseCollectionPosts = 'posts';
  static const String firebaseCollectionComments = 'comments';
  static const String firebaseCollectionLikes = 'likes';
  static const String firebaseCollectionNotifications = 'notifications';

  /// مجموعات المرشدين الزراعيين
  static const String firebaseCollectionAdvisors = 'advisors';
  static const String firebaseCollectionConsultations = 'consultations';
  static const String firebaseCollectionAppointments = 'appointments';

  /// إعدادات الإشعارات
  static const String notificationChannelId = 'default_channel';
  static const String notificationChannelName = 'Default Channel';
  static const String notificationChannelDescription =
      'Default Notification Channel';

  /// ثوابت التحقق من المدخلات
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phoneRegex = r'^\+?[0-9]{10,15}$';
  static const String urlRegex =
      r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$';

  /// رسائل المعلومات
  static const String infoNoData = 'لا توجد بيانات متاحة.';
  static const String infoNoResults = 'لا توجد نتائج للبحث.';
  static const String infoNoNotifications = 'لا توجد إشعارات.';
  static const String infoNoComments = 'لا توجد تعليقات.';
  static const String infoNoPosts = 'لا توجد منشورات.';
  static const String infoNoInternet = 'لا يوجد اتصال بالإنترنت.';
  static const String infoLoading = 'جاري التحميل...';
  static const String infoProcessing = 'جاري المعالجة...';
  static const String infoUploading = 'جاري الرفع...';
  static const String infoDownloading = 'جاري التنزيل...';

  /// رسائل الخطأ العامة
  static const String errorGeneral = 'حدث خطأ ما. يرجى المحاولة مرة أخرى.';
  static const String errorNetwork =
      'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.';
  static const String errorServer =
      'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقًا.';
  static const String errorTimeout =
      'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
  static const String errorAuth =
      'فشل في المصادقة. يرجى تسجيل الدخول مرة أخرى.';
  static const String errorPermission =
      'ليس لديك صلاحية للوصول إلى هذه الميزة.';
  static const String errorValidation =
      'يرجى التحقق من المدخلات وإعادة المحاولة.';
  static const String errorNotFound = 'العنصر المطلوب غير موجود.';

  /// رسائل النجاح العامة
  static const String successLogin = 'تم تسجيل الدخول بنجاح.';
  static const String successRegister = 'تم التسجيل بنجاح.';
  static const String successLogout = 'تم تسجيل الخروج بنجاح.';
  static const String successProfileUpdate = 'تم تحديث الملف الشخصي بنجاح.';
  static const String successPasswordChange = 'تم تغيير كلمة المرور بنجاح.';
  static const String successPasswordReset =
      'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.';
  static const String successEmailVerification =
      'تم التحقق من بريدك الإلكتروني بنجاح.';
  static const String successPostCreation = 'تم إنشاء المنشور بنجاح.';
  static const String successPostUpdate = 'تم تحديث المنشور بنجاح.';
  static const String successPostDelete = 'تم حذف المنشور بنجاح.';
  static const String successCommentCreation = 'تم إضافة التعليق بنجاح.';
  static const String successCommentUpdate = 'تم تحديث التعليق بنجاح.';
  static const String successCommentDelete = 'تم حذف التعليق بنجاح.';

  /// رسائل التحقق
  static const String validationRequired = 'هذا الحقل مطلوب.';
  static const String validationEmail = 'يرجى إدخال بريد إلكتروني صالح.';
  static const String validationPassword =
      'يجب أن تكون كلمة المرور على الأقل $minPasswordLength أحرف.';
  static const String validationPasswordMatch = 'كلمات المرور غير متطابقة.';
  static const String validationPhone = 'يرجى إدخال رقم هاتف صالح.';
  static const String validationName = 'يرجى إدخال اسم صالح.';
  static const String validationUsername =
      'يجب أن يكون اسم المستخدم بين $minUsernameLength و $maxUsernameLength حرفًا.';
  static const String validationOtp =
      'يرجى إدخال رمز التحقق المكون من $otpLength أرقام.';
  static const String validationPostLength =
      'يجب أن يكون المنشور أقل من $maxPostLength حرف.';
  static const String validationCommentLength =
      'يجب أن يكون التعليق أقل من $maxCommentLength حرف.';
  static const String validationImageSize =
      'يجب أن يكون حجم الصورة أقل من ${maxImageSize / (1024 * 1024)} ميجابايت.';
  static const String validationVideoSize =
      'يجب أن يكون حجم الفيديو أقل من ${maxVideoSize / (1024 * 1024)} ميجابايت.';
  static const String validationVideoLength =
      'يجب أن تكون مدة الفيديو أقل من $maxVideoLength ثانية.';

  /// رسائل التأكيد
  static const String confirmLogout = 'هل أنت متأكد أنك تريد تسجيل الخروج؟';
  static const String confirmDeleteAccount =
      'هل أنت متأكد أنك تريد حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.';
  static const String confirmDeletePost =
      'هل أنت متأكد أنك تريد حذف هذا المنشور؟';
  static const String confirmDeleteComment =
      'هل أنت متأكد أنك تريد حذف هذا التعليق؟';
  static const String confirmDiscardChanges =
      'هل أنت متأكد أنك تريد تجاهل التغييرات؟';

  /// أسماء الأيام
  static const List<String> weekDays = [
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد',
  ];

  /// أسماء الأشهر
  static const List<String> months = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  /// أسماء الفصول
  static const List<String> seasons = [
    'الربيع',
    'الصيف',
    'الخريف',
    'الشتاء',
  ];
}
