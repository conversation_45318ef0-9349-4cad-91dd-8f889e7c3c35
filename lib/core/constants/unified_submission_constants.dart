/// ثوابت الخدمة الموحدة لإرسال الطلبات
/// 
/// وفق المعيار #4: توحيد القيم باستخدام ملفات الثوابت
/// يحتوي على جميع النصوص والقيم الثابتة المستخدمة في الخدمة الموحدة
class UnifiedSubmissionConstants {
  
  /// === معرفات افتراضية ===
  
  /// معرف المرشد العام الافتراضي
  static const String defaultAdvisorId = 'general_advisor';
  
  /// معرف المستخدم المجهول
  static const String anonymousUserId = 'anonymous_user';
  
  /// اسم المستخدم الافتراضي
  static const String defaultUserName = 'مزارع';
  
  /// === رسائل التحميل ===
  
  /// رسالة تحميل الاستشارة
  static const String consultationLoadingMessage = 'جاري إرسال الاستشارة...';
  
  /// رسالة تحميل الموعد
  static const String appointmentLoadingMessage = 'جاري حجز الموعد...';
  
  /// رسالة تحميل مراقبة النبات
  static const String plantMonitoringLoadingMessage = 'جاري إرسال طلب مراقبة النبات...';
  
  /// === رسائل النجاح ===
  
  /// عنوان نجاح الاستشارة
  static const String consultationSuccessTitle = 'تم إرسال الاستشارة بنجاح!';
  
  /// رسالة نجاح الاستشارة
  static const String consultationSuccessMessage = 'تم تقديم استشارتك للمرشد الزراعي وسيتم الرد عليها قريباً';
  
  /// عنوان نجاح الموعد
  static const String appointmentSuccessTitle = 'تم حجز الموعد بنجاح!';
  
  /// رسالة نجاح الموعد
  static const String appointmentSuccessMessage = 'تم تسجيل موعدك مع المرشد الزراعي وسيتم التأكيد قريباً';
  
  /// عنوان نجاح مراقبة النبات
  static const String plantMonitoringSuccessTitle = 'تم إرسال طلب مراقبة النبات بنجاح!';
  
  /// رسالة نجاح مراقبة النبات
  static const String plantMonitoringSuccessMessage = 'تم تقديم طلبك للمرشد الزراعي وسيتم التواصل معك قريباً';
  
  /// === رسائل الخطأ ===
  
  /// رسالة خطأ الاستشارة
  static const String consultationErrorMessage = 'حدث خطأ أثناء إرسال الاستشارة';
  
  /// رسالة خطأ الموعد
  static const String appointmentErrorMessage = 'حدث خطأ أثناء حجز الموعد';
  
  /// رسالة خطأ مراقبة النبات
  static const String plantMonitoringErrorMessage = 'حدث خطأ أثناء إرسال طلب مراقبة النبات';
  
  /// === رسائل التحقق من الصحة ===
  
  /// رسالة خطأ نوع الاستشارة
  static const String consultationTypeError = 'يرجى اختيار نوع الاستشارة';
  
  /// رسالة خطأ التاريخ في الماضي
  static const String pastDateError = 'لا يمكن حجز موعد في الماضي';
  
  /// رسالة خطأ التاريخ غير صحيح
  static const String invalidDateError = 'تاريخ غير صحيح';
  
  /// رسالة خطأ نوع النبات
  static const String plantTypeError = 'يرجى إدخال نوع النبات';
  
  /// رسالة خطأ الموقع
  static const String locationError = 'يرجى إدخال الموقع';
  
  /// رسالة خطأ نوع المراقبة
  static const String monitoringTypeError = 'يرجى اختيار نوع المراقبة';
  
  /// === تسميات التفاصيل ===
  
  /// تسمية نوع المحصول
  static const String cropTypeLabel = 'نوع المحصول';
  
  /// تسمية المساحة
  static const String areaLabel = 'المساحة';
  
  /// تسمية عدد الصور
  static const String imagesCountLabel = 'عدد الصور';
  
  /// تسمية نوع الاستشارة
  static const String consultationTypeLabel = 'نوع الاستشارة';
  
  /// تسمية التاريخ
  static const String dateLabel = 'التاريخ';
  
  /// تسمية الوقت
  static const String timeLabel = 'الوقت';
  
  /// تسمية نوع النبات
  static const String plantTypeLabel = 'نوع النبات';
  
  /// تسمية الموقع
  static const String locationLabel = 'الموقع';
  
  /// تسمية نوع المراقبة
  static const String monitoringTypeLabel = 'نوع المراقبة';
  
  /// === قيم افتراضية ===
  
  /// قيمة "غير محدد"
  static const String notSpecifiedValue = 'غير محدد';
  
  /// قيمة "0" للعدد
  static const String zeroValue = '0';
  
  /// === عناوين الطلبات ===
  
  /// بادئة عنوان الاستشارة
  static const String consultationTitlePrefix = 'استشارة في';
  
  /// بادئة عنوان الموعد
  static const String appointmentTitlePrefix = 'موعد';
  
  /// بادئة عنوان مراقبة النبات
  static const String plantMonitoringTitlePrefix = 'مراقبة';
  
  /// === نص وصف مراقبة النبات ===
  
  /// قالب وصف مراقبة النبات
  static const String plantMonitoringDescriptionTemplate = '''
طلب مراقبة نبات:
- نوع النبات: {plantType}
- الموقع: {location}
- نوع المراقبة: {monitoringType}
{notes}''';
  
  /// بادئة الملاحظات
  static const String notesPrefix = '- ملاحظات: ';
  
  /// === إعدادات الحوارات ===
  
  /// مدة عرض SnackBar (بالثواني)
  static const int snackBarDuration = 4;
  
  /// نص زر الموافق
  static const String okButtonText = 'موافق';
  
  /// === مفاتيح SharedPreferences ===
  
  /// مفتاح معرف المستخدم
  static const String userIdKey = 'uid';
  
  /// مفتاح اسم المستخدم
  static const String userNameKey = 'userName';
  
  /// مفتاح صورة المستخدم
  static const String userImageKey = 'userImage';
  
  /// مفتاح هاتف المستخدم
  static const String userPhoneKey = 'userPhone';
  
  /// === إعدادات المساحة الافتراضية ===
  
  /// مساحة افتراضية لمراقبة النبات
  static const String defaultPlantMonitoringArea = '1000';
  
  /// === أولويات الطلبات ===

  /// الأولوية العادية (افتراضية)
  static const String defaultPriorityName = 'normal';
  
  /// === رسائل التصحيح ===
  
  /// رسالة بدء العملية
  static const String debugStartMessage = 'بدء عملية إرسال الطلب';
  
  /// رسالة نجاح العملية
  static const String debugSuccessMessage = 'تم إرسال الطلب بنجاح';
  
  /// رسالة فشل العملية
  static const String debugFailureMessage = 'فشل في إرسال الطلب';
}
