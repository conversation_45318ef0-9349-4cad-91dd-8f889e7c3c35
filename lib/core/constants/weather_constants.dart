import 'package:flutter/material.dart';

import 'assets_colors.dart';

/// ثوابت الطقس
///
/// يحتوي هذا الكلاس على ثوابت مستخدمة في مكونات الطقس.
class WeatherConstants {
  // منع إنشاء نسخة من الكلاس
  WeatherConstants._();

  /// ألوان الطقس
  static final Color cardBackgroundColor = AssetsColors.wether2;
  static const Color cardBackgroundColorLight = Color(0xff0d7743);
  static const Color textColor = AssetsColors.kWhite;
  static const Color textColorSecondary = Color(0xFFE0E0E0);
  static const Color dividerColor = Color(0x33FFFFFF);

  /// أحجام الطقس
  static const double cardBorderRadius = 16.0;
  static const double cardPadding = 16.0;
  static const double iconSize = 40.0;
  static const double smallIconSize = 24.0;
  static const double microIconSize = 14.0;
  static const double titleFontSize = 18.0;
  static const double subtitleFontSize = 16.0;
  static const double bodyFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double microFontSize = 10.0;

  /// مؤشرات ملاءمة الطقس للأنشطة الزراعية
  static const Color suitabilityHighColor = Colors.green;
  static const Color suitabilityMediumColor = Colors.orange;
  static const Color suitabilityLowColor = Colors.red;

  /// نصوص مؤشرات ملاءمة الطقس
  static const String suitabilityHighText = 'مناسب';
  static const String suitabilityMediumText = 'متوسط';
  static const String suitabilityLowText = 'غير مناسب';

  /// عدد الساعات المعروضة افتراضياً في توقعات الطقس بالساعة
  static const int defaultHourlyForecastCount = 8;

  /// عناوين الأقسام
  static const String agriculturalInfoTitle = 'معلومات زراعية';
  static const String sunriseSunsetTitle = 'الشروق والغروب';
  static const String pressureWindTitle = 'الضغط الجوي والرياح';
  static const String activitySuitabilityTitle = 'ملاءمة الطقس للأنشطة الزراعية';
  static const String hourlyForecastTitle = 'توقعات الساعات القادمة';
  static const String weeklyForecastTitle = 'توقعات الأسبوع';

  /// أسماء الأنشطة الزراعية
  static const String irrigationActivity = 'الري';
  static const String sprayingActivity = 'الرش';
  static const String harvestingActivity = 'الحصاد';

  /// أسماء معلومات الطقس
  static const String sunriseInfo = 'الشروق';
  static const String sunsetInfo = 'الغروب';
  static const String dayLengthInfo = 'طول النهار';
  static const String pressureInfo = 'الضغط الجوي';
  static const String windSpeedInfo = 'سرعة الرياح';
  static const String windDirectionInfo = 'اتجاه الرياح';
  static const String humidityInfo = 'الرطوبة';
  static const String rainProbabilityInfo = 'احتمال المطر';

  /// وحدات القياس
  static const String pressureUnit = 'هيكتوباسكال';
  static const String windSpeedUnit = 'م/ث';
  static const String percentUnit = '%';
  static const String temperatureUnit = '°';
}
