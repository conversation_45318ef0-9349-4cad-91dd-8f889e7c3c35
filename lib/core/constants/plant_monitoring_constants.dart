import 'agricultural_services_constants.dart';

/// ثوابت خدمة مراقبة النبات
///
/// يحتوي على جميع الثوابت المستخدمة في خدمة مراقبة النبات
class PlantMonitoringConstants {
  
  /// أنواع المحاصيل المدعومة (مرجع من الثوابت المشتركة)
  static const List<String> cropTypes = AgriculturalServicesConstants.cropTypes;

  /// أنواع المراقبة المتاحة
  static const List<String> monitoringTypes = [
    'فحص شامل',
    'تشخيص الأمراض',
    'مراقبة النمو',
    'فحص التربة',
    'تحليل المياه',
    'مكافحة الآفات',
    'تقييم المحصول',
    'استشارة طارئة',
  ];

  /// مستويات الأولوية (مرجع من الثوابت المشتركة)
  static const List<String> urgencyLevels = AgriculturalServicesConstants.urgencyLevels;

  /// حالات طلب المراقبة
  static const List<String> requestStatuses = [
    'pending',      // في الانتظار
    'assigned',     // تم التعيين
    'scheduled',    // تم الجدولة
    'in_progress',  // قيد التنفيذ
    'completed',    // مكتمل
    'cancelled',    // ملغي
  ];

  /// أولويات المهام
  static const List<String> taskPriorities = [
    'low',     // منخفضة
    'normal',  // عادية
    'high',    // عالية
    'urgent',  // عاجلة
  ];

  /// التكاليف الأساسية
  static const double baseCost = 1000.0;
  static const double urgentMultiplier = 2.5;
  static const double highPriorityMultiplier = 1.8;
  static const double normalMultiplier = 1.0;
  static const double lowPriorityMultiplier = 0.7;

  /// تكاليف أنواع المراقبة
  static const Map<String, double> monitoringTypeCosts = {
    'استشارة طارئة': 2000.0,
    'فحص شامل': 1500.0,
    'تشخيص الأمراض': 1200.0,
    'تحليل المياه': 800.0,
    'فحص التربة': 600.0,
    'مراقبة النمو': 500.0,
    'مكافحة الآفات': 400.0,
    'تقييم المحصول': 300.0,
  };

  /// تكلفة المساحة (لكل 1000 م²)
  static const double costPerThousandSqm = 200.0;

  /// تكلفة تحليل الصور (لكل صورة)
  static const double aiAnalysisCostPerImage = 150.0;

  /// تكلفة النقل الأساسية
  static const double baseTransportCost = 300.0;

  /// الحد الأقصى لعدد الصور
  static const int maxImagesCount = 10;

  /// أبعاد الصور المسموحة
  static const double maxImageWidth = 1920.0;
  static const double maxImageHeight = 1080.0;
  static const int imageQuality = 85;

  /// مدة الجلسة المقدرة (بالدقائق)
  static const Map<String, int> estimatedDurations = {
    'استشارة طارئة': 30,
    'فحص شامل': 180,
    'تشخيص الأمراض': 120,
    'تحليل المياه': 90,
    'فحص التربة': 90,
    'مراقبة النمو': 60,
    'مكافحة الآفات': 90,
    'تقييم المحصول': 120,
  };

  /// رسائل النجاح
  static const String successMessage = 'تم إرسال طلب مراقبة النبات بنجاح';
  static const String imageUploadSuccess = 'تم رفع الصور بنجاح';
  static const String locationDetectedSuccess = 'تم تحديد الموقع بنجاح';

  /// رسائل الخطأ
  static const String errorMessage = 'حدث خطأ في إرسال الطلب';
  static const String imageUploadError = 'فشل في رفع الصور';
  static const String locationError = 'فشل في تحديد الموقع';
  static const String validationError = 'يرجى التحقق من البيانات المدخلة';

  /// رسائل التحقق
  static const String nameRequired = 'يرجى إدخال اسم المزارع';
  static const String phoneRequired = 'يرجى إدخال رقم الهاتف';
  static const String locationRequired = 'يرجى إدخال موقع المزرعة';
  static const String farmSizeRequired = 'يرجى إدخال مساحة المزرعة';
  static const String invalidPhoneFormat = 'رقم الهاتف غير صحيح (مثال: +967712345678)';
  static const String invalidFarmSize = 'يرجى إدخال مساحة صحيحة';

  /// نصوص الواجهة
  static const String pageTitle = 'مراقبة النبات الحقيقية';
  static const String farmerInfoTitle = 'معلومات المزارع';
  static const String farmInfoTitle = 'معلومات المزرعة';
  static const String monitoringDetailsTitle = 'تفاصيل المراقبة';
  static const String plantImagesTitle = 'صور النبات';
  static const String locationTitle = 'الموقع الجغرافي';
  static const String schedulingTitle = 'جدولة الزيارة';
  static const String costTitle = 'التكلفة المقدرة';

  /// نصوص الأزرار
  static const String submitButtonText = 'طلب مراقبة حقيقية';
  static const String selectDateText = 'اختر التاريخ';
  static const String selectTimeText = 'اختر الوقت';
  static const String cameraButtonText = 'كاميرا';
  static const String galleryButtonText = 'المعرض';
  static const String refreshLocationText = 'تحديث الموقع';

  /// نصوص المساعدة
  static const String helpTitle = 'مساعدة مراقبة النبات';
  static const String helpContent = '''
هذه خدمة حقيقية مفعلة بالكامل:

🔥 تحليل صور بالذكاء الاصطناعي
📍 تحديد موقع دقيق بـ GPS
👨‍🌾 جدولة زيارات المهندسين
📊 تقارير مفصلة وإحصائيات
🔔 إشعارات فورية
💰 نظام دفع آمن

⚡ زمن الاستجابة: أقل من 30 ثانية
📞 دعم فني: 24/7
  ''';

  /// معلومات الخدمة
  static const String serviceVersion = '2.0';
  static const String platformInfo = 'mobile';
  static const String appVersion = '1.0.0';

  /// إعدادات Firebase
  static const String firestoreCollection = 'plant_monitoring_requests';
  static const String storageFolder = 'plant_monitoring';
  static const String notificationsCollection = 'notifications';
  static const String tasksCollection = 'engineer_tasks';

  /// إعدادات الموقع
  static const double locationAccuracy = 10.0; // متر
  static const int locationTimeoutSeconds = 30;

  /// إعدادات الإشعارات
  static const String notificationTopic = 'plant_monitoring';
  static const String notificationSound = 'default';

  /// منع إنشاء كائن من الكلاس
  PlantMonitoringConstants._();
}
