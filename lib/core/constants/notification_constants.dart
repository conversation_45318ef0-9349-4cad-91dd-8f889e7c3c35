/// ثوابت الإشعارات
/// 
/// يحتوي هذا الملف على جميع الثوابت المتعلقة بنظام الإشعارات
/// لضمان التوحيد وسهولة الصيانة
class NotificationConstants {
  
  // أنواع الإشعارات
  static const String typeConsultation = 'consultation';
  static const String typeAppointment = 'appointment';
  static const String typeAdvisorResponse = 'advisor_response';
  static const String typeUrgent = 'urgent';
  static const String typeDefault = 'default';
  static const String typeReminder = 'reminder';
  static const String typeWeatherAlert = 'weather_alert';
  static const String typePestAlert = 'pest_alert';
  
  // أولويات الإشعارات
  static const String priorityLow = 'low';
  static const String priorityNormal = 'normal';
  static const String priorityHigh = 'high';
  static const String priorityUrgent = 'urgent';
  
  // حالات الإشعارات
  static const String statusUnread = 'unread';
  static const String statusRead = 'read';
  static const String statusArchived = 'archived';
  
  // مسارات الأصوات
  static const String soundDefault = 'sounds/notification.mp3';
  static const String soundConsultation = 'sounds/consultation_notification.mp3';
  static const String soundAppointment = 'sounds/appointment_notification.mp3';
  static const String soundResponse = 'sounds/response_notification.mp3';
  static const String soundUrgent = 'sounds/urgent_notification.mp3';
  static const String soundReminder = 'sounds/reminder_notification.mp3';
  
  // مفاتيح التخزين المحلي
  static const String keyStoredNotifications = 'stored_notifications';
  static const String keyUnreadCount = 'unread_notifications_count';
  static const String keySoundEnabled = 'notification_sound_enabled';
  static const String keyVibrationEnabled = 'notification_vibration_enabled';
  static const String keyNotificationSettings = 'notification_settings';
  
  // رسائل الإشعارات
  static const String titleNewConsultation = 'استشارة جديدة';
  static const String titleAdvisorResponse = 'رد جديد من المرشد الزراعي';
  static const String titleAppointmentReminder = 'تذكير بالموعد';
  static const String titleWeatherAlert = 'تنبيه جوي';
  static const String titlePestAlert = 'تحذير من الآفات';
  
  // أيقونات الإشعارات
  static const String iconConsultation = '🌱';
  static const String iconResponse = '💬';
  static const String iconAppointment = '📅';
  static const String iconWeather = '🌤️';
  static const String iconPest = '🐛';
  static const String iconUrgent = '⚠️';
  static const String iconSuccess = '✅';
  
  // ألوان الإشعارات
  static const int colorConsultation = 0xFF4CAF50; // أخضر
  static const int colorResponse = 0xFF2196F3;     // أزرق
  static const int colorAppointment = 0xFFFF9800;  // برتقالي
  static const int colorUrgent = 0xFFF44336;       // أحمر
  static const int colorSuccess = 0xFF4CAF50;      // أخضر
  static const int colorWarning = 0xFFFF9800;      // برتقالي
  
  // حدود النظام
  static const int maxStoredNotifications = 100;
  static const int maxNotificationTitleLength = 50;
  static const int maxNotificationBodyLength = 200;
  static const int notificationTimeoutSeconds = 30;
  
  // إعدادات الصوت
  static const double defaultVolume = 0.8;
  static const double maxVolume = 1.0;
  static const double minVolume = 0.0;
  
  // إعدادات الاهتزاز
  static const int vibrationDurationMs = 500;
  static const int vibrationPatternShort = 200;
  static const int vibrationPatternLong = 800;
  
  // قنوات الإشعارات (Android)
  static const String channelIdDefault = 'agriculture_default';
  static const String channelIdConsultation = 'agriculture_consultation';
  static const String channelIdUrgent = 'agriculture_urgent';
  static const String channelIdReminder = 'agriculture_reminder';
  
  // أسماء القنوات
  static const String channelNameDefault = 'الإشعارات العامة';
  static const String channelNameConsultation = 'إشعارات الاستشارات';
  static const String channelNameUrgent = 'الإشعارات العاجلة';
  static const String channelNameReminder = 'التذكيرات';
  
  // أوصاف القنوات
  static const String channelDescDefault = 'إشعارات عامة للتطبيق الزراعي';
  static const String channelDescConsultation = 'إشعارات الاستشارات الزراعية والردود';
  static const String channelDescUrgent = 'إشعارات عاجلة تتطلب انتباه فوري';
  static const String channelDescReminder = 'تذكيرات المواعيد والمهام';
  
  // رسائل الخطأ
  static const String errorSoundPlayback = 'فشل في تشغيل صوت الإشعار';
  static const String errorNotificationSave = 'فشل في حفظ الإشعار';
  static const String errorNotificationLoad = 'فشل في تحميل الإشعارات';
  static const String errorPermissionDenied = 'تم رفض إذن الإشعارات';
  
  // رسائل النجاح
  static const String successNotificationSent = 'تم إرسال الإشعار بنجاح';
  static const String successSettingsSaved = 'تم حفظ إعدادات الإشعارات';
  static const String successNotificationCleared = 'تم مسح الإشعارات';
  
  // قوالب الرسائل
  static String getConsultationMessage(String farmerName) {
    return 'استشارة جديدة من $farmerName';
  }
  
  static String getResponseMessage(String advisorName) {
    return 'رد جديد من المرشد $advisorName';
  }
  
  static String getAppointmentMessage(String time) {
    return 'لديك موعد في $time';
  }
  
  static String getWeatherAlertMessage(String condition) {
    return 'تنبيه جوي: $condition';
  }
  
  static String getPestAlertMessage(String pestType) {
    return 'تحذير: تم رصد $pestType في منطقتك';
  }
  
  // دوال مساعدة للألوان
  static int getColorByType(String type) {
    switch (type) {
      case typeConsultation:
        return colorConsultation;
      case typeAdvisorResponse:
        return colorResponse;
      case typeAppointment:
        return colorAppointment;
      case typeUrgent:
        return colorUrgent;
      default:
        return colorConsultation;
    }
  }
  
  // دوال مساعدة للأيقونات
  static String getIconByType(String type) {
    switch (type) {
      case typeConsultation:
        return iconConsultation;
      case typeAdvisorResponse:
        return iconResponse;
      case typeAppointment:
        return iconAppointment;
      case typeWeatherAlert:
        return iconWeather;
      case typePestAlert:
        return iconPest;
      case typeUrgent:
        return iconUrgent;
      default:
        return iconConsultation;
    }
  }
  
  // دوال مساعدة للأصوات
  static String getSoundByType(String type) {
    switch (type) {
      case typeConsultation:
        return soundConsultation;
      case typeAdvisorResponse:
        return soundResponse;
      case typeAppointment:
        return soundAppointment;
      case typeUrgent:
        return soundUrgent;
      case typeReminder:
        return soundReminder;
      default:
        return soundDefault;
    }
  }
  
  // دوال مساعدة للقنوات
  static String getChannelByType(String type) {
    switch (type) {
      case typeConsultation:
      case typeAdvisorResponse:
        return channelIdConsultation;
      case typeUrgent:
        return channelIdUrgent;
      case typeReminder:
        return channelIdReminder;
      default:
        return channelIdDefault;
    }
  }
}
