/// ثوابت الأبعاد في التطبيق
///
/// يحتوي هذا الكلاس على جميع ثوابت الأبعاد المستخدمة في التطبيق.
/// يتم استخدام هذه الثوابت للحفاظ على تناسق التصميم في جميع أنحاء التطبيق.
///
/// مثال الاستخدام:
/// ```dart
/// Container(
///   padding: EdgeInsets.all(Dimensions.paddingM),
///   margin: EdgeInsets.symmetric(
///     vertical: Dimensions.marginS,
///     horizontal: Dimensions.marginM,
///   ),
///   decoration: BoxDecoration(
///     borderRadius: BorderRadius.circular(Dimensions.radiusM),
///   ),
///   child: Text(
///     'مثال على النص',
///     style: TextStyle(fontSize: Dimensions.fontSizeM),
///   ),
/// )
/// ```
class Dimensions {
  // منع إنشاء نسخة من الكلاس
  Dimensions._();

  /// المسافات الداخلية (Padding)
  ///
  /// تستخدم هذه القيم لتحديد المسافات الداخلية للعناصر.
  /// - XS: مسافة صغيرة جدًا (4.0)
  /// - S: مسافة صغيرة (8.0)
  /// - M: مسافة متوسطة (16.0)
  /// - L: مسافة كبيرة (24.0)
  /// - XL: مسافة كبيرة جدًا (32.0)
  /// - XXL: مسافة كبيرة جدًا جدًا (48.0)
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;

  /// الهوامش (Margin)
  ///
  /// تستخدم هذه القيم لتحديد الهوامش الخارجية للعناصر.
  /// - XS: هامش صغير جدًا (4.0)
  /// - S: هامش صغير (8.0)
  /// - M: هامش متوسط (16.0)
  /// - L: هامش كبير (24.0)
  /// - XL: هامش كبير جدًا (32.0)
  /// - XXL: هامش كبير جدًا جدًا (48.0)
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  static const double marginXXL = 48.0;

  /// نصف قطر الزوايا (Border Radius)
  ///
  /// تستخدم هذه القيم لتحديد نصف قطر زوايا العناصر.
  /// - XS: نصف قطر صغير جدًا (4.0)
  /// - S: نصف قطر صغير (8.0)
  /// - M: نصف قطر متوسط (12.0)
  /// - L: نصف قطر كبير (16.0)
  /// - XL: نصف قطر كبير جدًا (24.0)
  /// - XXL: نصف قطر كبير جدًا جدًا (32.0)
  /// - radiusCircular: نصف قطر دائري (50.0) - يستخدم لإنشاء عناصر دائرية
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusXXL = 32.0;
  static const double radiusCircular = 50.0; // نصف قطر دائري

  /// أحجام الخطوط
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeadline = 28.0;

  /// أحجام الأيقونات
  static const double iconSizeXS = 16.0;
  static const double iconSizeS = 20.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXL = 48.0;
  static const double iconSizeXXL = 64.0;

  /// ارتفاعات الأزرار (Button Heights)
  ///
  /// تستخدم هذه القيم لتحديد ارتفاعات الأزرار المختلفة.
  /// - S: ارتفاع صغير (32.0)
  /// - M: ارتفاع متوسط (40.0)
  /// - L: ارتفاع كبير (48.0)
  /// - XL: ارتفاع كبير جدًا (56.0)
  /// - buttonHeight: الارتفاع الافتراضي للأزرار (48.0) - يستخدم في ButtonStyle
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;
  static const double buttonHeight = 48.0; // الارتفاع الافتراضي للأزرار

  /// عروض الأزرار (Button Widths)
  ///
  /// تستخدم هذه القيم لتحديد عروض الأزرار المختلفة.
  /// - S: عرض صغير (80.0)
  /// - M: عرض متوسط (120.0)
  /// - L: عرض كبير (160.0)
  /// - XL: عرض كبير جدًا (200.0)
  /// - XXL: عرض كبير جدًا جدًا (240.0)
  /// - buttonMinWidth: الحد الأدنى لعرض الأزرار (120.0) - يستخدم في ButtonStyle
  static const double buttonWidthS = 80.0;
  static const double buttonWidthM = 120.0;
  static const double buttonWidthL = 160.0;
  static const double buttonWidthXL = 200.0;
  static const double buttonWidthXXL = 240.0;
  static const double buttonMinWidth = 120.0; // الحد الأدنى لعرض الأزرار

  /// أحجام الصور
  static const double imageSizeXS = 24.0;
  static const double imageSizeS = 32.0;
  static const double imageSizeM = 48.0;
  static const double imageSizeL = 64.0;
  static const double imageSizeXL = 96.0;
  static const double imageSizeXXL = 128.0;

  /// أحجام الصور الشخصية
  static const double avatarSizeXS = 24.0;
  static const double avatarSizeS = 32.0;
  static const double avatarSizeM = 48.0;
  static const double avatarSizeL = 64.0;
  static const double avatarSizeXL = 96.0;
  static const double avatarSizeXXL = 128.0;

  /// ارتفاعات الشاشات
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavBarHeight = 56.0;
  static const double bottomSheetHeaderHeight = 64.0;
  static const double bottomSheetMinHeight = 200.0;
  static const double drawerWidth = 280.0;
  static const double drawerHeaderHeight = 160.0;

  /// عروض الشاشات
  static const double maxScreenWidth = 500.0;
  static const double dialogWidth = 300.0;
  static const double cardWidth = 320.0;
  static const double cardWidthSmall = 160.0;
  static const double maxContentWidth = 800.0;

  /// ارتفاعات البطاقات
  static const double cardHeight = 200.0;
  static const double cardHeightSmall = 120.0;
  static const double cardHeightMedium = 160.0;
  static const double cardHeightLarge = 240.0;

  /// سماكات الخطوط والحدود
  static const double dividerThickness = 1.0;
  static const double borderThickness = 1.0;
  static const double borderThicknessL = 2.0;
  static const double borderThicknessXL = 4.0;
  static const double borderWidthRegular = 1.0; // سماكة الحدود العادية

  /// ارتفاعات المدخلات
  static const double inputHeight = 48.0;
  static const double inputHeightSmall = 40.0;
  static const double inputHeightLarge = 56.0;

  /// ارتفاعات القوائم
  static const double listItemHeight = 56.0;
  static const double listItemHeightSmall = 48.0;
  static const double listItemHeightLarge = 72.0;

  /// مسافات بين العناصر (Spacing)
  ///
  /// تستخدم هذه القيم لتحديد المسافات بين العناصر.
  /// - XS: مسافة صغيرة جدًا (4.0)
  /// - S: مسافة صغيرة (8.0)
  /// - M: مسافة متوسطة (16.0)
  /// - L: مسافة كبيرة (24.0)
  /// - XL: مسافة كبيرة جدًا (32.0)
  /// - XXL: مسافة كبيرة جدًا جدًا (48.0)
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  /// قيم الظل
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationXLarge = 16.0;

  /// قيم الشفافية
  static const double opacityDisabled = 0.38;
  static const double opacityHint = 0.6;
  static const double opacityOverlay = 0.5;

  /// قيم الرسوم المتحركة
  static const int animationDurationFast = 150;
  static const int animationDurationMedium = 300;
  static const int animationDurationSlow = 500;

  /// نقاط الكسر للشاشة
  static const double breakpointXSmall = 0.0;
  static const double breakpointSmall = 600.0;
  static const double breakpointMedium = 960.0;
  static const double breakpointLarge = 1280.0;
  static const double breakpointXLarge = 1920.0;
}
