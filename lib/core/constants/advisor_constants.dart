/// ثوابت نظام الاستشاري الزراعي
/// 
/// يحتوي على جميع الثوابت المستخدمة في نظام الاستشاري الزراعي
/// مقسمة حسب الفئات لسهولة الاستخدام والصيانة
class AdvisorConstants {
  // منع إنشاء نسخة من الكلاس
  AdvisorConstants._();

  // ===== ثوابت الواجهة =====
  
  /// عناوين الصفحات
  static const String dashboardTitle = 'لوحة تحكم المرشد';
  static const String consultationsTitle = 'الاستشارات';
  static const String appointmentsTitle = 'المواعيد';
  static const String plantMonitoringTitle = 'مراقبة النبات';
  static const String reportsTitle = 'التقارير';
  
  /// تسميات التبويبات
  static const String consultationsTab = 'الاستشارات';
  static const String appointmentsTab = 'المواعيد';
  static const String plantMonitoringTab = 'مراقبة النبات';
  static const String reportsTab = 'التقارير';
  
  /// تسميات الإحصائيات
  static const String statNew = 'جديد';
  static const String statPending = 'معلق';
  static const String statUrgent = 'عاجل';
  static const String statCompleted = 'مكتمل';
  static const String statInProgress = 'قيد التنفيذ';
  static const String statCancelled = 'ملغي';
  
  /// عناوين الأقسام
  static const String quickStatsTitle = 'الإحصائيات السريعة';
  static const String recentActivityTitle = 'النشاط الأخير';
  static const String quickActionsTitle = 'الإجراءات السريعة';
  
  // ===== ثوابت حالات الاستشارة =====
  
  /// حالات الاستشارة
  static const String consultationStatusPending = 'pending';
  static const String consultationStatusInProgress = 'in_progress';
  static const String consultationStatusCompleted = 'completed';
  static const String consultationStatusCancelled = 'cancelled';
  static const String consultationStatusUrgent = 'urgent';
  
  /// تسميات حالات الاستشارة بالعربية
  static const Map<String, String> consultationStatusLabels = {
    consultationStatusPending: 'في الانتظار',
    consultationStatusInProgress: 'قيد المراجعة',
    consultationStatusCompleted: 'مكتملة',
    consultationStatusCancelled: 'ملغية',
    consultationStatusUrgent: 'عاجلة',
  };
  
  // ===== ثوابت حالات المواعيد =====
  
  /// حالات المواعيد
  static const String appointmentStatusScheduled = 'scheduled';
  static const String appointmentStatusConfirmed = 'confirmed';
  static const String appointmentStatusCompleted = 'completed';
  static const String appointmentStatusCancelled = 'cancelled';
  static const String appointmentStatusRescheduled = 'rescheduled';
  
  /// تسميات حالات المواعيد بالعربية
  static const Map<String, String> appointmentStatusLabels = {
    appointmentStatusScheduled: 'مجدول',
    appointmentStatusConfirmed: 'مؤكد',
    appointmentStatusCompleted: 'مكتمل',
    appointmentStatusCancelled: 'ملغي',
    appointmentStatusRescheduled: 'معاد جدولته',
  };
  
  // ===== ثوابت أنواع المراقبة =====
  
  /// أنواع مراقبة النبات
  static const String monitoringTypeGrowth = 'growth';
  static const String monitoringTypeHealth = 'health';
  static const String monitoringTypePests = 'pests';
  static const String monitoringTypeIrrigation = 'irrigation';
  static const String monitoringTypeFertilization = 'fertilization';
  
  /// تسميات أنواع المراقبة بالعربية
  static const Map<String, String> monitoringTypeLabels = {
    monitoringTypeGrowth: 'مراقبة النمو',
    monitoringTypeHealth: 'مراقبة الصحة',
    monitoringTypePests: 'مراقبة الآفات',
    monitoringTypeIrrigation: 'مراقبة الري',
    monitoringTypeFertilization: 'مراقبة التسميد',
  };
  
  // ===== ثوابت أولويات الاستشارة =====
  
  /// مستويات الأولوية
  static const String priorityLow = 'low';
  static const String priorityMedium = 'medium';
  static const String priorityHigh = 'high';
  static const String priorityUrgent = 'urgent';
  
  /// تسميات الأولوية بالعربية
  static const Map<String, String> priorityLabels = {
    priorityLow: 'منخفضة',
    priorityMedium: 'متوسطة',
    priorityHigh: 'عالية',
    priorityUrgent: 'عاجلة',
  };
  
  // ===== ثوابت التخصصات =====
  
  /// تخصصات المرشدين الزراعيين
  static const List<String> advisorSpecialties = [
    'زراعة الخضروات',
    'زراعة الفواكه',
    'زراعة الحبوب',
    'مكافحة الآفات',
    'أمراض النبات',
    'التسميد والتغذية',
    'أنظمة الري',
    'الزراعة العضوية',
    'البيوت المحمية',
    'تربية النحل',
    'الثروة الحيوانية',
    'إدارة المزارع',
  ];
  
  // ===== ثوابت الحدود والقيود =====
  
  /// حدود البيانات
  static const int maxConsultationsPerPage = 20;
  static const int maxAppointmentsPerPage = 15;
  static const int maxMonitoringRecordsPerPage = 25;
  static const int maxImageUploads = 5;
  static const int maxDescriptionLength = 1000;
  static const int maxResponseLength = 2000;
  
  /// أحجام الملفات (بالبايت)
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5 ميجابايت
  static const int maxDocumentSizeBytes = 10 * 1024 * 1024; // 10 ميجابايت
  
  // ===== ثوابت الألوان =====
  
  /// ألوان حالات الاستشارة
  static const Map<String, int> consultationStatusColors = {
    consultationStatusPending: 0xFFFF9800, // برتقالي
    consultationStatusInProgress: 0xFF2196F3, // أزرق
    consultationStatusCompleted: 0xFF4CAF50, // أخضر
    consultationStatusCancelled: 0xFF9E9E9E, // رمادي
    consultationStatusUrgent: 0xFFF44336, // أحمر
  };
  
  /// ألوان مستويات الأولوية
  static const Map<String, int> priorityColors = {
    priorityLow: 0xFF4CAF50, // أخضر
    priorityMedium: 0xFFFF9800, // برتقالي
    priorityHigh: 0xFFFF5722, // برتقالي محمر
    priorityUrgent: 0xFFF44336, // أحمر
  };
  
  // ===== ثوابت الرسائل =====
  
  /// رسائل النجاح
  static const String successConsultationCreated = 'تم إنشاء الاستشارة بنجاح';
  static const String successAppointmentBooked = 'تم حجز الموعد بنجاح';
  static const String successResponseSent = 'تم إرسال الرد بنجاح';
  
  /// رسائل الخطأ
  static const String errorNetworkConnection = 'خطأ في الاتصال بالشبكة';
  static const String errorInvalidData = 'البيانات المدخلة غير صحيحة';
  static const String errorUnauthorized = 'غير مصرح لك بهذا الإجراء';
  static const String errorServerError = 'خطأ في الخادم، حاول مرة أخرى';
  
  /// رسائل التحذير
  static const String warningUnsavedChanges = 'لديك تغييرات غير محفوظة';
  static const String warningDeleteConfirmation = 'هل أنت متأكد من الحذف؟';
}
