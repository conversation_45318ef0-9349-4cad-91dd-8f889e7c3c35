import 'package:flutter/material.dart';

///
/// | NAME           | SIZE |  WEIGHT |  SPACING |             |
/// |----------------|------|---------|----------|-------------|
/// | displayLarge   | 96.0 | w300   | -1.5     |             |Light
/// | displayMedium  | 60.0 | w300   | -0.5     |             |w300
/// | displaySmall   | 48.0 | w400 |  0.0     |             |regular
/// | headlineMedium | 34.0 | w400 |  0.25    |             |w400
/// | headlineSmall  | 24.0 | w400 |  0.0     |             |
/// | titleLarge     | 20.0 | w500  |  0.15    |             |
/// | titleMedium    | 16.0 | w400 |  0.15    |             |
/// | titleSmall     | 14.0 | w500  |  0.1     |             |
/// | bodyLarge      | 16.0 | w400 |  0.5     |             |
/// | bodyMedium     | 14.0 | w400 |  0.25    |             |
/// | bodySmall      | 12.0 | w400 |  0.4     |             |
/// | labelLarge     | 14.0 | w500  |  1.25    |             |
/// | labelSmall     | 10.0 | w400 |  1.5     |             |
class TextStyles {
  BuildContext context;

  TextStyles({required this.context});

  static TextStyles of(BuildContext context) {
    return TextStyles(context: context);
  }

  TextStyle displayLarge({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'sultan',
        fontSize: fontSize ?? 96,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w600,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );

  TextStyle displayMedium(
          {Color? color,
          TextDecoration? decoration,
          Color? decorationColor,
          double? height,
          FontWeight? fontWeight,
          double? fontSize,
          String? fontFamily}) =>
      TextStyle(
        fontFamily: fontFamily ?? 'sultan',
        fontSize: fontSize ?? 60,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w300,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle displaySmall(
          {Color? color,
          TextDecoration? decoration,
          Color? decorationColor,
          double? height,
          FontWeight? fontWeight,
          double? fontSize,
          String? fontFamily}) =>
      TextStyle(
        fontFamily: fontFamily ?? 'sultan',
        fontSize: fontSize ?? 48,
        fontWeight: fontWeight ?? FontWeight.w300,
        height: height,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle headlineMedium({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 34,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle headlineSmall({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 24,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle titleLarge({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    FontWeight? weight,
    double? height,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'sultan',
        fontSize: fontSize ?? 20,
        height: height,
        fontWeight: weight ?? FontWeight.w700,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle titleMedium({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 16,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle titleSmall({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 14,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle bodyLarge({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily,
        fontSize: fontSize ?? 16,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle bodyMedium({
    Color? color,
    FontWeight? fontWeight,
    double? fontSize,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 14,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle bodySmall({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    FontWeight? fontWeight,
    double? fontSize,
    double? height,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 12,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle labelLarge({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    FontWeight? fontWeight,
    double? fontSize,
    double? height,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 14,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
  TextStyle labelSmall({
    Color? color,
    TextDecoration? decoration,
    Color? decorationColor,
    double? height,
    FontWeight? fontWeight,
    double? fontSize,
    String? fontFamily,
  }) =>
      TextStyle(
        fontFamily: fontFamily ?? 'Cairo-Bold',
        fontSize: fontSize ?? 10,
        height: height,
        fontWeight: fontWeight ?? FontWeight.w500,
        color: color ??
            Theme.of(context).textTheme.displayLarge?.color ??
            Colors.black,
        decoration: decoration,
        decorationColor: decorationColor,
      );
}
