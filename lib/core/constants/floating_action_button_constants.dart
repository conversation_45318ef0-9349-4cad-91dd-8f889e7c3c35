/// ثوابت الزر العائم الذكي للاستشاري الزراعي
/// وفق المعيار #4: توحيد القيم باستخدام ملفات الثوابت
class FloatingActionButtonConstants {
  /// أيقونات الإجراءات
  static const Map<String, dynamic> actionIcons = {
    'calendar_sync': 'calendar_today',
    'image_analysis': 'photo_camera',
    'treatment_plan': 'auto_fix_high',
    'weather_alert': 'cloud',
    'pest_detection': 'bug_report',
    'irrigation_reminder': 'water_drop',
    'crop_monitoring': 'eco',
    'ai_assistant': 'psychology',
  };

  /// عناوين الإجراءات باللغة العربية
  static const Map<String, String> actionTitles = {
    'calendar_sync': 'مزامنة مع التقويم الزراعي',
    'image_analysis': 'تحليل صورة استشارة',
    'treatment_plan': 'اقتراح خطة علاجية',
    'weather_alert': 'تحذير طقس',
    'pest_detection': 'كشف الآفات',
    'irrigation_reminder': 'تذكير الري',
    'crop_monitoring': 'مراقبة المحصول',
    'ai_assistant': 'المساعد الذكي',
  };

  /// أوصاف الإجراءات
  static const Map<String, String> actionDescriptions = {
    'calendar_sync': 'استخراج التواريخ المهمة وإضافتها للتقويم',
    'image_analysis': 'تحليل صور النباتات باستخدام الذكاء الاصطناعي',
    'treatment_plan': 'إنشاء خطة علاجية مخصصة للمحصول',
    'weather_alert': 'إرسال تحذيرات الطقس للمزارعين',
    'pest_detection': 'اكتشاف الآفات والأمراض النباتية',
    'irrigation_reminder': 'جدولة تذكيرات الري',
    'crop_monitoring': 'متابعة نمو وصحة المحاصيل',
    'ai_assistant': 'مساعد ذكي لتحسين الإنتاجية',
  };

  /// ألوان الإجراءات
  static const Map<String, int> actionColors = {
    'calendar_sync': 0xFF2196F3, // أزرق
    'image_analysis': 0xFF4CAF50, // أخضر
    'treatment_plan': 0xFF9C27B0, // بنفسجي
    'weather_alert': 0xFFFF9800, // برتقالي
    'pest_detection': 0xFFF44336, // أحمر
    'irrigation_reminder': 0xFF00BCD4, // سماوي
    'crop_monitoring': 0xFF8BC34A, // أخضر فاتح
    'ai_assistant': 0xFF673AB7, // بنفسجي داكن
  };

  /// أولوية الإجراءات (للترتيب)
  static const Map<String, int> actionPriority = {
    'calendar_sync': 1,
    'image_analysis': 2,
    'treatment_plan': 3,
    'weather_alert': 4,
    'pest_detection': 5,
    'irrigation_reminder': 6,
    'crop_monitoring': 7,
    'ai_assistant': 8,
  };

  /// إعدادات الرسوم المتحركة
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration delayBetweenActions = Duration(milliseconds: 50);
  static const double fabSize = 56.0;
  static const double miniFabSize = 40.0;
  static const double fabSpacing = 16.0;

  /// إعدادات الحوار
  static const double dialogBorderRadius = 16.0;
  static const double dialogPadding = 20.0;
  static const double dialogMaxWidth = 400.0;

  /// إعدادات التقويم
  static const int maxCalendarEvents = 50;
  static const Duration calendarCacheDuration = Duration(hours: 1);
  static const List<String> supportedDateFormats = [
    'yyyy-MM-dd',
    'dd/MM/yyyy',
    'MM/dd/yyyy',
    'dd-MM-yyyy',
  ];

  /// إعدادات تحليل الصور
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const double imageCompressionQuality = 0.8;

  /// إعدادات الذكاء الاصطناعي
  static const int maxPromptLength = 1000;
  static const Duration aiResponseTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;

  /// رسائل النجاح
  static const Map<String, String> successMessages = {
    'calendar_sync': 'تمت مزامنة التواريخ مع التقويم بنجاح',
    'image_analysis': 'تم تحليل الصورة بنجاح',
    'treatment_plan': 'تم إنشاء خطة العلاج بنجاح',
    'weather_alert': 'تم إرسال تحذير الطقس',
    'pest_detection': 'تم فحص الآفات بنجاح',
    'irrigation_reminder': 'تم جدولة تذكير الري',
    'crop_monitoring': 'تم تحديث بيانات المراقبة',
    'ai_assistant': 'تم الحصول على الاقتراحات الذكية',
  };

  /// رسائل الخطأ
  static const Map<String, String> errorMessages = {
    'calendar_sync': 'فشل في مزامنة التقويم',
    'image_analysis': 'فشل في تحليل الصورة',
    'treatment_plan': 'فشل في إنشاء خطة العلاج',
    'weather_alert': 'فشل في إرسال تحذير الطقس',
    'pest_detection': 'فشل في فحص الآفات',
    'irrigation_reminder': 'فشل في جدولة التذكير',
    'crop_monitoring': 'فشل في تحديث المراقبة',
    'ai_assistant': 'فشل في الحصول على الاقتراحات',
  };

  /// إعدادات الإشعارات
  static const Map<String, String> notificationChannels = {
    'calendar_sync': 'calendar_notifications',
    'weather_alert': 'weather_notifications',
    'pest_detection': 'pest_notifications',
    'irrigation_reminder': 'irrigation_notifications',
  };

  /// أصوات الإشعارات
  static const Map<String, String> notificationSounds = {
    'calendar_sync': 'calendar_notification.mp3',
    'weather_alert': 'weather_alert.mp3',
    'pest_detection': 'pest_alert.mp3',
    'irrigation_reminder': 'irrigation_reminder.mp3',
  };
}
