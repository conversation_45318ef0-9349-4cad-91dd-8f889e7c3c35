/// ثوابت الخدمات الزراعية المشتركة
/// 
/// يحتوي على جميع الثوابت المشتركة بين خدمات الاستشارة وحجز المواعيد ومراقبة النبات
/// يتبع المعيار #4 - توحيد القيم باستخدام ملفات الثوابت
class AgriculturalServicesConstants {
  
  /// أنواع المحاصيل المدعومة (مشتركة)
  static const List<String> cropTypes = [
    'طماطم',
    'خيار', 
    'فلفل',
    'باذنجان',
    'كوسا',
    'فاصولياء',
    'قمح',
    'ذرة',
    'أرز',
    'شعير',
    'دخن',
    'برتقال',
    'ليمون',
    'مانجو',
    'عنب',
    'تفاح',
    'بصل',
    'ثوم',
    'جزر',
    'بطاطس',
    'بطاطا حلوة',
  ];

  /// أنواع الاستشارات المتاحة (مشتركة)
  static const List<String> consultationTypes = [
    'مشكلة في المحصول',
    'استشارة زراعية عامة',
    'مكافحة الآفات',
    'نصائح الري',
    'التسميد والتغذية',
    'اختيار البذور',
    'تشخيص الأمراض',
    'استشارة طارئة',
    'تحسين الإنتاج',
    'إدارة التربة',
  ];

  /// مستويات الأولوية (مشتركة)
  static const List<String> urgencyLevels = [
    'عاجلة جداً',
    'عاجلة',
    'عادية',
    'غير عاجلة',
  ];

  /// طرق الدفع المتاحة (مشتركة)
  static const List<String> paymentMethods = [
    'نقدي',
    'تحويل بنكي',
    'محفظة إلكترونية',
    'بطاقة ائتمان',
    'دفع آجل',
  ];

  /// حالات الطلبات (مشتركة)
  static const List<String> requestStatuses = [
    'pending',      // في الانتظار
    'accepted',     // مقبول
    'assigned',     // تم التعيين
    'in_progress',  // قيد التنفيذ
    'completed',    // مكتمل
    'cancelled',    // ملغي
  ];

  /// حالات الدفع (مشتركة)
  static const List<String> paymentStatuses = [
    'pending',   // في الانتظار
    'paid',      // مدفوع
    'failed',    // فشل
    'refunded',  // مسترد
  ];

  /// أولويات المهام (مشتركة)
  static const List<String> taskPriorities = [
    'low',     // منخفضة
    'normal',  // عادية
    'high',    // عالية
    'urgent',  // عاجلة
  ];

  /// التكاليف الأساسية (مشتركة)
  static const double baseCost = 500.0;
  static const double urgentMultiplier = 2.5;
  static const double highPriorityMultiplier = 1.8;
  static const double normalMultiplier = 1.0;
  static const double lowPriorityMultiplier = 0.8;

  /// تكلفة تحليل الصور (لكل صورة)
  static const double imageCostPerImage = 100.0;

  /// الحد الأقصى لعدد الصور
  static const int maxImagesCount = 5;

  /// أبعاد الصور المسموحة
  static const double maxImageWidth = 1920.0;
  static const double maxImageHeight = 1080.0;
  static const int imageQuality = 85;

  /// ساعات العمل (مشتركة)
  static const int workStartHour = 8;  // 8 صباحاً
  static const int workEndHour = 16;   // 4 عصراً
  static const int lunchStartHour = 12; // 12 ظهراً
  static const int lunchEndHour = 13;   // 1 ظهراً

  /// أيام العمل (0 = الأحد, 6 = السبت)
  static const List<int> workDays = [0, 1, 2, 3, 4, 6]; // الأحد إلى الخميس + السبت

  /// فترات الوقت المتاحة (مشتركة)
  static const List<String> timeSlots = [
    '08:00 - 09:00',
    '09:00 - 10:00',
    '10:00 - 11:00',
    '11:00 - 12:00',
    '13:00 - 14:00',
    '14:00 - 15:00',
    '15:00 - 16:00',
  ];

  /// رسائل النجاح (مشتركة)
  static const String successMessage = 'تم إرسال الطلب بنجاح';
  static const String imageUploadSuccess = 'تم رفع الصور بنجاح';
  static const String confirmationMessage = 'تم تأكيد الطلب';

  /// رسائل الخطأ (مشتركة)
  static const String errorMessage = 'حدث خطأ في إرسال الطلب';
  static const String imageUploadError = 'فشل في رفع الصور';
  static const String validationError = 'يرجى التحقق من البيانات المدخلة';
  static const String networkError = 'خطأ في الاتصال بالشبكة';

  /// رسائل التحقق (مشتركة)
  static const String nameRequired = 'يرجى إدخال اسم المزارع';
  static const String phoneRequired = 'يرجى إدخال رقم الهاتف';
  static const String locationRequired = 'يرجى إدخال الموقع';
  static const String problemRequired = 'يرجى وصف المشكلة';
  static const String dateRequired = 'يرجى اختيار التاريخ';
  static const String timeRequired = 'يرجى اختيار الوقت';
  static const String invalidPhoneFormat = 'رقم الهاتف غير صحيح (مثال: +967712345678)';

  /// نصوص الأزرار (مشتركة)
  static const String submitButtonText = 'إرسال الطلب';
  static const String confirmButtonText = 'تأكيد';
  static const String cancelButtonText = 'إلغاء';
  static const String selectDateText = 'اختر التاريخ';
  static const String selectTimeText = 'اختر الوقت';
  static const String cameraButtonText = 'كاميرا';
  static const String galleryButtonText = 'المعرض';

  /// معلومات الخدمة (مشتركة)
  static const String serviceVersion = '2.0';
  static const String platformInfo = 'mobile';
  static const String appVersion = '1.0.0';

  /// إعدادات Firebase (مشتركة)
  static const String consultationsCollection = 'consultations';
  static const String appointmentsCollection = 'appointments';
  static const String plantMonitoringCollection = 'plant_monitoring_requests';
  static const String notificationsCollection = 'notifications';
  static const String storageFolder = 'agricultural_services';

  /// إعدادات الإشعارات (مشتركة)
  static const String notificationTopic = 'agricultural_services';
  static const String notificationSound = 'default';
  static const String fcmServerKey = 'YOUR_FCM_SERVER_KEY'; // يجب تغييرها في الإنتاج

  /// إعدادات API الخارجي (مشتركة)
  static const String apiBaseUrl = 'https://api.agriculture-yemen.com';
  static const String paymentApiUrl = 'https://payment.agriculture-yemen.com';
  static const String smsApiUrl = 'https://sms.agriculture-yemen.com';

  /// Headers للطلبات (مشتركة)
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'Agriculture-Yemen-App/1.0.0',
  };

  /// إعدادات الطلبات (مشتركة)
  static const int connectionTimeout = 30;
  static const int receiveTimeout = 60;
  static const int retryAttempts = 3;

  /// رموز الاستجابة (مشتركة)
  static const int successCode = 200;
  static const int createdCode = 201;
  static const int badRequestCode = 400;
  static const int unauthorizedCode = 401;
  static const int notFoundCode = 404;
  static const int serverErrorCode = 500;

  /// منع إنشاء كائن من الكلاس
  AgriculturalServicesConstants._();
}
