import '../../imports.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';

/// ثوابت التطبيق العامة
///
/// يحتوي هذا الملف على المتغيرات العامة المستخدمة في جميع أنحاء التطبيق.
/// يتم تهيئة هذه المتغيرات في خدمة تهيئة التطبيق.

/// متغير للتحقق مما إذا كان المستخدم قد رأى صفحة الترحيب من قبل
///
/// يتم تعيين هذه القيمة إلى true بعد أن يرى المستخدم صفحة الترحيب للمرة الأولى
/// أو عندما ينقر على زر "تخطي" في صفحة الترحيب.
///
/// عندما تكون القيمة true، سيتم تخطي صفحة الترحيب في المرات القادمة.
bool isOnBoarding = false;

/// رمز الوصول (token) للمستخدم
String? token;

/// معرف المستخدم الحالي
String uid = '';

/// مفتاح API لخدمة Google Drive
String get googleDriveApiKey =>
    ApiKeysService.getApiKey('google_drive_api_key');

/// مفتاح API لخدمة الطقس
String get weatherApiKey => ApiKeysService.getApiKey('weather_api_key');

/// دالة لتهيئة المتغيرات العامة
///
/// تقوم هذه الدالة بتحميل المتغيرات العامة من التخزين المحلي.
/// لا تقوم بالتحقق من حالة المصادقة أو تصحيح التناقضات، حيث يتم ذلك في
/// `_synchronizeAuthenticationState` في `AppInitializationService`.
Future<void> initializeGlobalVariables() async {
  try {
    // تسجيل بداية تهيئة المتغيرات العامة
    LoggerService.debug(
      'بدء تهيئة المتغيرات العامة',
      tag: 'GlobalVariables',
    );

    // تحميل القيم من التخزين المحلي
    isOnBoarding =
        SharedPrefs.getBool(AppConstants.prefsKeyIsOnBoarding) ?? false;
    token = SharedPrefs.getString(AppConstants.prefsKeyToken);
    uid = SharedPrefs.getString('uid') ?? '';

    // تسجيل القيم المحملة للتصحيح بشكل أكثر تفصيلاً
    LoggerService.debug(
      'تم تحميل isOnBoarding من SharedPrefs: $isOnBoarding (المفتاح: ${AppConstants.prefsKeyIsOnBoarding})',
      tag: 'GlobalVariables',
    );

    LoggerService.debug(
      'تم تحميل uid من SharedPrefs: "$uid" (المفتاح: uid)',
      tag: 'GlobalVariables',
    );

    // تسجيل القيم النهائية للمتغيرات العامة
    LoggerService.debug(
      'اكتملت تهيئة المتغيرات العامة: isOnBoarding=$isOnBoarding, uid="$uid"',
      tag: 'GlobalVariables',
    );
  } catch (e) {
    LoggerService.error(
      'فشل في تهيئة المتغيرات العامة: $e',
      error: e,
      tag: 'GlobalVariables',
    );
    rethrow;
  }
}

/// دالة لتحديث uid بعد تسجيل الدخول
void updateUid(String newUid) {
  uid = newUid;
  LoggerService.debug(
    'تم تحديث uid إلى: "$uid"',
    tag: 'GlobalVariables.updateUid',
  );
}

/// دالة لتهيئة المتغيرات العامة

/// دالة للحصول على المستخدم الحالي من Firebase
User? getCurrentUser() {
  try {
    // التحقق من أن Firebase تم تهيئته
    if (Firebase.apps.isNotEmpty) {
      return FirebaseAuth.instance.currentUser;
    } else {
      // إذا لم يتم تهيئة Firebase بعد، نرجع null
      LoggerService.warning(
        'محاولة الوصول إلى FirebaseAuth قبل تهيئة Firebase',
        tag: 'getCurrentUser',
      );
      return null;
    }
  } catch (e) {
    // التقاط أي استثناءات قد تحدث
    LoggerService.error(
      'خطأ في الوصول إلى FirebaseAuth: $e',
      error: e,
      tag: 'getCurrentUser',
    );
    return null;
  }
}
