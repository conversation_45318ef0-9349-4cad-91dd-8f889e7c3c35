import 'package:flutter/material.dart';

/// ثوابت الألوان في التطبيق
///
/// يحتوي هذا الكلاس على جميع ثوابت الألوان المستخدمة في التطبيق.
class AppColors {
  // منع إنشاء نسخة من الكلاس
  AppColors._();

  /// الألوان الأساسية
  static const Color primary = Color(0xFF0D7743); // لون أخضر غامق مثل الأشجار
  static const Color primaryDark = Color(0xFF0B5E36); // لون أخضر أغمق
  static const Color primaryLight = Color(0xFF4CAF50); // لون أخضر فاتح
  static const Color accent = Color(0xFF8BC34A); // لون أخضر مصفر

  /// اللون الأخضر الافتراضي (للتوافق مع الكود القديم)
  static const Color dufaultGreencolor = primary;

  /// ألوان الخلفية
  static const Color background = Colors.white;
  static const Color surface = Colors.white;
  static const Color card = Colors.white;
  static const Color cardDark = Color(0xFFF5F5F5);

  /// ألوان النصوص
  static const Color textPrimary = Color(0xFF212121); // لون أسود غامق
  static const Color textSecondary = Color(0xFF757575); // لون رمادي
  static const Color textHint = Color(0xFF9E9E9E); // لون رمادي فاتح
  static const Color textOnPrimary =
      Colors.white; // لون أبيض للنصوص على الخلفية الخضراء
  static const Color textOnAccent =
      Colors.white; // لون أبيض للنصوص على الخلفية الخضراء المصفرة

  /// ألوان الحالة
  static const Color error = Color(0xFFD32F2F); // لون أحمر للأخطاء
  static const Color success = Color(0xFF388E3C); // لون أخضر للنجاح
  static const Color warning = Color(0xFFFFA000); // لون برتقالي للتحذيرات
  static const Color info = Color(0xFF1976D2); // لون أزرق للمعلومات

  /// ألوان الأزرار
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = accent;
  static const Color buttonDisabled = Color(0xFFBDBDBD);
  static const Color buttonText = textOnPrimary;

  /// ألوان الحدود
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocused = primary;
  static const Color borderError = error;

  /// ألوان أخرى
  static const Color divider = Color(0xFFBDBDBD); // لون رمادي فاتح للفواصل
  static const Color disabled =
      Color(0xFFE0E0E0); // لون رمادي فاتح للعناصر المعطلة
  static const Color shadow = Color(0x40000000); // لون أسود شفاف للظلال

  /// ألوان التفاعل
  static const Color ripple = Color(0x20000000); // لون أسود شفاف للتموجات
  static const Color highlight = Color(0x20000000); // لون أسود شفاف للتظليل
  static const Color splash = Color(0x20000000); // لون أسود شفاف للرشاش

  /// ألوان الشبكات الاجتماعية
  static const Color facebook = Color(0xFF1877F2);
  static const Color google = Color(0xFFDB4437);
  static const Color twitter = Color(0xFF1DA1F2);

  /// ألوان الطقس
  static const Color sunny = Color(0xFFFFA000);
  static const Color cloudy = Color(0xFF78909C);
  static const Color rainy = Color(0xFF42A5F5);
  static const Color stormy = Color(0xFF5C6BC0);
  static const Color snowy = Color(0xFFECEFF1);
  static const Color foggy = Color(0xFFB0BEC5);

  /// ألوان المحاصيل
  static const Color crops = Color(0xFF8BC34A);
  static const Color fruits = Color(0xFFFF9800);
  static const Color vegetables = Color(0xFF4CAF50);
  static const Color legumes = Color(0xFF795548);
  static const Color oilCrops = Color(0xFFFFC107);

  /// ألوان الآفات والأمراض
  static const Color pests = Color(0xFFFF5722);
  static const Color diseases = Color(0xFFE91E63);
  static const Color lowSeverity = Color(0xFF4CAF50);
  static const Color mediumSeverity = Color(0xFFFFC107);
  static const Color highSeverity = Color(0xFFFF5722);
  static const Color criticalSeverity = Color(0xFFF44336);

  /// ألوان المنتدى المجتمعي
  static const Color like = Color(0xFFF44336);
  static const Color comment = Color(0xFF2196F3);
  static const Color share = Color(0xFF4CAF50);
  static const Color postBackground = Colors.white;
  static const Color postBorder = Color(0xFFE0E0E0);

  /// ألوان التدرج
  static const List<Color> primaryGradient = [
    Color(0xFF0D7743),
    Color(0xFF4CAF50),
  ];

  static const List<Color> accentGradient = [
    Color(0xFF8BC34A),
    Color(0xFFCDDC39),
  ];

  static const List<Color> errorGradient = [
    Color(0xFFD32F2F),
    Color(0xFFFF5722),
  ];

  static const List<Color> successGradient = [
    Color(0xFF388E3C),
    Color(0xFF4CAF50),
  ];

  static const List<Color> warningGradient = [
    Color(0xFFFFA000),
    Color(0xFFFFCA28),
  ];

  static const List<Color> infoGradient = [
    Color(0xFF1976D2),
    Color(0xFF42A5F5),
  ];

  static const List<Color> sunnyGradient = [
    Color(0xFFFFA000),
    Color(0xFFFFD54F),
  ];

  static const List<Color> cloudyGradient = [
    Color(0xFF78909C),
    Color(0xFFB0BEC5),
  ];

  static const List<Color> rainyGradient = [
    Color(0xFF42A5F5),
    Color(0xFF90CAF9),
  ];

  /// ألوان الوضع الداكن
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkCard = Color(0xFF242424);
  static const Color darkDivider = Color(0xFF424242);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Color(0xFFB0B0B0);
  static const Color darkBorder = Color(0xFF424242);

  /// الحصول على لون من قيمة hex
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// الحصول على لون بشفافية معينة
  static Color withOpacity(Color color, double opacity) {
    return color.withAlpha((opacity * 255).round());
  }

  /// الحصول على لون أفتح
  static Color lighter(Color color, [double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight =
        hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  /// الحصول على لون أغمق
  static Color darker(Color color, [double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
