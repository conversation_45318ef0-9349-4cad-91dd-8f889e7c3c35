/// نقاط النهاية للـ APIs الخارجية
/// 
/// يحتوي على جميع روابط الخدمات الخارجية المستخدمة في التطبيق
class ApiEndpoints {
  
  /// الرابط الأساسي للخدمات الزراعية
  static const String baseUrl = 'https://api.agriculture-yemen.com';
  
  /// خدمة الذكاء الاصطناعي
  static const String aiAnalysisUrl = 'https://ai.agriculture-yemen.com';
  
  /// خدمة الدفع
  static const String paymentUrl = 'https://payment.agriculture-yemen.com';
  
  /// خدمة الرسائل النصية
  static const String smsUrl = 'https://sms.agriculture-yemen.com';
  
  /// خدمة الطقس
  static const String weatherUrl = 'https://api.openweathermap.org/data/2.5';
  
  /// خدمة الخرائط
  static const String mapsUrl = 'https://maps.googleapis.com/maps/api';

  /// === نقاط نهاية مراقبة النبات ===
  
  /// إنشاء طلب مراقبة جديد
  static const String createMonitoringRequest = '$baseUrl/monitoring/create';
  
  /// تحديث طلب مراقبة
  static const String updateMonitoringRequest = '$baseUrl/monitoring/update';
  
  /// الحصول على طلبات المراقبة
  static const String getMonitoringRequests = '$baseUrl/monitoring/list';
  
  /// حذف طلب مراقبة
  static const String deleteMonitoringRequest = '$baseUrl/monitoring/delete';

  /// === نقاط نهاية الذكاء الاصطناعي ===
  
  /// تحليل صور النبات
  static const String analyzePlantImages = '$aiAnalysisUrl/analyze/plant';
  
  /// تشخيص الأمراض
  static const String diagnoseDiseases = '$aiAnalysisUrl/diagnose/diseases';
  
  /// تحديد الآفات
  static const String identifyPests = '$aiAnalysisUrl/identify/pests';
  
  /// تقييم صحة النبات
  static const String assessPlantHealth = '$aiAnalysisUrl/assess/health';

  /// === نقاط نهاية الاستشارات ===
  
  /// إنشاء استشارة جديدة
  static const String createConsultation = '$baseUrl/consultations/create';
  
  /// تحديث استشارة
  static const String updateConsultation = '$baseUrl/consultations/update';
  
  /// الحصول على الاستشارات
  static const String getConsultations = '$baseUrl/consultations/list';

  /// === نقاط نهاية حجز المواعيد ===
  
  /// إنشاء موعد جديد
  static const String createAppointment = '$baseUrl/appointments/create';
  
  /// تحديث موعد
  static const String updateAppointment = '$baseUrl/appointments/update';
  
  /// الحصول على المواعيد المتاحة
  static const String getAvailableSlots = '$baseUrl/appointments/available';
  
  /// إلغاء موعد
  static const String cancelAppointment = '$baseUrl/appointments/cancel';

  /// === نقاط نهاية الإشعارات ===
  
  /// إرسال إشعار
  static const String sendNotification = '$baseUrl/notifications/send';
  
  /// إرسال إشعار جماعي
  static const String sendBulkNotification = '$baseUrl/notifications/bulk';
  
  /// Firebase Cloud Messaging
  static const String fcmUrl = 'https://fcm.googleapis.com/fcm/send';

  /// === نقاط نهاية الدفع ===
  
  /// معالجة دفعة
  static const String processPayment = '$paymentUrl/process';
  
  /// التحقق من حالة الدفع
  static const String checkPaymentStatus = '$paymentUrl/status';
  
  /// استرداد دفعة
  static const String refundPayment = '$paymentUrl/refund';

  /// === نقاط نهاية الرسائل النصية ===
  
  /// إرسال رسالة نصية
  static const String sendSMS = '$smsUrl/send';
  
  /// التحقق من حالة الرسالة
  static const String checkSMSStatus = '$smsUrl/status';

  /// === نقاط نهاية الطقس ===
  
  /// الطقس الحالي
  static const String currentWeather = '$weatherUrl/weather';
  
  /// توقعات الطقس
  static const String weatherForecast = '$weatherUrl/forecast';

  /// === نقاط نهاية الخرائط ===
  
  /// البحث عن مكان
  static const String geocoding = '$mapsUrl/geocode/json';
  
  /// الاتجاهات
  static const String directions = '$mapsUrl/directions/json';
  
  /// الأماكن القريبة
  static const String nearbyPlaces = '$mapsUrl/place/nearbysearch/json';

  /// === نقاط نهاية المهندسين ===
  
  /// الحصول على المهندسين المتاحين
  static const String getAvailableEngineers = '$baseUrl/engineers/available';
  
  /// تعيين مهندس لمهمة
  static const String assignEngineer = '$baseUrl/engineers/assign';
  
  /// تحديث حالة المهندس
  static const String updateEngineerStatus = '$baseUrl/engineers/status';

  /// === نقاط نهاية التقارير ===
  
  /// إنشاء تقرير
  static const String generateReport = '$baseUrl/reports/generate';
  
  /// الحصول على التقارير
  static const String getReports = '$baseUrl/reports/list';
  
  /// تحميل تقرير
  static const String downloadReport = '$baseUrl/reports/download';

  /// === Headers للطلبات ===
  
  /// Headers أساسية
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'Agriculture-Yemen-App/1.0.0',
  };

  /// Headers مع التوثيق
  static Map<String, String> authHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };

  /// Headers للذكاء الاصطناعي
  static Map<String, String> aiHeaders(String apiKey) => {
    ...defaultHeaders,
    'X-API-Key': apiKey,
    'X-Service': 'plant-analysis',
  };

  /// Headers للدفع
  static Map<String, String> paymentHeaders(String apiKey) => {
    ...defaultHeaders,
    'X-Payment-Key': apiKey,
    'X-Merchant-ID': 'agriculture-yemen',
  };

  /// === مفاتيح API ===
  
  /// مفتاح الذكاء الاصطناعي (يجب تخزينه بشكل آمن)
  static const String aiApiKey = 'YOUR_AI_API_KEY';
  
  /// مفتاح الدفع (يجب تخزينه بشكل آمن)
  static const String paymentApiKey = 'YOUR_PAYMENT_API_KEY';
  
  /// مفتاح الرسائل النصية (يجب تخزينه بشكل آمن)
  static const String smsApiKey = 'YOUR_SMS_API_KEY';
  
  /// مفتاح الطقس (يجب تخزينه بشكل آمن)
  static const String weatherApiKey = 'YOUR_WEATHER_API_KEY';
  
  /// مفتاح الخرائط (يجب تخزينه بشكل آمن)
  static const String mapsApiKey = 'YOUR_MAPS_API_KEY';
  
  /// مفتاح Firebase (يجب تخزينه بشكل آمن)
  static const String fcmServerKey = 'YOUR_FCM_SERVER_KEY';

  /// === إعدادات الطلبات ===
  
  /// مهلة الاتصال (بالثواني)
  static const int connectionTimeout = 30;
  
  /// مهلة الاستقبال (بالثواني)
  static const int receiveTimeout = 60;
  
  /// عدد المحاولات عند الفشل
  static const int retryAttempts = 3;

  /// === رموز الاستجابة ===
  
  /// نجح الطلب
  static const int successCode = 200;
  
  /// تم الإنشاء بنجاح
  static const int createdCode = 201;
  
  /// لا يوجد محتوى
  static const int noContentCode = 204;
  
  /// طلب خاطئ
  static const int badRequestCode = 400;
  
  /// غير مصرح
  static const int unauthorizedCode = 401;
  
  /// ممنوع
  static const int forbiddenCode = 403;
  
  /// غير موجود
  static const int notFoundCode = 404;
  
  /// خطأ في الخادم
  static const int serverErrorCode = 500;

  /// منع إنشاء كائن من الكلاس
  ApiEndpoints._();
}
