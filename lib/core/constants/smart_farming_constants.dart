/// ثوابت نظام الزراعة الذكية
/// يحتوي على جميع القيم الثابتة المستخدمة في نظام الزراعة الذكية
/// وفق المعايير الـ18 - توحيد القيم باستخدام ملفات الثوابت

class SmartFarmingConstants {
  // منع إنشاء كائن من الكلاس
  SmartFarmingConstants._();

  // ===== عناوين التبويبات =====
  static const String cropRecordsTabTitle = 'سجل المحاصيل';
  static const String cropMonitoringTabTitle = 'مراقبة المحاصيل';
  static const String analyticsTabTitle = 'التحليلات الذكية';

  // ===== عناوين الأقسام =====
  static const String smartFarmingTitle = 'الزراعة الذكية';
  static const String cropRecordsTitle = 'سجل المحاصيل';
  static const String smartMonitoringTitle = 'نظام المراقبة الذكية';
  static const String smartAnalyticsTitle = 'التحليلات الذكية';
  static const String realTimeDataTitle = 'بيانات الوقت الحقيقي';
  static const String smartRecommendationsTitle = 'توصيات ذكية بناءً على البيانات';
  static const String cropPerformanceTitle = 'أداء المحاصيل خلال العام';

  // ===== أزرار وإجراءات =====
  static const String addNewCropButton = 'إضافة محصول جديد';
  static const String addCropDialogTitle = 'إضافة محصول جديد';
  static const String editCropDataButton = 'تعديل البيانات';
  static const String deleteCropButton = 'حذف المحصول';
  static const String saveButton = 'حفظ';
  static const String cancelButton = 'إلغاء';
  static const String closeButton = 'إغلاق';
  static const String updateSensorDataButton = 'تحديث البيانات';

  // ===== تسميات الحقول =====
  static const String cropNameLabel = 'اسم المحصول';
  static const String cropTypeLabel = 'نوع المحصول';
  static const String cropAreaLabel = 'المساحة (هكتار)';
  static const String plantingDateLabel = 'تاريخ الزراعة';
  static const String expectedHarvestLabel = 'تاريخ الحصاد المتوقع';
  static const String cropLocationLabel = 'الموقع';
  static const String cropProvinceLabel = 'المحافظة';
  static const String cropNotesLabel = 'ملاحظات';
  static const String cropStatusLabel = 'الحالة';
  static const String growthProgressLabel = 'تقدم النمو';

  // ===== أنواع المحاصيل اليمنية =====
  static const List<String> yemeniCropTypes = [
    'قمح',
    'ذرة رفيعة',
    'ذرة شامية',
    'أرز',
    'شعير',
    'دخن',
    'طماطم',
    'بصل',
    'بطاطس',
    'جزر',
    'خيار',
    'باذنجان',
    'فلفل',
    'كوسة',
    'ملفوف',
    'خس',
    'فجل',
    'لوبيا',
    'فاصوليا',
    'بازلاء',
    'حمص',
    'عدس',
    'سمسم',
    'قطن',
    'تبغ',
    'قصب السكر',
    'موز',
    'مانجو',
    'جوافة',
    'رمان',
    'عنب',
    'تين',
    'نخيل',
    'قات',
    'بن',
    'هيل',
  ];

  // ===== المحافظات اليمنية =====
  static const List<String> yemeniProvinces = [
    'صنعاء',
    'عدن',
    'تعز',
    'الحديدة',
    'إب',
    'ذمار',
    'حضرموت',
    'المحويت',
    'حجة',
    'صعدة',
    'عمران',
    'البيضاء',
    'لحج',
    'أبين',
    'شبوة',
    'المهرة',
    'الجوف',
    'مأرب',
    'الضالع',
    'ريمة',
    'المحويت',
    'أرخبيل سقطرى',
  ];

  // ===== تسميات المستشعرات =====
  static const String soilMoistureLabel = 'رطوبة التربة';
  static const String temperatureLabel = 'درجة الحرارة';
  static const String humidityLabel = 'الرطوبة الجوية';
  static const String lightIntensityLabel = 'شدة الإضاءة';
  static const String soilPHLabel = 'حموضة التربة';
  static const String nitrogenLevelLabel = 'مستوى النيتروجين';
  static const String phosphorusLevelLabel = 'مستوى الفوسفور';
  static const String potassiumLevelLabel = 'مستوى البوتاسيوم';
  static const String windSpeedLabel = 'سرعة الرياح';
  static const String rainfallLabel = 'كمية الأمطار';

  // ===== وحدات القياس =====
  static const String percentageUnit = '%';
  static const String celsiusUnit = '°C';
  static const String luxUnit = 'لوكس';
  static const String pHUnit = 'pH';
  static const String kmPerHourUnit = 'كم/ساعة';
  static const String millimeterUnit = 'مم';
  static const String hectareUnit = 'هكتار';

  // ===== رسائل النجاح =====
  static const String cropAddedSuccessMessage = 'تم إضافة المحصول بنجاح';
  static const String cropUpdatedSuccessMessage = 'تم تحديث المحصول بنجاح';
  static const String cropDeletedSuccessMessage = 'تم حذف المحصول بنجاح';
  static const String sensorDataUpdatedMessage = 'تم تحديث بيانات المستشعرات';

  // ===== رسائل الخطأ =====
  static const String loadingDataErrorMessage = 'خطأ في تحميل بيانات الزراعة الذكية';
  static const String addingCropErrorMessage = 'خطأ في إضافة المحصول';
  static const String updatingCropErrorMessage = 'خطأ في تحديث المحصول';
  static const String deletingCropErrorMessage = 'خطأ في حذف المحصول';
  static const String sensorUpdateErrorMessage = 'خطأ في تحديث بيانات المستشعرات';
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String validationErrorMessage = 'يرجى التحقق من البيانات المدخلة';

  // ===== رسائل التحذير =====
  static const String lowSoilMoistureAlert = 'رطوبة التربة منخفضة - يحتاج ري';
  static const String highSoilMoistureAlert = 'رطوبة التربة عالية - قلل الري';
  static const String highTemperatureAlert = 'درجة الحرارة عالية جداً - قد تضر بالمحصول';
  static const String lowTemperatureAlert = 'درجة الحرارة منخفضة - قد تؤثر على النمو';
  static const String lowNitrogenAlert = 'مستوى النيتروجين منخفض - أضف سماد نيتروجيني';
  static const String lowPhosphorusAlert = 'مستوى الفوسفور منخفض - أضف سماد فوسفاتي';
  static const String lowPotassiumAlert = 'مستوى البوتاسيوم منخفض - أضف سماد بوتاسي';

  // ===== حالات المحاصيل =====
  static const String plantingStatus = 'قيد الزراعة';
  static const String growingStatus = 'قيد النمو';
  static const String floweringStatus = 'مرحلة الإزهار';
  static const String fruitingStatus = 'مرحلة الإثمار';
  static const String readyToHarvestStatus = 'جاهز للحصاد';
  static const String harvestedStatus = 'تم الحصاد';
  static const String damagedStatus = 'متضرر';
  static const String failedStatus = 'فاشل';

  // ===== مراحل النمو =====
  static const String seedlingStage = 'مرحلة الإنبات';
  static const String vegetativeStage = 'مرحلة النمو الخضري';
  static const String floweringStage = 'مرحلة الإزهار';
  static const String fruitingStage = 'مرحلة الإثمار';
  static const String maturityStage = 'مرحلة النضج';

  // ===== مستويات المغذيات =====
  static const String lowNutrientLevel = 'منخفض';
  static const String mediumNutrientLevel = 'متوسط';
  static const String highNutrientLevel = 'عالي';

  // ===== حالات صحة المحصول =====
  static const String excellentHealthStatus = 'ممتاز';
  static const String goodHealthStatus = 'جيد';
  static const String fairHealthStatus = 'مقبول';
  static const String poorHealthStatus = 'ضعيف';

  // ===== القيم المثلى للمستشعرات =====
  static const double optimalSoilMoistureMin = 40.0;
  static const double optimalSoilMoistureMax = 70.0;
  static const double optimalTemperatureMin = 20.0;
  static const double optimalTemperatureMax = 30.0;
  static const double optimalSoilPHMin = 6.0;
  static const double optimalSoilPHMax = 7.5;

  // ===== حدود التحذير =====
  static const double lowSoilMoistureThreshold = 30.0;
  static const double highSoilMoistureThreshold = 80.0;
  static const double highTemperatureThreshold = 35.0;
  static const double lowTemperatureThreshold = 15.0;

  // ===== إعدادات التحديث =====
  static const int sensorUpdateIntervalSeconds = 30;
  static const int dataRefreshIntervalMinutes = 5;
  static const int maxRetryAttempts = 3;

  // ===== أحجام وأبعاد =====
  static const double cardElevation = 4.0;
  static const double cardBorderRadius = 16.0;
  static const double iconSize = 24.0;
  static const double largeIconSize = 40.0;
  static const double buttonHeight = 50.0;
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // ===== ألوان =====
  static const String primaryColorHex = '#4CAF50';
  static const String secondaryColorHex = '#2196F3';
  static const String warningColorHex = '#FF9800';
  static const String errorColorHex = '#F44336';
  static const String successColorHex = '#4CAF50';
}
