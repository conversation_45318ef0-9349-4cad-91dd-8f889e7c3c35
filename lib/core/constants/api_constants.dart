import '../utils/services/api_keys_service.dart';

/// ثوابت واجهات برمجة التطبيقات
///
/// يحتوي هذا الكلاس على ثوابت واجهات برمجة التطبيقات المستخدمة في التطبيق.
class ApiConstants {
  // منع إنشاء نسخة من الكلاس
  ApiConstants._();

  /// مفتاح API للطقس (OpenWeatherMap)
  static const String weatherApiKeyName = 'weather_api_key';

  /// مفتاح API الافتراضي للطقس (يستخدم فقط إذا لم يتم العثور على المفتاح في ApiKeysService)
  static const String defaultWeatherApiKey = 'c0e77dfadc4d891c92faec7481e71c0b';

  /// الحصول على مفتاح API للطقس
  static String getWeatherApiKey() {
    return ApiKeysService.getApiKey(weatherApiKeyName) ?? defaultWeatherApiKey;
  }

  /// عنوان URL الأساسي لـ OpenWeatherMap
  static const String openWeatherMapBaseUrl =
      'https://api.openweathermap.org/data/2.5';

  /// عنوان URL الأساسي لـ Open-Meteo
  static const String openMeteoBaseUrl =
      'https://api.open-meteo.com/v1/forecast';

  /// الحصول على عنوان URL للطقس الحالي
  static String weatherUrl(double lat, double lon) =>
      '$openWeatherMapBaseUrl/weather?lat=$lat&lon=$lon&units=metric&appid=${getWeatherApiKey()}&lang=ar';

  /// الحصول على عنوان URL للتوقعات بالساعة
  static String forecastHourlyUrl(double lat, double lon) =>
      '$openWeatherMapBaseUrl/forecast?lat=$lat&lon=$lon&units=metric&appid=${getWeatherApiKey()}&lang=ar';

  /// الحصول على عنوان URL للطقس حسب المدينة
  static String weatherByCityUrl(String city) =>
      '$openWeatherMapBaseUrl/weather?q=$city&appid=${getWeatherApiKey()}&units=metric&lang=ar';

  /// الحصول على عنوان URL للتوقعات الأسبوعية
  static String weeklyForecastUrl(double lat, double lon) =>
      '$openMeteoBaseUrl?current=&daily=weather_code,temperature_2m_max,temperature_2m_min&timezone=auto&latitude=$lat&longitude=$lon';

  /// مفاتيح التخزين المؤقت للطقس
  static String currentWeatherCacheKey(double lat, double lon) =>
      'weather_current_${lat.toStringAsFixed(2)}_${lon.toStringAsFixed(2)}';
  static String hourlyForecastCacheKey(double lat, double lon) =>
      'weather_hourly_${lat.toStringAsFixed(2)}_${lon.toStringAsFixed(2)}';
  static String weeklyForecastCacheKey(double lat, double lon) =>
      'weather_weekly_${lat.toStringAsFixed(2)}_${lon.toStringAsFixed(2)}';

  /// مدة صلاحية التخزين المؤقت للطقس (بالدقائق)
  static const int currentWeatherCacheMinutes = 30; // 30 دقيقة
  static const int hourlyForecastCacheMinutes = 60; // ساعة واحدة
  static const int weeklyForecastCacheMinutes = 120; // ساعتان
}
